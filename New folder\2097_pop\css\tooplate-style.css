/*

Template 2097 Pop

https://www.tooplate.com/view/2097-pop

*/

body {
  font-family: "Open Sans", Helvetica, Arial, sans-serif;
  font-size: 19px;
  font-weight: 300;
  color: white;
  position: relative;
}

h1 {
  font-size: 2.8rem;
}

#tm-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url(../img/pop-bg.jpg);
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  z-index: -1;
  transition: all 0.3s ease;
}

#tm-bg:after {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

#tm-wrap {
  display: flex;
  align-items: center;
  height: 100%;
  min-height: 100vh;
}

p {
  line-height: 1.9;
  letter-spacing: 1px;
}

.tm-main-content {
  display: flex;
  align-items: center;
  padding: 50px 15px;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  overflow: auto;
}

.tm-site-header-col {
  display: flex;
  align-items: center;
}

.tm-site-header {
  margin: 10px;
  text-align: right;
  width: 100%;
}

.tm-nav ul {
  padding-left: 0;
  display: block;
}

.tm-nav li {
  list-style: none;
  display: inline-block;
  border: 1px solid white;
  float: left;
  margin: 10px;
}

.tm-nav-link {
  color: white;
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 165px;
  height: 165px;
  text-align: center;
  border: 1px solid white;
  cursor: pointer;
  position: relative;
}

.tm-nav-text {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 10px;
}

.fa-3x {
  font-size: 2.5em;
}

.product {
  background: transparent;
  transition: all 0.3s ease;
}

.product:hover {
  background: #2c2a35;
  color: #a9a9a9;
}

.tm-nav-icon {
  display: block;
  margin-bottom: 25px;
}

/* Expanding Grid Items from Codrops */
.tm-page-title {
  font-weight: 300;
}
.content {
  max-width: 370px;
}
.grid {
  display: flex;
  flex-wrap: wrap;
}
.grid__item {
  margin: 10px;
}
.product {
  position: relative;
  cursor: pointer;
}

.product__bg {
  width: 100%;
  height: 100%;
  top: 0px;
}

.product__bg,
.product__description {
  opacity: 0;
  position: absolute;
}

.product__description {
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  display: none;
}

.tm-fa-close {
  font-size: 30px;
}

.details {
  font-size: 1rem;
  width: 100%;
  height: 100%;
  bottom: 0;
  left: 0;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  justify-content: center;
  pointer-events: none;
  padding: 15px;
  display: none;
}

.details > * {
  position: relative;
  opacity: 0;
}

.details--open {
  pointer-events: auto;
}

.details__bg {
  width: 100%;
  max-width: 900px;
  left: 0;
  right: 0;
  margin-left: auto;
  margin-right: auto;
}

.details__bg--down {
  background: #2c2a35;
}

.details__bg {
  padding: 50px;
  transform-origin: 0 0;
}

.details__close {
  position: absolute;
  top: 5px;
  right: 0;
  border: 0;
  background: none;
  margin: 1em;
  cursor: pointer;
  font-size: 0.85em;
  color: white;
  z-index: 1001;
}

/* Team */
.tm-reverse-sm {
  flex-direction: row;
}

/* Our Work */
.tm-slider-img-link {
  padding-left: 10px;
  padding-right: 10px;
}

.slick-dots li button:before {
  font-size: 20px;
  color: white;
  opacity: 1;
}

.slick-dots {
  position: absolute;
  bottom: -40px;
  display: block;
  width: 100%;
  padding: 0;
  list-style: none;
  text-align: center;
}

.slick-dots li.slick-active button:before {
  opacity: 0.75;
  color: deepskyblue;
}

/* Contact */
.form-control {
  font-size: 1.1rem;
  border-radius: 0;
  border: none;
  background-color: #353541;
  color: white;
  padding: 0.75rem;
}

.form-control:focus {
  color: #cecece;
  background-color: #4d4c59;
  border-color: transparent;
}

.tm-col-email {
  padding-left: 0;
}

.tm-btn-submit {
  background-color: #006599;
}

.tm-btn-gray {
  background-color: #494752;
  color: white;
}

.tm-btn-gray:hover {
  background-color: #77757d;
  color: white;
}

.btn {
  padding: 0.5rem 2.4rem;
  font-size: 1.2rem;
  border-radius: 0;
}

.btn-primary {
  border-color: transparent;
}

.btn-primary:hover {
  background-color: #0e7fb9;
  border-color: transparent;
}

/* Footer */
footer {
  position: absolute;
  bottom: 30px;
  text-align: center;
  left: 0;
  right: 0;
}

.tm-text-highlight {
  color: white;
}
.tm-text-highlight:hover {
  color: red;
}
.tm-main-content.tm-footer-relative {
  flex-direction: column;
}
.tm-main-content.tm-footer-relative footer {
  position: relative;
  bottom: 0;
  margin-top: 50px;
}

.tm-copyright-text {
  background-color: rgba(0, 0, 0, 0.6);
  display: inline-block;
  padding: 10px 30px;
}

a,
button {
  transition: all 0.3s ease;
}
a:hover,
a:focus {
  text-decoration: none;
  outline: none;
}

p:last-child {
  margin-bottom: 0;
}

@media (min-width: 900px) {
  .container {
    max-width: 900px;
  }
}

@media (min-width: 767px) and (max-width: 991px), (max-width: 440px) {
  .tm-nav-link {
    width: 145px;
    height: 145px;
  }
}

@media (max-width: 767px) {
  .container {
    max-width: 450px;
  }
  .content {
    margin-left: auto;
    margin-right: 0;
  }
  .tm-nav ul {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-end;
  }
  .grid {
    justify-content: flex-end;
  }
  .tm-col-email {
    padding-left: 15px;
  }
  .tm-reverse-sm {
    flex-direction: column-reverse;
  }
}

@media (max-width: 544px) {
  .tm-main-content {
    flex-direction: column;
  }
  .details__bg {
    padding: 55px 40px;
  }
  .slick-dots li button:before {
    font-size: 15px;
  }

  footer {
    position: relative;
    bottom: 0;
  }
}

@media (max-width: 419px) {
  .tm-nav-link {
    width: 120px;
    height: 120px;
  }

  .fa-3x {
    font-size: 2.2em;
  }
}
