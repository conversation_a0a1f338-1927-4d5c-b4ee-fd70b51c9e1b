/* Slick Slider */
.slick-slider {
    position: relative;
    display: block;
    -webkit-box-sizing: border-box;
            box-sizing: border-box;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -ms-touch-action: pan-y;
    touch-action: pan-y;
    -webkit-tap-highlight-color: transparent;
  }
  
  .slick-list {
    position: relative;
    overflow: hidden;
    display: block;
    margin: 0;
    padding: 0;
  }
  
  .slick-list:focus {
    outline: none;
  }
  
  .slick-list.dragging {
    cursor: move;
    cursor: grab;
    cursor: -webkit-grab;
  }
  
  .slick-slider .slick-track,
  .slick-slider .slick-list {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  
  .slick-track {
    position: relative;
    left: 0;
    top: 0;
    display: block;
    margin-left: auto;
    margin-right: auto;
  }
  
  .slick-track:before, .slick-track:after {
    content: "";
    display: table;
  }
  
  .slick-track:after {
    clear: both;
  }
  
  .slick-loading .slick-track {
    visibility: hidden;
  }
  
  .slick-slide {
    float: left;
    height: 100%;
    min-height: 1px;
    outline: 0;
    display: none;
  }
  
  [dir="rtl"] .slick-slide {
    float: right;
  }
  
  .slick-slide.slick-loading img {
    display: none;
  }
  
  .slick-slide.dragging img {
    pointer-events: none;
  }
  
  .slick-initialized .slick-slide {
    display: block;
  }
  
  .slick-loading .slick-slide {
    visibility: hidden;
  }
  
  .slick-vertical .slick-slide {
    display: block;
    height: auto;
    border: 1px solid transparent;
  }
  
  .slick-arrow.slick-hidden {
    display: none;
  }
  
  /* Arrows */
  .slick-prev,
  .slick-next {
    position: absolute;
    display: block;
    font-size: 0px;
    cursor: pointer;
    background-color: transparent;
    color: transparent;
    top: 50%;
    -webkit-transform: translate(0, -50%);
    transform: translate(0, -50%);
    padding: 0;
    border: none;
    outline: none;
    z-index: 1;
  }
  
  .slick-prev:hover, .slick-prev:focus,
  .slick-next:hover,
  .slick-next:focus {
    outline: none;
    background-color: transparent;
    color: transparent;
  }
  
  .slick-prev:hover:before, .slick-prev:focus:before,
  .slick-next:hover:before,
  .slick-next:focus:before {
    opacity: 1;
  }
  
  .slick-prev.slick-disabled:before,
  .slick-next.slick-disabled:before {
    opacity: 1;
  }
  
  .slick-prev:before,
  .slick-next:before {
    font-family: fontawesome;
    font-size: 30px;
    line-height: 1;
    color: #000;
    opacity: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  .slick-prev {
    left: -20px;
  }
  
  [dir="rtl"] .slick-prev {
    left: auto;
    right: -25px;
  }
  
  .slick-prev:before {
    content: '\f104';
  }
  
  [dir="rtl"] .slick-prev:before {
    content: '';
  }
  
  .slick-next {
    right: -20px;
  }
  
  [dir="rtl"] .slick-next {
    left: -25px;
    right: auto;
  }
  
  .slick-next:before {
    content: '\f105';
  }
  
  [dir="rtl"] .slick-next:before {
    content: '';
  }
  
  /* Dots */
  .slick-dotted.slick-slider {
    margin-bottom: 30px;
  }
  
  .slick-dots {
    position: absolute;
    bottom: -25px;
    list-style: none;
    display: block;
    text-align: center;
    padding: 0;
    margin: 0;
    width: 100%;
  }
  
  .slick-dots li {
    position: relative;
    display: inline-block;
    margin: 0 5px;
    padding: 0;
    cursor: pointer;
  }
  
  .slick-dots li button {
    border: 0;
    background-color: transparent;
    outline: none;
    font-size: 0px;
    color: transparent;
    padding: 0;
    cursor: pointer;
  }
  
  .slick-dots li button:hover, .slick-dots li button:focus {
    outline: none;
  }
  
  .slick-dots li button:hover:before, .slick-dots li button:focus:before {
    opacity: 1;
  }
  
  .slick-dots li button:before {
    content: '\f111';
    font-family: fontawesome;
    font-size: 18px;
    text-align: center;
    color: #000;
    opacity: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  .slick-dots li.slick-active button:before {
    color: red;
    opacity: 1;
    content: '\f192';
  }