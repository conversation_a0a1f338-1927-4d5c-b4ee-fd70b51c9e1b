/*

Tooplate 2126 Antique Cafe

https://www.tooplate.com/view/2126-antique-cafe

*/


body {
    font-family: 'Raleway', sans-serif;
    background-color: #666;
}

p a {
	color: #FF6;
}

p a:hover {
	color: #9FF;
}

#about p a { color: #066; }
#about p a:hover { color: #F36; }

#contact p a { color: #066; }

footer span a { color: #FFF; }

.tm-logo-font {
    font-family: '<PERSON>', sans-serif;
}

.tm-container {
    max-width: 1200px;
}

.parallax-window {
    min-height: 1064px;
    background: transparent;
}

.tm-text-yellow {
    color: #FC6;
}

.tm-text-gold {
    color: #CC9966;
}

.tm-text-brown {
    color: #544639;
}

.tm-text-green {
    color: #006666;
}

a.tm-bg-green:hover {
    background-color: #0a8585;
}

.tm-bg-brown {
    background-color: #544639;
}

.tm-bg-green {
    background-color: #006666;
}

.tm-border-gold {
    border-color: #CC9966;
}

.tm-intro-width {
    max-width: 436px;
}

.tm-item-container {
    max-width: 520px;
}

.input:focus {
    outline: none !important;
    border:1px solid #CC9966;
    box-shadow: 0 0 10px #b67533;
}

input::placeholder,
textarea::placeholder { /* Chrome, Firefox, Opera, Safari 10.1+ */
    color: rgb(214, 212, 212);
    opacity: 1; /* Firefox */
}
  
input:-ms-input-placeholder,
textarea:-ms-input-placeholder { /* Internet Explorer 10-11 */
    color: rgb(214, 212, 212);
}
  
input::-ms-input-placeholder,
textarea::-ms-input-placeholder { /* Microsoft Edge */
    color: rgb(214, 212, 212);
}

#tm-nav {
    z-index: 1000;
    transition: all 0.3s ease;
}

#tm-nav.scroll {
    background-color: rgba(0,0,0,0.7);
}

#tm-nav li a {
    border-bottom-color: transparent;
    transition: all 0.3s ease;
}

#tm-nav li a.current,
#tm-nav li a:hover {
      border-bottom: 4px solid #CC9966;
}

.tm-text-2xl {
    font-size: 1.2rem;
    line-height: 2rem;
}

@media (min-width: 768px) {
    #tm-nav.scroll .tm-container {
        padding-top: 10px;
        padding-bottom: 10px;
    }
}

@media (max-width: 767px) {
    #tm-nav {
        width: auto;
        right: 0;
        border-radius: 5px;
    }
}

@media (max-width: 639px) {
    .parallax-window {
        min-height: 600px;
    }
}

@media (max-width: 370px) {
    .tm-menu-item {
        flex-direction: column;
    }

    .tm-menu-item-2 {
        flex-direction: column-reverse;
    }

    .tm-menu-item img {
        margin-bottom: 10px;
    }

    .tm-menu-item-2 img {
        margin-bottom: 10px;
    }
}