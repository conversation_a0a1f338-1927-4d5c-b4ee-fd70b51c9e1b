# دليل التعليقات التوضيحية - Comments Guide

## نظرة عامة - Overview

هذا المشروع عبارة عن متجر إلكتروني متكامل يحتوي على نظام سلة تسوق متطور مع دعم الكوبونات وإرسال الطلبات عبر واتساب.

## هيكل الملفات - File Structure

### الملفات الرئيسية - Main Files

#### 1. index.html
- **الوصف**: الصفحة الرئيسية للموقع
- **المحتوى**:
  - رأس الصفحة مع القائمة الرئيسية
  - قسم العروض والمنتجات
  - نافذة سلة التسوق
  - نظام الكوبونات
  - معلومات العميل

#### 2. script.js
- **الوصف**: ملف الجافاسكريبت الرئيسي
- **الوظائف الأساسية**:
  - إدارة سلة التسوق
  - نظام الكوبونات
  - إدارة المخزون
  - إرسال الطلبات عبر واتساب

#### 3. styles.css
- **الوصف**: ملف التنسيقات الرئيسي
- **المحتوى**:
  - تنسيقات الهيدر والقوائم
  - تنسيقات سلة التسوق
  - تنسيقات الكوبونات
  - التنسيقات المتجاوبة

#### 4. admin.html
- **الوصف**: لوحة إدارة المتجر
- **الوظائف**:
  - إدارة المنتجات
  - إدارة الكوبونات
  - إدارة العروض
  - إعدادات الموقع

## الوظائف الرئيسية - Main Functions

### 1. نظام سلة التسوق - Shopping Cart System

```javascript
// إضافة منتج للسلة
function addToCart(id, name, price, image)

// إزالة منتج من السلة
function removeFromCart(id)

// تحديث كمية المنتج
function updateQuantity(id, newQuantity)

// إفراغ السلة
function clearCart()

// عرض/إخفاء السلة
function toggleCart()
```

### 2. نظام الكوبونات - Coupon System

```javascript
// تطبيق كوبون خصم
function applyCoupon()

// إزالة الكوبون
function removeCoupon()

// تحميل الكوبونات
function loadCoupons()
```

### 3. إدارة المخزون - Stock Management

```javascript
// تحديث المخزون
function updateProductStock(productId, quantity)

// إعادة الكمية للمخزون
function returnProductStock(productId, quantity)
```

## التعليقات التوضيحية - Code Comments

### أنواع التعليقات المستخدمة:

#### 1. تعليقات الأقسام الرئيسية
```css
/* =============================================== */
/* اسم القسم - Section Name */
/* =============================================== */
```

#### 2. تعليقات الوظائف
```javascript
/**
 * وصف الوظيفة
 * Function description
 * @param {type} paramName - وصف المعامل
 */
```

#### 3. تعليقات الأسطر
```javascript
const cart = []; // سلة التسوق - Shopping cart
```

#### 4. تعليقات HTML
```html
<!-- وصف العنصر - Element description -->
<div class="element">
```

## الميزات المضافة - Added Features

### 1. تحسينات سلة التسوق:
- ✅ واجهة محسنة مع تأثيرات بصرية
- ✅ عرض تفصيلي للمنتجات
- ✅ حساب المجاميع والخصومات
- ✅ معلومات التوفير

### 2. نظام الكوبونات المحسن:
- ✅ واجهة سهلة الاستخدام
- ✅ التحقق من صحة الكوبونات
- ✅ عرض رسائل الحالة
- ✅ إدارة متقدمة للكوبونات

### 3. تحسينات التصميم:
- ✅ تصميم متجاوب
- ✅ ألوان متناسقة
- ✅ أيقونات واضحة
- ✅ تأثيرات حركية

## إرشادات الصيانة - Maintenance Guidelines

### 1. إضافة تعليقات جديدة:
- استخدم التنسيق المحدد أعلاه
- اكتب التعليقات بالعربية والإنجليزية
- كن واضحاً ومختصراً

### 2. تحديث الكود:
- احرص على تحديث التعليقات عند تغيير الكود
- اتبع نفس نمط التعليقات الموجود
- اختبر الكود بعد التحديث

### 3. إضافة ميزات جديدة:
- أضف تعليقات توضيحية شاملة
- وثق المعاملات والقيم المرجعة
- اشرح الغرض من الميزة

## الدعم والمساعدة - Support

للحصول على المساعدة أو الإبلاغ عن مشاكل:
1. راجع التعليقات في الكود
2. تحقق من ملف README الرئيسي
3. اتصل بفريق التطوير

---

**ملاحظة**: هذا الدليل يحتوي على شرح مفصل للتعليقات التوضيحية المضافة للمشروع لتسهيل فهم الكود وصيانته.
