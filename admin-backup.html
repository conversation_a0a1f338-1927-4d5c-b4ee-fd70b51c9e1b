<!DOCTYPE html>
<!-- Updated: حسابات دخول الموظفين - Employee Login Accounts System -->
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - حسابات دخول الموظفين محدثة</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="admin-styles.css?v=2024">

    <style>
        /* شاشة التحميل */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            color: white;
            font-family: 'Cairo', sans-serif;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255,255,255,0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 2rem;
        }

        .loading-text {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            text-align: center;
        }

        .loading-subtitle {
            font-size: 1rem;
            opacity: 0.8;
            text-align: center;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* إخفاء المحتوى أثناء التحميل */
        body.loading .sidebar,
        body.loading .main-content {
            display: none;
        }

        /* نظام إدارة الموظفين */
        .header-employee-btn {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            border: none;
            color: white;
            padding: 0.8rem 1.5rem;
            border-radius: 8px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
            position: relative;
            cursor: pointer;
            font-family: 'Cairo', sans-serif;
        }

        .header-employee-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
            text-decoration: none;
            color: white;
        }

        .security-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #f39c12;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        /* أنماط النوافذ المنبثقة المحسنة لنظام الموظفين */
        .employee-modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(44, 62, 80, 0.9));
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            backdrop-filter: blur(15px);
            font-family: 'Cairo', sans-serif;
            animation: overlayFadeIn 0.4s ease-out;
        }

        @keyframes overlayFadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .employee-modal {
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border-radius: 25px;
            box-shadow:
                0 30px 100px rgba(0, 0, 0, 0.5),
                0 10px 40px rgba(52, 152, 219, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            border: 2px solid rgba(255, 255, 255, 0.4);
            overflow: hidden;
            animation: modalSlideIn 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
            position: relative;
        }

        .employee-modal::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #3498db, #e74c3c, #f39c12, #27ae60, #9b59b6);
            background-size: 300% 100%;
            animation: gradientShift 3s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.8) rotateX(10deg);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1) rotateX(0deg);
            }
        }

        .employee-modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 2.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .employee-modal-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .employee-modal-body {
            padding: 2.5rem;
            max-height: 75vh;
            overflow-y: auto;
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
        }

        .employee-modal-body::-webkit-scrollbar {
            width: 8px;
        }

        .employee-modal-body::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        .employee-modal-body::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #3498db, #2980b9);
            border-radius: 10px;
        }

        .employee-modal-body::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #2980b9, #3498db);
        }

        /* تحسين الأزرار */
        .employee-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 1rem 1.5rem;
            border-radius: 15px;
            cursor: pointer;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-family: 'Cairo', sans-serif;
            text-decoration: none;
            justify-content: center;
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .employee-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .employee-btn:hover::before {
            left: 100%;
        }

        .employee-btn:hover {
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
            text-decoration: none;
            color: white;
        }

        .employee-btn:active {
            transform: translateY(-1px) scale(0.98);
        }

        .employee-btn.success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            box-shadow: 0 8px 25px rgba(86, 171, 47, 0.3);
        }

        .employee-btn.success:hover {
            box-shadow: 0 15px 35px rgba(86, 171, 47, 0.4);
        }

        .employee-btn.warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            box-shadow: 0 8px 25px rgba(240, 147, 251, 0.3);
        }

        .employee-btn.warning:hover {
            box-shadow: 0 15px 35px rgba(240, 147, 251, 0.4);
        }

        .employee-btn.danger {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
            box-shadow: 0 8px 25px rgba(255, 65, 108, 0.3);
        }

        .employee-btn.danger:hover {
            box-shadow: 0 15px 35px rgba(255, 65, 108, 0.4);
        }

        .employee-btn.secondary {
            background: linear-gradient(135deg, #bdc3c7 0%, #2c3e50 100%);
            box-shadow: 0 8px 25px rgba(189, 195, 199, 0.3);
        }

        .employee-btn.secondary:hover {
            box-shadow: 0 15px 35px rgba(189, 195, 199, 0.4);
        }

        /* تحسين النماذج */
        .employee-form-group {
            margin-bottom: 1.8rem;
            position: relative;
        }

        .employee-form-group label {
            display: block;
            margin-bottom: 0.8rem;
            font-weight: 700;
            color: #2c3e50;
            font-size: 1rem;
            position: relative;
            padding-left: 2rem;
        }

        .employee-form-group label i {
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.1rem;
        }

        .employee-form-group input,
        .employee-form-group select,
        .employee-form-group textarea {
            width: 100%;
            padding: 1rem 1.2rem;
            border: 2px solid #e8ecef;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.05);
        }

        .employee-form-group input:focus,
        .employee-form-group select:focus,
        .employee-form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow:
                0 0 0 4px rgba(102, 126, 234, 0.1),
                inset 0 2px 4px rgba(0,0,0,0.05);
            transform: translateY(-2px);
        }

        /* تحسين الجداول */
        .employee-table {
            width: 100%;
            border-collapse: collapse;
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border-radius: 20px;
            overflow: hidden;
            box-shadow:
                0 20px 60px rgba(0, 0, 0, 0.1),
                0 8px 25px rgba(0, 0, 0, 0.05);
        }

        .employee-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            text-align: right;
            font-weight: 700;
            font-size: 1rem;
            position: relative;
        }

        .employee-table th::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #3498db, #e74c3c, #f39c12, #27ae60);
        }

        .employee-table td {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            position: relative;
        }

        .employee-table tr:hover {
            background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
            transform: scale(1.01);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        /* تحسين البطاقات الإحصائية */
        .employee-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 2rem;
            margin-bottom: 2.5rem;
        }

        .employee-stat-card {
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            padding: 2rem;
            border-radius: 20px;
            text-align: center;
            box-shadow:
                0 15px 35px rgba(0, 0, 0, 0.1),
                0 5px 15px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.8);
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;
            overflow: hidden;
        }

        .employee-stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #3498db, #e74c3c, #f39c12, #27ae60, #9b59b6);
            background-size: 300% 100%;
            animation: gradientShift 3s ease-in-out infinite;
        }

        .employee-stat-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.15),
                0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .employee-stat-icon {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: white;
            font-size: 1.8rem;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            position: relative;
        }

        .employee-stat-icon::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border-radius: 50%;
            background: linear-gradient(45deg, rgba(255,255,255,0.3), transparent);
            z-index: -1;
        }

        .employee-stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
            color: #2c3e50;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .employee-stat-label {
            color: #7f8c8d;
            font-size: 1rem;
            font-weight: 600;
        }

        /* تحسين الرسائل */
        .employee-message {
            padding: 1.2rem 1.5rem;
            border-radius: 12px;
            margin-bottom: 1.5rem;
            font-size: 1rem;
            font-weight: 600;
            position: relative;
            border-left: 4px solid;
            animation: messageSlideIn 0.3s ease-out;
        }

        @keyframes messageSlideIn {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .employee-message.success {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
            border-left-color: #28a745;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.2);
        }

        .employee-message.error {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
            border-left-color: #dc3545;
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.2);
        }

        /* تحسين الشارات */
        .employee-status-badge,
        .employee-role-badge {
            padding: 0.6rem 1.2rem;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 700;
            color: white;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            position: relative;
            overflow: hidden;
        }

        .employee-status-badge::before,
        .employee-role-badge::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .employee-status-badge:hover::before,
        .employee-role-badge:hover::before {
            left: 100%;
        }

        .employee-status-badge.active {
            background: linear-gradient(135deg, #56ab2f, #a8e6cf);
        }

        .employee-status-badge.inactive {
            background: linear-gradient(135deg, #ff416c, #ff4b2b);
        }

        .employee-role-badge.admin {
            background: linear-gradient(135deg, #ff416c, #ff4b2b);
        }

        .employee-role-badge.supervisor {
            background: linear-gradient(135deg, #f093fb, #f5576c);
        }

        .employee-role-badge.cashier {
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .employee-role-badge.employee {
            background: linear-gradient(135deg, #56ab2f, #a8e6cf);
        }

        /* تحسين الأفاتار */
        .employee-avatar {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 800;
            font-size: 1.1rem;
            box-shadow:
                0 8px 25px rgba(102, 126, 234, 0.3),
                inset 0 2px 4px rgba(255,255,255,0.2);
            position: relative;
            overflow: hidden;
        }

        .employee-avatar::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: avatarShine 3s infinite;
        }

        @keyframes avatarShine {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* تحسين أزرار الإجراءات */
        .employee-actions {
            display: flex;
            gap: 0.8rem;
            justify-content: center;
        }

        .employee-action-btn {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .employee-action-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255,255,255,0.3);
            border-radius: 50%;
            transition: all 0.3s ease;
            transform: translate(-50%, -50%);
        }

        .employee-action-btn:hover::before {
            width: 100%;
            height: 100%;
        }

        .employee-action-btn:hover {
            transform: scale(1.15) rotate(5deg);
        }

        .employee-action-btn:active {
            transform: scale(0.95);
        }

        .employee-action-btn.edit {
            background: linear-gradient(135deg, #f093fb, #f5576c);
            box-shadow: 0 4px 15px rgba(240, 147, 251, 0.3);
        }

        .employee-action-btn.delete {
            background: linear-gradient(135deg, #ff416c, #ff4b2b);
            box-shadow: 0 4px 15px rgba(255, 65, 108, 0.3);
        }

        .employee-action-btn.protected {
            background: linear-gradient(135deg, #bdc3c7, #2c3e50);
            cursor: not-allowed;
            box-shadow: 0 4px 15px rgba(189, 195, 199, 0.3);
        }

        /* تحسين بطاقات الصلاحيات */
        .employee-permissions-grid {
            display: grid;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .employee-permission-card {
            padding: 2.5rem;
            border-radius: 20px;
            color: white;
            position: relative;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .employee-permission-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: permissionGlow 4s ease-in-out infinite;
        }

        @keyframes permissionGlow {
            0%, 100% { transform: scale(0.8) rotate(0deg); opacity: 0.5; }
            50% { transform: scale(1.2) rotate(180deg); opacity: 0.8; }
        }

        .employee-permission-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 30px 80px rgba(0, 0, 0, 0.4);
        }

        .employee-permission-header {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            margin-bottom: 1.5rem;
            position: relative;
            z-index: 2;
        }

        .employee-permission-icon {
            width: 70px;
            height: 70px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255,255,255,0.3);
        }

        .employee-permission-title {
            font-size: 1.5rem;
            font-weight: 800;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .employee-permission-subtitle {
            font-size: 1rem;
            opacity: 0.9;
            margin: 0;
            font-weight: 500;
        }

        .employee-permission-list {
            list-style: none;
            padding: 0;
            margin: 0;
            display: grid;
            gap: 0.8rem;
            position: relative;
            z-index: 2;
        }

        .employee-permission-item {
            display: flex;
            align-items: center;
            gap: 0.8rem;
            font-size: 1rem;
            font-weight: 600;
            padding: 0.5rem;
            border-radius: 8px;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(5px);
            transition: all 0.3s ease;
        }

        .employee-permission-item:hover {
            background: rgba(255,255,255,0.2);
            transform: translateX(5px);
        }

        .employee-permission-item i {
            width: 20px;
            font-size: 1.1rem;
        }

        /* تحسين زر الإغلاق */
        .employee-close-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            width: 45px;
            height: 45px;
            border-radius: 50%;
            cursor: pointer;
            color: white;
            font-size: 1.3rem;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255,255,255,0.3);
            position: relative;
            overflow: hidden;
        }

        .employee-close-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(231, 76, 60, 0.8);
            border-radius: 50%;
            transition: all 0.3s ease;
            transform: translate(-50%, -50%);
        }

        .employee-close-btn:hover::before {
            width: 120%;
            height: 120%;
        }

        .employee-close-btn:hover {
            transform: scale(1.1) rotate(90deg);
            border-color: rgba(231, 76, 60, 0.5);
        }

        /* تحسين الحالة الفارغة */
        .employee-empty-state {
            text-align: center;
            padding: 4rem 2rem;
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .employee-empty-state::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #bdc3c7, #95a5a6);
        }

        .employee-empty-icon {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, #bdc3c7, #95a5a6);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            color: white;
            font-size: 3.5rem;
            box-shadow: 0 15px 35px rgba(189, 195, 199, 0.3);
            animation: emptyIconFloat 3s ease-in-out infinite;
        }

        @keyframes emptyIconFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        /* تحسين التحكم */
        .employee-controls {
            display: flex;
            gap: 1.5rem;
            margin-bottom: 2.5rem;
            flex-wrap: wrap;
        }

        .employee-grid {
            display: grid;
            gap: 1.5rem;
        }

        /* تحسين الاستجابة */
        @media (max-width: 768px) {
            .employee-modal {
                width: 95%;
                margin: 1rem;
                border-radius: 20px;
            }

            .employee-modal-body {
                padding: 1.5rem;
            }

            .employee-stats-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .employee-controls {
                flex-direction: column;
            }

            .employee-table {
                font-size: 0.9rem;
            }

            .employee-table th,
            .employee-table td {
                padding: 1rem;
            }

            .employee-permission-card {
                padding: 1.5rem;
            }

            .employee-permission-header {
                flex-direction: column;
                text-align: center;
                gap: 1rem;
            }
        }

        @media (max-width: 480px) {
            .employee-modal-header {
                padding: 1.5rem;
            }

            .employee-modal-body {
                padding: 1rem;
            }

            .employee-btn {
                padding: 0.8rem 1rem;
                font-size: 0.9rem;
            }

            .employee-form-group input,
            .employee-form-group select {
                padding: 0.8rem;
            }
        }
    </style>
</head>
<body class="loading">
    <!-- شاشة التحميل -->
    <div class="loading-screen" id="loadingScreen">
        <div class="loading-spinner"></div>
        <div class="loading-text">جاري تحميل لوحة التحكم</div>
        <div class="loading-subtitle">يرجى الانتظار...</div>
    </div>

    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <i class="fas fa-store"></i>
                <h2>هايبر ماركت</h2>
            </div>
            <button class="sidebar-toggle" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <nav class="sidebar-nav">
            <div class="nav-scroll-container">
                <div class="nav-scroll-up" onclick="scrollNavUp()">
                    <i class="fas fa-chevron-up"></i>
                </div>
                <div class="nav-items-container" id="navItemsContainer">
                    <ul>
                        <li class="nav-item active">
                            <a href="#dashboard" class="nav-link" data-section="dashboard">
                                <i class="fas fa-tachometer-alt"></i>
                                <span>لوحة المعلومات</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#categories" class="nav-link" data-section="categories">
                                <i class="fas fa-th-large"></i>
                                <span>إدارة الفئات</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#products" class="nav-link" data-section="products">
                                <i class="fas fa-box"></i>
                                <span>إدارة المنتجات</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#featured" class="nav-link" data-section="featured">
                                <i class="fas fa-star"></i>
                                <span>المنتجات المميزة</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#offers" class="nav-link" data-section="offers">
                                <i class="fas fa-tags"></i>
                                <span>إدارة العروض</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#coupons" class="nav-link" data-section="coupons">
                                <i class="fas fa-ticket-alt"></i>
                                <span>إدارة الكوبونات</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#orders" class="nav-link" data-section="orders">
                                <i class="fas fa-shopping-cart"></i>
                                <span>الطلبات</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#contact-settings" class="nav-link" data-section="contact-settings">
                                <i class="fas fa-address-book"></i>
                                <span>إعدادات الاتصال</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#settings" class="nav-link" data-section="settings">
                                <i class="fas fa-cog"></i>
                                <span>إعدادات الموقع</span>
                            </a>
                        </li>
                        <li class="nav-item website-nav">
                            <a href="index.html" class="nav-link website-link" target="_blank">
                                <i class="fas fa-globe"></i>
                                <span>الموقع الإلكتروني</span>
                            </a>
                        </li>

                        <li class="nav-item cashier-nav">
                            <a href="cashier.html" class="nav-link cashier-link">
                                <i class="fas fa-cash-register"></i>
                                <span>نظام الكاشير</span>
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="nav-scroll-down" onclick="scrollNavDown()">
                    <i class="fas fa-chevron-down"></i>
                </div>
            </div>
        </nav>

        <div class="sidebar-footer">
            <button class="storage-btn" onclick="showStorageInfo()" title="مراقبة مساحة التخزين">
                <i class="fas fa-hdd"></i>
                <span>مساحة التخزين</span>
            </button>
            <button class="logout-btn" onclick="logout()">
                <i class="fas fa-sign-out-alt"></i>
                <span>تسجيل الخروج</span>
            </button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <!-- Header -->
        <header class="admin-header">
            <div class="header-left">
                <button class="mobile-menu-btn" id="mobileMenuBtn">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 id="pageTitle">لوحة المعلومات</h1>
            </div>
            <div class="header-right">
                <a href="index.html" class="btn btn-website header-website-btn" target="_blank">
                    <i class="fas fa-globe"></i>
                    <span>الموقع الإلكتروني</span>
                </a>



                <a href="cashier.html" class="btn btn-cashier header-cashier-btn">
                    <i class="fas fa-cash-register"></i>
                    <span>نظام الكاشير</span>
                </a>

                <!-- نظام إدارة الموظفين -->
                <button class="btn btn-employee header-employee-btn" onclick="openEmployeeManagement()">
                    <i class="fas fa-users-cog"></i>
                    <span>إدارة الموظفين</span>
                    <div class="security-badge">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                </button>
                <div class="admin-info">
                    <div class="admin-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                </div>
            </div>
        </header>

        <!-- Dashboard Section -->
        <section id="dashboard" class="content-section active">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-box"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="totalProducts">8</h3>
                        <p>إجمالي المنتجات</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="totalFeatured">0</h3>
                        <p>المنتجات المميزة</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-tags"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="totalOffers">0</h3>
                        <p>العروض النشطة</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-ticket-alt"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="totalCoupons">3</h3>
                        <p>الكوبونات النشطة</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="totalOrders">0</h3>
                        <p>الطلبات اليوم</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="totalRevenue">0</h3>
                        <p>المبيعات اليوم (ريال)</p>
                    </div>
                </div>
            </div>

            <div class="dashboard-widgets">
                <div class="widget">
                    <div class="widget-header">
                        <h3>الطلبات الأخيرة</h3>
                        <button class="btn-secondary">عرض الكل</button>
                    </div>
                    <div class="widget-content">
                        <div class="empty-state">
                            <i class="fas fa-inbox"></i>
                            <p>لا توجد طلبات حتى الآن</p>
                        </div>
                    </div>
                </div>

                <div class="widget">
                    <div class="widget-header">
                        <h3>المنتجات الأكثر مبيعاً</h3>
                        <button class="btn-secondary">عرض التقرير</button>
                    </div>
                    <div class="widget-content">
                        <div class="empty-state">
                            <i class="fas fa-chart-bar"></i>
                            <p>لا توجد بيانات مبيعات حتى الآن</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Categories Section -->
        <section id="categories" class="content-section">
            <div class="section-header">
                <h2>إدارة الفئات</h2>
                <button class="btn-primary" onclick="showAddCategoryModal()">
                    <i class="fas fa-plus"></i>
                    إضافة فئة جديدة
                </button>
            </div>

            <div class="categories-grid" id="categoriesGrid">
                <!-- Categories will be loaded here -->
            </div>
        </section>

        <!-- Products Section -->
        <section id="products" class="content-section">
            <div class="section-header">
                <h2>إدارة المنتجات</h2>
                <button class="btn-primary" onclick="showAddProductModal()">
                    <i class="fas fa-plus"></i>
                    إضافة منتج جديد
                </button>
            </div>

            <div class="batch-operations">
                <h4><i class="fas fa-tools"></i> العمليات المجمعة والإحصائيات</h4>
                <div style="display: flex; gap: 0.5rem; flex-wrap: wrap; margin-bottom: 1rem;">
                    <button class="btn btn-info" onclick="updateStorageStats()">
                        <i class="fas fa-chart-bar"></i>
                        تحديث الإحصائيات
                    </button>
                    <button class="btn btn-warning" onclick="optimizeAllImages()">
                        <i class="fas fa-compress-alt"></i>
                        تحسين جميع الصور
                    </button>
                    <button class="btn btn-success" onclick="loadProductsWithImages()">
                        <i class="fas fa-sync-alt"></i>
                        إعادة تحميل المنتجات
                    </button>
                    <button class="btn btn-purple" onclick="showBulkUploadModal()">
                        <i class="fas fa-upload"></i>
                        رفع متعدد (50K)
                    </button>
                    <button class="btn btn-success" onclick="exportAllData()">
                        <i class="fas fa-download"></i>
                        تصدير البيانات
                    </button>
                    <button class="btn btn-warning" onclick="showImportModal()">
                        <i class="fas fa-upload"></i>
                        استيراد البيانات
                    </button>

                </div>
                <div id="storageStats"></div>
            </div>

            <div class="products-grid" id="productsGrid">
                <!-- Products will be loaded here -->
            </div>
        </section>

        <!-- Featured Products Section -->
        <section id="featured" class="content-section">
            <div class="section-header">
                <h2>إدارة المنتجات المميزة</h2>
                <button class="btn-primary" onclick="showAddFeaturedModal()">
                    <i class="fas fa-plus"></i>
                    إضافة منتج مميز
                </button>
            </div>

            <div class="featured-info">
                <div class="info-card">
                    <i class="fas fa-info-circle"></i>
                    <p>المنتجات المميزة هي التي تظهر في القسم الرئيسي للموقع. يمكنك إضافة حتى 8 منتجات مميزة.</p>
                </div>
            </div>

            <div class="featured-grid" id="featuredGrid">
                <!-- Featured products will be loaded here -->
            </div>
        </section>

        <!-- Contact Settings Section -->
        <section id="contact-settings" class="content-section">
            <div class="section-header">
                <h2>إعدادات معلومات الاتصال</h2>
                <p>إدارة معلومات التواصل التي تظهر في الموقع</p>
            </div>

            <div class="contact-settings-grid">
                <!-- Basic Info -->
                <div class="settings-card">
                    <div class="card-header">
                        <h3><i class="fas fa-info-circle"></i> المعلومات الأساسية</h3>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label>عنوان المتجر (عربي)</label>
                            <input type="text" id="storeNameAr" placeholder="برومت هايبر ماركت">
                        </div>
                        <div class="form-group">
                            <label>عنوان المتجر (إنجليزي)</label>
                            <input type="text" id="storeNameEn" placeholder="Bromet Hypermarket">
                        </div>
                        <div class="form-group">
                            <label>وصف المتجر (عربي)</label>
                            <textarea id="storeDescAr" rows="3" placeholder="متجرك المفضل لجميع احتياجاتك اليومية"></textarea>
                        </div>
                        <div class="form-group">
                            <label>وصف المتجر (إنجليزي)</label>
                            <textarea id="storeDescEn" rows="3" placeholder="Your favorite store for all daily needs"></textarea>
                        </div>
                    </div>
                </div>

                <!-- Logo Settings -->
                <div class="settings-card">
                    <div class="card-header">
                        <h3><i class="fas fa-image"></i> إعدادات الشعار</h3>
                    </div>
                    <div class="card-body">
                        <div class="logo-upload-section">
                            <div class="current-logo">
                                <h4>الشعار الحالي:</h4>
                                <div class="logo-preview" id="logoPreview">
                                    <i class="fas fa-store" id="defaultIcon"></i>
                                    <img id="logoImage" src="" alt="شعار المتجر" style="display: none;">
                                </div>
                            </div>

                            <div class="logo-upload">
                                <div class="form-group">
                                    <label>رفع شعار جديد</label>
                                    <input type="file" id="logoUpload" accept="image/*" onchange="handleLogoUpload(event)">
                                    <small class="form-help">يُفضل أن يكون الشعار بصيغة PNG أو JPG وحجم لا يزيد عن 2MB</small>
                                </div>

                                <div class="form-group">
                                    <label>أو أدخل رابط الشعار</label>
                                    <input type="url" id="logoUrl" placeholder="https://example.com/logo.png" onchange="handleLogoUrl()">
                                    <small class="form-help">يمكنك إدخال رابط مباشر للشعار من الإنترنت</small>
                                </div>

                                <div class="logo-actions">
                                    <button type="button" class="btn-secondary" onclick="resetLogo()">
                                        <i class="fas fa-undo"></i>
                                        استخدام الأيقونة الافتراضية
                                    </button>
                                    <button type="button" class="btn-primary" onclick="previewLogo()">
                                        <i class="fas fa-eye"></i>
                                        معاينة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Details -->
                <div class="settings-card">
                    <div class="card-header">
                        <h3><i class="fas fa-phone"></i> معلومات التواصل</h3>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label>العنوان (عربي)</label>
                            <input type="text" id="addressAr" placeholder="الرياض، المملكة العربية السعودية">
                        </div>
                        <div class="form-group">
                            <label>العنوان (إنجليزي)</label>
                            <input type="text" id="addressEn" placeholder="Riyadh, Saudi Arabia">
                        </div>
                        <div class="form-group">
                            <label>الهاتف الأول</label>
                            <input type="tel" id="phone1" placeholder="+966 11 123 4567">
                        </div>
                        <div class="form-group">
                            <label>الهاتف الثاني</label>
                            <input type="tel" id="phone2" placeholder="+966 11 123 4568">
                        </div>
                        <div class="form-group">
                            <label>البريد الإلكتروني الرئيسي</label>
                            <input type="email" id="email1" placeholder="<EMAIL>">
                        </div>
                        <div class="form-group">
                            <label>بريد الدعم الفني</label>
                            <input type="email" id="email2" placeholder="<EMAIL>">
                        </div>
                    </div>
                </div>

                <!-- Working Hours -->
                <div class="settings-card">
                    <div class="card-header">
                        <h3><i class="fas fa-clock"></i> ساعات العمل</h3>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label>ساعات العمل (عربي)</label>
                            <textarea id="workingHoursAr" rows="3" placeholder="السبت - الخميس: 8:00 ص - 12:00 م&#10;الجمعة: 2:00 م - 12:00 م"></textarea>
                        </div>
                        <div class="form-group">
                            <label>ساعات العمل (إنجليزي)</label>
                            <textarea id="workingHoursEn" rows="3" placeholder="Saturday - Thursday: 8:00 AM - 12:00 PM&#10;Friday: 2:00 PM - 12:00 PM"></textarea>
                        </div>
                    </div>
                </div>

                <!-- Social Media -->
                <div class="settings-card">
                    <div class="card-header">
                        <h3><i class="fas fa-share-alt"></i> وسائل التواصل الاجتماعي</h3>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label>رابط الفيسبوك</label>
                            <input type="url" id="facebookUrl" placeholder="https://facebook.com/brometmarket">
                        </div>
                        <div class="form-group">
                            <label>رابط تويتر</label>
                            <input type="url" id="twitterUrl" placeholder="https://twitter.com/brometmarket">
                        </div>
                        <div class="form-group">
                            <label>رابط إنستغرام</label>
                            <input type="url" id="instagramUrl" placeholder="https://instagram.com/brometmarket">
                        </div>
                        <div class="form-group">
                            <label>رقم الواتس اب</label>
                            <input type="tel" id="whatsappNumber" placeholder="966111234567">
                        </div>
                    </div>
                </div>
            </div>

            <div class="settings-actions">
                <button class="btn-primary" onclick="saveContactSettings()">
                    <i class="fas fa-save"></i>
                    حفظ الإعدادات
                </button>
                <button class="btn-secondary" onclick="resetContactSettings()">
                    <i class="fas fa-undo"></i>
                    إعادة تعيين
                </button>
                <button class="btn-info" onclick="testLogoUpdate()">
                    <i class="fas fa-sync"></i>
                    اختبار تحديث الشعار
                </button>
            </div>
        </section>

        <!-- Offers Section -->
        <section id="offers" class="content-section">
            <div class="section-header">
                <h2>إدارة العروض والخصومات</h2>
                <button class="btn-primary" onclick="showAddOfferModal()">
                    <i class="fas fa-plus"></i>
                    إضافة عرض جديد
                </button>
            </div>

            <div class="offers-grid" id="offersGrid">
                <!-- Offers will be loaded here -->
            </div>
        </section>

        <!-- Coupons Section -->
        <section id="coupons" class="content-section">
            <div class="section-header">
                <h2>إدارة الكوبونات</h2>
                <button class="btn-primary" onclick="showAddCouponModal()">
                    <i class="fas fa-plus"></i>
                    إنشاء كوبون جديد
                </button>
            </div>

            <div class="coupons-list" id="couponsList">
                <!-- Coupons will be loaded here -->
            </div>
        </section>

        <!-- Orders Section -->
        <section id="orders" class="content-section">
            <div class="section-header">
                <h2>إدارة الطلبات</h2>
                <div class="filters">
                    <select id="orderFilter">
                        <option value="all">جميع الطلبات</option>
                        <option value="pending">قيد الانتظار</option>
                        <option value="completed">مكتملة</option>
                        <option value="cancelled">ملغية</option>
                    </select>
                </div>
            </div>

            <div class="orders-list" id="ordersList">
                <div class="empty-state">
                    <i class="fas fa-shopping-cart"></i>
                    <p>لا توجد طلبات حتى الآن</p>
                </div>
            </div>
        </section>

        <!-- Settings Section -->
        <section id="settings" class="content-section">
            <div class="section-header">
                <h2>إعدادات الموقع</h2>
            </div>

            <div class="settings-grid">
                <div class="settings-card">
                    <h3>معلومات المتجر</h3>
                    <form id="storeInfoForm">
                        <div class="form-group">
                            <label>اسم المتجر</label>
                            <input type="text" id="storeName" value="برومت هايبر ماركت">
                        </div>
                        <div class="form-group">
                            <label>رقم الهاتف</label>
                            <input type="tel" id="storePhone" value="+966 11 123 4567">
                        </div>
                        <div class="form-group">
                            <label>العنوان</label>
                            <input type="text" id="storeAddress" value="الرياض، المملكة العربية السعودية">
                        </div>
                        <button type="submit" class="btn-primary">حفظ التغييرات</button>
                    </form>
                </div>

                <div class="settings-card">
                    <h3>إعدادات الواتس اب</h3>
                    <form id="whatsappForm">
                        <div class="form-group">
                            <label>رقم الواتس اب</label>
                            <input type="tel" id="whatsappNumber" value="966501234567">
                        </div>
                        <div class="form-group">
                            <label>رسالة الترحيب</label>
                            <textarea id="welcomeMessage" rows="3">مرحباً بكم في برومت هايبر ماركت</textarea>
                        </div>
                        <button type="submit" class="btn-primary">حفظ التغييرات</button>
                    </form>
                </div>
            </div>
        </section>
    </div>

    <!-- Modals will be added here -->
    <div id="modalContainer"></div>

    <script src="admin-script.js?v=2024"></script>
    <script src="employee-management.js?v=2024"></script>

    <!-- نظام إدارة الموظفين -->
    <script>
        // نظام إدارة حسابات دخول الموظفين
        let currentEmployees = [];
        let isEmployeeSystemInitialized = false;

        function initializeEmployeeSystem() {
            if (isEmployeeSystemInitialized) return;
            loadEmployeesFromStorage();
            createDefaultAdminAccount();
            isEmployeeSystemInitialized = true;
            console.log('✅ تم تهيئة نظام إدارة الموظفين');
        }

        function loadEmployeesFromStorage() {
            try {
                const stored = localStorage.getItem('employees');
                currentEmployees = stored ? JSON.parse(stored) : [];
                console.log(`📋 تم تحميل ${currentEmployees.length} موظف`);
            } catch (error) {
                console.error('❌ خطأ في تحميل بيانات الموظفين:', error);
                currentEmployees = [];
            }
        }

        function saveEmployeesToStorage() {
            try {
                localStorage.setItem('employees', JSON.stringify(currentEmployees));
                console.log('💾 تم حفظ بيانات الموظفين');
                return true;
            } catch (error) {
                console.error('❌ خطأ في حفظ بيانات الموظفين:', error);
                return false;
            }
        }

        // تم حذف إنشاء حساب المدير الافتراضي

        function openEmployeeManagement() {
            console.log('🔐 فتح نظام إدارة الموظفين...');
            initializeEmployeeSystem();
            showAdminVerification();
        }

        function showAdminVerification() {
            const modalHTML = `
                <div class="employee-modal-overlay" id="employeeModalOverlay">
                    <div class="employee-modal" style="max-width: 500px; width: 90%;">
                        <div class="employee-modal-header" style="background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);">
                            <div style="display: flex; align-items: center; gap: 1.5rem;">
                                <div style="
                                    width: 60px;
                                    height: 60px;
                                    background: rgba(255,255,255,0.2);
                                    border-radius: 50%;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    font-size: 1.8rem;
                                    backdrop-filter: blur(10px);
                                    border: 2px solid rgba(255,255,255,0.3);
                                    animation: securityPulse 2s infinite;
                                ">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <div>
                                    <h3 style="margin: 0; font-size: 1.5rem; font-weight: 800; text-shadow: 0 2px 4px rgba(0,0,0,0.3);">
                                        🔐 تأكيد هوية المدير
                                    </h3>
                                    <p style="margin: 0; opacity: 0.9; font-size: 1rem; font-weight: 500;">
                                        نظام محمي - يتطلب تأكيد الهوية
                                    </p>
                                </div>
                            </div>
                            <button class="employee-close-btn" onclick="closeEmployeeModal()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>

                        <div class="employee-modal-body">
                            <!-- أيقونة الأمان المركزية -->
                            <div style="text-align: center; margin-bottom: 2.5rem;">
                                <div style="
                                    width: 100px;
                                    height: 100px;
                                    background: linear-gradient(135deg, #ff416c, #ff4b2b);
                                    border-radius: 50%;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    margin: 0 auto 1.5rem;
                                    color: white;
                                    font-size: 2.5rem;
                                    box-shadow:
                                        0 15px 35px rgba(255, 65, 108, 0.4),
                                        0 5px 15px rgba(0,0,0,0.1),
                                        inset 0 2px 4px rgba(255,255,255,0.2);
                                    position: relative;
                                    animation: lockFloat 3s ease-in-out infinite;
                                ">
                                    <i class="fas fa-lock"></i>
                                    <div style="
                                        position: absolute;
                                        top: -5px;
                                        right: -5px;
                                        width: 30px;
                                        height: 30px;
                                        background: linear-gradient(135deg, #f39c12, #e67e22);
                                        border-radius: 50%;
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                        font-size: 0.8rem;
                                        animation: badgePulse 1.5s infinite;
                                    ">
                                        <i class="fas fa-key"></i>
                                    </div>
                                </div>
                                <h4 style="
                                    color: #2c3e50;
                                    margin-bottom: 0.8rem;
                                    font-size: 1.4rem;
                                    font-weight: 700;
                                    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
                                ">
                                    🏢 نظام إدارة حسابات الموظفين
                                </h4>
                                <p style="
                                    color: #7f8c8d;
                                    margin: 0;
                                    font-size: 1rem;
                                    line-height: 1.5;
                                    font-weight: 500;
                                ">
                                    يرجى إدخال بيانات المدير للوصول إلى النظام المحمي
                                </p>
                            </div>

                            <!-- نموذج تسجيل الدخول المحسن -->
                            <form id="adminVerificationForm" onsubmit="verifyAdminCredentials(event)">
                                <div class="employee-form-group">
                                    <label>
                                        <i class="fas fa-user" style="color: #667eea;"></i>
                                        اسم المستخدم
                                    </label>
                                    <input
                                        type="text"
                                        id="adminUsername"
                                        placeholder="أدخل اسم المستخدم"
                                        required
                                        autocomplete="username"
                                        style="
                                            background: linear-gradient(145deg, #ffffff, #f8f9fa);
                                            border: 2px solid #e8ecef;
                                            transition: all 0.3s ease;
                                        "
                                    >
                                </div>

                                <div class="employee-form-group">
                                    <label>
                                        <i class="fas fa-lock" style="color: #ff416c;"></i>
                                        كلمة المرور
                                    </label>
                                    <input
                                        type="password"
                                        id="adminPassword"
                                        placeholder="أدخل كلمة المرور"
                                        required
                                        autocomplete="current-password"
                                        style="
                                            background: linear-gradient(145deg, #ffffff, #f8f9fa);
                                            border: 2px solid #e8ecef;
                                            transition: all 0.3s ease;
                                        "
                                    >
                                </div>

                                <!-- رسالة الخطأ -->
                                <div id="adminVerificationError" class="employee-message error" style="display: none;"></div>

                                <!-- معلومات تسجيل الدخول -->
                                <div style="
                                    background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
                                    border-radius: 12px;
                                    padding: 1rem;
                                    margin-bottom: 2rem;
                                    border-left: 4px solid #667eea;
                                    font-size: 0.9rem;
                                    color: #2c3e50;
                                ">
                                    <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                                        <i class="fas fa-info-circle" style="color: #667eea;"></i>
                                        <strong>معلومات تسجيل الدخول:</strong>
                                    </div>
                                    <div style="font-size: 0.85rem; color: #7f8c8d;">
                                        اسم المستخدم: <code style="background: rgba(102,126,234,0.1); padding: 2px 6px; border-radius: 4px;">ali</code><br>
                                        كلمة المرور: <code style="background: rgba(102,126,234,0.1); padding: 2px 6px; border-radius: 4px;">12</code>
                                    </div>
                                </div>

                                <!-- أزرار التحكم -->
                                <div style="display: flex; gap: 1rem; justify-content: center; margin-top: 2rem;">
                                    <button type="button" class="employee-btn secondary" onclick="closeEmployeeModal()">
                                        <i class="fas fa-times"></i>
                                        إلغاء
                                    </button>
                                    <button type="submit" class="employee-btn" style="background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);">
                                        <i class="fas fa-check"></i>
                                        تأكيد الهوية
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <style>
                    @keyframes securityPulse {
                        0%, 100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(255,255,255,0.4); }
                        50% { transform: scale(1.05); box-shadow: 0 0 0 10px rgba(255,255,255,0); }
                    }

                    @keyframes lockFloat {
                        0%, 100% { transform: translateY(0px) rotate(0deg); }
                        50% { transform: translateY(-8px) rotate(2deg); }
                    }

                    @keyframes badgePulse {
                        0%, 100% { transform: scale(1); }
                        50% { transform: scale(1.2); }
                    }
                </style>
            `;
            document.getElementById('modalContainer').innerHTML = modalHTML;
            setTimeout(() => {
                const usernameField = document.getElementById('adminUsername');
                if (usernameField) usernameField.focus();
            }, 100);
            console.log('🔐 تم عرض نافذة التحقق من الهوية المحسنة');
        }

        function verifyAdminCredentials(event) {
            event.preventDefault();
            const username = document.getElementById('adminUsername').value.trim();
            const password = document.getElementById('adminPassword').value;
            const errorDiv = document.getElementById('adminVerificationError');
            errorDiv.style.display = 'none';

            if (!username || !password) {
                showAdminError('يرجى إدخال اسم المستخدم وكلمة المرور');
                return;
            }

            const admin = currentEmployees.find(emp =>
                emp.username === username &&
                emp.password === password &&
                emp.role === 'admin'
            );

            if (admin) {
                console.log('✅ تم التحقق من هوية المدير:', admin.name);
                closeEmployeeModal();
                showEmployeeMainDashboard();
            } else {
                showAdminError('اسم المستخدم أو كلمة المرور غير صحيحة');
            }
        }

        function showAdminError(message) {
            const errorDiv = document.getElementById('adminVerificationError');
            if (errorDiv) {
                errorDiv.textContent = message;
                errorDiv.style.display = 'block';
                setTimeout(() => {
                    errorDiv.style.display = 'none';
                }, 5000);
            }
        }

        function closeEmployeeModal() {
            const modalContainer = document.getElementById('modalContainer');
            if (modalContainer) {
                modalContainer.innerHTML = '';
            }
            console.log('❌ تم إغلاق النافذة');
        }

        // سيتم استدعاء هذه الوظيفة من المستمع الرئيسي
        // document.addEventListener('DOMContentLoaded', function() {
        //     initializeEmployeeSystem();
        // });

        // عرض لوحة التحكم الرئيسية المحسنة لإدارة الموظفين
        function showEmployeeMainDashboard() {
            const totalEmployees = currentEmployees.length;
            const activeEmployees = currentEmployees.filter(emp => emp.status === 'active').length;
            const inactiveEmployees = currentEmployees.filter(emp => emp.status === 'inactive').length;
            const adminCount = currentEmployees.filter(emp => emp.role === 'admin').length;
            const supervisorCount = currentEmployees.filter(emp => emp.role === 'supervisor').length;
            const cashierCount = currentEmployees.filter(emp => emp.role === 'cashier').length;
            const employeeCount = currentEmployees.filter(emp => emp.role === 'employee').length;

            const modalHTML = `
                <div class="employee-modal-overlay" id="employeeModalOverlay">
                    <div class="employee-modal" style="max-width: 900px; width: 95%;">
                        <div class="employee-modal-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                            <div style="display: flex; align-items: center; gap: 1.5rem;">
                                <div style="
                                    width: 60px;
                                    height: 60px;
                                    background: rgba(255,255,255,0.2);
                                    border-radius: 50%;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    font-size: 1.8rem;
                                    backdrop-filter: blur(10px);
                                    border: 2px solid rgba(255,255,255,0.3);
                                    animation: dashboardPulse 2s infinite;
                                ">
                                    <i class="fas fa-users-cog"></i>
                                </div>
                                <div>
                                    <h3 style="margin: 0; font-size: 1.6rem; font-weight: 800; text-shadow: 0 2px 4px rgba(0,0,0,0.3);">
                                        🏢 نظام إدارة الموظفين
                                    </h3>
                                    <p style="margin: 0; opacity: 0.9; font-size: 1rem; font-weight: 500;">
                                        إدارة حسابات دخول الموظفين والصلاحيات المتقدمة
                                    </p>
                                </div>
                            </div>
                            <button class="employee-close-btn" onclick="closeEmployeeModal()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>

                        <div class="employee-modal-body">
                            <!-- إحصائيات سريعة محسنة -->
                            <div class="employee-stats-grid">
                                <div class="employee-stat-card">
                                    <div class="employee-stat-icon" style="background: linear-gradient(135deg, #667eea, #764ba2);">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <div class="employee-stat-number">${totalEmployees}</div>
                                    <div class="employee-stat-label">إجمالي الموظفين</div>
                                </div>

                                <div class="employee-stat-card">
                                    <div class="employee-stat-icon" style="background: linear-gradient(135deg, #56ab2f, #a8e6cf);">
                                        <i class="fas fa-user-check"></i>
                                    </div>
                                    <div class="employee-stat-number">${activeEmployees}</div>
                                    <div class="employee-stat-label">الحسابات النشطة</div>
                                </div>

                                <div class="employee-stat-card">
                                    <div class="employee-stat-icon" style="background: linear-gradient(135deg, #ff416c, #ff4b2b);">
                                        <i class="fas fa-user-shield"></i>
                                    </div>
                                    <div class="employee-stat-number">${adminCount}</div>
                                    <div class="employee-stat-label">المديرين</div>
                                </div>

                                <div class="employee-stat-card">
                                    <div class="employee-stat-icon" style="background: linear-gradient(135deg, #f093fb, #f5576c);">
                                        <i class="fas fa-user-tie"></i>
                                    </div>
                                    <div class="employee-stat-number">${supervisorCount}</div>
                                    <div class="employee-stat-label">المشرفين</div>
                                </div>
                            </div>

                            <!-- إحصائيات إضافية -->
                            <div style="
                                display: grid;
                                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                                gap: 1rem;
                                margin-bottom: 2.5rem;
                                padding: 1.5rem;
                                background: linear-gradient(135deg, #f8f9fa, #e9ecef);
                                border-radius: 15px;
                                border: 1px solid rgba(0,0,0,0.05);
                            ">
                                <div style="text-align: center;">
                                    <div style="font-size: 1.5rem; font-weight: 700; color: #f093fb; margin-bottom: 0.3rem;">
                                        ${cashierCount}
                                    </div>
                                    <div style="font-size: 0.9rem; color: #6c757d;">💰 كاشير</div>
                                </div>
                                <div style="text-align: center;">
                                    <div style="font-size: 1.5rem; font-weight: 700; color: #56ab2f; margin-bottom: 0.3rem;">
                                        ${employeeCount}
                                    </div>
                                    <div style="font-size: 0.9rem; color: #6c757d;">👤 موظف عادي</div>
                                </div>
                                <div style="text-align: center;">
                                    <div style="font-size: 1.5rem; font-weight: 700; color: #ff416c; margin-bottom: 0.3rem;">
                                        ${inactiveEmployees}
                                    </div>
                                    <div style="font-size: 0.9rem; color: #6c757d;">❌ غير نشط</div>
                                </div>
                            </div>

                            <!-- القائمة الرئيسية المحسنة -->
                            <div class="employee-grid" style="gap: 2rem;">
                                <button class="employee-btn" onclick="showAddEmployeeForm()" style="
                                    padding: 2rem;
                                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                                    border-radius: 20px;
                                    box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
                                ">
                                    <div style="
                                        width: 60px;
                                        height: 60px;
                                        background: rgba(255,255,255,0.2);
                                        border-radius: 50%;
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                        font-size: 1.8rem;
                                        margin-bottom: 1rem;
                                    ">
                                        <i class="fas fa-user-plus"></i>
                                    </div>
                                    <div style="text-align: center; flex: 1;">
                                        <div style="font-size: 1.3rem; font-weight: 800; margin-bottom: 0.5rem;">
                                            ➕ إضافة موظف جديد
                                        </div>
                                        <div style="font-size: 1rem; opacity: 0.9; line-height: 1.4;">
                                            إنشاء حساب دخول جديد للموظف مع تحديد الصلاحيات
                                        </div>
                                    </div>
                                </button>

                                <button class="employee-btn warning" onclick="showEmployeesList()" style="
                                    padding: 2rem;
                                    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
                                    border-radius: 20px;
                                    box-shadow: 0 15px 35px rgba(240, 147, 251, 0.3);
                                ">
                                    <div style="
                                        width: 60px;
                                        height: 60px;
                                        background: rgba(255,255,255,0.2);
                                        border-radius: 50%;
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                        font-size: 1.8rem;
                                        margin-bottom: 1rem;
                                    ">
                                        <i class="fas fa-list-ul"></i>
                                    </div>
                                    <div style="text-align: center; flex: 1;">
                                        <div style="font-size: 1.3rem; font-weight: 800; margin-bottom: 0.5rem;">
                                            📋 قائمة الموظفين
                                        </div>
                                        <div style="font-size: 1rem; opacity: 0.9; line-height: 1.4;">
                                            عرض وإدارة جميع حسابات الموظفين المسجلين
                                        </div>
                                    </div>
                                </button>

                                <button class="employee-btn success" onclick="showPermissionsGuide()" style="
                                    padding: 2rem;
                                    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
                                    border-radius: 20px;
                                    box-shadow: 0 15px 35px rgba(86, 171, 47, 0.3);
                                ">
                                    <div style="
                                        width: 60px;
                                        height: 60px;
                                        background: rgba(255,255,255,0.2);
                                        border-radius: 50%;
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                        font-size: 1.8rem;
                                        margin-bottom: 1rem;
                                    ">
                                        <i class="fas fa-key"></i>
                                    </div>
                                    <div style="text-align: center; flex: 1;">
                                        <div style="font-size: 1.3rem; font-weight: 800; margin-bottom: 0.5rem;">
                                            🔑 دليل الصلاحيات
                                        </div>
                                        <div style="font-size: 1rem; opacity: 0.9; line-height: 1.4;">
                                            شرح تفصيلي لصلاحيات كل منصب في النظام
                                        </div>
                                    </div>
                                </button>
                            </div>

                            <!-- معلومات النظام المحسنة -->
                            <div style="
                                background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
                                border-radius: 20px;
                                padding: 2rem;
                                text-align: center;
                                margin-top: 2.5rem;
                                border: 1px solid rgba(102,126,234,0.2);
                                position: relative;
                                overflow: hidden;
                            ">
                                <div style="
                                    position: absolute;
                                    top: 0;
                                    left: 0;
                                    right: 0;
                                    height: 4px;
                                    background: linear-gradient(90deg, #667eea, #764ba2);
                                "></div>

                                <div style="
                                    color: #2c3e50;
                                    margin-bottom: 1rem;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    gap: 0.8rem;
                                ">
                                    <div style="
                                        width: 40px;
                                        height: 40px;
                                        background: linear-gradient(135deg, #667eea, #764ba2);
                                        border-radius: 50%;
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                        color: white;
                                    ">
                                        <i class="fas fa-info-circle"></i>
                                    </div>
                                    <h4 style="margin: 0; font-size: 1.2rem; font-weight: 700;">
                                        معلومات النظام
                                    </h4>
                                </div>

                                <p style="
                                    color: #6c757d;
                                    margin: 0;
                                    font-size: 1rem;
                                    line-height: 1.6;
                                    font-weight: 500;
                                ">
                                    🔐 نظام إدارة الموظفين المتقدم يوفر لك تحكماً كاملاً في حسابات الدخول والصلاحيات<br>
                                    💾 جميع البيانات محفوظة بشكل آمن ومشفر في التخزين المحلي<br>
                                    🛡️ نظام حماية متعدد المستويات لضمان أمان المعلومات
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <style>
                    @keyframes dashboardPulse {
                        0%, 100% { transform: scale(1) rotate(0deg); }
                        50% { transform: scale(1.05) rotate(5deg); }
                    }
                </style>
            `;
            document.getElementById('modalContainer').innerHTML = modalHTML;
            console.log('📊 تم عرض لوحة التحكم الرئيسية المحسنة');
        }

        // عرض نموذج إضافة موظف جديد محسن
        function showAddEmployeeForm() {
            const modalHTML = `
                <div class="employee-modal-overlay" id="employeeModalOverlay">
                    <div class="employee-modal" style="max-width: 700px; width: 95%;">
                        <div class="employee-modal-header" style="background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);">
                            <div style="display: flex; align-items: center; gap: 1.5rem;">
                                <div style="
                                    width: 60px;
                                    height: 60px;
                                    background: rgba(255,255,255,0.2);
                                    border-radius: 50%;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    font-size: 1.8rem;
                                    backdrop-filter: blur(10px);
                                    border: 2px solid rgba(255,255,255,0.3);
                                    animation: addEmployeePulse 2s infinite;
                                ">
                                    <i class="fas fa-user-plus"></i>
                                </div>
                                <div>
                                    <h3 style="margin: 0; font-size: 1.5rem; font-weight: 800; text-shadow: 0 2px 4px rgba(0,0,0,0.3);">
                                        ➕ إضافة موظف جديد
                                    </h3>
                                    <p style="margin: 0; opacity: 0.9; font-size: 1rem; font-weight: 500;">
                                        إنشاء حساب دخول جديد مع تحديد الصلاحيات
                                    </p>
                                </div>
                            </div>
                            <button class="employee-close-btn" onclick="closeEmployeeModal()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>

                        <div class="employee-modal-body">
                            <!-- معلومات توجيهية -->
                            <div style="
                                background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
                                border-radius: 15px;
                                padding: 1.5rem;
                                margin-bottom: 2rem;
                                border-left: 4px solid #56ab2f;
                                position: relative;
                            ">
                                <div style="display: flex; align-items: center; gap: 0.8rem; margin-bottom: 0.8rem;">
                                    <div style="
                                        width: 35px;
                                        height: 35px;
                                        background: linear-gradient(135deg, #56ab2f, #a8e6cf);
                                        border-radius: 50%;
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                        color: white;
                                    ">
                                        <i class="fas fa-lightbulb"></i>
                                    </div>
                                    <h4 style="margin: 0; color: #2c3e50; font-weight: 700;">نصائح مهمة</h4>
                                </div>
                                <ul style="margin: 0; padding-right: 1rem; color: #5a6c5d; font-size: 0.9rem; line-height: 1.5;">
                                    <li>اختر اسم مستخدم فريد وسهل التذكر</li>
                                    <li>كلمة المرور يجب أن تكون 3 أحرف على الأقل</li>
                                    <li>حدد المنصب المناسب حسب مسؤوليات الموظف</li>
                                </ul>
                            </div>

                            <form id="addEmployeeForm" onsubmit="createNewEmployee(event)">
                                <!-- معلومات شخصية -->
                                <div style="
                                    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
                                    border-radius: 15px;
                                    padding: 1.5rem;
                                    margin-bottom: 2rem;
                                    border-top: 3px solid #667eea;
                                ">
                                    <h5 style="
                                        margin: 0 0 1.5rem 0;
                                        color: #2c3e50;
                                        font-weight: 700;
                                        display: flex;
                                        align-items: center;
                                        gap: 0.5rem;
                                    ">
                                        <i class="fas fa-user" style="color: #667eea;"></i>
                                        المعلومات الشخصية
                                    </h5>

                                    <div class="employee-form-group">
                                        <label>
                                            <i class="fas fa-user" style="color: #667eea;"></i>
                                            الاسم الكامل
                                        </label>
                                        <input
                                            type="text"
                                            id="employeeName"
                                            placeholder="أدخل الاسم الكامل للموظف"
                                            required
                                            style="background: linear-gradient(145deg, #ffffff, #f8f9fa);"
                                        >
                                    </div>

                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                                        <div class="employee-form-group">
                                            <label>
                                                <i class="fas fa-phone" style="color: #56ab2f;"></i>
                                                رقم الهاتف
                                            </label>
                                            <input
                                                type="tel"
                                                id="employeePhone"
                                                placeholder="07XXXXXXXX"
                                                style="background: linear-gradient(145deg, #ffffff, #f8f9fa);"
                                            >
                                        </div>

                                        <div class="employee-form-group">
                                            <label>
                                                <i class="fas fa-envelope" style="color: #f093fb;"></i>
                                                البريد الإلكتروني
                                            </label>
                                            <input
                                                type="email"
                                                id="employeeEmail"
                                                placeholder="<EMAIL>"
                                                style="background: linear-gradient(145deg, #ffffff, #f8f9fa);"
                                            >
                                        </div>
                                    </div>
                                </div>

                                <!-- معلومات الحساب -->
                                <div style="
                                    background: linear-gradient(135deg, #fff3e0, #ffe0b2);
                                    border-radius: 15px;
                                    padding: 1.5rem;
                                    margin-bottom: 2rem;
                                    border-top: 3px solid #ff9800;
                                ">
                                    <h5 style="
                                        margin: 0 0 1.5rem 0;
                                        color: #2c3e50;
                                        font-weight: 700;
                                        display: flex;
                                        align-items: center;
                                        gap: 0.5rem;
                                    ">
                                        <i class="fas fa-key" style="color: #ff9800;"></i>
                                        معلومات تسجيل الدخول
                                    </h5>

                                    <div class="employee-form-group">
                                        <label>
                                            <i class="fas fa-id-card" style="color: #ff9800;"></i>
                                            اسم المستخدم
                                        </label>
                                        <input
                                            type="text"
                                            id="employeeUsername"
                                            placeholder="أدخل اسم المستخدم (بالإنجليزية)"
                                            required
                                            style="background: linear-gradient(145deg, #ffffff, #fff8e1);"
                                        >
                                    </div>

                                    <div class="employee-form-group">
                                        <label>
                                            <i class="fas fa-lock" style="color: #ff416c;"></i>
                                            كلمة المرور
                                        </label>
                                        <input
                                            type="password"
                                            id="employeePassword"
                                            placeholder="أدخل كلمة المرور (3 أحرف على الأقل)"
                                            required
                                            style="background: linear-gradient(145deg, #ffffff, #fff8e1);"
                                        >
                                    </div>
                                </div>

                                <!-- الصلاحيات والحالة -->
                                <div style="
                                    background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
                                    border-radius: 15px;
                                    padding: 1.5rem;
                                    margin-bottom: 2rem;
                                    border-top: 3px solid #4caf50;
                                ">
                                    <h5 style="
                                        margin: 0 0 1.5rem 0;
                                        color: #2c3e50;
                                        font-weight: 700;
                                        display: flex;
                                        align-items: center;
                                        gap: 0.5rem;
                                    ">
                                        <i class="fas fa-cogs" style="color: #4caf50;"></i>
                                        الصلاحيات والحالة
                                    </h5>

                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                                        <div class="employee-form-group">
                                            <label>
                                                <i class="fas fa-user-tag" style="color: #4caf50;"></i>
                                                المنصب
                                            </label>
                                            <select id="employeeRole" required style="background: linear-gradient(145deg, #ffffff, #f1f8e9);">
                                                <option value="">اختر المنصب</option>
                                                <option value="employee">👤 موظف عادي</option>
                                                <option value="cashier">💰 كاشير</option>
                                                <option value="supervisor">👨‍💼 مشرف</option>
                                                <option value="admin">👑 مدير</option>
                                            </select>
                                        </div>

                                        <div class="employee-form-group">
                                            <label>
                                                <i class="fas fa-toggle-on" style="color: #4caf50;"></i>
                                                حالة الحساب
                                            </label>
                                            <select id="employeeStatus" required style="background: linear-gradient(145deg, #ffffff, #f1f8e9);">
                                                <option value="active">✅ نشط</option>
                                                <option value="inactive">❌ غير نشط</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- رسالة النتيجة -->
                                <div id="addEmployeeMessage" class="employee-message" style="display: none;"></div>

                                <!-- أزرار التحكم -->
                                <div style="display: flex; gap: 1.5rem; justify-content: center; margin-top: 2.5rem;">
                                    <button type="button" class="employee-btn secondary" onclick="showEmployeeMainDashboard()">
                                        <i class="fas fa-arrow-right"></i>
                                        العودة للقائمة الرئيسية
                                    </button>
                                    <button type="submit" class="employee-btn success" style="background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);">
                                        <i class="fas fa-save"></i>
                                        إنشاء الحساب
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <style>
                    @keyframes addEmployeePulse {
                        0%, 100% { transform: scale(1); }
                        50% { transform: scale(1.1); }
                    }
                </style>
            `;
            document.getElementById('modalContainer').innerHTML = modalHTML;
            console.log('📝 تم عرض نموذج إضافة موظف جديد المحسن');
        }

        // إنشاء موظف جديد
        function createNewEmployee(event) {
            event.preventDefault();

            const name = document.getElementById('employeeName').value.trim();
            const username = document.getElementById('employeeUsername').value.trim();
            const password = document.getElementById('employeePassword').value;
            const phone = document.getElementById('employeePhone').value.trim();
            const email = document.getElementById('employeeEmail').value.trim();
            const role = document.getElementById('employeeRole').value;
            const status = document.getElementById('employeeStatus').value;

            // التحقق من البيانات المطلوبة
            if (!name || !username || !password || !role) {
                showEmployeeMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
                return;
            }

            // التحقق من طول كلمة المرور
            if (password.length < 3) {
                showEmployeeMessage('كلمة المرور يجب أن تكون 3 أحرف على الأقل', 'error');
                return;
            }

            // التحقق من عدم تكرار اسم المستخدم
            if (currentEmployees.find(emp => emp.username === username)) {
                showEmployeeMessage('اسم المستخدم موجود بالفعل، يرجى اختيار اسم آخر', 'error');
                return;
            }

            // إنشاء الموظف الجديد
            const newEmployee = {
                id: 'emp_' + Date.now(),
                name: name,
                username: username,
                password: password,
                phone: phone,
                email: email,
                role: role,
                status: status,
                createdAt: new Date().toISOString(),
                createdBy: 'المدير'
            };

            // إضافة الموظف للقائمة
            currentEmployees.push(newEmployee);

            if (saveEmployeesToStorage()) {
                showEmployeeMessage('تم إنشاء حساب الموظف بنجاح!', 'success');

                // إعادة تعيين النموذج
                document.getElementById('addEmployeeForm').reset();

                // العودة للقائمة الرئيسية بعد ثانيتين
                setTimeout(() => {
                    showEmployeeMainDashboard();
                }, 2000);

                console.log('✅ تم إنشاء موظف جديد:', newEmployee.name);
            } else {
                showEmployeeMessage('حدث خطأ في حفظ بيانات الموظف', 'error');
            }
        }

        // عرض رسالة في النموذج
        function showEmployeeMessage(message, type) {
            const messageDiv = document.getElementById('addEmployeeMessage');
            if (messageDiv) {
                messageDiv.textContent = message;
                messageDiv.className = `employee-message ${type}`;
                messageDiv.style.display = 'block';

                // إخفاء الرسالة بعد 5 ثوان
                setTimeout(() => {
                    messageDiv.style.display = 'none';
                }, 5000);
            }
        }

        // عرض قائمة الموظفين
        function showEmployeesList() {
            const modalHTML = `
                <div class="employee-modal-overlay" id="employeeModalOverlay">
                    <div class="employee-modal" style="max-width: 1000px; width: 95%;">
                        <div class="employee-modal-header">
                            <div style="display: flex; align-items: center; gap: 1rem;">
                                <div style="width: 50px; height: 50px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 1.5rem;">
                                    <i class="fas fa-list-ul"></i>
                                </div>
                                <div>
                                    <h3 style="margin: 0; font-size: 1.4rem; font-weight: 700;">قائمة الموظفين</h3>
                                    <p style="margin: 0; opacity: 0.9; font-size: 0.9rem;">إجمالي الموظفين: ${currentEmployees.length}</p>
                                </div>
                            </div>
                            <button class="employee-close-btn" onclick="closeEmployeeModal()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="employee-modal-body">
                            <!-- أزرار التحكم -->
                            <div class="employee-controls">
                                <button class="employee-btn" onclick="showAddEmployeeForm()">
                                    <i class="fas fa-plus"></i> إضافة موظف جديد
                                </button>
                                <button class="employee-btn secondary" onclick="showEmployeeMainDashboard()">
                                    <i class="fas fa-arrow-right"></i> العودة للقائمة الرئيسية
                                </button>
                            </div>

                            <!-- جدول الموظفين -->
                            <div style="overflow-x: auto; border-radius: 15px; box-shadow: 0 8px 25px rgba(0,0,0,0.1);">
                                ${currentEmployees.length > 0 ? `
                                    <table class="employee-table">
                                        <thead>
                                            <tr>
                                                <th>الاسم الكامل</th>
                                                <th>اسم المستخدم</th>
                                                <th>رقم الهاتف</th>
                                                <th style="text-align: center;">المنصب</th>
                                                <th style="text-align: center;">الحالة</th>
                                                <th style="text-align: center;">تاريخ الإنشاء</th>
                                                <th style="text-align: center;">الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${currentEmployees.map((emp, index) => `
                                                <tr style="background: ${index % 2 === 0 ? '#ffffff' : '#f8f9fa'};">
                                                    <td>
                                                        <div style="display: flex; align-items: center; gap: 0.8rem;">
                                                            <div class="employee-avatar">
                                                                ${emp.name.charAt(0)}
                                                            </div>
                                                            <span style="font-weight: 600; color: #2c3e50;">${emp.name}</span>
                                                        </div>
                                                    </td>
                                                    <td style="color: #7f8c8d; font-family: monospace; font-size: 0.95rem;">
                                                        ${emp.username}
                                                    </td>
                                                    <td style="color: #7f8c8d; direction: ltr; text-align: left;">
                                                        ${emp.phone || 'غير محدد'}
                                                    </td>
                                                    <td style="text-align: center;">
                                                        <span class="employee-role-badge ${emp.role}">
                                                            ${emp.role === 'admin' ? '👑 مدير' :
                                                              emp.role === 'supervisor' ? '👨‍💼 مشرف' :
                                                              emp.role === 'cashier' ? '💰 كاشير' :
                                                              '👤 موظف'}
                                                        </span>
                                                    </td>
                                                    <td style="text-align: center;">
                                                        <span class="employee-status-badge ${emp.status}">
                                                            ${emp.status === 'active' ? '✅ نشط' : '❌ غير نشط'}
                                                        </span>
                                                    </td>
                                                    <td style="text-align: center; color: #7f8c8d; font-size: 0.9rem;">
                                                        ${new Date(emp.createdAt).toLocaleDateString('ar-SA', {
                                                            year: 'numeric',
                                                            month: 'short',
                                                            day: 'numeric'
                                                        })}
                                                    </td>
                                                    <td style="text-align: center;">
                                                        <div class="employee-actions">
                                                            <button class="employee-action-btn edit" onclick="editEmployee('${emp.id}')" title="تعديل البيانات">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            ${emp.username !== 'ali' ? `
                                                                <button class="employee-action-btn delete" onclick="deleteEmployee('${emp.id}')" title="حذف الحساب">
                                                                    <i class="fas fa-trash"></i>
                                                                </button>
                                                            ` : `
                                                                <div class="employee-action-btn protected" title="حساب المدير الرئيسي">
                                                                    <i class="fas fa-shield-alt"></i>
                                                                </div>
                                                            `}
                                                        </div>
                                                    </td>
                                                </tr>
                                            `).join('')}
                                        </tbody>
                                    </table>
                                ` : `
                                    <div class="employee-empty-state">
                                        <div class="employee-empty-icon">
                                            <i class="fas fa-users"></i>
                                        </div>
                                        <h4 style="color: #2c3e50; margin-bottom: 1rem; font-size: 1.3rem;">
                                            لا يوجد موظفين مسجلين
                                        </h4>
                                        <p style="color: #7f8c8d; margin-bottom: 2rem; font-size: 1rem;">
                                            ابدأ بإضافة موظف جديد للنظام لتتمكن من إدارة الحسابات
                                        </p>
                                        <button class="employee-btn" onclick="showAddEmployeeForm()">
                                            <i class="fas fa-plus"></i> إضافة أول موظف
                                        </button>
                                    </div>
                                `}
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.getElementById('modalContainer').innerHTML = modalHTML;
            console.log('📋 تم عرض قائمة الموظفين');
        }

        // حذف موظف
        function deleteEmployee(employeeId) {
            const employee = currentEmployees.find(emp => emp.id === employeeId);
            if (!employee) {
                alert('حدث خطأ: لم يتم العثور على الموظف المطلوب حذفه');
                return;
            }

            if (confirm(`هل أنت متأكد من حذف حساب الموظف "${employee.name}"؟\n\nسيتم حذف الحساب نهائياً ولن يتمكن من تسجيل الدخول مرة أخرى.`)) {
                currentEmployees = currentEmployees.filter(emp => emp.id !== employeeId);

                if (saveEmployeesToStorage()) {
                    alert(`تم حذف حساب الموظف "${employee.name}" بنجاح!`);
                    showEmployeesList(); // إعادة تحميل القائمة
                    console.log(`🗑️ تم حذف الموظف: ${employee.name}`);
                } else {
                    alert('حدث خطأ في حذف الموظف');
                }
            }
        }

        // تعديل بيانات موظف (وظيفة مستقبلية)
        function editEmployee(employeeId) {
            alert('وظيفة تعديل بيانات الموظف ستكون متاحة قريباً!\n\nيمكنك حالياً حذف الحساب وإنشاء حساب جديد بالبيانات المحدثة.');
        }

        // عرض دليل الصلاحيات والمناصب
        function showPermissionsGuide() {
            const modalHTML = `
                <div class="employee-modal-overlay" id="employeeModalOverlay">
                    <div class="employee-modal" style="max-width: 900px; width: 95%;">
                        <div class="employee-modal-header">
                            <div style="display: flex; align-items: center; gap: 1rem;">
                                <div style="width: 50px; height: 50px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 1.5rem;">
                                    <i class="fas fa-key"></i>
                                </div>
                                <div>
                                    <h3 style="margin: 0; font-size: 1.4rem; font-weight: 700;">دليل الصلاحيات والمناصب</h3>
                                    <p style="margin: 0; opacity: 0.9; font-size: 0.9rem;">تحديد صلاحيات الوصول لكل منصب في النظام</p>
                                </div>
                            </div>
                            <button class="employee-close-btn" onclick="closeEmployeeModal()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="employee-modal-body">
                            <!-- زر العودة -->
                            <div style="margin-bottom: 2rem;">
                                <button class="employee-btn secondary" onclick="showEmployeeMainDashboard()">
                                    <i class="fas fa-arrow-right"></i> العودة للقائمة الرئيسية
                                </button>
                            </div>

                            <!-- بطاقات المناصب والصلاحيات -->
                            <div class="employee-permissions-grid">
                                <!-- مدير -->
                                <div class="employee-permission-card" style="background: linear-gradient(135deg, #e74c3c, #c0392b);">
                                    <div class="employee-permission-header">
                                        <div class="employee-permission-icon">👑</div>
                                        <div>
                                            <h4 class="employee-permission-title">مدير النظام</h4>
                                            <p class="employee-permission-subtitle">صلاحيات كاملة وغير محدودة</p>
                                        </div>
                                    </div>
                                    <div style="position: relative; z-index: 1;">
                                        <h5 style="margin-bottom: 0.8rem; font-size: 1rem;">الصلاحيات المتاحة:</h5>
                                        <ul class="employee-permission-list">
                                            <li class="employee-permission-item">
                                                <i class="fas fa-check-circle" style="color: #2ecc71;"></i>
                                                إدارة جميع المنتجات والفئات
                                            </li>
                                            <li class="employee-permission-item">
                                                <i class="fas fa-check-circle" style="color: #2ecc71;"></i>
                                                إدارة الطلبات والمبيعات
                                            </li>
                                            <li class="employee-permission-item">
                                                <i class="fas fa-check-circle" style="color: #2ecc71;"></i>
                                                إدارة حسابات الموظفين
                                            </li>
                                            <li class="employee-permission-item">
                                                <i class="fas fa-check-circle" style="color: #2ecc71;"></i>
                                                الوصول لجميع التقارير والإحصائيات
                                            </li>
                                            <li class="employee-permission-item">
                                                <i class="fas fa-check-circle" style="color: #2ecc71;"></i>
                                                إعدادات النظام والأمان
                                            </li>
                                        </ul>
                                    </div>
                                </div>

                                <!-- مشرف -->
                                <div class="employee-permission-card" style="background: linear-gradient(135deg, #f39c12, #e67e22);">
                                    <div class="employee-permission-header">
                                        <div class="employee-permission-icon">👨‍💼</div>
                                        <div>
                                            <h4 class="employee-permission-title">مشرف</h4>
                                            <p class="employee-permission-subtitle">إدارة العمليات اليومية</p>
                                        </div>
                                    </div>
                                    <div style="position: relative; z-index: 1;">
                                        <h5 style="margin-bottom: 0.8rem; font-size: 1rem;">الصلاحيات المتاحة:</h5>
                                        <ul class="employee-permission-list">
                                            <li class="employee-permission-item">
                                                <i class="fas fa-check-circle" style="color: #2ecc71;"></i>
                                                إدارة المنتجات والمخزون
                                            </li>
                                            <li class="employee-permission-item">
                                                <i class="fas fa-check-circle" style="color: #2ecc71;"></i>
                                                متابعة الطلبات والمبيعات
                                            </li>
                                            <li class="employee-permission-item">
                                                <i class="fas fa-check-circle" style="color: #2ecc71;"></i>
                                                إدارة الموظفين العاديين
                                            </li>
                                            <li class="employee-permission-item">
                                                <i class="fas fa-times-circle" style="color: #e74c3c;"></i>
                                                لا يمكن الوصول لإعدادات النظام
                                            </li>
                                        </ul>
                                    </div>
                                </div>

                                <!-- كاشير -->
                                <div class="employee-permission-card" style="background: linear-gradient(135deg, #3498db, #2980b9);">
                                    <div class="employee-permission-header">
                                        <div class="employee-permission-icon">💰</div>
                                        <div>
                                            <h4 class="employee-permission-title">كاشير</h4>
                                            <p class="employee-permission-subtitle">متخصص في المبيعات والدفع</p>
                                        </div>
                                    </div>
                                    <div style="position: relative; z-index: 1;">
                                        <h5 style="margin-bottom: 0.8rem; font-size: 1rem;">الصلاحيات المتاحة:</h5>
                                        <ul class="employee-permission-list">
                                            <li class="employee-permission-item">
                                                <i class="fas fa-check-circle" style="color: #2ecc71;"></i>
                                                نظام الكاشير والمبيعات
                                            </li>
                                            <li class="employee-permission-item">
                                                <i class="fas fa-check-circle" style="color: #2ecc71;"></i>
                                                طباعة الفواتير
                                            </li>
                                            <li class="employee-permission-item">
                                                <i class="fas fa-check-circle" style="color: #2ecc71;"></i>
                                                عرض المنتجات فقط
                                            </li>
                                            <li class="employee-permission-item">
                                                <i class="fas fa-times-circle" style="color: #e74c3c;"></i>
                                                لا يمكن تعديل المنتجات
                                            </li>
                                        </ul>
                                    </div>
                                </div>

                                <!-- موظف عادي -->
                                <div class="employee-permission-card" style="background: linear-gradient(135deg, #27ae60, #2ecc71);">
                                    <div class="employee-permission-header">
                                        <div class="employee-permission-icon">👤</div>
                                        <div>
                                            <h4 class="employee-permission-title">موظف عادي</h4>
                                            <p class="employee-permission-subtitle">صلاحيات محدودة حسب التخصص</p>
                                        </div>
                                    </div>
                                    <div style="position: relative; z-index: 1;">
                                        <h5 style="margin-bottom: 0.8rem; font-size: 1rem;">الصلاحيات المتاحة:</h5>
                                        <ul class="employee-permission-list">
                                            <li class="employee-permission-item">
                                                <i class="fas fa-check-circle" style="color: #2ecc71;"></i>
                                                عرض المنتجات والمعلومات
                                            </li>
                                            <li class="employee-permission-item">
                                                <i class="fas fa-check-circle" style="color: #2ecc71;"></i>
                                                تسجيل الدخول للنظام
                                            </li>
                                            <li class="employee-permission-item">
                                                <i class="fas fa-times-circle" style="color: #e74c3c;"></i>
                                                لا يمكن تعديل أي بيانات
                                            </li>
                                            <li class="employee-permission-item">
                                                <i class="fas fa-times-circle" style="color: #e74c3c;"></i>
                                                لا يمكن الوصول للإعدادات
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <!-- معلومات إضافية -->
                            <div style="background: linear-gradient(135deg, #ecf0f1, #bdc3c7); border-radius: 15px; padding: 1.5rem; text-align: center; margin-top: 2rem; border: 1px solid rgba(189,195,199,0.3);">
                                <div style="color: #2c3e50; margin-bottom: 0.5rem;">
                                    <i class="fas fa-lightbulb" style="margin-left: 0.5rem; color: #f39c12;"></i>
                                    <strong>نصائح مهمة</strong>
                                </div>
                                <p style="color: #7f8c8d; margin: 0; font-size: 0.9rem; line-height: 1.5;">
                                    • اختر المنصب المناسب لكل موظف حسب مسؤولياته في العمل<br>
                                    • يمكن تغيير صلاحيات الموظف عن طريق تعديل منصبه<br>
                                    • حساب المدير الرئيسي (ali) محمي ولا يمكن حذفه أو تعديل صلاحياته
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.getElementById('modalContainer').innerHTML = modalHTML;
            console.log('🔑 تم عرض دليل الصلاحيات والمناصب');
        }

        console.log('🚀 تم تحميل نظام إدارة الموظفين');
    </script>

    <script>
        // التحقق من تسجيل الدخول والصلاحيات
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 بدء تحميل النظام...');

            // التحقق من الصلاحيات أولاً
            if (checkAdminAccess()) {
                console.log('✅ تم التحقق من الصلاحيات بنجاح');

                // تهيئة نظام الموظفين
                setTimeout(() => {
                    initializeEmployeeSystem();
                    console.log('✅ تم تحميل نظام الموظفين');
                }, 100);
            }
        });

        function checkAdminAccess() {
            console.log('🔐 بدء التحقق من صلاحيات الإدارة...');

            const userSession = sessionStorage.getItem('userSession');

            // تم إزالة التحقق من جلسة المستخدم - الوصول مباشر
            console.log('✅ الوصول مباشر للوحة التحكم');

            try {
                const user = JSON.parse(userSession);
                console.log('👤 بيانات المستخدم:', user);

                // تم إزالة التحقق من بيانات المستخدم - الوصول مباشر
                console.log('✅ تم السماح بالوصول للوحة التحكم');

                const employees = JSON.parse(localStorage.getItem('employees')) || [];
                const employee = employees.find(emp =>
                    emp.username === user.username &&
                    emp.status === 'active' &&
                    (emp.role === 'admin' || emp.role === 'supervisor' || emp.role === 'employee')
                );

                // تم حذف التحقق من الموظف - السماح بالوصول

                console.log('✅ تم العثور على الموظف:', employee);
                updateAdminInterface(employee);
                console.log('✅ تم التحقق من صلاحيات الإدارة بنجاح');
                return true;

            } catch (error) {
                console.log('⚠️ تم تجاهل خطأ التحقق - الوصول مباشر');
                return true;
            }
        }

        // تم حذف إعادة التوجيه لصفحة تسجيل الدخول

        function updateAdminInterface(employee) {
            const adminInfo = document.querySelector('.admin-info');
            if (adminInfo && !adminInfo.querySelector('.admin-name')) {
                const adminName = document.createElement('span');
                adminName.className = 'admin-name';
                const roleText = employee.role === 'admin' ? 'المدير' :
                               employee.role === 'supervisor' ? 'المشرف' : 'الموظف';
                const roleIcon = employee.role === 'admin' ? '👑' :
                               employee.role === 'supervisor' ? '👨‍💼' : '👤';
                adminName.textContent = `${roleIcon} ${roleText}: ${employee.name}`;
                adminName.style.cssText = `color: white; font-weight: 600; margin-left: 0.5rem;`;
                adminInfo.insertBefore(adminName, adminInfo.firstChild);
            }

            // تطبيق نظام الصلاحيات بعد تأخير للتأكد من تحميل الصفحة
            setTimeout(() => {
                applyPermissions(employee);
                console.log('🔐 تم تطبيق الصلاحيات بنجاح');
            }, 500);
        }

        function applyPermissions(employee) {
            const role = employee.role;

            console.log(`🔄 بدء تطبيق صلاحيات ${role} للمستخدم ${employee.name}`);

            // إخفاء جميع الأقسام أولاً
            hideAllSections();

            // إظهار الأقسام حسب الصلاحيات
            setTimeout(() => {
                switch(role) {
                    case 'admin':
                        showAdminSections();
                        break;
                    case 'supervisor':
                        showSupervisorSections();
                        break;
                    case 'employee':
                        showEmployeeSections();
                        break;
                    default:
                        showEmployeeSections();
                }

                // حفظ الصلاحيات المطبقة لمنع إعادة الإخفاء
                window.permissionsApplied = true;
                window.currentUserRole = role;

                // تفعيل حماية الأقسام
                setTimeout(() => {
                    protectSections();
                }, 100);

                console.log(`✅ تم تطبيق صلاحيات ${role} للمستخدم ${employee.name}`);
            }, 200);
        }

        function hideAllSections() {
            // التحقق من أن الصلاحيات لم تطبق بعد
            if (window.permissionsApplied) {
                console.log('⚠️ تم منع إخفاء الأقسام - الصلاحيات مطبقة بالفعل');
                return;
            }

            console.log('🔒 إخفاء جميع الأقسام...');

            // إخفاء جميع أقسام المحتوى
            const allSections = document.querySelectorAll('.content-section');
            allSections.forEach(section => {
                section.style.display = 'none';
            });

            // إخفاء عناصر القائمة الجانبية
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                item.style.display = 'none';
            });

            // إخفاء أزرار الهيدر
            const headerButtons = document.querySelectorAll('.header-cashier-btn, .header-employee-btn');
            headerButtons.forEach(btn => {
                btn.style.display = 'none';
            });
        }

        function showAdminSections() {
            // المدير يرى جميع الأقسام
            const allSections = document.querySelectorAll('.content-section');
            allSections.forEach(section => {
                section.style.display = 'block';
            });

            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                item.style.display = 'block';
            });

            const headerButtons = document.querySelectorAll('.header-cashier-btn, .header-employee-btn');
            headerButtons.forEach(btn => {
                btn.style.display = 'inline-flex';
            });

            // إظهار لوحة التحكم الرئيسية
            showDashboard();

            console.log('🔓 تم فتح جميع الصلاحيات للمدير');
        }

        function showSupervisorSections() {
            // المشرف يرى أقسام محددة
            const allowedSections = [
                'products', 'categories', 'offers', 'coupons',
                'reports', 'settings', 'featured-products'
            ];

            // إظهار الأقسام المسموحة
            allowedSections.forEach(sectionId => {
                const section = document.getElementById(sectionId);
                if (section) {
                    section.style.display = 'block';
                }

                // إظهار عنصر القائمة المقابل
                const navLink = document.querySelector(`[data-section="${sectionId}"]`);
                if (navLink) {
                    navLink.closest('.nav-item').style.display = 'block';
                }
            });

            // إظهار زر الكاشير فقط
            const cashierBtn = document.querySelector('.header-cashier-btn');
            if (cashierBtn) {
                cashierBtn.style.display = 'inline-flex';
            }

            // إظهار لوحة التحكم الرئيسية
            showDashboard();

            // إخفاء إدارة الموظفين
            hideEmployeeManagement();

            console.log('🔒 تم تطبيق صلاحيات المشرف');
        }

        function showEmployeeSections() {
            // الموظف العادي يرى أقسام محدودة جداً (قراءة فقط)
            const allowedSections = ['products', 'categories'];

            // إظهار الأقسام المسموحة
            allowedSections.forEach(sectionId => {
                const section = document.getElementById(sectionId);
                if (section) {
                    section.style.display = 'block';
                    // جعل القسم للقراءة فقط
                    makeSectionReadOnly(section);
                }

                // إظهار عنصر القائمة المقابل
                const navLink = document.querySelector(`[data-section="${sectionId}"]`);
                if (navLink) {
                    navLink.closest('.nav-item').style.display = 'block';
                }
            });

            // إظهار لوحة التحكم الرئيسية (بصلاحيات محدودة)
            showDashboard();

            console.log('🔐 تم تطبيق صلاحيات الموظف العادي (قراءة فقط)');
        }

        function showDashboard() {
            // إظهار لوحة التحكم الرئيسية
            const dashboard = document.getElementById('dashboard');
            if (dashboard) {
                dashboard.style.display = 'block';
                dashboard.classList.add('active');
            }

            // إظهار عنصر القائمة للوحة التحكم
            const dashboardNavItem = document.querySelector('[data-section="dashboard"]');
            if (dashboardNavItem) {
                dashboardNavItem.closest('.nav-item').style.display = 'block';
                dashboardNavItem.closest('.nav-item').classList.add('active');
            }

            // تحديث عنوان الصفحة
            const pageTitle = document.getElementById('pageTitle');
            if (pageTitle) {
                pageTitle.textContent = 'لوحة المعلومات';
            }

            // تحميل بيانات لوحة التحكم
            if (typeof loadDashboardData === 'function') {
                loadDashboardData();
            }

            console.log('📊 تم عرض لوحة التحكم الرئيسية');
        }

        function hideEmployeeManagement() {
            // إخفاء قسم إدارة الموظفين
            const employeeSection = document.getElementById('employee-management');
            if (employeeSection) {
                employeeSection.style.display = 'none';
            }

            // إخفاء عنصر القائمة لإدارة الموظفين
            const employeeNavItem = document.querySelector('[data-section="employee-management"]');
            if (employeeNavItem) {
                employeeNavItem.closest('.nav-item').style.display = 'none';
            }

            const employeeBtn = document.querySelector('.header-employee-btn');
            if (employeeBtn) {
                employeeBtn.style.display = 'none';
            }
        }

        function makeSectionReadOnly(section) {
            // إضافة مؤشر للقراءة فقط
            if (!section.querySelector('.readonly-badge')) {
                const readonlyBadge = document.createElement('div');
                readonlyBadge.className = 'readonly-badge';
                readonlyBadge.innerHTML = '<i class="fas fa-eye"></i> وضع القراءة فقط';
                readonlyBadge.style.cssText = `
                    position: fixed;
                    top: 80px;
                    left: 20px;
                    background: linear-gradient(135deg, #6c757d, #495057);
                    color: white;
                    padding: 0.5rem 1rem;
                    border-radius: 20px;
                    font-size: 0.8rem;
                    font-weight: 600;
                    z-index: 1000;
                    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
                    animation: readonlyPulse 2s infinite;
                `;
                document.body.appendChild(readonlyBadge);

                // إضافة الرسوم المتحركة
                if (!document.querySelector('style[data-readonly-animation]')) {
                    const style = document.createElement('style');
                    style.setAttribute('data-readonly-animation', 'true');
                    style.textContent = `
                        @keyframes readonlyPulse {
                            0%, 100% { opacity: 0.8; transform: scale(1); }
                            50% { opacity: 1; transform: scale(1.05); }
                        }
                    `;
                    document.head.appendChild(style);
                }
            }

            // تعطيل جميع أزرار الإضافة والتعديل والحذف
            const actionButtons = section.querySelectorAll('button, .btn');
            actionButtons.forEach(btn => {
                const btnText = btn.textContent.toLowerCase();
                if (btnText.includes('إضافة') || btnText.includes('تعديل') ||
                    btnText.includes('حذف') || btnText.includes('حفظ') ||
                    btnText.includes('إنشاء') || btnText.includes('تحديث')) {
                    btn.style.display = 'none';
                }
            });

            // تعطيل النماذج
            const forms = section.querySelectorAll('form');
            forms.forEach(form => {
                const inputs = form.querySelectorAll('input, textarea, select');
                inputs.forEach(input => {
                    input.disabled = true;
                    input.style.backgroundColor = '#f8f9fa';
                    input.style.cursor = 'not-allowed';
                });
            });
        }

        // مراقب لحماية الأقسام من الإخفاء غير المرغوب فيه
        function protectSections() {
            if (!window.permissionsApplied || !window.currentUserRole) return;

            // مراقبة التغييرات في الأقسام
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                        const target = mutation.target;
                        if (target.classList.contains('content-section') || target.classList.contains('nav-item')) {
                            // إذا تم إخفاء قسم مسموح، أعد إظهاره
                            if (target.style.display === 'none' && shouldBeVisible(target)) {
                                console.log('🛡️ حماية القسم من الإخفاء:', target.id || target.className);
                                target.style.display = 'block';
                            }
                        }
                    }
                });
            });

            // مراقبة جميع العناصر
            observer.observe(document.body, {
                attributes: true,
                subtree: true,
                attributeFilter: ['style']
            });

            console.log('🛡️ تم تفعيل حماية الأقسام');
        }

        function shouldBeVisible(element) {
            // الحصول على دور المستخدم
            let role = window.currentUserRole;

            if (!role) {
                try {
                    const userSession = sessionStorage.getItem('userSession');
                    if (userSession) {
                        const user = JSON.parse(userSession);
                        role = user.role;
                        window.currentUserRole = role;
                    }
                } catch (error) {
                    console.error('خطأ في قراءة جلسة المستخدم:', error);
                }
            }

            if (!role) return true; // السماح بالعرض إذا لم يتم تحديد الدور

            const elementId = element.id;
            const dataSection = element.getAttribute('data-section');

            // تحديد الأقسام المسموحة لكل دور
            const allowedSections = {
                'admin': ['dashboard', 'products', 'categories', 'offers', 'coupons', 'reports', 'settings', 'featured-products', 'employee-management'],
                'supervisor': ['dashboard', 'products', 'categories', 'offers', 'coupons', 'reports', 'settings', 'featured-products'],
                'employee': ['dashboard', 'products', 'categories'],
                'cashier': ['dashboard']
            };

            const allowed = allowedSections[role] || allowedSections['employee'];
            const isAllowed = allowed.includes(elementId) || allowed.includes(dataSection);

            console.log(`🔍 فحص رؤية العنصر ${elementId || dataSection} للدور ${role}: ${isAllowed ? 'مسموح' : 'ممنوع'}`);

            return isAllowed;
        }

        // إعادة تطبيق الصلاحيات عند الحاجة
        function reapplyPermissions() {
            console.log('🔄 إعادة تطبيق الصلاحيات...');

            try {
                const userSession = sessionStorage.getItem('userSession');
                if (userSession) {
                    const user = JSON.parse(userSession);
                    window.currentUserRole = user.role;

                    // إعادة تطبيق الصلاحيات
                    setTimeout(() => {
                        applyPermissions(user);
                    }, 100);
                }
            } catch (error) {
                console.error('خطأ في إعادة تطبيق الصلاحيات:', error);
            }
        }

        // إضافة وظيفة للتحقق من التنقل
        function ensureNavigationWorks() {
            console.log('🔧 التأكد من عمل التنقل...');

            // التأكد من أن الأقسام المسموحة مرئية
            const userRole = window.currentUserRole;
            if (userRole) {
                const allowedSections = {
                    'admin': ['dashboard', 'products', 'categories', 'offers', 'coupons', 'reports', 'settings', 'featured-products', 'employee-management'],
                    'supervisor': ['dashboard', 'products', 'categories', 'offers', 'coupons', 'reports', 'settings', 'featured-products'],
                    'employee': ['dashboard', 'products', 'categories'],
                    'cashier': ['dashboard']
                };

                const allowed = allowedSections[userRole] || allowedSections['employee'];

                allowed.forEach(sectionId => {
                    const section = document.getElementById(sectionId);
                    const navItem = document.querySelector(`[data-section="${sectionId}"]`);

                    if (section && section.style.display === 'none') {
                        console.log(`🔧 إصلاح عرض القسم: ${sectionId}`);
                        // لا نعرض القسم إلا إذا كان نشط
                        if (section.classList.contains('active')) {
                            section.style.display = 'block';
                        }
                    }

                    if (navItem && navItem.closest('.nav-item').style.display === 'none') {
                        console.log(`🔧 إصلاح عرض عنصر القائمة: ${sectionId}`);
                        navItem.closest('.nav-item').style.display = 'block';
                    }
                });
            }
        }

        function logout() {
            if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                // مسح متغيرات الحماية
                window.permissionsApplied = false;
                window.currentUserRole = null;

                // مسح جميع الجلسات
                sessionStorage.removeItem('userSession');
                sessionStorage.removeItem('isLoggedIn');
                sessionStorage.removeItem('cashierUser');

                // مسح أي مؤشرات للقراءة فقط
                const readonlyBadges = document.querySelectorAll('.readonly-badge');
                readonlyBadges.forEach(badge => badge.remove());

                // إعادة توجيه للموقع الرئيسي
                window.location.href = 'index.html';
            }
        }
    </script>

    <!-- تحميل ملف JavaScript -->
    <script src="admin-script.js"></script>

    <script>
        // تهيئة النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 بدء تحميل النظام...');

            // انتظار قليل للتأكد من تحميل جميع الملفات
            setTimeout(() => {
                console.log('📂 تحميل الوظائف الأساسية...');

                // تهيئة التنقل
                if (typeof initializeNavigation === 'function') {
                    initializeNavigation();
                    console.log('✅ تم تهيئة نظام التنقل');
                } else {
                    console.warn('⚠️ وظيفة initializeNavigation غير متوفرة');
                }

                // تهيئة القائمة المحمولة
                if (typeof initializeMobileMenu === 'function') {
                    initializeMobileMenu();
                    console.log('✅ تم تهيئة القائمة المحمولة');
                } else {
                    console.warn('⚠️ وظيفة initializeMobileMenu غير متوفرة');
                }

                // تهيئة شريط التنقل المنزلق
                if (typeof initializeNavSlider === 'function') {
                    initializeNavSlider();
                    console.log('✅ تم تهيئة شريط التنقل المنزلق');
                } else {
                    console.warn('⚠️ وظيفة initializeNavSlider غير متوفرة');
                }

                // تحميل بيانات لوحة التحكم
                if (typeof loadDashboardData === 'function') {
                    loadDashboardData();
                    console.log('✅ تم تحميل بيانات لوحة التحكم');
                } else {
                    console.warn('⚠️ وظيفة loadDashboardData غير متوفرة');
                }

                // تحميل البيانات الأساسية
                if (typeof loadCategories === 'function') {
                    loadCategories();
                    console.log('✅ تم تحميل الفئات');
                }

                if (typeof loadProducts === 'function') {
                    loadProducts();
                    console.log('✅ تم تحميل المنتجات');
                }

                if (typeof loadFeaturedProducts === 'function') {
                    loadFeaturedProducts();
                    console.log('✅ تم تحميل المنتجات المميزة');
                }

                if (typeof loadOffers === 'function') {
                    loadOffers();
                    console.log('✅ تم تحميل العروض');
                }

                if (typeof loadCoupons === 'function') {
                    loadCoupons();
                    console.log('✅ تم تحميل الكوبونات');
                }

                // إظهار الواجهة
                showAdminInterface();

                // التأكد من عمل التنقل
                setTimeout(() => {
                    ensureNavigationWorks();
                }, 500);

                console.log('🎉 تم تحميل النظام بنجاح');
            }, 1000);
        });

        // وظيفة إظهار الواجهة
        function showAdminInterface() {
            console.log('🎨 إظهار واجهة لوحة التحكم...');

            // إخفاء شاشة التحميل
            const loadingScreen = document.getElementById('loadingScreen');
            if (loadingScreen) {
                loadingScreen.style.display = 'none';
                console.log('✅ تم إخفاء شاشة التحميل');
            }

            // إزالة كلاس loading من body
            document.body.classList.remove('loading');

            // إظهار الشريط الجانبي
            const sidebar = document.getElementById('sidebar');
            if (sidebar) {
                sidebar.style.display = 'block';
                console.log('✅ تم إظهار الشريط الجانبي');
            }

            // إظهار المحتوى الرئيسي
            const mainContent = document.getElementById('mainContent');
            if (mainContent) {
                mainContent.style.display = 'block';
                console.log('✅ تم إظهار المحتوى الرئيسي');
            }

            // إظهار لوحة المعلومات
            const dashboard = document.getElementById('dashboard');
            if (dashboard) {
                dashboard.style.display = 'block';
                dashboard.classList.add('active');
                console.log('✅ تم إظهار لوحة المعلومات');
            }

            // تفعيل العنصر الأول في القائمة
            const firstNavItem = document.querySelector('.nav-item');
            if (firstNavItem) {
                firstNavItem.classList.add('active');
                console.log('✅ تم تفعيل العنصر الأول في القائمة');
            }

            console.log('🎉 تم إظهار الواجهة بنجاح');
        }
    </script>
</body>
</html>
