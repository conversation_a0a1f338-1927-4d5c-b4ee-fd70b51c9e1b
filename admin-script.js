// Admin Panel JavaScript

// تم حذف وظيفة التحقق من تسجيل الدخول - النظام يعمل مباشرة

// وظيفة تسجيل الخروج - تم تبسيطها
function logout() {
    if (confirm('هل تريد حقاً تسجيل الخروج؟')) {
        sessionStorage.clear();
        localStorage.clear();
        location.reload();
    }
}

// تم حذف التهيئة التلقائية - يتم التحكم من admin.html

// Navigation
function initializeNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    const sections = document.querySelectorAll('.content-section');
    const pageTitle = document.getElementById('pageTitle');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // السماح للروابط الخارجية بالعمل بشكل طبيعي
            if (this.classList.contains('website-link') || this.classList.contains('cashier-link')) {
                console.log('🔗 رابط خارجي - السماح بالتنقل الطبيعي');
                return; // لا نمنع السلوك الافتراضي للروابط الخارجية
            }

            e.preventDefault();

            const targetSection = this.getAttribute('data-section');

            // التحقق من الصلاحيات قبل التنقل
            if (!checkSectionPermission(targetSection)) {
                showNotification('ليس لديك صلاحية للوصول إلى هذا القسم', 'error');
                console.log(`❌ تم منع الوصول للقسم: ${targetSection}`);
                return;
            }

            console.log(`✅ تم السماح بالوصول للقسم: ${targetSection}`);

            // Update active nav item
            const currentActive = document.querySelector('.nav-item.active');
            if (currentActive) {
                currentActive.classList.remove('active');
            }
            this.parentElement.classList.add('active');

            // Hide all sections first
            sections.forEach(section => {
                section.classList.remove('active');
                section.style.display = 'none';
            });

            // Show target section
            const targetElement = document.getElementById(targetSection);
            if (targetElement) {
                targetElement.classList.add('active');
                targetElement.style.display = 'block';

                console.log(`📄 تم التنقل إلى قسم: ${targetSection}`);

                // التأكد من أن القسم مرئي
                setTimeout(() => {
                    if (targetElement.style.display === 'none') {
                        targetElement.style.display = 'block';
                        console.log(`🔄 تم إعادة إظهار القسم: ${targetSection}`);
                    }
                }, 100);
            } else {
                console.error(`❌ لم يتم العثور على القسم: ${targetSection}`);
            }

            // Update page title
            const titles = {
                'dashboard': 'لوحة المعلومات',
                'categories': 'إدارة الفئات',
                'products': 'إدارة المنتجات',
                'featured': 'المنتجات المميزة',
                'offers': 'إدارة العروض',
                'coupons': 'إدارة الكوبونات',
                'orders': 'إدارة الطلبات',
                'contact-settings': 'إعدادات الاتصال',
                'settings': 'إعدادات الموقع',
                'employee-management': 'إدارة الموظفين'
            };

            const pageTitle = document.getElementById('pageTitle');
            if (pageTitle) {
                pageTitle.textContent = titles[targetSection] || 'لوحة التحكم';
            }

            // تحديث البيانات للقسم المحدد
            loadSectionData(targetSection);
        });
    });
}

// تم حذف التحقق من الصلاحيات - السماح بالوصول لجميع الأقسام
function checkSectionPermission(sectionId) {
    return true; // السماح بالوصول لجميع الأقسام
}

// تحميل بيانات القسم المحدد
function loadSectionData(sectionId) {
    console.log(`📊 تحميل بيانات القسم: ${sectionId}`);

    switch(sectionId) {
        case 'dashboard':
            loadDashboardData();
            break;
        case 'products':
            loadProducts();
            break;
        case 'categories':
            loadCategories();
            break;
        case 'featured':
            loadFeaturedProducts();
            break;
        case 'offers':
            loadOffers();
            break;
        case 'coupons':
            loadCoupons();
            break;
        case 'orders':
            // loadOrders(); // إذا كانت الوظيفة موجودة
            break;
        case 'contact-settings':
            // loadContactSettings(); // إذا كانت الوظيفة موجودة
            break;
        case 'settings':
            // loadSettings(); // إذا كانت الوظيفة موجودة
            break;
        case 'employee-management':
            loadEmployeeManagement();
            break;
        default:
            console.log(`⚠️ لا توجد وظيفة تحميل للقسم: ${sectionId}`);
    }
}

// إظهار إشعار
function showNotification(message, type = 'info') {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${getNotificationIcon(type)}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;

    // إضافة الأنماط
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        left: 20px;
        background: ${getNotificationColor(type)};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        z-index: 10000;
        display: flex;
        align-items: center;
        gap: 1rem;
        max-width: 400px;
        animation: slideInLeft 0.3s ease-out;
    `;

    // إضافة الرسوم المتحركة
    if (!document.querySelector('style[data-notification-styles]')) {
        const style = document.createElement('style');
        style.setAttribute('data-notification-styles', 'true');
        style.textContent = `
            @keyframes slideInLeft {
                from { transform: translateX(-100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            .notification-content { display: flex; align-items: center; gap: 0.5rem; flex: 1; }
            .notification-close { background: none; border: none; color: white; cursor: pointer; padding: 0.2rem; }
            .notification-close:hover { opacity: 0.7; }
        `;
        document.head.appendChild(style);
    }

    // إضافة للصفحة
    document.body.appendChild(notification);

    // إزالة تلقائية بعد 5 ثوان
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

function getNotificationIcon(type) {
    const icons = {
        'success': 'check-circle',
        'error': 'exclamation-circle',
        'warning': 'exclamation-triangle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}

function getNotificationColor(type) {
    const colors = {
        'success': 'linear-gradient(135deg, #28a745, #20c997)',
        'error': 'linear-gradient(135deg, #dc3545, #c82333)',
        'warning': 'linear-gradient(135deg, #ffc107, #e0a800)',
        'info': 'linear-gradient(135deg, #17a2b8, #138496)'
    };
    return colors[type] || colors.info;
}

// وظيفة showSection للتوافق مع الكود الموجود
function showSection(sectionId) {
    console.log(`🔄 طلب عرض القسم: ${sectionId}`);

    // التحقق من الصلاحيات
    if (!checkSectionPermission(sectionId)) {
        showNotification('ليس لديك صلاحية للوصول إلى هذا القسم', 'error');
        return;
    }

    // إخفاء جميع الأقسام
    const sections = document.querySelectorAll('.content-section');
    sections.forEach(section => {
        section.classList.remove('active');
        section.style.display = 'none';
    });

    // إظهار القسم المطلوب
    const targetSection = document.getElementById(sectionId);
    if (targetSection) {
        targetSection.classList.add('active');
        targetSection.style.display = 'block';

        // تحديث القائمة الجانبية
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => item.classList.remove('active'));

        const targetNavItem = document.querySelector(`[data-section="${sectionId}"]`);
        if (targetNavItem) {
            targetNavItem.closest('.nav-item').classList.add('active');
        }

        // تحديث عنوان الصفحة
        const titles = {
            'dashboard': 'لوحة المعلومات',
            'categories': 'إدارة الفئات',
            'products': 'إدارة المنتجات',
            'featured': 'المنتجات المميزة',
            'offers': 'إدارة العروض',
            'coupons': 'إدارة الكوبونات',
            'orders': 'إدارة الطلبات',
            'contact-settings': 'إعدادات الاتصال',
            'settings': 'إعدادات الموقع',
            'employee-management': 'إدارة الموظفين'
        };

        const pageTitle = document.getElementById('pageTitle');
        if (pageTitle) {
            pageTitle.textContent = titles[sectionId] || 'لوحة التحكم';
        }

        // تحميل بيانات القسم
        loadSectionData(sectionId);

        console.log(`✅ تم عرض القسم: ${sectionId}`);
    } else {
        console.error(`❌ لم يتم العثور على القسم: ${sectionId}`);
        showNotification(`لم يتم العثور على القسم: ${sectionId}`, 'error');
    }
}

// Mobile menu
function initializeMobileMenu() {
    const mobileMenuBtn = document.getElementById('mobileMenuBtn');
    const sidebar = document.getElementById('sidebar');

    if (mobileMenuBtn && sidebar) {
        mobileMenuBtn.addEventListener('click', function() {
            sidebar.classList.toggle('active');
        });

        // Close sidebar when clicking outside
        document.addEventListener('click', function(e) {
            if (!sidebar.contains(e.target) && !mobileMenuBtn.contains(e.target)) {
                sidebar.classList.remove('active');
            }
        });
    }
}

// Navigation Slider
function initializeNavSlider() {
    const container = document.getElementById('navItemsContainer');
    if (!container) return;

    // Set initial height
    setTimeout(() => {
        updateScrollButtons();
    }, 100);

    // Update scroll buttons on resize
    window.addEventListener('resize', () => {
        setTimeout(updateScrollButtons, 100);
    });

    // Ensure all nav items are visible
    const navItems = container.querySelectorAll('.nav-item');
    if (navItems.length > 0) {
        // Calculate required height
        const itemHeight = 50; // approximate height per item
        const totalHeight = navItems.length * itemHeight;
        const availableHeight = window.innerHeight - 200; // minus header and footer

        if (totalHeight > availableHeight) {
            container.style.maxHeight = availableHeight + 'px';
        }
    }
}

function scrollNavUp() {
    const container = document.getElementById('navItemsContainer');
    if (container) {
        const scrollAmount = Math.min(80, container.clientHeight / 3);
        container.scrollBy({
            top: -scrollAmount,
            behavior: 'smooth'
        });

        // Update button states after scroll
        setTimeout(() => {
            updateScrollButtons();
        }, 300);
    }
}

function scrollNavDown() {
    const container = document.getElementById('navItemsContainer');
    if (container) {
        const scrollAmount = Math.min(80, container.clientHeight / 3);
        container.scrollBy({
            top: scrollAmount,
            behavior: 'smooth'
        });

        // Update button states after scroll
        setTimeout(() => {
            updateScrollButtons();
        }, 300);
    }
}

function updateScrollButtons() {
    const container = document.getElementById('navItemsContainer');
    const scrollUpBtn = document.querySelector('.nav-scroll-up');
    const scrollDownBtn = document.querySelector('.nav-scroll-down');

    if (!container || !scrollUpBtn || !scrollDownBtn) return;

    // Always show scroll buttons for better UX
    scrollUpBtn.style.display = 'block';
    scrollDownBtn.style.display = 'block';

    // Function to update button states
    function updateButtonStates() {
        const isAtTop = container.scrollTop <= 5;
        const isAtBottom = container.scrollTop >= container.scrollHeight - container.clientHeight - 5;

        scrollUpBtn.style.opacity = isAtTop ? '0.3' : '1';
        scrollDownBtn.style.opacity = isAtBottom ? '0.3' : '1';
        scrollUpBtn.style.pointerEvents = isAtTop ? 'none' : 'auto';
        scrollDownBtn.style.pointerEvents = isAtBottom ? 'none' : 'auto';
    }

    // Initial state
    updateButtonStates();

    // Update on scroll
    container.addEventListener('scroll', updateButtonStates);
}

// Load dashboard data
function loadDashboardData() {
    const products = JSON.parse(localStorage.getItem('adminProducts')) || getDefaultProducts();
    const coupons = JSON.parse(localStorage.getItem('adminCoupons')) || getDefaultCoupons();
    const offers = JSON.parse(localStorage.getItem('adminOffers')) || [];
    const featured = JSON.parse(localStorage.getItem('featuredProducts')) || [];

    document.getElementById('totalProducts').textContent = products.length;
    document.getElementById('totalFeatured').textContent = featured.length;
    document.getElementById('totalOffers').textContent = offers.filter(o => o.status === 'active' && new Date(o.endDate) >= new Date()).length;
    document.getElementById('totalCoupons').textContent = coupons.filter(c => c.status === 'active').length;
}

// Default products data
function getDefaultProducts() {
    const now = new Date();
    return [
        {
            id: 'prod_default_1',
            name: 'تفاح أحمر طازج',
            price: 15,
            stock: 25,
            minStock: 5,
            image: 'https://images.unsplash.com/photo-1560806887-1e4cd0b6cbd6?w=300&h=200&fit=crop',
            category: 'منتجات غذائية',
            description: 'تفاح أحمر طازج ولذيذ مستورد من أفضل المزارع',
            createdAt: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
            id: 'prod_default_2',
            name: 'دجاج طازج',
            price: 25,
            stock: 15,
            minStock: 3,
            image: 'https://images.unsplash.com/photo-1604503468506-a8da13d82791?w=300&h=200&fit=crop',
            category: 'منتجات غذائية',
            description: 'دجاج طازج عالي الجودة من مزارع محلية',
            createdAt: new Date(now.getTime() - 6 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date(now.getTime() - 6 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
            id: 'prod_default_3',
            name: 'أرز بسمتي فاخر',
            price: 35,
            stock: 30,
            minStock: 10,
            image: 'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=300&h=200&fit=crop',
            category: 'منتجات غذائية',
            description: 'أرز بسمتي فاخر عالي الجودة مستورد من الهند',
            createdAt: new Date(now.getTime() - 5 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date(now.getTime() - 5 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
            id: 'prod_default_4',
            name: 'منظف غسيل فعال',
            price: 18,
            stock: 12,
            minStock: 5,
            image: 'https://images.unsplash.com/photo-1563453392212-326f5e854473?w=300&h=200&fit=crop',
            category: 'منتجات تنظيف',
            description: 'منظف غسيل فعال وقوي لجميع أنواع الأقمشة',
            createdAt: new Date(now.getTime() - 4 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date(now.getTime() - 4 * 24 * 60 * 60 * 1000).toISOString()
        },

        {
            id: 'prod_default_5',
            name: 'شامبو للشعر',
            price: 22,
            stock: 15,
            minStock: 5,
            image: 'https://images.unsplash.com/photo-1571781926291-c477ebfd024b?w=300&h=200&fit=crop',
            category: 'العناية الشخصية',
            description: 'شامبو طبيعي للعناية بالشعر لجميع أنواع الشعر',
            createdAt: new Date(now.getTime() - 1 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date(now.getTime() - 1 * 24 * 60 * 60 * 1000).toISOString()
        }
    ];
}

// Default coupons data
function getDefaultCoupons() {
    return [
        {
            id: 'WELCOME10',
            code: 'WELCOME10',
            type: 'percentage',
            value: 10,
            description: 'خصم ترحيبي 10%',
            minAmount: 0,
            maxUses: 100,
            currentUses: 0,
            expiryDate: '2024-12-31',
            status: 'active'
        },
        {
            id: 'SAVE20',
            code: 'SAVE20',
            type: 'fixed',
            value: 20,
            description: 'خصم 20000 دينار',
            minAmount: 50,
            maxUses: 50,
            currentUses: 0,
            expiryDate: '2024-12-31',
            status: 'active'
        },
        {
            id: 'FIRST50',
            code: 'FIRST50',
            type: 'percentage',
            value: 50,
            description: 'خصم 50% للعملاء الجدد',
            minAmount: 100,
            maxUses: 20,
            currentUses: 0,
            expiryDate: '2024-12-31',
            status: 'active'
        }
    ];
}

// Load products
function loadProducts() {
    // Use enhanced loading with image support
    loadProductsWithImages();
}

function loadProductsLegacy() {
    const products = loadFromStorage('adminProducts', getDefaultProducts());
    const productsGrid = document.getElementById('productsGrid');

    if (products.length === 0) {
        productsGrid.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-box"></i>
                <p>لا توجد منتجات حتى الآن</p>
            </div>
        `;
        return;
    }

    // Sort products by creation date (newest first)
    const sortedProducts = products.sort((a, b) => {
        const dateA = new Date(a.createdAt || 0);
        const dateB = new Date(b.createdAt || 0);
        return dateB - dateA;
    });

    productsGrid.innerHTML = sortedProducts.map(product => {
        const stockStatus = getStockStatus(product);
        return `
            <div class="product-card ${stockStatus.class}">
                <div class="product-image">
                    ${product.image ?
                        `<img src="${product.image}" alt="${product.name}" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                         <div class="placeholder" style="display:none;"><i class="fas fa-image"></i></div>` :
                        `<div class="placeholder"><i class="fas fa-image"></i></div>`
                    }
                    ${stockStatus.badge ? `<div class="stock-badge ${stockStatus.badgeClass}">${stockStatus.badge}</div>` : ''}
                </div>
                <div class="product-info">
                    <div class="product-name">${product.name}</div>
                    <div class="product-price">${product.price} دينار</div>
                    <div class="product-category">${product.category}</div>
                    <div class="product-stock">
                        <span class="stock-label">المخزون:</span>
                        <span class="stock-value ${stockStatus.class}">${product.stock || 0} قطعة</span>
                        ${stockStatus.warning ? `<div class="stock-warning">${stockStatus.warning}</div>` : ''}
                    </div>
                    <div class="product-actions">
                        <button class="btn-edit" onclick="editProduct('${product.id}')">
                            <i class="fas fa-edit"></i> تعديل
                        </button>
                        <button class="btn-delete" onclick="deleteProduct('${product.id}')">
                            <i class="fas fa-trash"></i> حذف
                        </button>
                    </div>
                </div>
            </div>
        `;
    }).join('');
    
    // Save to localStorage if not exists
    if (!localStorage.getItem('adminProducts')) {
        localStorage.setItem('adminProducts', JSON.stringify(products));
    }
}

// Load featured products
function loadFeaturedProducts() {
    const featured = JSON.parse(localStorage.getItem('featuredProducts')) || [];
    const featuredGrid = document.getElementById('featuredGrid');

    if (!featuredGrid) return;

    if (featured.length === 0) {
        featuredGrid.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-star"></i>
                <p>لا توجد منتجات مميزة حتى الآن</p>
                <small>يمكنك إضافة حتى 8 منتجات مميزة</small>
            </div>
        `;
        return;
    }

    featuredGrid.innerHTML = featured.map(product => {
        const stockStatus = getStockStatus(product);
        return `
            <div class="featured-card ${stockStatus.class}">
                <div class="featured-badge">مميز</div>
                <div class="featured-image">
                    ${product.image ?
                        `<img src="${product.image}" alt="${product.name}" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                         <div class="placeholder" style="display:none;"><i class="fas fa-image"></i></div>` :
                        `<div class="placeholder"><i class="fas fa-image"></i></div>`
                    }
                    ${stockStatus.badge ? `<div class="stock-badge ${stockStatus.badgeClass}">${stockStatus.badge}</div>` : ''}
                </div>
                <div class="featured-info-card">
                    <div class="featured-name">${product.name}</div>
                    <div class="featured-price">${product.price} دينار</div>
                    <div class="featured-category">${product.category}</div>
                    <div class="product-stock">
                        <span class="stock-label">المخزون:</span>
                        <span class="stock-value ${stockStatus.class}">${product.stock || 0} قطعة</span>
                        ${stockStatus.warning ? `<div class="stock-warning">${stockStatus.warning}</div>` : ''}
                    </div>
                    <div class="featured-actions">
                        <button class="btn-remove-featured" onclick="removeFeaturedProduct('${product.id}')">
                            <i class="fas fa-times"></i> إزالة من المميزة
                        </button>
                    </div>
                </div>
            </div>
        `;
    }).join('');
}

// Featured Products Management Functions
function showAddFeaturedModal() {
    const products = JSON.parse(localStorage.getItem('adminProducts')) || [];
    const featured = JSON.parse(localStorage.getItem('featuredProducts')) || [];

    if (products.length === 0) {
        alert('يجب إضافة منتجات أولاً قبل تمييزها');
        return;
    }

    if (featured.length >= 8) {
        alert('يمكنك إضافة حتى 8 منتجات مميزة فقط');
        return;
    }

    // Filter out already featured products
    const availableProducts = products.filter(product =>
        !featured.find(f => f.id === product.id)
    );

    if (availableProducts.length === 0) {
        alert('جميع المنتجات مميزة بالفعل');
        return;
    }

    const modal = createModal('إضافة منتج مميز', `
        <form id="addFeaturedForm">
            <div class="form-group">
                <label>اختر المنتج</label>
                <select id="featuredProductSelect" required onchange="updateFeaturedPreview()">
                    <option value="">اختر المنتج</option>
                    ${availableProducts.map(product => `
                        <option value="${product.id}"
                                data-name="${product.name}"
                                data-price="${product.price}"
                                data-image="${product.image}"
                                data-category="${product.category}"
                                data-stock="${product.stock || 0}">
                            ${product.name} - ${product.price} دينار
                        </option>
                    `).join('')}
                </select>
            </div>
            <div id="featuredPreview" class="featured-preview" style="display: none;">
                <h4>معاينة المنتج المميز:</h4>
                <div class="preview-card">
                    <div class="preview-image">
                        <img id="previewImage" src="" alt="">
                    </div>
                    <div class="preview-info">
                        <div class="preview-name" id="previewName"></div>
                        <div class="preview-price" id="previewPrice"></div>
                        <div class="preview-category" id="previewCategory"></div>
                        <div class="preview-stock" id="previewStock"></div>
                    </div>
                </div>
            </div>
        </form>
    `, [
        { text: 'إلغاء', class: 'btn-secondary', onclick: 'closeModal()' },
        { text: 'إضافة للمميزة', class: 'btn-primary', onclick: 'addFeaturedProduct()' }
    ]);

    showModal(modal);
}

function updateFeaturedPreview() {
    const select = document.getElementById('featuredProductSelect');
    const preview = document.getElementById('featuredPreview');
    const selectedOption = select.options[select.selectedIndex];

    if (selectedOption.value) {
        const name = selectedOption.getAttribute('data-name');
        const price = selectedOption.getAttribute('data-price');
        const image = selectedOption.getAttribute('data-image');
        const category = selectedOption.getAttribute('data-category');
        const stock = selectedOption.getAttribute('data-stock');

        document.getElementById('previewImage').src = image;
        document.getElementById('previewName').textContent = name;
        document.getElementById('previewPrice').textContent = `${price} دينار`;
        document.getElementById('previewCategory').textContent = category;
        document.getElementById('previewStock').textContent = `المخزون: ${stock} قطعة`;

        preview.style.display = 'block';
    } else {
        preview.style.display = 'none';
    }
}

function addFeaturedProduct() {
    const select = document.getElementById('featuredProductSelect');
    const productId = select.value;

    if (!productId) {
        alert('يرجى اختيار منتج');
        return;
    }

    const products = JSON.parse(localStorage.getItem('adminProducts')) || [];
    const product = products.find(p => p.id === productId);

    if (!product) {
        alert('المنتج غير موجود');
        return;
    }

    const featured = JSON.parse(localStorage.getItem('featuredProducts')) || [];

    // Check if already featured
    if (featured.find(f => f.id === productId)) {
        alert('هذا المنتج مميز بالفعل');
        return;
    }

    // Check limit
    if (featured.length >= 8) {
        alert('يمكنك إضافة حتى 8 منتجات مميزة فقط');
        return;
    }

    // Add to featured
    featured.push({
        ...product,
        featuredAt: new Date().toISOString()
    });

    localStorage.setItem('featuredProducts', JSON.stringify(featured));

    // Update main site
    updateMainSiteFeatured();

    loadFeaturedProducts();
    loadDashboardData();
    closeModal();

    alert('تم إضافة المنتج للمميزة بنجاح!');
}

function removeFeaturedProduct(productId) {
    if (!confirm('هل أنت متأكد من إزالة هذا المنتج من المميزة؟')) return;

    const featured = JSON.parse(localStorage.getItem('featuredProducts')) || [];
    const filteredFeatured = featured.filter(p => p.id !== productId);

    localStorage.setItem('featuredProducts', JSON.stringify(filteredFeatured));

    // Update main site
    updateMainSiteFeatured();

    loadFeaturedProducts();
    loadDashboardData();

    alert('تم إزالة المنتج من المميزة بنجاح!');
}

function updateMainSiteFeatured() {
    const featured = JSON.parse(localStorage.getItem('featuredProducts')) || [];
    localStorage.setItem('mainSiteFeatured', JSON.stringify(featured));
}

// Contact Settings Management
function loadContactSettings() {
    const settings = JSON.parse(localStorage.getItem('contactSettings')) || getDefaultContactSettings();

    // Load values into form
    document.getElementById('storeNameAr').value = settings.storeNameAr || '';
    document.getElementById('storeNameEn').value = settings.storeNameEn || '';
    document.getElementById('storeDescAr').value = settings.storeDescAr || '';
    document.getElementById('storeDescEn').value = settings.storeDescEn || '';
    document.getElementById('addressAr').value = settings.addressAr || '';
    document.getElementById('addressEn').value = settings.addressEn || '';
    document.getElementById('phone1').value = settings.phone1 || '';
    document.getElementById('phone2').value = settings.phone2 || '';
    document.getElementById('email1').value = settings.email1 || '';
    document.getElementById('email2').value = settings.email2 || '';
    document.getElementById('workingHoursAr').value = settings.workingHoursAr || '';
    document.getElementById('workingHoursEn').value = settings.workingHoursEn || '';
    document.getElementById('facebookUrl').value = settings.facebookUrl || '';
    document.getElementById('twitterUrl').value = settings.twitterUrl || '';
    document.getElementById('instagramUrl').value = settings.instagramUrl || '';
    document.getElementById('whatsappNumber').value = settings.whatsappNumber || '';
    document.getElementById('logoUrl').value = settings.logoUrl || '';

    // Load logo
    loadLogoPreview(settings.logoUrl);
}

function getDefaultContactSettings() {
    return {
        storeNameAr: 'برومت هايبر ماركت',
        storeNameEn: 'Bromet Hypermarket',
        storeDescAr: 'متجرك المفضل لجميع احتياجاتك اليومية بأفضل الأسعار وأعلى جودة',
        storeDescEn: 'Your favorite store for all your daily needs at the best prices and highest quality',
        addressAr: 'الرياض، المملكة العربية السعودية',
        addressEn: 'Riyadh, Saudi Arabia',
        phone1: '+966 11 123 4567',
        phone2: '+966 11 123 4568',
        email1: '<EMAIL>',
        email2: '<EMAIL>',
        workingHoursAr: 'السبت - الخميس: 8:00 ص - 12:00 م\nالجمعة: 2:00 م - 12:00 م',
        workingHoursEn: 'Saturday - Thursday: 8:00 AM - 12:00 PM\nFriday: 2:00 PM - 12:00 PM',
        facebookUrl: 'https://facebook.com/brometmarket',
        twitterUrl: 'https://twitter.com/brometmarket',
        instagramUrl: 'https://instagram.com/brometmarket',
        whatsappNumber: '966111234567',
        logoUrl: '' // Empty means use default icon
    };
}

function saveContactSettings() {
    const settings = {
        storeNameAr: document.getElementById('storeNameAr').value,
        storeNameEn: document.getElementById('storeNameEn').value,
        storeDescAr: document.getElementById('storeDescAr').value,
        storeDescEn: document.getElementById('storeDescEn').value,
        addressAr: document.getElementById('addressAr').value,
        addressEn: document.getElementById('addressEn').value,
        phone1: document.getElementById('phone1').value,
        phone2: document.getElementById('phone2').value,
        email1: document.getElementById('email1').value,
        email2: document.getElementById('email2').value,
        workingHoursAr: document.getElementById('workingHoursAr').value,
        workingHoursEn: document.getElementById('workingHoursEn').value,
        facebookUrl: document.getElementById('facebookUrl').value,
        twitterUrl: document.getElementById('twitterUrl').value,
        instagramUrl: document.getElementById('instagramUrl').value,
        whatsappNumber: document.getElementById('whatsappNumber').value,
        logoUrl: document.getElementById('logoUrl').value
    };

    // Save to localStorage
    localStorage.setItem('contactSettings', JSON.stringify(settings));

    // Update main site
    updateMainSiteContact();

    alert('تم حفظ إعدادات الاتصال والشعار بنجاح!');
}

function resetContactSettings() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع إعدادات الاتصال؟')) {
        localStorage.removeItem('contactSettings');
        loadContactSettings();
        alert('تم إعادة تعيين الإعدادات بنجاح!');
    }
}

function updateMainSiteContact() {
    const settings = JSON.parse(localStorage.getItem('contactSettings')) || getDefaultContactSettings();
    localStorage.setItem('mainSiteContact', JSON.stringify(settings));

    console.log('Updated main site contact settings:', settings); // Debug log

    // Force update logo in main site if it's open
    try {
        if (window.opener && !window.opener.closed) {
            window.opener.postMessage({
                type: 'updateContactSettings',
                settings: settings
            }, '*');
        }
    } catch (e) {
        console.log('Could not communicate with main site:', e);
    }
}

// Logo Management Functions
function handleLogoUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    // Check file size (max 2MB)
    if (file.size > 2 * 1024 * 1024) {
        alert('حجم الملف كبير جداً. يرجى اختيار ملف أصغر من 2MB');
        return;
    }

    // Check file type
    if (!file.type.startsWith('image/')) {
        alert('يرجى اختيار ملف صورة صحيح (PNG, JPG, GIF)');
        return;
    }

    const reader = new FileReader();
    reader.onload = function(e) {
        const logoUrl = e.target.result;
        document.getElementById('logoUrl').value = logoUrl;
        loadLogoPreview(logoUrl);
    };
    reader.readAsDataURL(file);
}

function handleLogoUrl() {
    const logoUrl = document.getElementById('logoUrl').value;
    loadLogoPreview(logoUrl);
}

function loadLogoPreview(logoUrl) {
    const logoImage = document.getElementById('logoImage');
    const defaultIcon = document.getElementById('defaultIcon');

    if (logoUrl && logoUrl.trim()) {
        logoImage.src = logoUrl;
        logoImage.style.display = 'block';
        defaultIcon.style.display = 'none';

        // Handle image load error
        logoImage.onerror = function() {
            logoImage.style.display = 'none';
            defaultIcon.style.display = 'block';
            alert('فشل في تحميل الصورة. يرجى التحقق من الرابط.');
        };
    } else {
        logoImage.style.display = 'none';
        defaultIcon.style.display = 'block';
    }
}

function resetLogo() {
    if (confirm('هل أنت متأكد من إعادة تعيين الشعار للأيقونة الافتراضية؟')) {
        document.getElementById('logoUrl').value = '';
        document.getElementById('logoUpload').value = '';
        loadLogoPreview('');
    }
}

function previewLogo() {
    const logoUrl = document.getElementById('logoUrl').value;
    if (logoUrl && logoUrl.trim()) {
        window.open(logoUrl, '_blank');
    } else {
        alert('لا يوجد شعار للمعاينة');
    }
}

function testLogoUpdate() {
    const logoUrl = document.getElementById('logoUrl').value;
    const settings = JSON.parse(localStorage.getItem('contactSettings')) || getDefaultContactSettings();

    console.log('Testing logo update with URL:', logoUrl);
    console.log('Current settings:', settings);

    // Update settings with current logo
    settings.logoUrl = logoUrl;
    localStorage.setItem('contactSettings', JSON.stringify(settings));
    localStorage.setItem('mainSiteContact', JSON.stringify(settings));

    // Try to update main site
    try {
        if (window.opener && !window.opener.closed) {
            window.opener.postMessage({
                type: 'updateContactSettings',
                settings: settings
            }, '*');
            alert('تم إرسال تحديث الشعار للموقع الرئيسي');
        } else {
            alert('الموقع الرئيسي غير مفتوح. يرجى فتح الموقع الرئيسي أولاً');
        }
    } catch (e) {
        console.error('Error updating main site:', e);
        alert('حدث خطأ في تحديث الموقع الرئيسي');
    }
}

// Categories Management
function loadCategories() {
    const categories = loadFromStorage('categories', getDefaultCategories());
    const categoriesGrid = document.getElementById('categoriesGrid');

    if (!categoriesGrid) return;

    if (categories.length === 0) {
        categoriesGrid.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-th-large"></i>
                <p>لا توجد فئات حتى الآن</p>
                <small>ابدأ بإضافة فئة جديدة</small>
            </div>
        `;
        return;
    }

    // Sort categories by creation date (newest first)
    const sortedCategories = categories.sort((a, b) => {
        const dateA = new Date(a.createdAt || 0);
        const dateB = new Date(b.createdAt || 0);
        return dateB - dateA;
    });

    categoriesGrid.innerHTML = sortedCategories.map(category => {
        const products = JSON.parse(localStorage.getItem('adminProducts')) || [];
        const categoryProducts = products.filter(p => p.category === category.nameAr);

        return `
            <div class="category-card">
                <div class="category-image">
                    ${category.image ?
                        `<img src="${category.image}" alt="${category.nameAr}" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                         <div class="placeholder" style="display:none;"><i class="fas fa-th-large"></i></div>` :
                        `<div class="placeholder"><i class="fas fa-th-large"></i></div>`
                    }
                </div>
                <div class="category-info">
                    <div class="category-name">${category.nameAr}</div>
                    <div class="category-description">${category.descriptionAr}</div>
                    <div class="category-stats">
                        <div class="category-stat">
                            <div class="number">${categoryProducts.length}</div>
                            <div class="label">منتج</div>
                        </div>
                        <div class="category-stat">
                            <div class="number">${categoryProducts.filter(p => (p.stock || 0) > 0).length}</div>
                            <div class="label">متوفر</div>
                        </div>
                    </div>
                    <div class="category-actions">
                        <button class="btn-view-products" onclick="viewCategoryProducts('${category.id}')">
                            <i class="fas fa-eye"></i> المنتجات
                        </button>
                        <button class="btn-edit-category" onclick="editCategory('${category.id}')">
                            <i class="fas fa-edit"></i> تعديل
                        </button>
                        <button class="btn-delete-category" onclick="deleteCategory('${category.id}')">
                            <i class="fas fa-trash"></i> حذف
                        </button>
                    </div>
                </div>
            </div>
        `;
    }).join('');
}

function getDefaultCategories() {
    const now = new Date();
    return [
        {
            id: 'cat_default_1',
            nameAr: 'منتجات غذائية',
            nameEn: 'Food Products',
            descriptionAr: 'جميع أنواع المواد الغذائية والمشروبات الطازجة',
            descriptionEn: 'All types of fresh food products and beverages',
            image: 'https://images.unsplash.com/photo-1542838132-92c53300491e?w=300&h=200&fit=crop',
            createdAt: new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
            id: 'cat_default_2',
            nameAr: 'منتجات تنظيف',
            nameEn: 'Cleaning Products',
            descriptionAr: 'مواد التنظيف ومستلزمات المنزل عالية الجودة',
            descriptionEn: 'High-quality cleaning supplies and household items',
            image: 'https://images.unsplash.com/photo-1563453392212-326f5e854473?w=300&h=200&fit=crop',
            createdAt: new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
            id: 'cat_default_3',
            nameAr: 'العناية الشخصية',
            nameEn: 'Personal Care',
            descriptionAr: 'منتجات العناية الشخصية والصحة والجمال',
            descriptionEn: 'Personal care, health and beauty products',
            image: 'https://images.unsplash.com/photo-1571781926291-c477ebfd024b?w=300&h=200&fit=crop',
            createdAt: new Date(now.getTime() - 1 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date(now.getTime() - 1 * 24 * 60 * 60 * 1000).toISOString()
        }
    ];
}

function showAddCategoryModal() {
    const modal = createModal('إضافة فئة جديدة', `
        <form id="addCategoryForm">
            <div class="form-group">
                <label>اسم الفئة (عربي)</label>
                <input type="text" id="categoryNameAr" placeholder="مثال: منتجات غذائية" required>
            </div>
            <div class="form-group">
                <label>اسم الفئة (إنجليزي)</label>
                <input type="text" id="categoryNameEn" placeholder="Example: Food Products" required>
            </div>
            <div class="form-group">
                <label>وصف الفئة (عربي)</label>
                <textarea id="categoryDescAr" rows="3" placeholder="وصف مختصر للفئة باللغة العربية"></textarea>
            </div>
            <div class="form-group">
                <label>وصف الفئة (إنجليزي)</label>
                <textarea id="categoryDescEn" rows="3" placeholder="Brief description in English"></textarea>
            </div>
            <div class="form-group">
                <label>صورة الفئة</label>
                <input type="file" id="categoryImageUpload" accept="image/*" onchange="handleCategoryImageUpload(event)">
                <small class="form-help">أو أدخل رابط الصورة:</small>
                <input type="url" id="categoryImageUrl" placeholder="https://example.com/image.jpg" style="margin-top: 0.5rem;">
            </div>
        </form>
    `, [
        { text: 'إلغاء', class: 'btn-secondary', onclick: 'closeModal()' },
        { text: 'إضافة الفئة', class: 'btn-primary', onclick: 'addCategory()' }
    ]);

    showModal(modal);
}

function handleCategoryImageUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    console.log('Processing category image:', file.name, file.size); // Debug log

    if (file.size > 2 * 1024 * 1024) {
        alert('حجم الملف كبير جداً. يرجى اختيار ملف أصغر من 2MB');
        return;
    }

    if (!file.type.startsWith('image/')) {
        alert('يرجى اختيار ملف صورة صحيح (PNG, JPG, GIF)');
        return;
    }

    const reader = new FileReader();
    reader.onload = function(e) {
        const imageData = e.target.result;
        console.log('Category image converted to base64, length:', imageData.length); // Debug log
        document.getElementById('categoryImageUrl').value = imageData;
    };
    reader.onerror = function(e) {
        console.error('Error reading category image file:', e);
        alert('حدث خطأ في قراءة الملف. يرجى المحاولة مرة أخرى.');
    };
    reader.readAsDataURL(file);
}

function addCategory() {
    const nameAr = document.getElementById('categoryNameAr').value.trim();
    const nameEn = document.getElementById('categoryNameEn').value.trim();
    const descAr = document.getElementById('categoryDescAr').value.trim();
    const descEn = document.getElementById('categoryDescEn').value.trim();
    const imageUrl = document.getElementById('categoryImageUrl').value.trim();

    console.log('Adding category:', { nameAr, nameEn, descAr, descEn, imageUrl }); // Debug log

    if (!nameAr || !nameEn) {
        alert('يرجى إدخال اسم الفئة باللغتين العربية والإنجليزية');
        return;
    }

    try {
        const categories = JSON.parse(localStorage.getItem('categories')) || [];

        // Check if category already exists
        const existingCategory = categories.find(cat =>
            cat.nameAr.toLowerCase() === nameAr.toLowerCase() ||
            cat.nameEn.toLowerCase() === nameEn.toLowerCase()
        );

        if (existingCategory) {
            alert('فئة بهذا الاسم موجودة بالفعل');
            return;
        }

        const newCategory = {
            id: 'cat_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
            nameAr: nameAr,
            nameEn: nameEn,
            descriptionAr: descAr || '',
            descriptionEn: descEn || '',
            image: imageUrl || '',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        if (saveCategoryWithStatus(newCategory)) {
            // Update main site categories
            updateMainSiteCategories();

            // Refresh displays
            loadCategories();
            closeModal();

            alert('تم إضافة الفئة بنجاح!');

            // Notify main site
            notifyMainSite('CATEGORIES_UPDATED');
        }

    } catch (error) {
        console.error('Error adding category:', error);
        alert('حدث خطأ في إضافة الفئة. يرجى المحاولة مرة أخرى.');
    }
}

function editCategory(categoryId) {
    const categories = JSON.parse(localStorage.getItem('categories')) || [];
    const category = categories.find(c => c.id === categoryId);

    if (!category) return;

    const modal = createModal('تعديل الفئة', `
        <form id="editCategoryForm">
            <div class="form-group">
                <label>اسم الفئة (عربي)</label>
                <input type="text" id="editCategoryNameAr" value="${category.nameAr}" required>
            </div>
            <div class="form-group">
                <label>اسم الفئة (إنجليزي)</label>
                <input type="text" id="editCategoryNameEn" value="${category.nameEn}" required>
            </div>
            <div class="form-group">
                <label>وصف الفئة (عربي)</label>
                <textarea id="editCategoryDescAr" rows="3">${category.descriptionAr || ''}</textarea>
            </div>
            <div class="form-group">
                <label>وصف الفئة (إنجليزي)</label>
                <textarea id="editCategoryDescEn" rows="3">${category.descriptionEn || ''}</textarea>
            </div>
            <div class="form-group">
                <label>صورة الفئة</label>
                <input type="file" id="editCategoryImageUpload" accept="image/*" onchange="handleEditCategoryImageUpload(event)">
                <small class="form-help">أو أدخل رابط الصورة:</small>
                <input type="url" id="editCategoryImageUrl" value="${category.image || ''}" style="margin-top: 0.5rem;">
            </div>
        </form>
    `, [
        { text: 'إلغاء', class: 'btn-secondary', onclick: 'closeModal()' },
        { text: 'حفظ التغييرات', class: 'btn-primary', onclick: `updateCategory('${categoryId}')` }
    ]);

    showModal(modal);
}

function handleEditCategoryImageUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    console.log('Processing edit category image:', file.name, file.size); // Debug log

    if (file.size > 2 * 1024 * 1024) {
        alert('حجم الملف كبير جداً. يرجى اختيار ملف أصغر من 2MB');
        return;
    }

    if (!file.type.startsWith('image/')) {
        alert('يرجى اختيار ملف صورة صحيح (PNG, JPG, GIF)');
        return;
    }

    const reader = new FileReader();
    reader.onload = function(e) {
        const imageData = e.target.result;
        console.log('Edit category image converted to base64, length:', imageData.length); // Debug log
        document.getElementById('editCategoryImageUrl').value = imageData;
    };
    reader.onerror = function(e) {
        console.error('Error reading edit category image file:', e);
        alert('حدث خطأ في قراءة الملف. يرجى المحاولة مرة أخرى.');
    };
    reader.readAsDataURL(file);
}

function updateCategory(categoryId) {
    const nameAr = document.getElementById('editCategoryNameAr').value.trim();
    const nameEn = document.getElementById('editCategoryNameEn').value.trim();
    const descAr = document.getElementById('editCategoryDescAr').value.trim();
    const descEn = document.getElementById('editCategoryDescEn').value.trim();
    const imageUrl = document.getElementById('editCategoryImageUrl').value.trim();

    console.log('Updating category:', { categoryId, nameAr, nameEn, descAr, descEn, imageUrl }); // Debug log

    if (!nameAr || !nameEn) {
        alert('يرجى إدخال اسم الفئة باللغتين العربية والإنجليزية');
        return;
    }

    const categories = JSON.parse(localStorage.getItem('categories')) || [];
    const categoryIndex = categories.findIndex(c => c.id === categoryId);

    if (categoryIndex !== -1) {
        const oldNameAr = categories[categoryIndex].nameAr;

        // Check if new name already exists (excluding current category)
        const existingCategory = categories.find((cat, index) =>
            index !== categoryIndex && (
                cat.nameAr.toLowerCase() === nameAr.toLowerCase() ||
                cat.nameEn.toLowerCase() === nameEn.toLowerCase()
            )
        );

        if (existingCategory) {
            alert('فئة بهذا الاسم موجودة بالفعل');
            return;
        }

        categories[categoryIndex] = {
            ...categories[categoryIndex],
            nameAr,
            nameEn,
            descriptionAr: descAr,
            descriptionEn: descEn,
            image: imageUrl,
            updatedAt: new Date().toISOString()
        };

        if (saveToStorage('categories', categories)) {
            // Update products that belong to this category
            if (oldNameAr !== nameAr) {
                updateProductsCategory(oldNameAr, nameAr);
            }

            // Update main site categories
            updateMainSiteCategories();

            // Reload categories display
            loadCategories();
            closeModal();

            alert('تم تحديث الفئة بنجاح!');

            // Notify main site
            notifyMainSite('CATEGORIES_UPDATED');
        }
    } else {
        alert('لم يتم العثور على الفئة');
    }
}

function updateProductsCategory(oldCategoryName, newCategoryName) {
    const products = JSON.parse(localStorage.getItem('adminProducts')) || [];
    const updatedProducts = products.map(product => {
        if (product.category === oldCategoryName) {
            return { ...product, category: newCategoryName };
        }
        return product;
    });

    localStorage.setItem('adminProducts', JSON.stringify(updatedProducts));
}

function deleteCategory(categoryId) {
    const categories = JSON.parse(localStorage.getItem('categories')) || [];
    const category = categories.find(c => c.id === categoryId);

    if (!category) {
        alert('لم يتم العثور على الفئة');
        return;
    }

    const products = JSON.parse(localStorage.getItem('adminProducts')) || [];
    const categoryProducts = products.filter(p => p.category === category.nameAr);

    if (categoryProducts.length > 0) {
        if (!confirm(`هذه الفئة تحتوي على ${categoryProducts.length} منتج. حذف الفئة سيؤثر على هذه المنتجات. هل تريد المتابعة؟`)) {
            return;
        }

        // Update products to remove category reference
        const updatedProducts = products.map(product => {
            if (product.category === category.nameAr) {
                return { ...product, category: '' };
            }
            return product;
        });
        localStorage.setItem('adminProducts', JSON.stringify(updatedProducts));
    }

    if (!confirm(`هل أنت متأكد من حذف فئة "${category.nameAr}"؟`)) return;

    const filteredCategories = categories.filter(c => c.id !== categoryId);
    localStorage.setItem('categories', JSON.stringify(filteredCategories));

    // Update main site categories
    updateMainSiteCategories();

    // Reload categories display
    loadCategories();

    alert('تم حذف الفئة بنجاح!');
}

function viewCategoryProducts(categoryId) {
    const categories = JSON.parse(localStorage.getItem('categories')) || [];
    const category = categories.find(c => c.id === categoryId);

    if (!category) return;

    // Switch to products section and filter by category
    showSection('products');

    // Add category filter
    setTimeout(() => {
        filterProductsByCategory(category.nameAr);
    }, 100);
}

function filterProductsByCategory(categoryName) {
    const products = JSON.parse(localStorage.getItem('adminProducts')) || [];
    const filteredProducts = products.filter(product => product.category === categoryName);

    const productsGrid = document.getElementById('productsGrid');
    if (!productsGrid) return;

    // Add filter indicator
    const existingFilter = document.querySelector('.category-filter-indicator');
    if (existingFilter) {
        existingFilter.remove();
    }

    const filterIndicator = document.createElement('div');
    filterIndicator.className = 'category-filter-indicator';
    filterIndicator.innerHTML = `
        <div style="background: #3498db; color: white; padding: 0.8rem 1.2rem; border-radius: 6px; margin-bottom: 1rem; display: flex; justify-content: space-between; align-items: center;">
            <span><i class="fas fa-filter"></i> تصفية حسب الفئة: ${categoryName}</span>
            <button onclick="clearCategoryFilter()" style="background: none; border: none; color: white; cursor: pointer; font-size: 1.2rem;">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    productsGrid.parentNode.insertBefore(filterIndicator, productsGrid);

    // Display filtered products
    displayFilteredProducts(filteredProducts);
}

function clearCategoryFilter() {
    const filterIndicator = document.querySelector('.category-filter-indicator');
    if (filterIndicator) {
        filterIndicator.remove();
    }

    // Reload all products
    loadProducts();
}

function displayFilteredProducts(products) {
    const productsGrid = document.getElementById('productsGrid');

    if (products.length === 0) {
        productsGrid.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-box-open"></i>
                <p>لا توجد منتجات في هذه الفئة</p>
                <small>يمكنك إضافة منتجات جديدة لهذه الفئة</small>
            </div>
        `;
        return;
    }

    productsGrid.innerHTML = products.map(product => {
        const stockStatus = getStockStatus(product);

        return `
            <div class="product-card ${stockStatus.class}">
                <div class="product-image">
                    <img src="${product.image}" alt="${product.name}" onerror="this.src='assets/placeholder.jpg'">
                    ${stockStatus.badge ? `<div class="stock-badge ${stockStatus.badgeClass}">${stockStatus.badge}</div>` : ''}
                </div>
                <div class="product-info">
                    <h3 class="product-name">${product.name}</h3>
                    <p class="product-category">${product.category}</p>
                    <div class="product-price">${product.price} دينار</div>
                    <div class="product-stock">
                        <span class="stock-label">المخزون:</span>
                        <span class="stock-value ${stockStatus.class}">${product.stock || 0}</span>
                    </div>
                    ${stockStatus.warning ? `<div class="stock-warning">${stockStatus.warning}</div>` : ''}
                    <div class="product-actions">
                        <button class="btn-edit" onclick="editProduct('${product.id}')">
                            <i class="fas fa-edit"></i> تعديل
                        </button>
                        <button class="btn-delete" onclick="deleteProduct('${product.id}')">
                            <i class="fas fa-trash"></i> حذف
                        </button>
                    </div>
                </div>
            </div>
        `;
    }).join('');
}

function updateMainSiteCategories() {
    const categories = JSON.parse(localStorage.getItem('categories')) || [];
    localStorage.setItem('mainSiteCategories', JSON.stringify(categories));

    // Also update main site products to sync with categories
    const adminProducts = JSON.parse(localStorage.getItem('adminProducts')) || [];
    const availableProducts = adminProducts.filter(product => (product.stock || 0) > 0);
    localStorage.setItem('mainSiteProducts', JSON.stringify(availableProducts));

    // Notify main site if it's open
    try {
        if (window.opener && !window.opener.closed) {
            window.opener.postMessage({
                type: 'updateCategories',
                categories: categories
            }, '*');
        }
    } catch (e) {
        console.log('Could not communicate with main site:', e);
    }
}

// Load offers
function loadOffers() {
    const offers = JSON.parse(localStorage.getItem('adminOffers')) || [];
    const offersGrid = document.getElementById('offersGrid');

    if (!offersGrid) return;

    if (offers.length === 0) {
        offersGrid.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-tags"></i>
                <p>لا توجد عروض حتى الآن</p>
            </div>
        `;
        return;
    }

    offersGrid.innerHTML = offers.map(offer => {
        const isExpired = new Date(offer.endDate) < new Date();
        const status = isExpired ? 'expired' : 'active';

        return `
            <div class="offer-card ${status}">
                <div class="offer-header">
                    <div class="offer-discount">${offer.discountPercentage}% خصم</div>
                    <div class="offer-title">${offer.title}</div>
                    <div class="offer-status ${status}">
                        ${status === 'active' ? 'نشط' : 'منتهي'}
                    </div>
                </div>
                <div class="offer-body">
                    <div class="offer-product">
                        <div class="offer-product-image">
                            ${offer.productImage ?
                                `<img src="${offer.productImage}" alt="${offer.productName}">` :
                                `<i class="fas fa-image"></i>`
                            }
                        </div>
                        <div class="offer-product-info">
                            <h4>${offer.productName}</h4>
                            <div class="offer-product-price">
                                <span class="offer-original-price">${offer.originalPrice} دينار</span>
                                <span class="offer-sale-price">${offer.salePrice} دينار</span>
                            </div>
                        </div>
                    </div>
                    <div class="offer-details">
                        <div class="offer-detail">
                            <span class="offer-detail-label">تاريخ البداية:</span>
                            <span class="offer-detail-value">${offer.startDate}</span>
                        </div>
                        <div class="offer-detail">
                            <span class="offer-detail-label">تاريخ الانتهاء:</span>
                            <span class="offer-detail-value">${offer.endDate}</span>
                        </div>
                        <div class="offer-detail">
                            <span class="offer-detail-label">الوصف:</span>
                            <span class="offer-detail-value">${offer.description || 'لا يوجد وصف'}</span>
                        </div>
                    </div>
                    <div class="offer-actions">
                        <button class="btn-edit" onclick="editOffer('${offer.id}')">
                            <i class="fas fa-edit"></i> تعديل
                        </button>
                        <button class="btn-delete" onclick="deleteOffer('${offer.id}')">
                            <i class="fas fa-trash"></i> حذف
                        </button>
                    </div>
                </div>
            </div>
        `;
    }).join('');
}

// Offers Management Functions
function showAddOfferModal() {
    const products = JSON.parse(localStorage.getItem('adminProducts')) || [];

    if (products.length === 0) {
        alert('يجب إضافة منتجات أولاً قبل إنشاء العروض');
        return;
    }

    const modal = createModal('إضافة عرض جديد', `
        <form id="addOfferForm">
            <div class="form-group">
                <label>عنوان العرض</label>
                <input type="text" id="offerTitle" placeholder="مثال: عرض خاص على الفواكه" required>
            </div>
            <div class="form-group">
                <label>اختر المنتج</label>
                <select id="offerProduct" required onchange="updateOfferPrices()">
                    <option value="">اختر المنتج</option>
                    ${products.map(product => `
                        <option value="${product.id}" data-price="${product.price}" data-name="${product.name}" data-image="${product.image}">
                            ${product.name} - ${product.price} دينار
                        </option>
                    `).join('')}
                </select>
            </div>
            <div class="form-group">
                <label>نسبة الخصم (%)</label>
                <input type="number" id="offerDiscount" min="1" max="90" value="20" required onchange="updateOfferPrices()">
            </div>
            <div class="form-group">
                <label>السعر الأصلي</label>
                <input type="number" id="offerOriginalPrice" step="0.01" readonly style="background: #f8f9fa;">
            </div>
            <div class="form-group">
                <label>السعر بعد الخصم</label>
                <input type="number" id="offerSalePrice" step="0.01" readonly style="background: #f8f9fa;">
            </div>
            <div class="form-group">
                <label>تاريخ بداية العرض</label>
                <input type="date" id="offerStartDate" required>
            </div>
            <div class="form-group">
                <label>تاريخ انتهاء العرض</label>
                <input type="date" id="offerEndDate" required>
            </div>
            <div class="form-group">
                <label>وصف العرض (اختياري)</label>
                <textarea id="offerDescription" rows="3" placeholder="وصف مختصر للعرض"></textarea>
            </div>
        </form>
    `, [
        { text: 'إلغاء', class: 'btn-secondary', onclick: 'closeModal()' },
        { text: 'إضافة العرض', class: 'btn-primary', onclick: 'addOffer()' }
    ]);

    showModal(modal);

    // Set default dates
    const today = new Date();
    const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);

    setTimeout(() => {
        document.getElementById('offerStartDate').value = today.toISOString().split('T')[0];
        document.getElementById('offerEndDate').value = nextWeek.toISOString().split('T')[0];
    }, 100);
}

function updateOfferPrices() {
    const productSelect = document.getElementById('offerProduct');
    const discountInput = document.getElementById('offerDiscount');
    const originalPriceInput = document.getElementById('offerOriginalPrice');
    const salePriceInput = document.getElementById('offerSalePrice');

    if (!productSelect || !discountInput || !originalPriceInput || !salePriceInput) return;

    const selectedOption = productSelect.options[productSelect.selectedIndex];
    const originalPrice = parseFloat(selectedOption.getAttribute('data-price')) || 0;
    const discount = parseFloat(discountInput.value) || 0;

    if (originalPrice > 0 && discount > 0) {
        const salePrice = originalPrice * (1 - discount / 100);
        originalPriceInput.value = originalPrice.toFixed(2);
        salePriceInput.value = salePrice.toFixed(2);
    } else {
        originalPriceInput.value = '';
        salePriceInput.value = '';
    }
}

function addOffer() {
    const title = document.getElementById('offerTitle').value.trim();
    const productSelect = document.getElementById('offerProduct');
    const productId = productSelect.value;
    const discount = parseFloat(document.getElementById('offerDiscount').value);
    const originalPrice = parseFloat(document.getElementById('offerOriginalPrice').value);
    const salePrice = parseFloat(document.getElementById('offerSalePrice').value);
    const startDate = document.getElementById('offerStartDate').value;
    const endDate = document.getElementById('offerEndDate').value;
    const description = document.getElementById('offerDescription').value.trim();

    if (!title || !productId || !discount || !originalPrice || !salePrice || !startDate || !endDate) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }

    if (new Date(startDate) >= new Date(endDate)) {
        alert('تاريخ الانتهاء يجب أن يكون بعد تاريخ البداية');
        return;
    }

    if (discount < 1 || discount > 90) {
        alert('نسبة الخصم يجب أن تكون بين 1% و 90%');
        return;
    }

    const selectedOption = productSelect.options[productSelect.selectedIndex];
    const productName = selectedOption.getAttribute('data-name');
    const productImage = selectedOption.getAttribute('data-image');

    const offers = JSON.parse(localStorage.getItem('adminOffers')) || [];

    // Check if product already has an active offer
    const existingOffer = offers.find(offer =>
        offer.productId === productId &&
        offer.status === 'active' &&
        new Date(offer.endDate) >= new Date()
    );

    if (existingOffer) {
        if (!confirm('هذا المنتج لديه عرض نشط بالفعل. هل تريد إنشاء عرض جديد؟')) {
            return;
        }
    }

    const newOffer = {
        id: Date.now().toString(),
        title,
        productId,
        productName,
        productImage,
        discountPercentage: discount,
        originalPrice,
        salePrice,
        startDate,
        endDate,
        description,
        status: 'active',
        createdAt: new Date().toISOString()
    };

    offers.push(newOffer);
    localStorage.setItem('adminOffers', JSON.stringify(offers));

    // Update main site offers
    updateMainSiteOffers();

    loadOffers();
    loadDashboardData();
    closeModal();

    alert('تم إضافة العرض بنجاح!');
}

function updateMainSiteOffers() {
    const adminOffers = JSON.parse(localStorage.getItem('adminOffers')) || [];
    const activeOffers = adminOffers.filter(offer =>
        offer.status === 'active' &&
        new Date(offer.endDate) >= new Date() &&
        new Date(offer.startDate) <= new Date()
    );

    localStorage.setItem('mainSiteOffers', JSON.stringify(activeOffers));
}

function editOffer(offerId) {
    const offers = JSON.parse(localStorage.getItem('adminOffers')) || [];
    const offer = offers.find(o => o.id === offerId);
    const products = JSON.parse(localStorage.getItem('adminProducts')) || [];

    if (!offer) return;

    const modal = createModal('تعديل العرض', `
        <form id="editOfferForm">
            <div class="form-group">
                <label>عنوان العرض</label>
                <input type="text" id="editOfferTitle" value="${offer.title}" required>
            </div>
            <div class="form-group">
                <label>المنتج الحالي</label>
                <input type="text" value="${offer.productName}" readonly style="background: #f8f9fa;">
            </div>
            <div class="form-group">
                <label>نسبة الخصم (%)</label>
                <input type="number" id="editOfferDiscount" value="${offer.discountPercentage}" min="1" max="90" required onchange="updateEditOfferPrices('${offer.productId}')">
            </div>
            <div class="form-group">
                <label>السعر الأصلي</label>
                <input type="number" id="editOfferOriginalPrice" value="${offer.originalPrice}" step="0.01" readonly style="background: #f8f9fa;">
            </div>
            <div class="form-group">
                <label>السعر بعد الخصم</label>
                <input type="number" id="editOfferSalePrice" value="${offer.salePrice}" step="0.01" readonly style="background: #f8f9fa;">
            </div>
            <div class="form-group">
                <label>تاريخ بداية العرض</label>
                <input type="date" id="editOfferStartDate" value="${offer.startDate}" required>
            </div>
            <div class="form-group">
                <label>تاريخ انتهاء العرض</label>
                <input type="date" id="editOfferEndDate" value="${offer.endDate}" required>
            </div>
            <div class="form-group">
                <label>وصف العرض (اختياري)</label>
                <textarea id="editOfferDescription" rows="3">${offer.description || ''}</textarea>
            </div>
        </form>
    `, [
        { text: 'إلغاء', class: 'btn-secondary', onclick: 'closeModal()' },
        { text: 'حفظ التغييرات', class: 'btn-primary', onclick: `updateOffer('${offerId}')` }
    ]);

    showModal(modal);
}

function updateEditOfferPrices(productId) {
    const products = JSON.parse(localStorage.getItem('adminProducts')) || [];
    const product = products.find(p => p.id === productId);
    const discountInput = document.getElementById('editOfferDiscount');
    const originalPriceInput = document.getElementById('editOfferOriginalPrice');
    const salePriceInput = document.getElementById('editOfferSalePrice');

    if (!product || !discountInput || !originalPriceInput || !salePriceInput) return;

    const originalPrice = product.price;
    const discount = parseFloat(discountInput.value) || 0;

    if (discount > 0) {
        const salePrice = originalPrice * (1 - discount / 100);
        originalPriceInput.value = originalPrice.toFixed(2);
        salePriceInput.value = salePrice.toFixed(2);
    }
}

function updateOffer(offerId) {
    const title = document.getElementById('editOfferTitle').value.trim();
    const discount = parseFloat(document.getElementById('editOfferDiscount').value);
    const originalPrice = parseFloat(document.getElementById('editOfferOriginalPrice').value);
    const salePrice = parseFloat(document.getElementById('editOfferSalePrice').value);
    const startDate = document.getElementById('editOfferStartDate').value;
    const endDate = document.getElementById('editOfferEndDate').value;
    const description = document.getElementById('editOfferDescription').value.trim();

    if (!title || !discount || !originalPrice || !salePrice || !startDate || !endDate) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }

    if (new Date(startDate) >= new Date(endDate)) {
        alert('تاريخ الانتهاء يجب أن يكون بعد تاريخ البداية');
        return;
    }

    const offers = JSON.parse(localStorage.getItem('adminOffers')) || [];
    const offerIndex = offers.findIndex(o => o.id === offerId);

    if (offerIndex !== -1) {
        offers[offerIndex] = {
            ...offers[offerIndex],
            title,
            discountPercentage: discount,
            originalPrice,
            salePrice,
            startDate,
            endDate,
            description,
            updatedAt: new Date().toISOString()
        };

        localStorage.setItem('adminOffers', JSON.stringify(offers));
        updateMainSiteOffers();
        loadOffers();
        closeModal();

        alert('تم تحديث العرض بنجاح!');
    }
}

function deleteOffer(offerId) {
    if (!confirm('هل أنت متأكد من حذف هذا العرض؟')) return;

    const offers = JSON.parse(localStorage.getItem('adminOffers')) || [];
    const filteredOffers = offers.filter(o => o.id !== offerId);

    localStorage.setItem('adminOffers', JSON.stringify(filteredOffers));
    updateMainSiteOffers();
    loadOffers();
    loadDashboardData();

    alert('تم حذف العرض بنجاح!');
}

// Stock status helper function
function getStockStatus(product) {
    const stock = product.stock || 0;
    const minStock = product.minStock || 0;

    if (stock === 0) {
        return {
            class: 'out-of-stock',
            badge: 'نفذت الكمية',
            badgeClass: 'out-of-stock',
            warning: 'المنتج غير متوفر حالياً'
        };
    } else if (stock <= minStock) {
        return {
            class: 'low-stock',
            badge: 'كمية قليلة',
            badgeClass: 'low-stock',
            warning: 'الكمية أقل من الحد الأدنى'
        };
    } else {
        return {
            class: 'in-stock',
            badge: null,
            badgeClass: null,
            warning: null
        };
    }
}

// Load coupons
function loadCoupons() {
    const coupons = JSON.parse(localStorage.getItem('adminCoupons')) || getDefaultCoupons();
    const couponsList = document.getElementById('couponsList');
    
    if (coupons.length === 0) {
        couponsList.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-ticket-alt"></i>
                <p>لا توجد كوبونات حتى الآن</p>
            </div>
        `;
        return;
    }
    
    couponsList.innerHTML = coupons.map(coupon => {
        const isExpired = new Date(coupon.expiryDate) < new Date();
        const isMaxUsed = coupon.currentUses >= coupon.maxUses;
        const status = isExpired || isMaxUsed ? 'expired' : 'active';
        
        return `
            <div class="coupon-card">
                <div class="coupon-info">
                    <div class="coupon-code">${coupon.code}</div>
                    <div class="coupon-details">
                        <span class="coupon-detail">
                            ${coupon.type === 'percentage' ? `${coupon.value}%` : `${coupon.value} دينار`}
                        </span>
                        <span class="coupon-detail">الحد الأدنى: ${coupon.minAmount} دينار</span>
                        <span class="coupon-detail">الاستخدام: ${coupon.currentUses}/${coupon.maxUses}</span>
                        <span class="coupon-detail">ينتهي: ${coupon.expiryDate}</span>
                    </div>
                    <span class="coupon-status ${status}">
                        ${status === 'active' ? 'نشط' : 'منتهي الصلاحية'}
                    </span>
                </div>
                <div class="coupon-actions">
                    <button class="btn-edit" onclick="editCoupon('${coupon.id}')">
                        <i class="fas fa-edit"></i> تعديل
                    </button>
                    <button class="btn-delete" onclick="deleteCoupon('${coupon.id}')">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </div>
            </div>
        `;
    }).join('');
    
    // Save to localStorage if not exists
    if (!localStorage.getItem('adminCoupons')) {
        localStorage.setItem('adminCoupons', JSON.stringify(coupons));
    }
}

// Product Management Functions
function showAddProductModal() {
    const categories = JSON.parse(localStorage.getItem('categories')) || getDefaultCategories();

    const modal = createModal('إضافة منتج جديد', `
        <form id="addProductForm">
            <div class="form-group">
                <label>اسم المنتج</label>
                <input type="text" id="productName" required>
            </div>
            <div class="form-group">
                <label>السعر (دينار)</label>
                <input type="number" id="productPrice" step="0.01" required>
            </div>
            <div class="form-group">
                <label>الكمية المتوفرة</label>
                <input type="number" id="productStock" min="0" value="10" required>
                <small class="form-help">عدد القطع المتوفرة في المخزون</small>
            </div>
            <div class="form-group">
                <label>الحد الأدنى للتنبيه</label>
                <input type="number" id="productMinStock" min="0" value="5" required>
                <small class="form-help">عندما تصل الكمية لهذا الرقم سيظهر تنبيه</small>
            </div>
            <div class="form-group">
                <label>الفئة</label>
                <select id="productCategory" required>
                    <option value="">اختر الفئة</option>
                    ${categories.map(category => `
                        <option value="${category.nameAr}">${category.nameAr}</option>
                    `).join('')}
                </select>
                <small class="form-help">يمكنك إدارة الفئات من قسم "إدارة الفئات"</small>
            </div>
            <div class="form-group">
                <label>صورة المنتج</label>
                <input type="file" id="productImageFile" accept="image/*" onchange="previewImage(this, 'addImagePreview')">
                <div id="addImagePreview" class="image-preview" style="display: none;">
                    <img id="addPreviewImg" src="" alt="معاينة الصورة">
                    <button type="button" class="remove-image-btn" onclick="removeImagePreview('addImagePreview', 'productImageFile')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <small class="form-help">اختر صورة بصيغة JPG, PNG أو GIF (الحد الأقصى 5MB)</small>
            </div>
            <div class="form-group">
                <label>الوصف</label>
                <textarea id="productDescription" rows="3"></textarea>
            </div>
        </form>
    `, [
        { text: 'إلغاء', class: 'btn-secondary', onclick: 'closeModal()' },
        { text: 'إضافة المنتج', class: 'btn-primary', onclick: 'addProduct()' }
    ]);

    showModal(modal);
}

async function addProduct() {
    const name = document.getElementById('productName').value.trim();
    const price = parseFloat(document.getElementById('productPrice').value);
    const stock = parseInt(document.getElementById('productStock').value);
    const minStock = parseInt(document.getElementById('productMinStock').value);
    const category = document.getElementById('productCategory').value;
    const imageFile = document.getElementById('productImageFile').files[0];
    const description = document.getElementById('productDescription').value.trim();

    console.log('🆕 Adding product:', { name, price, stock, minStock, category, imageFile, description });

    if (!name || !price || isNaN(stock) || isNaN(minStock) || !category) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }

    if (stock < 0 || minStock < 0) {
        alert('الكمية والحد الأدنى يجب أن تكون أرقام موجبة');
        return;
    }

    // Show loading indicator
    showLoadingIndicator('جاري حفظ المنتج...');

    try {
        let imageId = null;

        // Process image if uploaded
        if (imageFile) {
            console.log('📷 Processing image file:', imageFile.name, imageFile.size);

            // Check file size (increased to 20MB for ultra storage)
            if (imageFile.size > 20 * 1024 * 1024) {
                hideLoadingIndicator();
                alert('حجم الصورة كبير جداً. الحد الأقصى 20MB للنظام الفائق');
                return;
            }

            // Check file type
            if (!imageFile.type.startsWith('image/')) {
                hideLoadingIndicator();
                alert('يرجى اختيار ملف صورة صحيح (PNG, JPG, GIF, WebP)');
                return;
            }

            // Ultra compress and save image using advanced storage
            const productId = 'prod_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            const compressedResults = await advancedStorage.compressImage(imageFile, {
                createThumbnail: true,
                quality: 0.6
            });

            const metadata = {
                category: category,
                originalSize: imageFile.size,
                originalName: imageFile.name,
                tags: [category, name.split(' ')[0]]
            };

            imageId = await advancedStorage.saveImage(compressedResults, productId, metadata);

            console.log('✅ Ultra image processed and saved:', imageId);
            await saveProduct(name, price, stock, minStock, category, imageId, description, productId);
        } else {
            // Use a default placeholder image URL
            const defaultImage = 'https://via.placeholder.com/400x300/f8f9fa/6c757d?text=No+Image';
            console.log('📝 No image file, using placeholder');
            await saveProduct(name, price, stock, minStock, category, defaultImage, description);
        }
    } catch (error) {
        console.error('❌ Error adding product:', error);
        hideLoadingIndicator();
        alert('حدث خطأ في إضافة المنتج. يرجى المحاولة مرة أخرى.');
    }
}

async function saveProduct(name, price, stock, minStock, category, image, description, productId = null) {
    console.log('💾 Saving product:', { name, price, stock, minStock, category, description });

    try {
        // Generate unique ID if not provided
        const id = productId || 'prod_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

        const newProduct = {
            id: id,
            name: name,
            price: parseFloat(price),
            stock: parseInt(stock),
            minStock: parseInt(minStock),
            category: category,
            image: image,
            imageType: image && image.startsWith('img_') ? 'advanced' : 'url',
            description: description || '',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        if (saveProductWithStatus(newProduct)) {
            // Update main site products
            updateMainSiteProductsFromAdmin();

            // Update categories count
            loadCategories();

            // Refresh displays
            loadProducts();
            loadDashboardData();

            hideLoadingIndicator();
            closeModal();

            alert('تم إضافة المنتج بنجاح!');

            // Notify main site
            notifyMainSite('PRODUCTS_UPDATED');

            // Update storage stats
            updateStorageStats();
        } else {
            hideLoadingIndicator();
        }
    } catch (error) {
        console.error('❌ Error in saveProduct:', error);
        hideLoadingIndicator();
        alert('حدث خطأ في حفظ المنتج. يرجى المحاولة مرة أخرى.');
    }
}

function updateMainSiteProductsFromAdmin() {
    const adminProducts = JSON.parse(localStorage.getItem('adminProducts')) || [];
    const availableProducts = adminProducts.filter(product => (product.stock || 0) > 0);
    localStorage.setItem('mainSiteProducts', JSON.stringify(availableProducts));

    // Notify main site if it's open
    try {
        if (window.opener && !window.opener.closed) {
            window.opener.postMessage({
                type: 'updateProducts',
                products: availableProducts
            }, '*');
        }
    } catch (e) {
        console.log('Could not communicate with main site:', e);
    }
}

// Image handling functions
function previewImage(input, previewId) {
    const file = input.files[0];
    const preview = document.getElementById(previewId);
    const previewImg = preview.querySelector('img');

    if (file) {
        // Check file size
        if (file.size > 5 * 1024 * 1024) {
            alert('حجم الصورة كبير جداً. الحد الأقصى 5MB');
            input.value = '';
            return;
        }

        // Check file type
        if (!file.type.startsWith('image/')) {
            alert('يرجى اختيار ملف صورة صحيح');
            input.value = '';
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            previewImg.src = e.target.result;
            preview.style.display = 'inline-block';
        };
        reader.readAsDataURL(file);
    }
}

function removeImagePreview(previewId, inputId) {
    const preview = document.getElementById(previewId);
    const input = document.getElementById(inputId);

    preview.style.display = 'none';
    input.value = '';
}

function editProduct(productId) {
    const products = JSON.parse(localStorage.getItem('adminProducts')) || [];
    const product = products.find(p => p.id === productId);
    const categories = JSON.parse(localStorage.getItem('categories')) || getDefaultCategories();

    if (!product) return;

    const modal = createModal('تعديل المنتج', `
        <form id="editProductForm">
            <div class="form-group">
                <label>اسم المنتج</label>
                <input type="text" id="editProductName" value="${product.name}" required>
            </div>
            <div class="form-group">
                <label>السعر (دينار)</label>
                <input type="number" id="editProductPrice" value="${product.price}" step="0.01" required>
            </div>
            <div class="form-group">
                <label>الكمية المتوفرة</label>
                <input type="number" id="editProductStock" value="${product.stock || 0}" min="0" required>
                <small class="form-help">عدد القطع المتوفرة في المخزون</small>
            </div>
            <div class="form-group">
                <label>الحد الأدنى للتنبيه</label>
                <input type="number" id="editProductMinStock" value="${product.minStock || 5}" min="0" required>
                <small class="form-help">عندما تصل الكمية لهذا الرقم سيظهر تنبيه</small>
            </div>
            <div class="form-group">
                <label>الفئة</label>
                <select id="editProductCategory" required>
                    <option value="">اختر الفئة</option>
                    ${categories.map(category => `
                        <option value="${category.nameAr}" ${product.category === category.nameAr ? 'selected' : ''}>${category.nameAr}</option>
                    `).join('')}
                </select>
                <small class="form-help">يمكنك إدارة الفئات من قسم "إدارة الفئات"</small>
            </div>
            <div class="form-group">
                <label>الصورة الحالية</label>
                <div class="current-image">
                    ${product.image && product.image !== 'assets/placeholder.jpg' ?
                        `<img src="${product.image}" alt="الصورة الحالية" style="max-width: 150px; max-height: 150px; border-radius: 6px; margin-bottom: 1rem;">` :
                        '<p style="color: #7f8c8d; font-style: italic;">لا توجد صورة</p>'
                    }
                </div>
                <label>تغيير الصورة (اختياري)</label>
                <input type="file" id="editProductImageFile" accept="image/*" onchange="previewImage(this, 'editImagePreview')">
                <div id="editImagePreview" class="image-preview" style="display: none;">
                    <img id="editPreviewImg" src="" alt="معاينة الصورة الجديدة">
                    <button type="button" class="remove-image-btn" onclick="removeImagePreview('editImagePreview', 'editProductImageFile')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <small class="form-help">اترك فارغاً للاحتفاظ بالصورة الحالية</small>
            </div>
            <div class="form-group">
                <label>الوصف</label>
                <textarea id="editProductDescription" rows="3">${product.description || ''}</textarea>
            </div>
        </form>
    `, [
        { text: 'إلغاء', class: 'btn-secondary', onclick: 'closeModal()' },
        { text: 'حفظ التغييرات', class: 'btn-primary', onclick: `updateProduct('${productId}')` }
    ]);

    showModal(modal);
}

function updateProduct(productId) {
    const name = document.getElementById('editProductName').value.trim();
    const price = parseFloat(document.getElementById('editProductPrice').value);
    const stock = parseInt(document.getElementById('editProductStock').value);
    const minStock = parseInt(document.getElementById('editProductMinStock').value);
    const category = document.getElementById('editProductCategory').value;
    const imageFile = document.getElementById('editProductImageFile').files[0];
    const description = document.getElementById('editProductDescription').value.trim();

    if (!name || !price || isNaN(stock) || isNaN(minStock) || !category) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }

    if (stock < 0 || minStock < 0) {
        alert('الكمية والحد الأدنى يجب أن تكون أرقام موجبة');
        return;
    }

    const products = JSON.parse(localStorage.getItem('adminProducts')) || [];
    const productIndex = products.findIndex(p => p.id === productId);

    if (productIndex === -1) {
        alert('المنتج غير موجود');
        return;
    }

    showLoadingIndicator('جاري تحديث المنتج...');

    // If new image is uploaded, process it
    if (imageFile) {
        console.log('📷 Processing new image for product update:', imageFile.name, imageFile.size);

        // Check file size (increased to 10MB)
        if (imageFile.size > 10 * 1024 * 1024) {
            hideLoadingIndicator();
            alert('حجم الصورة كبير جداً. الحد الأقصى 10MB');
            return;
        }

        // Check file type
        if (!imageFile.type.startsWith('image/')) {
            hideLoadingIndicator();
            alert('يرجى اختيار ملف صورة صحيح (PNG, JPG, GIF, WebP)');
            return;
        }

        // Process with advanced storage
        advancedStorage.compressImage(imageFile).then(async (compressedImage) => {
            try {
                // Delete old image if it was stored in advanced storage
                const oldProduct = products[productIndex];
                if (oldProduct.imageType === 'advanced' && oldProduct.image && oldProduct.image.startsWith('img_')) {
                    await advancedStorage.deleteImage(oldProduct.image);
                }

                // Save new image
                const imageId = await advancedStorage.saveImage(compressedImage, productId);
                saveUpdatedProduct(productId, productIndex, products, name, price, stock, minStock, category, imageId, description, 'advanced');
            } catch (error) {
                console.error('Error processing image update:', error);
                hideLoadingIndicator();
                alert('حدث خطأ في معالجة الصورة. يرجى المحاولة مرة أخرى.');
            }
        }).catch((error) => {
            console.error('Error compressing image:', error);
            hideLoadingIndicator();
            alert('حدث خطأ في ضغط الصورة. يرجى المحاولة مرة أخرى.');
        });
    } else {
        // Keep existing image
        const currentImage = products[productIndex].image;
        const currentImageType = products[productIndex].imageType || 'url';
        console.log('📝 Keeping existing image for product update');
        saveUpdatedProduct(productId, productIndex, products, name, price, stock, minStock, category, currentImage, description, currentImageType);
    }
}

function saveUpdatedProduct(productId, productIndex, products, name, price, stock, minStock, category, image, description, imageType = 'url') {
    console.log('💾 Updating product:', { productId, name, price, stock, minStock, category, description, imageType });

    try {
        const updatedProduct = {
            ...products[productIndex],
            name: name,
            price: parseFloat(price),
            stock: parseInt(stock),
            minStock: parseInt(minStock),
            category: category,
            image: image,
            imageType: imageType,
            description: description || '',
            updatedAt: new Date().toISOString()
        };

        if (updateProductSafely(productId, updatedProduct)) {
            // Update main site products
            updateMainSiteProductsFromAdmin();

            // Update categories count
            loadCategories();

            // Refresh displays
            loadProducts();

            hideLoadingIndicator();
            closeModal();

            alert('تم تحديث المنتج بنجاح!');

            // Notify main site
            notifyMainSite('PRODUCTS_UPDATED');

            // Update storage stats
            updateStorageStats();
        } else {
            hideLoadingIndicator();
        }
    } catch (error) {
        console.error('❌ Error in saveUpdatedProduct:', error);
        hideLoadingIndicator();
        alert('حدث خطأ في تحديث المنتج. يرجى المحاولة مرة أخرى.');
    }
}

function deleteProduct(productId) {
    if (!confirm('هل أنت متأكد من حذف هذا المنتج؟')) return;

    const products = JSON.parse(localStorage.getItem('adminProducts')) || [];
    const filteredProducts = products.filter(p => p.id !== productId);

    localStorage.setItem('adminProducts', JSON.stringify(filteredProducts));
    loadProducts();
    loadDashboardData();

    alert('تم حذف المنتج بنجاح!');
}

// Coupon Management Functions
function showAddCouponModal() {
    const modal = createModal('إنشاء كوبون جديد', `
        <form id="addCouponForm">
            <div class="form-group">
                <label>رمز الكوبون</label>
                <input type="text" id="couponCode" placeholder="مثال: SAVE30" required style="direction: ltr; text-align: left;">
            </div>
            <div class="form-group">
                <label>نوع الخصم</label>
                <select id="couponType" required onchange="toggleCouponValue()">
                    <option value="">اختر نوع الخصم</option>
                    <option value="percentage">نسبة مئوية (%)</option>
                    <option value="fixed">مبلغ ثابت (دينار)</option>
                </select>
            </div>
            <div class="form-group">
                <label id="couponValueLabel">قيمة الخصم</label>
                <input type="number" id="couponValue" step="0.01" required>
            </div>
            <div class="form-group">
                <label>الحد الأدنى للطلب (دينار)</label>
                <input type="number" id="couponMinAmount" value="0" step="0.01" required>
            </div>
            <div class="form-group">
                <label>عدد مرات الاستخدام المسموحة</label>
                <input type="number" id="couponMaxUses" value="100" required>
            </div>
            <div class="form-group">
                <label>تاريخ الانتهاء</label>
                <input type="date" id="couponExpiryDate" required>
            </div>
            <div class="form-group">
                <label>الوصف</label>
                <input type="text" id="couponDescription" placeholder="وصف مختصر للكوبون">
            </div>
        </form>
    `, [
        { text: 'إلغاء', class: 'btn-secondary', onclick: 'closeModal()' },
        { text: 'إنشاء الكوبون', class: 'btn-primary', onclick: 'addCoupon()' }
    ]);

    showModal(modal);

    // Set default expiry date (30 days from now)
    const defaultDate = new Date();
    defaultDate.setDate(defaultDate.getDate() + 30);
    document.getElementById('couponExpiryDate').value = defaultDate.toISOString().split('T')[0];
}

function toggleCouponValue() {
    const type = document.getElementById('couponType').value;
    const label = document.getElementById('couponValueLabel');
    const input = document.getElementById('couponValue');

    if (type === 'percentage') {
        label.textContent = 'نسبة الخصم (%)';
        input.placeholder = 'مثال: 10';
        input.max = '100';
    } else if (type === 'fixed') {
        label.textContent = 'مبلغ الخصم (دينار)';
        input.placeholder = 'مثال: 50';
        input.removeAttribute('max');
    }
}

function addCoupon() {
    const code = document.getElementById('couponCode').value.trim().toUpperCase();
    const type = document.getElementById('couponType').value;
    const value = parseFloat(document.getElementById('couponValue').value);
    const minAmount = parseFloat(document.getElementById('couponMinAmount').value);
    const maxUses = parseInt(document.getElementById('couponMaxUses').value);
    const expiryDate = document.getElementById('couponExpiryDate').value;
    const description = document.getElementById('couponDescription').value.trim();

    if (!code || !type || !value || maxUses < 1 || !expiryDate) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }

    if (type === 'percentage' && (value < 1 || value > 100)) {
        alert('نسبة الخصم يجب أن تكون بين 1% و 100%');
        return;
    }

    if (type === 'fixed' && value < 1) {
        alert('مبلغ الخصم يجب أن يكون أكبر من 0');
        return;
    }

    const coupons = JSON.parse(localStorage.getItem('adminCoupons')) || [];

    // Check if coupon code already exists
    if (coupons.find(c => c.code === code)) {
        alert('رمز الكوبون موجود بالفعل، يرجى اختيار رمز آخر');
        return;
    }

    const newCoupon = {
        id: code,
        code,
        type,
        value,
        description: description || `${type === 'percentage' ? value + '%' : value + ' دينار'} خصم`,
        minAmount,
        maxUses,
        currentUses: 0,
        expiryDate,
        status: 'active'
    };

    coupons.push(newCoupon);
    localStorage.setItem('adminCoupons', JSON.stringify(coupons));

    // Update main site coupons
    updateMainSiteCoupons();

    loadCoupons();
    loadDashboardData();
    closeModal();

    alert('تم إنشاء الكوبون بنجاح!');
}

function editCoupon(couponId) {
    const coupons = JSON.parse(localStorage.getItem('adminCoupons')) || [];
    const coupon = coupons.find(c => c.id === couponId);

    if (!coupon) return;

    const modal = createModal('تعديل الكوبون', `
        <form id="editCouponForm">
            <div class="form-group">
                <label>رمز الكوبون</label>
                <input type="text" id="editCouponCode" value="${coupon.code}" readonly style="background: #f8f9fa; direction: ltr; text-align: left;">
            </div>
            <div class="form-group">
                <label>نوع الخصم</label>
                <select id="editCouponType" required onchange="toggleEditCouponValue()">
                    <option value="percentage" ${coupon.type === 'percentage' ? 'selected' : ''}>نسبة مئوية (%)</option>
                    <option value="fixed" ${coupon.type === 'fixed' ? 'selected' : ''}>مبلغ ثابت (دينار)</option>
                </select>
            </div>
            <div class="form-group">
                <label id="editCouponValueLabel">${coupon.type === 'percentage' ? 'نسبة الخصم (%)' : 'مبلغ الخصم (دينار)'}</label>
                <input type="number" id="editCouponValue" value="${coupon.value}" step="0.01" required>
            </div>
            <div class="form-group">
                <label>الحد الأدنى للطلب (دينار)</label>
                <input type="number" id="editCouponMinAmount" value="${coupon.minAmount}" step="0.01" required>
            </div>
            <div class="form-group">
                <label>عدد مرات الاستخدام المسموحة</label>
                <input type="number" id="editCouponMaxUses" value="${coupon.maxUses}" required>
            </div>
            <div class="form-group">
                <label>تاريخ الانتهاء</label>
                <input type="date" id="editCouponExpiryDate" value="${coupon.expiryDate}" required>
            </div>
            <div class="form-group">
                <label>الوصف</label>
                <input type="text" id="editCouponDescription" value="${coupon.description}">
            </div>
            <div class="form-group">
                <label>عدد مرات الاستخدام الحالية</label>
                <input type="number" id="editCouponCurrentUses" value="${coupon.currentUses}" readonly style="background: #f8f9fa;">
            </div>
        </form>
    `, [
        { text: 'إلغاء', class: 'btn-secondary', onclick: 'closeModal()' },
        { text: 'حفظ التغييرات', class: 'btn-primary', onclick: `updateCoupon('${couponId}')` }
    ]);

    showModal(modal);
}

function toggleEditCouponValue() {
    const type = document.getElementById('editCouponType').value;
    const label = document.getElementById('editCouponValueLabel');
    const input = document.getElementById('editCouponValue');

    if (type === 'percentage') {
        label.textContent = 'نسبة الخصم (%)';
        input.max = '100';
    } else if (type === 'fixed') {
        label.textContent = 'مبلغ الخصم (دينار)';
        input.removeAttribute('max');
    }
}

function updateCoupon(couponId) {
    const type = document.getElementById('editCouponType').value;
    const value = parseFloat(document.getElementById('editCouponValue').value);
    const minAmount = parseFloat(document.getElementById('editCouponMinAmount').value);
    const maxUses = parseInt(document.getElementById('editCouponMaxUses').value);
    const expiryDate = document.getElementById('editCouponExpiryDate').value;
    const description = document.getElementById('editCouponDescription').value.trim();

    if (!type || !value || maxUses < 1 || !expiryDate) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }

    if (type === 'percentage' && (value < 1 || value > 100)) {
        alert('نسبة الخصم يجب أن تكون بين 1% و 100%');
        return;
    }

    if (type === 'fixed' && value < 1) {
        alert('مبلغ الخصم يجب أن يكون أكبر من 0');
        return;
    }

    const coupons = JSON.parse(localStorage.getItem('adminCoupons')) || [];
    const couponIndex = coupons.findIndex(c => c.id === couponId);

    if (couponIndex !== -1) {
        coupons[couponIndex] = {
            ...coupons[couponIndex],
            type,
            value,
            description,
            minAmount,
            maxUses,
            expiryDate
        };

        localStorage.setItem('adminCoupons', JSON.stringify(coupons));
        updateMainSiteCoupons();
        loadCoupons();
        closeModal();

        alert('تم تحديث الكوبون بنجاح!');
    }
}

function deleteCoupon(couponId) {
    if (!confirm('هل أنت متأكد من حذف هذا الكوبون؟')) return;

    const coupons = JSON.parse(localStorage.getItem('adminCoupons')) || [];
    const filteredCoupons = coupons.filter(c => c.id !== couponId);

    localStorage.setItem('adminCoupons', JSON.stringify(filteredCoupons));
    updateMainSiteCoupons();
    loadCoupons();
    loadDashboardData();

    alert('تم حذف الكوبون بنجاح!');
}

function updateMainSiteCoupons() {
    // This function updates the coupons in the main site's script
    // We'll store them in a format that the main site can use
    const adminCoupons = JSON.parse(localStorage.getItem('adminCoupons')) || [];
    const mainSiteCoupons = {};

    adminCoupons.forEach(coupon => {
        if (coupon.status === 'active' && new Date(coupon.expiryDate) >= new Date()) {
            mainSiteCoupons[coupon.code] = {
                type: coupon.type,
                value: coupon.value,
                description: coupon.description,
                minAmount: coupon.minAmount
            };
        }
    });

    localStorage.setItem('mainSiteCoupons', JSON.stringify(mainSiteCoupons));
}

// Modal Functions
function createModal(title, content, buttons = []) {
    return `
        <div class="modal-overlay" id="modalOverlay">
            <div class="modal">
                <div class="modal-header">
                    <h3>${title}</h3>
                    <button class="modal-close" onclick="closeModal()">&times;</button>
                </div>
                <div class="modal-body">
                    ${content}
                </div>
                <div class="modal-footer">
                    ${buttons.map(btn => `
                        <button class="${btn.class}" onclick="${btn.onclick}">${btn.text}</button>
                    `).join('')}
                </div>
            </div>
        </div>
    `;
}

function showModal(modalHTML) {
    const modalContainer = document.getElementById('modalContainer');
    modalContainer.innerHTML = modalHTML;

    setTimeout(() => {
        const overlay = document.getElementById('modalOverlay');
        if (overlay) {
            overlay.classList.add('active');
        }
    }, 10);
}

function closeModal() {
    const overlay = document.getElementById('modalOverlay');
    if (overlay) {
        overlay.classList.remove('active');
        setTimeout(() => {
            document.getElementById('modalContainer').innerHTML = '';
        }, 300);
    }
}

// Settings Functions
document.addEventListener('DOMContentLoaded', function() {
    // Monitor storage usage
    monitorStorage();

    // Initialize advanced storage and update stats
    setTimeout(() => {
        updateStorageStats();
    }, 1000);
    // Store settings form handlers
    const storeInfoForm = document.getElementById('storeInfoForm');
    const whatsappForm = document.getElementById('whatsappForm');

    if (storeInfoForm) {
        storeInfoForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const settings = {
                storeName: document.getElementById('storeName').value,
                storePhone: document.getElementById('storePhone').value,
                storeAddress: document.getElementById('storeAddress').value
            };

            if (saveToStorage('storeSettings', settings)) {
                alert('تم حفظ إعدادات المتجر بنجاح!');
                notifyMainSite('SETTINGS_UPDATED');
            }
        });
    }

    if (whatsappForm) {
        whatsappForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const settings = {
                whatsappNumber: document.getElementById('whatsappNumber').value,
                welcomeMessage: document.getElementById('welcomeMessage').value
            };

            if (saveToStorage('whatsappSettings', settings)) {
                alert('تم حفظ إعدادات الواتس اب بنجاح!');
            }
        });
    }
});

// Ultra Advanced Storage System for 50000+ Images
class UltraAdvancedStorage {
    constructor() {
        this.maxChunkSize = 512 * 1024; // 512KB chunks for better performance
        this.compressionQuality = 0.6; // 60% quality for maximum compression
        this.useIndexedDB = this.checkIndexedDBSupport();
        this.imageCache = new Map();
        this.maxCacheSize = 100; // Cache only 100 most recent images
        this.compressionWorker = null;
        this.dbVersion = 2;
        this.batchSize = 50; // Process images in batches
        this.virtualScrolling = true;
        this.lazyLoading = true;
        this.thumbnailSize = { width: 150, height: 150 };
        this.fullImageSize = { width: 800, height: 600 };
        this.init();
    }

    checkIndexedDBSupport() {
        return 'indexedDB' in window;
    }

    async init() {
        if (this.useIndexedDB) {
            await this.initIndexedDB();
        }
        console.log(`🚀 Advanced Storage initialized (IndexedDB: ${this.useIndexedDB})`);
    }

    async initIndexedDB() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open('UltraStoreDatabase', this.dbVersion);

            request.onerror = () => reject(request.error);
            request.onsuccess = () => {
                this.db = request.result;
                console.log('🗄️ Ultra IndexedDB initialized successfully');
                resolve();
            };

            request.onupgradeneeded = (event) => {
                const db = event.target.result;

                // Create optimized object stores for 50000+ images
                if (!db.objectStoreNames.contains('images')) {
                    const imageStore = db.createObjectStore('images', { keyPath: 'id' });
                    imageStore.createIndex('productId', 'productId', { unique: false });
                    imageStore.createIndex('createdAt', 'createdAt', { unique: false });
                    imageStore.createIndex('size', 'size', { unique: false });
                    imageStore.createIndex('type', 'type', { unique: false });
                }

                // Separate store for thumbnails for faster loading
                if (!db.objectStoreNames.contains('thumbnails')) {
                    const thumbStore = db.createObjectStore('thumbnails', { keyPath: 'id' });
                    thumbStore.createIndex('productId', 'productId', { unique: false });
                }

                // Metadata store for quick queries
                if (!db.objectStoreNames.contains('imageMetadata')) {
                    const metaStore = db.createObjectStore('imageMetadata', { keyPath: 'id' });
                    metaStore.createIndex('productId', 'productId', { unique: false });
                    metaStore.createIndex('category', 'category', { unique: false });
                    metaStore.createIndex('tags', 'tags', { multiEntry: true });
                }

                if (!db.objectStoreNames.contains('products')) {
                    const productStore = db.createObjectStore('products', { keyPath: 'id' });
                    productStore.createIndex('category', 'category', { unique: false });
                    productStore.createIndex('createdAt', 'createdAt', { unique: false });
                }

                if (!db.objectStoreNames.contains('categories')) {
                    const categoryStore = db.createObjectStore('categories', { keyPath: 'id' });
                    categoryStore.createIndex('nameAr', 'nameAr', { unique: false });
                }

                // Performance optimization store
                if (!db.objectStoreNames.contains('performance')) {
                    db.createObjectStore('performance', { keyPath: 'id' });
                }
            };
        });
    }

    // Ultra-advanced image compression with multiple sizes
    async compressImage(file, options = {}) {
        const {
            quality = this.compressionQuality,
            createThumbnail = true,
            maxWidth = this.fullImageSize.width,
            maxHeight = this.fullImageSize.height
        } = options;

        return new Promise((resolve, reject) => {
            const img = new Image();

            img.onload = () => {
                try {
                    const results = {};

                    // Create full-size compressed image
                    const fullCanvas = this.createCanvas(img, maxWidth, maxHeight);
                    results.fullImage = fullCanvas.toDataURL('image/jpeg', quality);

                    // Create thumbnail
                    if (createThumbnail) {
                        const thumbCanvas = this.createCanvas(img, this.thumbnailSize.width, this.thumbnailSize.height);
                        results.thumbnail = thumbCanvas.toDataURL('image/jpeg', 0.8);
                    }

                    // Calculate compression ratio
                    const originalSize = file.size;
                    const compressedSize = results.fullImage.length;
                    const compressionRatio = ((originalSize - compressedSize) / originalSize * 100).toFixed(1);

                    console.log(`📷 Ultra compression: ${originalSize} → ${compressedSize} bytes (${compressionRatio}% reduction)`);

                    resolve(results);
                } catch (error) {
                    reject(error);
                }
            };

            img.onerror = () => reject(new Error('Failed to load image'));
            img.src = URL.createObjectURL(file);
        });
    }

    createCanvas(img, maxWidth, maxHeight) {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        // Calculate optimal dimensions maintaining aspect ratio
        let { width, height } = img;
        const aspectRatio = width / height;

        if (width > height) {
            if (width > maxWidth) {
                width = maxWidth;
                height = width / aspectRatio;
            }
        } else {
            if (height > maxHeight) {
                height = maxHeight;
                width = height * aspectRatio;
            }
        }

        // Ensure dimensions don't exceed limits
        if (width > maxWidth) {
            width = maxWidth;
            height = width / aspectRatio;
        }
        if (height > maxHeight) {
            height = maxHeight;
            width = height * aspectRatio;
        }

        canvas.width = Math.round(width);
        canvas.height = Math.round(height);

        // High-quality rendering
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';

        // Draw image
        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

        return canvas;
    }

    // Batch compression for multiple images
    async compressBatch(files, onProgress = null) {
        const results = [];
        const total = files.length;

        for (let i = 0; i < total; i++) {
            try {
                const result = await this.compressImage(files[i]);
                results.push({ success: true, data: result, file: files[i] });

                if (onProgress) {
                    onProgress(i + 1, total, files[i].name);
                }
            } catch (error) {
                console.error(`Failed to compress ${files[i].name}:`, error);
                results.push({ success: false, error, file: files[i] });
            }
        }

        return results;
    }

    // Advanced cache management for 50000+ images
    manageCacheSize() {
        if (this.imageCache.size > this.maxCacheSize) {
            // Remove oldest entries (LRU strategy)
            const entries = Array.from(this.imageCache.entries());
            const toRemove = entries.slice(0, entries.length - this.maxCacheSize);

            toRemove.forEach(([key]) => {
                this.imageCache.delete(key);
            });

            console.log(`🧹 Cache cleaned: removed ${toRemove.length} old entries`);
        }
    }

    // Preload images for better performance
    async preloadImages(imageIds, type = 'thumbnail') {
        const promises = imageIds.slice(0, 20).map(async (imageId) => {
            try {
                const image = await this.loadImage(imageId, type);
                return { imageId, success: true, image };
            } catch (error) {
                return { imageId, success: false, error };
            }
        });

        const results = await Promise.allSettled(promises);
        console.log(`🚀 Preloaded ${results.filter(r => r.status === 'fulfilled').length}/${imageIds.length} images`);
        return results;
    }

    // Ultra-advanced image saving with thumbnails and metadata
    async saveImage(imageResults, productId, metadata = {}) {
        const imageId = `img_${productId}_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;

        if (this.useIndexedDB) {
            return await this.saveImageToIndexedDB(imageId, imageResults, productId, metadata);
        } else {
            return await this.saveImageToLocalStorage(imageId, imageResults, productId, metadata);
        }
    }

    async saveImageToIndexedDB(imageId, imageResults, productId, metadata = {}) {
        try {
            const transaction = this.db.transaction(['images', 'thumbnails', 'imageMetadata'], 'readwrite');

            // Save full image
            const imageStore = transaction.objectStore('images');
            const imageRecord = {
                id: imageId,
                productId: productId,
                data: imageResults.fullImage,
                size: imageResults.fullImage.length,
                type: 'full',
                createdAt: new Date().toISOString()
            };
            await imageStore.put(imageRecord);

            // Save thumbnail if available
            if (imageResults.thumbnail) {
                const thumbStore = transaction.objectStore('thumbnails');
                const thumbRecord = {
                    id: imageId,
                    productId: productId,
                    data: imageResults.thumbnail,
                    size: imageResults.thumbnail.length,
                    createdAt: new Date().toISOString()
                };
                await thumbStore.put(thumbRecord);
            }

            // Save metadata for quick queries
            const metaStore = transaction.objectStore('imageMetadata');
            const metaRecord = {
                id: imageId,
                productId: productId,
                fullSize: imageResults.fullImage.length,
                thumbnailSize: imageResults.thumbnail ? imageResults.thumbnail.length : 0,
                category: metadata.category || '',
                tags: metadata.tags || [],
                createdAt: new Date().toISOString(),
                ...metadata
            };
            await metaStore.put(metaRecord);

            console.log(`💾 Ultra image saved to IndexedDB: ${imageId} (Full: ${imageRecord.size}, Thumb: ${thumbRecord?.size || 0})`);

            // Update cache management
            this.manageCacheSize();

            return imageId;
        } catch (error) {
            console.error('Error saving ultra image to IndexedDB:', error);
            return await this.saveImageToLocalStorage(imageId, imageResults, productId, metadata);
        }
    }

    async saveImageToLocalStorage(imageId, imageData, productId) {
        try {
            // Split large images into chunks
            const chunks = this.chunkData(imageData);
            const chunkIds = [];

            for (let i = 0; i < chunks.length; i++) {
                const chunkId = `${imageId}_chunk_${i}`;
                localStorage.setItem(chunkId, chunks[i]);
                chunkIds.push(chunkId);
            }

            // Save metadata
            const metadata = {
                id: imageId,
                productId: productId,
                chunks: chunkIds,
                totalSize: imageData.length,
                createdAt: new Date().toISOString()
            };

            localStorage.setItem(`${imageId}_meta`, JSON.stringify(metadata));
            console.log(`💾 Image saved to localStorage in ${chunks.length} chunks: ${imageId}`);
            return imageId;
        } catch (error) {
            console.error('Error saving image to localStorage:', error);
            throw error;
        }
    }

    chunkData(data) {
        const chunks = [];
        for (let i = 0; i < data.length; i += this.maxChunkSize) {
            chunks.push(data.slice(i, i + this.maxChunkSize));
        }
        return chunks;
    }

    // Ultra-fast image loading with thumbnail support
    async loadImage(imageId, type = 'full') {
        const cacheKey = `${imageId}_${type}`;

        if (this.imageCache.has(cacheKey)) {
            return this.imageCache.get(cacheKey);
        }

        let imageData;
        if (this.useIndexedDB) {
            imageData = await this.loadImageFromIndexedDB(imageId, type);
        } else {
            imageData = await this.loadImageFromLocalStorage(imageId, type);
        }

        if (imageData) {
            this.imageCache.set(cacheKey, imageData);
            this.manageCacheSize();
        }

        return imageData;
    }

    // Load multiple images efficiently
    async loadImagesBatch(imageIds, type = 'thumbnail') {
        const promises = imageIds.map(async (imageId) => {
            try {
                const image = await this.loadImage(imageId, type);
                return { imageId, image, success: true };
            } catch (error) {
                console.warn(`Failed to load image ${imageId}:`, error);
                return { imageId, image: null, success: false, error };
            }
        });

        return await Promise.all(promises);
    }

    async loadImageFromIndexedDB(imageId, type = 'full') {
        try {
            const storeName = type === 'thumbnail' ? 'thumbnails' : 'images';
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.get(imageId);

            return new Promise((resolve, reject) => {
                request.onsuccess = () => {
                    const result = request.result;
                    if (result) {
                        resolve(result.data);
                    } else if (type === 'thumbnail') {
                        // Fallback to full image if thumbnail not found
                        this.loadImageFromIndexedDB(imageId, 'full').then(resolve).catch(reject);
                    } else {
                        resolve(null);
                    }
                };
                request.onerror = () => reject(request.error);
            });
        } catch (error) {
            console.error(`Error loading ${type} image from IndexedDB:`, error);
            return await this.loadImageFromLocalStorage(imageId, type);
        }
    }

    async loadImageFromLocalStorage(imageId) {
        try {
            const metadataStr = localStorage.getItem(`${imageId}_meta`);
            if (!metadataStr) return null;

            const metadata = JSON.parse(metadataStr);
            const chunks = [];

            for (const chunkId of metadata.chunks) {
                const chunk = localStorage.getItem(chunkId);
                if (chunk) {
                    chunks.push(chunk);
                } else {
                    console.warn(`Missing chunk: ${chunkId}`);
                    return null;
                }
            }

            return chunks.join('');
        } catch (error) {
            console.error('Error loading image from localStorage:', error);
            return null;
        }
    }

    // Delete image
    async deleteImage(imageId) {
        if (this.useIndexedDB) {
            await this.deleteImageFromIndexedDB(imageId);
        } else {
            await this.deleteImageFromLocalStorage(imageId);
        }

        this.imageCache.delete(imageId);
    }

    async deleteImageFromIndexedDB(imageId) {
        try {
            const transaction = this.db.transaction(['images'], 'readwrite');
            const store = transaction.objectStore('images');
            await store.delete(imageId);
        } catch (error) {
            console.error('Error deleting image from IndexedDB:', error);
        }
    }

    async deleteImageFromLocalStorage(imageId) {
        try {
            const metadataStr = localStorage.getItem(`${imageId}_meta`);
            if (metadataStr) {
                const metadata = JSON.parse(metadataStr);

                // Delete chunks
                for (const chunkId of metadata.chunks) {
                    localStorage.removeItem(chunkId);
                }

                // Delete metadata
                localStorage.removeItem(`${imageId}_meta`);
            }
        } catch (error) {
            console.error('Error deleting image from localStorage:', error);
        }
    }

    // Get storage statistics
    async getStorageStats() {
        let totalImages = 0;
        let totalSize = 0;

        if (this.useIndexedDB) {
            try {
                const transaction = this.db.transaction(['images'], 'readonly');
                const store = transaction.objectStore('images');
                const request = store.getAll();

                const images = await new Promise((resolve, reject) => {
                    request.onsuccess = () => resolve(request.result);
                    request.onerror = () => reject(request.error);
                });

                totalImages = images.length;
                totalSize = images.reduce((sum, img) => sum + (img.size || 0), 0);
            } catch (error) {
                console.error('Error getting IndexedDB stats:', error);
            }
        } else {
            // Count localStorage images
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.includes('_meta')) {
                    try {
                        const metadata = JSON.parse(localStorage.getItem(key));
                        totalImages++;
                        totalSize += metadata.totalSize || 0;
                    } catch (error) {
                        console.warn('Invalid metadata:', key);
                    }
                }
            }
        }

        return { totalImages, totalSize };
    }
}

// Initialize ultra advanced storage
const advancedStorage = new UltraAdvancedStorage();

// Virtual Scrolling System for 50000+ Products
class VirtualScrollManager {
    constructor(container, itemHeight = 300, visibleItems = 20) {
        this.container = container;
        this.itemHeight = itemHeight;
        this.visibleItems = visibleItems;
        this.scrollTop = 0;
        this.totalItems = 0;
        this.items = [];
        this.renderedItems = new Map();
        this.observer = null;
        this.init();
    }

    init() {
        if (!this.container) return;

        this.container.style.position = 'relative';
        this.container.style.overflow = 'auto';
        this.container.style.height = `${this.visibleItems * this.itemHeight}px`;

        this.container.addEventListener('scroll', this.handleScroll.bind(this));

        // Intersection Observer for lazy loading
        this.observer = new IntersectionObserver(
            this.handleIntersection.bind(this),
            { threshold: 0.1, rootMargin: '100px' }
        );
    }

    setItems(items) {
        this.items = items;
        this.totalItems = items.length;
        this.render();
    }

    handleScroll() {
        this.scrollTop = this.container.scrollTop;
        this.render();
    }

    handleIntersection(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const element = entry.target;
                const imageId = element.dataset.imageId;
                if (imageId && !element.querySelector('img').src.startsWith('data:')) {
                    this.loadImageLazy(element, imageId);
                }
            }
        });
    }

    async loadImageLazy(element, imageId) {
        try {
            const thumbnail = await advancedStorage.loadImage(imageId, 'thumbnail');
            const img = element.querySelector('img');
            if (img && thumbnail) {
                img.src = thumbnail;
                img.classList.add('loaded');
            }
        } catch (error) {
            console.warn('Failed to load lazy image:', imageId, error);
        }
    }

    render() {
        const startIndex = Math.floor(this.scrollTop / this.itemHeight);
        const endIndex = Math.min(startIndex + this.visibleItems + 5, this.totalItems);

        // Clear container
        this.container.innerHTML = '';

        // Create spacer for items above viewport
        if (startIndex > 0) {
            const topSpacer = document.createElement('div');
            topSpacer.style.height = `${startIndex * this.itemHeight}px`;
            this.container.appendChild(topSpacer);
        }

        // Render visible items
        for (let i = startIndex; i < endIndex; i++) {
            const item = this.items[i];
            if (item) {
                const element = this.createItemElement(item, i);
                this.container.appendChild(element);

                // Observe for lazy loading
                if (this.observer && item.imageId) {
                    this.observer.observe(element);
                }
            }
        }

        // Create spacer for items below viewport
        const remainingItems = this.totalItems - endIndex;
        if (remainingItems > 0) {
            const bottomSpacer = document.createElement('div');
            bottomSpacer.style.height = `${remainingItems * this.itemHeight}px`;
            this.container.appendChild(bottomSpacer);
        }
    }

    createItemElement(item, index) {
        const element = document.createElement('div');
        element.className = 'virtual-item product-card';
        element.style.height = `${this.itemHeight}px`;
        element.dataset.index = index;
        element.dataset.imageId = item.imageId;

        const stockStatus = item.stock <= item.minStock ? 'low-stock' : 'in-stock';
        const stockIcon = item.stock <= item.minStock ? 'fa-exclamation-triangle' : 'fa-check-circle';

        element.innerHTML = `
            <div class="product-image">
                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjhmOWZhIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzZjNzU3ZCIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkxvYWRpbmcuLi48L3RleHQ+PC9zdmc+"
                     alt="${item.name}"
                     class="lazy-image"
                     onerror="this.src='https://via.placeholder.com/300x200/f8f9fa/6c757d?text=No+Image'">
                <div class="product-actions">
                    <button class="btn-icon" onclick="editProduct('${item.id}')" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-icon btn-danger" onclick="deleteProduct('${item.id}')" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
                ${item.imageType === 'advanced' ? '<div class="advanced-storage-badge"><i class="fas fa-rocket"></i> Ultra</div>' : ''}
            </div>
            <div class="product-info">
                <h3 class="product-name">${item.name}</h3>
                <p class="product-category">${item.category}</p>
                <p class="product-price">${item.price} دينار</p>
                <div class="product-stock ${stockStatus}">
                    <i class="fas ${stockIcon}"></i>
                    <span>المخزون: ${item.stock}</span>
                </div>
                ${item.description ? `<p class="product-description">${item.description}</p>` : ''}
            </div>
        `;

        return element;
    }

    destroy() {
        if (this.observer) {
            this.observer.disconnect();
        }
        if (this.container) {
            this.container.removeEventListener('scroll', this.handleScroll);
        }
    }
}

// Global virtual scroll manager
let virtualScrollManager = null;

// Simple and Reliable Storage System
function saveToStorage(key, data) {
    console.log(`💾 Attempting to save ${key}...`);

    // Method 1: Try localStorage first (simple and fast)
    try {
        const jsonData = JSON.stringify(data);
        localStorage.setItem(key, jsonData);
        console.log(`✅ SUCCESS: Saved ${key} to localStorage (${data.length || Object.keys(data).length} items)`);
        return true;
    } catch (error) {
        console.warn(`⚠️ localStorage failed for ${key}:`, error.message);
    }

    // Method 2: Try sessionStorage as backup
    try {
        const jsonData = JSON.stringify(data);
        sessionStorage.setItem(key, jsonData);
        console.log(`✅ SUCCESS: Saved ${key} to sessionStorage (backup)`);
        return true;
    } catch (error) {
        console.warn(`⚠️ sessionStorage failed for ${key}:`, error.message);
    }

    // Method 3: Try IndexedDB as last resort
    try {
        saveToIndexedDBSimple(key, data);
        console.log(`✅ SUCCESS: Saved ${key} to IndexedDB (advanced)`);
        return true;
    } catch (error) {
        console.error(`❌ All storage methods failed for ${key}:`, error);
    }

    // Method 4: Emergency fallback - save to memory
    if (!window.emergencyStorage) {
        window.emergencyStorage = {};
    }
    window.emergencyStorage[key] = data;
    console.log(`🆘 EMERGENCY: Saved ${key} to memory (temporary)`);

    alert(`تم حفظ البيانات مؤقتاً في الذاكرة. يرجى تصدير البيانات كنسخة احتياطية.`);
    return true;
}

// Simple IndexedDB functions
function saveToIndexedDBSimple(key, data) {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open('SimpleStoreDB', 1);

        request.onerror = () => reject(request.error);

        request.onupgradeneeded = (event) => {
            const db = event.target.result;
            if (!db.objectStoreNames.contains('data')) {
                db.createObjectStore('data', { keyPath: 'key' });
            }
        };

        request.onsuccess = (event) => {
            const db = event.target.result;
            const transaction = db.transaction(['data'], 'readwrite');
            const store = transaction.objectStore('data');

            const record = {
                key: key,
                value: data,
                timestamp: new Date().toISOString()
            };

            const saveRequest = store.put(record);
            saveRequest.onsuccess = () => resolve(true);
            saveRequest.onerror = () => reject(saveRequest.error);
        };
    });
}

function loadFromIndexedDBSimple(key) {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open('SimpleStoreDB', 1);

        request.onerror = () => reject(request.error);

        request.onsuccess = (event) => {
            const db = event.target.result;

            if (!db.objectStoreNames.contains('data')) {
                resolve(null);
                return;
            }

            const transaction = db.transaction(['data'], 'readonly');
            const store = transaction.objectStore('data');
            const getRequest = store.get(key);

            getRequest.onsuccess = () => {
                const result = getRequest.result;
                resolve(result ? result.value : null);
            };
            getRequest.onerror = () => reject(getRequest.error);
        };
    });
}

// Force save function for critical data
function forceSave(key, data) {
    console.log(`🚨 FORCE SAVE: ${key}`);

    let success = false;

    // Try all methods synchronously
    const methods = [
        () => {
            localStorage.setItem(key, JSON.stringify(data));
            return 'localStorage';
        },
        () => {
            sessionStorage.setItem(key, JSON.stringify(data));
            return 'sessionStorage';
        },
        () => {
            if (!window.emergencyStorage) window.emergencyStorage = {};
            window.emergencyStorage[key] = data;
            return 'memory';
        }
    ];

    for (const method of methods) {
        try {
            const location = method();
            console.log(`✅ FORCE SAVE SUCCESS: ${key} saved to ${location}`);
            success = true;
            break;
        } catch (error) {
            console.warn(`⚠️ Force save method failed:`, error.message);
        }
    }

    if (!success) {
        console.error(`❌ CRITICAL: Could not save ${key} anywhere!`);
        alert(`خطأ حرج: لا يمكن حفظ البيانات! يرجى إعادة تشغيل المتصفح.`);
    }

    return success;
}

// Data Export/Import System
function exportAllData() {
    console.log('📤 Exporting all data...');

    try {
        const exportData = {
            products: loadFromStorage('adminProducts', []),
            categories: loadFromStorage('categories', []),
            coupons: loadFromStorage('adminCoupons', []),
            storeSettings: loadFromStorage('storeSettings', {}),
            whatsappSettings: loadFromStorage('whatsappSettings', {}),
            exportDate: new Date().toISOString(),
            version: '1.0'
        };

        const jsonString = JSON.stringify(exportData, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `store-backup-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        console.log('✅ Data exported successfully');
        alert(`تم تصدير البيانات بنجاح!\n\nالملف: store-backup-${new Date().toISOString().split('T')[0]}.json\nالمنتجات: ${exportData.products.length}\nالفئات: ${exportData.categories.length}`);

    } catch (error) {
        console.error('❌ Export failed:', error);
        alert('حدث خطأ في تصدير البيانات. يرجى المحاولة مرة أخرى.');
    }
}

function showImportModal() {
    const modal = createModal('استيراد البيانات', `
        <div class="import-data-form">
            <div class="import-warning">
                <i class="fas fa-exclamation-triangle"></i>
                <p><strong>تحذير:</strong> سيتم استبدال جميع البيانات الحالية بالبيانات المستوردة!</p>
            </div>

            <div class="form-group">
                <label for="importFile">اختر ملف النسخة الاحتياطية:</label>
                <input type="file" id="importFile" accept=".json" class="form-control">
            </div>

            <div class="import-options">
                <label>
                    <input type="checkbox" id="importProducts" checked> استيراد المنتجات
                </label>
                <label>
                    <input type="checkbox" id="importCategories" checked> استيراد الفئات
                </label>
                <label>
                    <input type="checkbox" id="importCoupons" checked> استيراد الكوبونات
                </label>
                <label>
                    <input type="checkbox" id="importSettings" checked> استيراد الإعدادات
                </label>
            </div>
        </div>
    `, [
        { text: 'إلغاء', class: 'btn-secondary', onclick: 'closeModal()' },
        { text: 'استيراد البيانات', class: 'btn-danger', onclick: 'importData()' }
    ]);

    showModal(modal);
}

function importData() {
    const fileInput = document.getElementById('importFile');
    const file = fileInput.files[0];

    if (!file) {
        alert('يرجى اختيار ملف للاستيراد');
        return;
    }

    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const importData = JSON.parse(e.target.result);

            console.log('📥 Importing data:', importData);

            // Validate data structure
            if (!importData.version || !importData.exportDate) {
                throw new Error('ملف غير صحيح - ليس نسخة احتياطية صالحة');
            }

            let importCount = 0;

            // Import products
            if (document.getElementById('importProducts').checked && importData.products) {
                forceSave('adminProducts', importData.products);
                importCount += importData.products.length;
                console.log(`✅ Imported ${importData.products.length} products`);
            }

            // Import categories
            if (document.getElementById('importCategories').checked && importData.categories) {
                forceSave('categories', importData.categories);
                console.log(`✅ Imported ${importData.categories.length} categories`);
            }

            // Import coupons
            if (document.getElementById('importCoupons').checked && importData.coupons) {
                forceSave('adminCoupons', importData.coupons);
                console.log(`✅ Imported ${importData.coupons.length} coupons`);
            }

            // Import settings
            if (document.getElementById('importSettings').checked) {
                if (importData.storeSettings) {
                    forceSave('storeSettings', importData.storeSettings);
                    console.log(`✅ Imported store settings`);
                }
                if (importData.whatsappSettings) {
                    forceSave('whatsappSettings', importData.whatsappSettings);
                    console.log(`✅ Imported WhatsApp settings`);
                }
            }

            closeModal();

            alert(`تم استيراد البيانات بنجاح!\n\nتاريخ النسخة الاحتياطية: ${new Date(importData.exportDate).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            })}\nالمنتجات المستوردة: ${importData.products?.length || 0}\nالفئات المستوردة: ${importData.categories?.length || 0}`);

            // Reload the page to reflect changes
            if (confirm('هل تريد إعادة تحميل الصفحة لعرض البيانات المستوردة؟')) {
                location.reload();
            }

        } catch (error) {
            console.error('❌ Import failed:', error);
            alert(`حدث خطأ في استيراد البيانات:\n${error.message}`);
        }
    };

    reader.onerror = function() {
        alert('حدث خطأ في قراءة الملف');
    };

    reader.readAsText(file);
}

// Employee Management System
let employees = [];

// Initialize employees data
function initializeEmployees() {
    employees = loadFromStorage('employees', getDefaultEmployees());
    console.log(`👥 Loaded ${employees.length} employees`);
}

function getDefaultEmployees() {
    return [
        // تم حذف الموظفين الافتراضيين - يمكن إنشاء حسابات جديدة من لوحة التحكم
    ];
}

// Show employees management modal
function showEmployeesModal() {
    initializeEmployees();

    const modal = createModal('إدارة الموظفين', `
        <div class="employees-management">
            <div class="employees-header">
                <div class="employees-stats">
                    <div class="stat-card">
                        <i class="fas fa-users"></i>
                        <div>
                            <span class="stat-number">${employees.length}</span>
                            <span class="stat-label">إجمالي الموظفين</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-user-check"></i>
                        <div>
                            <span class="stat-number">${employees.filter(emp => emp.status === 'active').length}</span>
                            <span class="stat-label">موظف نشط</span>
                        </div>
                    </div>
                    <div class="stat-card">
                        <i class="fas fa-user-tie"></i>
                        <div>
                            <span class="stat-number">${employees.filter(emp => emp.role === 'admin').length}</span>
                            <span class="stat-label">مدير</span>
                        </div>
                    </div>
                </div>
                <button class="btn btn-primary" onclick="showAddEmployeeModal()">
                    <i class="fas fa-user-plus"></i>
                    إضافة موظف جديد
                </button>
            </div>

            <div class="employees-list">
                ${employees.map(employee => `
                    <div class="employee-card ${employee.status}">
                        <div class="employee-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="employee-info">
                            <h4>${employee.name}</h4>
                            <p class="employee-role">${getRoleDisplayName(employee.role)}</p>
                            <p class="employee-username">@${employee.username}</p>
                            <p class="employee-phone">${employee.phone}</p>
                        </div>
                        <div class="employee-details">
                            <div class="employee-salary">${employee.salary.toLocaleString()} دينار</div>
                            <div class="employee-status ${employee.status}">
                                ${employee.status === 'active' ? 'نشط' : 'غير نشط'}
                            </div>
                        </div>
                        <div class="employee-actions">
                            <button class="btn btn-sm btn-info" onclick="editEmployee('${employee.id}')">
                                <i class="fas fa-edit"></i>
                                تعديل
                            </button>
                            <button class="btn btn-sm btn-warning" onclick="toggleEmployeeStatus('${employee.id}')">
                                <i class="fas fa-${employee.status === 'active' ? 'pause' : 'play'}"></i>
                                ${employee.status === 'active' ? 'إيقاف' : 'تفعيل'}
                            </button>
                            ${employee.role !== 'admin' ? `
                                <button class="btn btn-sm btn-danger" onclick="deleteEmployee('${employee.id}')">
                                    <i class="fas fa-trash"></i>
                                    حذف
                                </button>
                            ` : ''}
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    `, [
        { text: 'إغلاق', class: 'btn-secondary', onclick: 'closeModal()' },
        { text: 'تصدير قائمة الموظفين', class: 'btn-info', onclick: 'exportEmployees()' }
    ]);

    showModal(modal);
}

function getRoleDisplayName(role) {
    const roles = {
        'admin': 'مدير النظام',
        'cashier': 'كاشير',
        'sales': 'مندوب مبيعات',
        'inventory': 'مسؤول مخزون'
    };
    return roles[role] || role;
}

// Show add employee modal
function showAddEmployeeModal() {
    const modal = createModal('إضافة موظف جديد', `
        <div class="add-employee-form">
            <div class="form-row">
                <div class="form-group">
                    <label>الاسم الكامل</label>
                    <input type="text" id="employeeName" placeholder="أدخل الاسم الكامل" required>
                </div>
                <div class="form-group">
                    <label>اسم المستخدم</label>
                    <input type="text" id="employeeUsername" placeholder="اسم المستخدم للدخول" required>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label>كلمة المرور</label>
                    <input type="password" id="employeePassword" placeholder="كلمة مرور قوية" required>
                </div>
                <div class="form-group">
                    <label>رقم الهاتف</label>
                    <input type="tel" id="employeePhone" placeholder="07xxxxxxxxx" required>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label>البريد الإلكتروني</label>
                    <input type="email" id="employeeEmail" placeholder="<EMAIL>">
                </div>
                <div class="form-group">
                    <label>المنصب</label>
                    <select id="employeeRole" onchange="updatePermissions()">
                        <option value="cashier">كاشير</option>
                        <option value="sales">مندوب مبيعات</option>
                        <option value="inventory">مسؤول مخزون</option>
                        <option value="admin">مدير النظام</option>
                    </select>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label>الراتب (دينار)</label>
                    <input type="number" id="employeeSalary" placeholder="500000" min="0">
                </div>
                <div class="form-group">
                    <label>تاريخ التوظيف</label>
                    <input type="date" id="employeeHireDate" value="${new Date().toISOString().split('T')[0]}">
                </div>
            </div>

            <div class="form-group">
                <label>الصلاحيات</label>
                <div class="permissions-grid" id="permissionsGrid">
                    <label class="permission-item">
                        <input type="checkbox" value="cashier" checked>
                        <span>نظام الكاشير</span>
                    </label>
                    <label class="permission-item">
                        <input type="checkbox" value="sales">
                        <span>إدارة المبيعات</span>
                    </label>
                    <label class="permission-item">
                        <input type="checkbox" value="products">
                        <span>إدارة المنتجات</span>
                    </label>
                    <label class="permission-item">
                        <input type="checkbox" value="inventory">
                        <span>إدارة المخزون</span>
                    </label>
                    <label class="permission-item">
                        <input type="checkbox" value="reports">
                        <span>التقارير</span>
                    </label>
                    <label class="permission-item">
                        <input type="checkbox" value="settings">
                        <span>الإعدادات</span>
                    </label>
                </div>
            </div>
        </div>
    `, [
        { text: 'إلغاء', class: 'btn-secondary', onclick: 'closeModal()' },
        { text: 'إضافة الموظف', class: 'btn-success', onclick: 'addEmployee()' }
    ]);

    showModal(modal);
}

// Update permissions based on role
function updatePermissions() {
    const role = document.getElementById('employeeRole').value;
    const checkboxes = document.querySelectorAll('#permissionsGrid input[type="checkbox"]');

    // Clear all permissions
    checkboxes.forEach(cb => cb.checked = false);

    // Set default permissions based on role
    const defaultPermissions = {
        'cashier': ['cashier', 'sales'],
        'sales': ['sales', 'products'],
        'inventory': ['products', 'inventory'],
        'admin': ['cashier', 'sales', 'products', 'inventory', 'reports', 'settings']
    };

    if (defaultPermissions[role]) {
        defaultPermissions[role].forEach(permission => {
            const checkbox = document.querySelector(`#permissionsGrid input[value="${permission}"]`);
            if (checkbox) checkbox.checked = true;
        });
    }
}

// Add new employee
function addEmployee() {
    const name = document.getElementById('employeeName').value.trim();
    const username = document.getElementById('employeeUsername').value.trim();
    const password = document.getElementById('employeePassword').value;
    const phone = document.getElementById('employeePhone').value.trim();
    const email = document.getElementById('employeeEmail').value.trim();
    const role = document.getElementById('employeeRole').value;
    const salary = parseInt(document.getElementById('employeeSalary').value) || 0;
    const hireDate = document.getElementById('employeeHireDate').value;

    // Validation
    if (!name || !username || !password || !phone) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }

    // Check if username already exists
    if (employees.find(emp => emp.username === username)) {
        alert('اسم المستخدم موجود بالفعل');
        return;
    }

    // Get selected permissions
    const permissions = Array.from(document.querySelectorAll('#permissionsGrid input[type="checkbox"]:checked'))
        .map(cb => cb.value);

    const newEmployee = {
        id: 'emp_' + Date.now(),
        username: username,
        password: password,
        name: name,
        role: role,
        permissions: permissions,
        phone: phone,
        email: email,
        salary: salary,
        hireDate: hireDate,
        status: 'active',
        createdAt: new Date().toISOString()
    };

    employees.push(newEmployee);
    saveToStorage('employees', employees);

    closeModal();
    showEmployeesModal();

    showNotification(`تم إضافة الموظف ${name} بنجاح`, 'success');
}

// Edit employee
function editEmployee(employeeId) {
    const employee = employees.find(emp => emp.id === employeeId);
    if (!employee) return;

    const modal = createModal('تعديل بيانات الموظف', `
        <div class="edit-employee-form">
            <div class="form-row">
                <div class="form-group">
                    <label>الاسم الكامل</label>
                    <input type="text" id="editEmployeeName" value="${employee.name}" required>
                </div>
                <div class="form-group">
                    <label>اسم المستخدم</label>
                    <input type="text" id="editEmployeeUsername" value="${employee.username}" required>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label>كلمة المرور الجديدة (اتركها فارغة للاحتفاظ بالحالية)</label>
                    <input type="password" id="editEmployeePassword" placeholder="كلمة مرور جديدة">
                </div>
                <div class="form-group">
                    <label>رقم الهاتف</label>
                    <input type="tel" id="editEmployeePhone" value="${employee.phone}" required>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label>البريد الإلكتروني</label>
                    <input type="email" id="editEmployeeEmail" value="${employee.email || ''}">
                </div>
                <div class="form-group">
                    <label>المنصب</label>
                    <select id="editEmployeeRole" onchange="updateEditPermissions()">
                        <option value="cashier" ${employee.role === 'cashier' ? 'selected' : ''}>كاشير</option>
                        <option value="sales" ${employee.role === 'sales' ? 'selected' : ''}>مندوب مبيعات</option>
                        <option value="inventory" ${employee.role === 'inventory' ? 'selected' : ''}>مسؤول مخزون</option>
                        <option value="admin" ${employee.role === 'admin' ? 'selected' : ''}>مدير النظام</option>
                    </select>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label>الراتب (دينار)</label>
                    <input type="number" id="editEmployeeSalary" value="${employee.salary}" min="0">
                </div>
                <div class="form-group">
                    <label>تاريخ التوظيف</label>
                    <input type="date" id="editEmployeeHireDate" value="${employee.hireDate}">
                </div>
            </div>

            <div class="form-group">
                <label>الصلاحيات</label>
                <div class="permissions-grid" id="editPermissionsGrid">
                    <label class="permission-item">
                        <input type="checkbox" value="cashier" ${employee.permissions.includes('cashier') ? 'checked' : ''}>
                        <span>نظام الكاشير</span>
                    </label>
                    <label class="permission-item">
                        <input type="checkbox" value="sales" ${employee.permissions.includes('sales') ? 'checked' : ''}>
                        <span>إدارة المبيعات</span>
                    </label>
                    <label class="permission-item">
                        <input type="checkbox" value="products" ${employee.permissions.includes('products') ? 'checked' : ''}>
                        <span>إدارة المنتجات</span>
                    </label>
                    <label class="permission-item">
                        <input type="checkbox" value="inventory" ${employee.permissions.includes('inventory') ? 'checked' : ''}>
                        <span>إدارة المخزون</span>
                    </label>
                    <label class="permission-item">
                        <input type="checkbox" value="reports" ${employee.permissions.includes('reports') ? 'checked' : ''}>
                        <span>التقارير</span>
                    </label>
                    <label class="permission-item">
                        <input type="checkbox" value="settings" ${employee.permissions.includes('settings') ? 'checked' : ''}>
                        <span>الإعدادات</span>
                    </label>
                </div>
            </div>
        </div>
    `, [
        { text: 'إلغاء', class: 'btn-secondary', onclick: 'closeModal()' },
        { text: 'حفظ التغييرات', class: 'btn-success', onclick: `updateEmployee('${employeeId}')` }
    ]);

    showModal(modal);
}

function updateEditPermissions() {
    const role = document.getElementById('editEmployeeRole').value;
    const checkboxes = document.querySelectorAll('#editPermissionsGrid input[type="checkbox"]');

    // Don't auto-change permissions when editing, just update role
    // User can manually adjust permissions
}

// Update employee
function updateEmployee(employeeId) {
    const employee = employees.find(emp => emp.id === employeeId);
    if (!employee) return;

    const name = document.getElementById('editEmployeeName').value.trim();
    const username = document.getElementById('editEmployeeUsername').value.trim();
    const password = document.getElementById('editEmployeePassword').value;
    const phone = document.getElementById('editEmployeePhone').value.trim();
    const email = document.getElementById('editEmployeeEmail').value.trim();
    const role = document.getElementById('editEmployeeRole').value;
    const salary = parseInt(document.getElementById('editEmployeeSalary').value) || 0;
    const hireDate = document.getElementById('editEmployeeHireDate').value;

    // Validation
    if (!name || !username || !phone) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }

    // Check if username already exists (excluding current employee)
    if (employees.find(emp => emp.username === username && emp.id !== employeeId)) {
        alert('اسم المستخدم موجود بالفعل');
        return;
    }

    // Get selected permissions
    const permissions = Array.from(document.querySelectorAll('#editPermissionsGrid input[type="checkbox"]:checked'))
        .map(cb => cb.value);

    // Update employee data
    employee.name = name;
    employee.username = username;
    if (password) employee.password = password; // Only update password if provided
    employee.phone = phone;
    employee.email = email;
    employee.role = role;
    employee.permissions = permissions;
    employee.salary = salary;
    employee.hireDate = hireDate;
    employee.updatedAt = new Date().toISOString();

    saveToStorage('employees', employees);

    closeModal();
    showEmployeesModal();

    showNotification(`تم تحديث بيانات الموظف ${name} بنجاح`, 'success');
}

// Toggle employee status
function toggleEmployeeStatus(employeeId) {
    const employee = employees.find(emp => emp.id === employeeId);
    if (!employee) return;

    const newStatus = employee.status === 'active' ? 'inactive' : 'active';
    const action = newStatus === 'active' ? 'تفعيل' : 'إيقاف';

    if (confirm(`هل أنت متأكد من ${action} الموظف ${employee.name}؟`)) {
        employee.status = newStatus;
        employee.updatedAt = new Date().toISOString();

        saveToStorage('employees', employees);
        showEmployeesModal();

        showNotification(`تم ${action} الموظف ${employee.name}`, 'success');
    }
}

// Delete employee
function deleteEmployee(employeeId) {
    const employee = employees.find(emp => emp.id === employeeId);
    if (!employee) return;

    if (employee.role === 'admin') {
        alert('لا يمكن حذف حساب المدير');
        return;
    }

    if (confirm(`هل أنت متأكد من حذف الموظف ${employee.name}؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        employees = employees.filter(emp => emp.id !== employeeId);
        saveToStorage('employees', employees);

        showEmployeesModal();
        showNotification(`تم حذف الموظف ${employee.name}`, 'info');
    }
}

// Export employees data
function exportEmployees() {
    const exportData = {
        employees: employees,
        exportDate: new Date().toISOString(),
        totalEmployees: employees.length,
        activeEmployees: employees.filter(emp => emp.status === 'active').length
    };

    const jsonString = JSON.stringify(exportData, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = `employees-export-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showNotification('تم تصدير قائمة الموظفين بنجاح', 'success');
}

// User Authentication and Permissions
let currentUser = {
    name: 'المدير',
    role: 'admin',
    permissions: ['all']
};

function hasPermission(permission) {
    if (!currentUser) return false;
    return currentUser.permissions.includes(permission) || currentUser.permissions.includes('all');
}

function updateUserInfo() {
    if (currentUser) {
        // Update admin name in header with role
        const adminName = document.querySelector('.admin-name');
        if (adminName) {
            const roleNames = {
                'admin': 'مدير النظام',
                'manager': 'مدير الفرع',
                'cashier': 'كاشير',
                'sales': 'مندوب مبيعات',
                'inventory': 'مسؤول مخزون'
            };

            const roleName = roleNames[currentUser.role] || currentUser.role;
            adminName.textContent = `مرحباً، ${currentUser.name} (${roleName})`;
        }

        // Update page title based on user role
        const pageTitle = document.getElementById('pageTitle');
        if (pageTitle && currentUser.role !== 'admin') {
            pageTitle.textContent = `لوحة التحكم - ${roleNames[currentUser.role] || currentUser.role}`;
        }

        // Show welcome message for non-admin users
        if (currentUser.role !== 'admin') {
            showWelcomeMessage();
        }

        // Show/hide sections based on permissions
        updateUIBasedOnPermissions();
    }
}

function showWelcomeMessage() {
    const roleMessages = {
        'manager': 'مرحباً بك في لوحة إدارة الفرع. يمكنك إدارة العمليات اليومية ومتابعة الأداء.',
        'cashier': 'مرحباً بك. يمكنك الوصول لنظام الكاشير وإدارة المبيعات.',
        'sales': 'مرحباً بك في قسم المبيعات. يمكنك إدارة المنتجات والعروض.',
        'inventory': 'مرحباً بك في إدارة المخزون. يمكنك إدارة المنتجات والكميات.'
    };

    const message = roleMessages[currentUser.role];
    if (message) {
        setTimeout(() => {
            showNotification(message, 'info');
        }, 1000);
    }
}

function updateUIBasedOnPermissions() {
    console.log('🔒 Updating UI based on permissions:', currentUser.permissions);

    // Define sections and their required permissions
    const sections = {
        'products': ['products', 'inventory', 'admin', 'all'],
        'categories': ['products', 'inventory', 'admin', 'all'],
        'featured': ['products', 'sales', 'admin', 'all'],
        'offers': ['sales', 'admin', 'all'],
        'coupons': ['admin', 'all'],
        'orders': ['sales', 'admin', 'all'],
        'contact-settings': ['admin', 'settings', 'all'],
        'settings': ['admin', 'settings', 'all']
    };

    // Handle employees section separately (admin only)
    const employeesButton = document.querySelector('.header-employees-btn');
    const employeesNavItem = document.querySelector('.employees-nav');

    if (!hasPermission('admin') && !hasPermission('all')) {
        if (employeesButton) employeesButton.style.display = 'none';
        if (employeesNavItem) employeesNavItem.style.display = 'none';
    }

    // Hide/show sections based on permissions
    Object.keys(sections).forEach(section => {
        const hasAccess = sections[section].some(perm => hasPermission(perm));
        const navItem = document.querySelector(`[data-section="${section}"]`);

        if (navItem) {
            if (hasAccess) {
                navItem.parentElement.style.display = 'block';
                console.log(`✅ Access granted to: ${section}`);
            } else {
                navItem.parentElement.style.display = 'none';
                console.log(`❌ Access denied to: ${section}`);
            }
        }
    });

    // Update dashboard stats based on permissions
    updateDashboardStats();
}

function updateDashboardStats() {
    // Show different stats based on user role
    const statsCards = document.querySelectorAll('.stat-card');

    if (currentUser.role === 'cashier') {
        // Cashier sees only sales-related stats
        statsCards.forEach((card, index) => {
            if (index > 4) { // Hide revenue and other advanced stats
                card.style.display = 'none';
            }
        });
    } else if (currentUser.role === 'inventory') {
        // Inventory manager sees product and stock stats
        statsCards.forEach((card, index) => {
            if (index === 4 || index === 5) { // Hide orders and revenue
                card.style.display = 'none';
            }
        });
    }
}

// تم حذف وظيفة logout المعقدة - موجودة في بداية الملف

// Initialize employees on page load
document.addEventListener('DOMContentLoaded', function() {
    initializeEmployees();
});

// Make sure functions are available globally
window.showEmployeesModal = showEmployeesModal;
window.showAddEmployeeModal = showAddEmployeeModal;
window.addEmployee = addEmployee;
window.editEmployee = editEmployee;
window.updateEmployee = updateEmployee;
window.toggleEmployeeStatus = toggleEmployeeStatus;
window.deleteEmployee = deleteEmployee;
window.exportEmployees = exportEmployees;
window.updatePermissions = updatePermissions;
window.updateEditPermissions = updateEditPermissions;



// Simple employees modal as backup
function showSimpleEmployeesModal() {
    console.log('🔧 showSimpleEmployeesModal called');
    try {
        // Initialize employees if needed
        if (!window.employees || window.employees.length === 0) {
            console.log('🔄 Initializing employees...');
            initializeEmployees();
        }

        // First, verify admin credentials
        showAdminVerificationModal();
    } catch (error) {
        console.error('❌ Error in showSimpleEmployeesModal:', error);
        alert('حدث خطأ في فتح نظام حسابات الموظفين: ' + error.message);
    }
}

// Make functions available immediately
window.showSimpleEmployeesModal = showSimpleEmployeesModal;
window.showAdminVerificationModal = showAdminVerificationModal;
window.verifyAdminCredentials = verifyAdminCredentials;
window.showEmployeeManagementModal = showEmployeeManagementModal;

// Ensure DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 DOM loaded, employee functions ready');

    // Re-export functions to ensure they're available
    window.showSimpleEmployeesModal = showSimpleEmployeesModal;
    window.closeModal = closeModal;

    // Initialize employees
    if (typeof initializeEmployees === 'function') {
        initializeEmployees();
    }
});

function showAdminVerificationModal() {
    console.log('🔐 showAdminVerificationModal called');
    try {
        const modalHTML = `
        <div id="modalOverlay" class="modal-overlay">
            <div class="modal">
                <div class="modal-header">
                    <h3><i class="fas fa-shield-alt"></i> تأكيد هوية المدير</h3>
                    <button class="close-btn" onclick="closeModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="admin-verification">
                        <div class="verification-icon">
                            <i class="fas fa-lock"></i>
                        </div>
                        <h4>حسابات دخول الموظفين</h4>
                        <p>هذا القسم محمي ويتطلب تأكيد هوية المدير لإنشاء وإدارة حسابات دخول الموظفين</p>

                        <div class="verification-form">
                            <div class="form-group">
                                <label>اسم المستخدم:</label>
                                <input type="text" id="adminVerifyUsername" placeholder="أدخل اسم المستخدم" required>
                            </div>
                            <div class="form-group">
                                <label>كلمة المرور:</label>
                                <input type="password" id="adminVerifyPassword" placeholder="أدخل كلمة المرور" required>
                            </div>
                            <div id="verificationError" class="error-message" style="display: none;"></div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
                    <button class="btn btn-primary" onclick="verifyAdminCredentials()">
                        <i class="fas fa-check"></i>
                        تأكيد الهوية
                    </button>
                </div>
            </div>
        </div>
    `;

        const modalContainer = document.getElementById('modalContainer');
        if (!modalContainer) {
            console.error('❌ modalContainer not found');
            alert('خطأ: لم يتم العثور على حاوي النوافذ');
            return;
        }

        modalContainer.innerHTML = modalHTML;
        document.getElementById('modalOverlay').style.display = 'flex';

        // Focus on username field
        setTimeout(() => {
            document.getElementById('adminVerifyUsername').focus();
        }, 100);

        console.log('✅ Admin verification modal displayed');

    } catch (error) {
        console.error('❌ Error in showAdminVerificationModal:', error);
        alert('حدث خطأ في عرض نافذة التحقق: ' + error.message);
    }
}

function verifyAdminCredentials() {
    const username = document.getElementById('adminVerifyUsername').value.trim();
    const password = document.getElementById('adminVerifyPassword').value;
    const errorDiv = document.getElementById('verificationError');

    // Clear previous errors
    errorDiv.style.display = 'none';

    if (!username || !password) {
        showVerificationError('يرجى ملء جميع الحقول');
        return;
    }

    // Check admin credentials
    const employees = JSON.parse(localStorage.getItem('employees')) || [];
    const admin = employees.find(emp =>
        emp.username === username &&
        emp.password === password &&
        (emp.role === 'admin' || emp.permissions.includes('admin') || emp.permissions.includes('all')) &&
        emp.status === 'active'
    );

    if (admin) {
        // Admin verified, show employee management
        closeModal();
        showEmployeeManagementModal();
    } else {
        showVerificationError('اسم المستخدم أو كلمة المرور غير صحيحة، أو ليس لديك صلاحيات مدير');
    }
}

function showVerificationError(message) {
    const errorDiv = document.getElementById('verificationError');
    errorDiv.textContent = message;
    errorDiv.style.display = 'block';

    // Auto hide after 5 seconds
    setTimeout(() => {
        errorDiv.style.display = 'none';
    }, 5000);
}

function showEmployeeManagementModal() {
    try {
        console.log('🔧 Showing employee management modal...');

        // Initialize employees if not done
        if (!employees || employees.length === 0) {
            initializeEmployees();
        }

        const modalHTML = `
            <div id="modalOverlay" class="modal-overlay">
                <div class="modal">
                    <div class="modal-header">
                        <h3><i class="fas fa-users-cog"></i> حسابات دخول الموظفين</h3>
                        <button class="close-btn" onclick="closeModal()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="employees-management-advanced">
                            <div class="management-stats">
                                <div class="stat-item">
                                    <i class="fas fa-users"></i>
                                    <span>إجمالي الحسابات المكونة: ${employees.length}</span>
                                </div>
                                <div class="stat-item">
                                    <i class="fas fa-user-check"></i>
                                    <span>حسابات نشطة: ${employees.filter(emp => emp.status === 'active').length}</span>
                                </div>
                                <div class="stat-item">
                                    <i class="fas fa-user-shield"></i>
                                    <span>حسابات المدراء: ${employees.filter(emp => emp.role === 'admin').length}</span>
                                </div>
                            </div>

                            <div class="management-actions">
                                <button class="btn btn-success" onclick="showCreateAccountModal()">
                                    <i class="fas fa-user-plus"></i>
                                    إنشاء حساب دخول جديد
                                </button>
                                <button class="btn btn-info" onclick="exportEmployeeAccounts()">
                                    <i class="fas fa-download"></i>
                                    تصدير حسابات الدخول
                                </button>
                            </div>

                            <div class="employees-table">
                                <table class="accounts-table">
                                    <thead>
                                        <tr>
                                            <th>الاسم</th>
                                            <th>اسم المستخدم</th>
                                            <th>المنصب</th>
                                            <th>الصلاحيات</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${employees.map(emp => `
                                            <tr class="account-row ${emp.status}">
                                                <td>
                                                    <div class="employee-info">
                                                        <strong>${emp.name}</strong>
                                                        <small>${emp.phone}</small>
                                                    </div>
                                                </td>
                                                <td>
                                                    <code class="username">${emp.username}</code>
                                                </td>
                                                <td>
                                                    <span class="role-badge ${emp.role}">${getRoleDisplayName(emp.role)}</span>
                                                </td>
                                                <td>
                                                    <div class="permissions-list">
                                                        ${emp.permissions.slice(0, 2).map(perm => `<span class="permission-tag">${perm}</span>`).join('')}
                                                        ${emp.permissions.length > 2 ? `<span class="more-permissions">+${emp.permissions.length - 2}</span>` : ''}
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="status-badge ${emp.status}">
                                                        ${emp.status === 'active' ? 'نشط' : 'معطل'}
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <button class="btn-small btn-edit" onclick="editEmployeeAccount('${emp.id}')" title="تعديل">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button class="btn-small btn-toggle" onclick="toggleEmployeeAccount('${emp.id}')" title="${emp.status === 'active' ? 'تعطيل' : 'تفعيل'}">
                                                            <i class="fas fa-${emp.status === 'active' ? 'pause' : 'play'}"></i>
                                                        </button>
                                                        ${emp.role !== 'admin' ? `
                                                            <button class="btn-small btn-delete" onclick="deleteEmployeeAccount('${emp.id}')" title="حذف">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        ` : ''}
                                                    </div>
                                                </td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button class="btn btn-secondary" onclick="closeModal()">إغلاق</button>
                    </div>
                </div>
            </div>
        `;

        document.getElementById('modalContainer').innerHTML = modalHTML;
        document.getElementById('modalOverlay').style.display = 'flex';

        console.log('✅ Employee management modal displayed');

    } catch (error) {
        console.error('❌ Error in employee management modal:', error);
        alert('حدث خطأ في عرض إدارة الموظفين: ' + error.message);
    }
}

function addSimpleEmployee() {
    // Create a proper form for adding employee
    const modalHTML = `
        <div id="modalOverlay" class="modal-overlay">
            <div class="modal">
                <div class="modal-header">
                    <h3><i class="fas fa-user-plus"></i> إضافة موظف جديد</h3>
                    <button class="close-btn" onclick="closeModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="add-employee-simple">
                        <div class="form-group">
                            <label>الاسم الكامل:</label>
                            <input type="text" id="newEmpName" placeholder="أدخل الاسم الكامل" required>
                        </div>
                        <div class="form-group">
                            <label>اسم المستخدم:</label>
                            <input type="text" id="newEmpUsername" placeholder="اسم المستخدم للدخول" required>
                        </div>
                        <div class="form-group">
                            <label>كلمة المرور:</label>
                            <input type="password" id="newEmpPassword" placeholder="كلمة مرور قوية" required>
                        </div>
                        <div class="form-group">
                            <label>رقم الهاتف:</label>
                            <input type="tel" id="newEmpPhone" placeholder="07xxxxxxxxx" required>
                        </div>
                        <div class="form-group">
                            <label>المنصب:</label>
                            <select id="newEmpRole" onchange="updateNewEmpPermissions()">
                                <option value="cashier">كاشير</option>
                                <option value="sales">مندوب مبيعات</option>
                                <option value="inventory">مسؤول مخزون</option>
                                <option value="manager">مدير فرع</option>
                                <option value="admin">مدير النظام</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>الراتب (دينار):</label>
                            <input type="number" id="newEmpSalary" placeholder="600000" min="0">
                        </div>
                        <div class="form-group">
                            <label>الصلاحيات:</label>
                            <div class="permissions-simple" id="newEmpPermissions">
                                <label><input type="checkbox" value="cashier" checked> نظام الكاشير</label>
                                <label><input type="checkbox" value="sales"> إدارة المبيعات</label>
                                <label><input type="checkbox" value="products"> إدارة المنتجات</label>
                                <label><input type="checkbox" value="inventory"> إدارة المخزون</label>
                                <label><input type="checkbox" value="reports"> التقارير</label>
                                <label><input type="checkbox" value="settings"> الإعدادات</label>
                                <label><input type="checkbox" value="admin"> صلاحيات المدير</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
                    <button class="btn btn-success" onclick="saveNewEmployee()">إضافة الموظف</button>
                </div>
            </div>
        </div>
    `;

    document.getElementById('modalContainer').innerHTML = modalHTML;
    document.getElementById('modalOverlay').style.display = 'flex';
}

function updateNewEmpPermissions() {
    const role = document.getElementById('newEmpRole').value;
    const checkboxes = document.querySelectorAll('#newEmpPermissions input[type="checkbox"]');

    // Clear all permissions
    checkboxes.forEach(cb => cb.checked = false);

    // Set default permissions based on role
    const defaultPermissions = {
        'cashier': ['cashier', 'sales'],
        'sales': ['sales', 'products'],
        'inventory': ['products', 'inventory'],
        'manager': ['cashier', 'sales', 'products', 'inventory', 'reports'],
        'admin': ['cashier', 'sales', 'products', 'inventory', 'reports', 'settings', 'admin']
    };

    if (defaultPermissions[role]) {
        defaultPermissions[role].forEach(permission => {
            const checkbox = document.querySelector(`#newEmpPermissions input[value="${permission}"]`);
            if (checkbox) checkbox.checked = true;
        });
    }
}

function saveNewEmployee() {
    const name = document.getElementById('newEmpName').value.trim();
    const username = document.getElementById('newEmpUsername').value.trim();
    const password = document.getElementById('newEmpPassword').value;
    const phone = document.getElementById('newEmpPhone').value.trim();
    const role = document.getElementById('newEmpRole').value;
    const salary = parseInt(document.getElementById('newEmpSalary').value) || 600000;

    // Validation
    if (!name || !username || !password || !phone) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }

    // Check if username already exists
    if (employees.find(emp => emp.username === username)) {
        alert('اسم المستخدم موجود بالفعل');
        return;
    }

    // Get selected permissions
    const permissions = Array.from(document.querySelectorAll('#newEmpPermissions input[type="checkbox"]:checked'))
        .map(cb => cb.value);

    if (permissions.length === 0) {
        alert('يرجى اختيار صلاحية واحدة على الأقل');
        return;
    }

    const newEmployee = {
        id: 'emp_' + Date.now(),
        name: name,
        username: username,
        password: password,
        phone: phone,
        role: role,
        permissions: permissions,
        salary: salary,
        status: 'active',
        hireDate: new Date().toISOString().split('T')[0],
        email: `${username}@store.com`,
        createdAt: new Date().toISOString()
    };

    employees.push(newEmployee);
    saveToStorage('employees', employees);

    alert(`تم إضافة الموظف ${name} بنجاح!\n\nبيانات الدخول:\nاسم المستخدم: ${username}\nكلمة المرور: ${password}\n\nيمكن للموظف الآن تسجيل الدخول للنظام.`);

    closeModal();
    showSimpleEmployeesModal();
}

// Show Create Account Modal
function showCreateAccountModal() {
    const modalHTML = `
        <div id="modalOverlay" class="modal-overlay">
            <div class="modal">
                <div class="modal-header">
                    <h3><i class="fas fa-user-plus"></i> إنشاء حساب دخول موظف جديد</h3>
                    <button class="close-btn" onclick="closeModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="create-account-form">
                        <div class="form-section">
                            <h4><i class="fas fa-user"></i> المعلومات الشخصية</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label>الاسم الكامل *</label>
                                    <input type="text" id="newAccountName" placeholder="أدخل الاسم الكامل" required>
                                </div>
                                <div class="form-group">
                                    <label>رقم الهاتف *</label>
                                    <input type="tel" id="newAccountPhone" placeholder="07xxxxxxxxx" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>البريد الإلكتروني</label>
                                <input type="email" id="newAccountEmail" placeholder="<EMAIL>">
                            </div>
                        </div>

                        <div class="form-section">
                            <h4><i class="fas fa-key"></i> بيانات تسجيل الدخول</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label>اسم المستخدم *</label>
                                    <input type="text" id="newAccountUsername" placeholder="اسم المستخدم للدخول" required>
                                    <small class="form-help">يجب أن يكون فريداً ولا يحتوي على مسافات</small>
                                </div>
                                <div class="form-group">
                                    <label>كلمة المرور *</label>
                                    <input type="password" id="newAccountPassword" placeholder="كلمة مرور قوية" required>
                                    <small class="form-help">يُفضل أن تحتوي على أرقام وحروف</small>
                                </div>
                            </div>
                        </div>

                        <div class="form-section">
                            <h4><i class="fas fa-briefcase"></i> المنصب والصلاحيات</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label>المنصب *</label>
                                    <select id="newAccountRole" onchange="updateAccountPermissions()" required>
                                        <option value="">اختر المنصب</option>
                                        <option value="employee">موظف عادي</option>
                                        <option value="cashier">كاشير</option>
                                        <option value="sales">مندوب مبيعات</option>
                                        <option value="inventory">مسؤول مخزون</option>
                                        <option value="manager">مدير فرع</option>
                                        <option value="admin">مدير النظام</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>الراتب (دينار)</label>
                                    <input type="number" id="newAccountSalary" placeholder="500000" min="0">
                                </div>
                            </div>

                            <div class="form-group">
                                <label>الصلاحيات *</label>
                                <div class="permissions-grid-advanced" id="newAccountPermissions">
                                    <label class="permission-item-advanced">
                                        <input type="checkbox" value="cashier">
                                        <span class="permission-label">
                                            <i class="fas fa-cash-register"></i>
                                            نظام الكاشير
                                        </span>
                                        <small>إدارة المبيعات والفواتير</small>
                                    </label>
                                    <label class="permission-item-advanced">
                                        <input type="checkbox" value="sales">
                                        <span class="permission-label">
                                            <i class="fas fa-chart-line"></i>
                                            إدارة المبيعات
                                        </span>
                                        <small>عرض وتتبع المبيعات</small>
                                    </label>
                                    <label class="permission-item-advanced">
                                        <input type="checkbox" value="products">
                                        <span class="permission-label">
                                            <i class="fas fa-box"></i>
                                            إدارة المنتجات
                                        </span>
                                        <small>إضافة وتعديل المنتجات</small>
                                    </label>
                                    <label class="permission-item-advanced">
                                        <input type="checkbox" value="inventory">
                                        <span class="permission-label">
                                            <i class="fas fa-warehouse"></i>
                                            إدارة المخزون
                                        </span>
                                        <small>تتبع الكميات والمخزون</small>
                                    </label>
                                    <label class="permission-item-advanced">
                                        <input type="checkbox" value="reports">
                                        <span class="permission-label">
                                            <i class="fas fa-chart-bar"></i>
                                            التقارير
                                        </span>
                                        <small>عرض التقارير والإحصائيات</small>
                                    </label>
                                    <label class="permission-item-advanced">
                                        <input type="checkbox" value="settings">
                                        <span class="permission-label">
                                            <i class="fas fa-cog"></i>
                                            الإعدادات
                                        </span>
                                        <small>تعديل إعدادات النظام</small>
                                    </label>
                                    <label class="permission-item-advanced">
                                        <input type="checkbox" value="admin">
                                        <span class="permission-label">
                                            <i class="fas fa-crown"></i>
                                            صلاحيات المدير
                                        </span>
                                        <small>وصول كامل للنظام</small>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
                    <button class="btn btn-success" onclick="createEmployeeAccount()">
                        <i class="fas fa-save"></i>
                        إنشاء حساب الدخول
                    </button>
                </div>
            </div>
        </div>
    `;

    document.getElementById('modalContainer').innerHTML = modalHTML;
    document.getElementById('modalOverlay').style.display = 'flex';
}

// Update permissions based on role
function updateAccountPermissions() {
    const role = document.getElementById('newAccountRole').value;
    const checkboxes = document.querySelectorAll('#newAccountPermissions input[type="checkbox"]');

    // Clear all permissions
    checkboxes.forEach(cb => cb.checked = false);

    // Set default permissions based on role
    const defaultPermissions = {
        'employee': [],
        'cashier': ['cashier', 'sales'],
        'sales': ['sales', 'products'],
        'inventory': ['products', 'inventory'],
        'manager': ['cashier', 'sales', 'products', 'inventory', 'reports'],
        'admin': ['cashier', 'sales', 'products', 'inventory', 'reports', 'settings', 'admin']
    };

    if (defaultPermissions[role]) {
        defaultPermissions[role].forEach(permission => {
            const checkbox = document.querySelector(`#newAccountPermissions input[value="${permission}"]`);
            if (checkbox) checkbox.checked = true;
        });
    }
}

// Create Employee Account
function createEmployeeAccount() {
    const name = document.getElementById('newAccountName').value.trim();
    const phone = document.getElementById('newAccountPhone').value.trim();
    const email = document.getElementById('newAccountEmail').value.trim();
    const username = document.getElementById('newAccountUsername').value.trim();
    const password = document.getElementById('newAccountPassword').value;
    const role = document.getElementById('newAccountRole').value;
    const salary = parseInt(document.getElementById('newAccountSalary').value) || 500000;

    // Validation
    if (!name || !phone || !username || !password || !role) {
        alert('يرجى ملء جميع الحقول المطلوبة (*)');
        return;
    }

    // Check username format
    if (!/^[a-zA-Z0-9_]+$/.test(username)) {
        alert('اسم المستخدم يجب أن يحتوي على حروف وأرقام فقط بدون مسافات');
        return;
    }

    // Check if username already exists
    if (employees.find(emp => emp.username === username)) {
        alert('اسم المستخدم موجود بالفعل، يرجى اختيار اسم آخر');
        return;
    }

    // Get selected permissions
    const permissions = Array.from(document.querySelectorAll('#newAccountPermissions input[type="checkbox"]:checked'))
        .map(cb => cb.value);

    if (permissions.length === 0) {
        alert('يرجى اختيار صلاحية واحدة على الأقل');
        return;
    }

    // Create new employee account
    const newEmployee = {
        id: 'emp_' + Date.now(),
        name: name,
        username: username,
        password: password,
        phone: phone,
        email: email || `${username}@store.com`,
        role: role,
        permissions: permissions,
        salary: salary,
        status: 'active',
        hireDate: new Date().toISOString().split('T')[0],
        createdAt: new Date().toISOString(),
        createdBy: currentUser ? currentUser.name : 'المدير'
    };

    employees.push(newEmployee);
    saveToStorage('employees', employees);

    // Show success message with login details
    alert(`تم إنشاء حساب دخول ${name} بنجاح! ✅

📋 بيانات تسجيل الدخول:
👤 اسم المستخدم: ${username}
🔑 كلمة المرور: ${password}
🏢 المنصب: ${getRoleDisplayName(role)}
📱 الهاتف: ${phone}

تم إنشاء حساب الدخول بنجاح ويمكن للموظف الآن تسجيل الدخول للنظام.`);

    closeModal();
    showEmployeeManagementModal();
}

// Make functions available globally
window.showSimpleEmployeesModal = showSimpleEmployeesModal;
window.showAdminVerificationModal = showAdminVerificationModal;
window.verifyAdminCredentials = verifyAdminCredentials;
window.showEmployeeManagementModal = showEmployeeManagementModal;
window.showCreateAccountModal = showCreateAccountModal;
window.updateAccountPermissions = updateAccountPermissions;
window.createEmployeeAccount = createEmployeeAccount;
window.logout = logout;

// Edit Employee Account
function editEmployeeAccount(employeeId) {
    const employee = employees.find(emp => emp.id === employeeId);
    if (!employee) return;

    const modalHTML = `
        <div id="modalOverlay" class="modal-overlay">
            <div class="modal">
                <div class="modal-header">
                    <h3><i class="fas fa-user-edit"></i> تعديل حساب الموظف المكون</h3>
                    <button class="close-btn" onclick="closeModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="edit-account-form">
                        <div class="form-section">
                            <h4><i class="fas fa-user"></i> المعلومات الشخصية</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label>الاسم الكامل *</label>
                                    <input type="text" id="editAccountName" value="${employee.name}" required>
                                </div>
                                <div class="form-group">
                                    <label>رقم الهاتف *</label>
                                    <input type="tel" id="editAccountPhone" value="${employee.phone}" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label>البريد الإلكتروني</label>
                                <input type="email" id="editAccountEmail" value="${employee.email || ''}">
                            </div>
                        </div>

                        <div class="form-section">
                            <h4><i class="fas fa-key"></i> بيانات تسجيل الدخول</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label>اسم المستخدم *</label>
                                    <input type="text" id="editAccountUsername" value="${employee.username}" required>
                                    <small class="form-help">يجب أن يكون فريداً ولا يحتوي على مسافات</small>
                                </div>
                                <div class="form-group">
                                    <label>كلمة المرور الجديدة</label>
                                    <input type="password" id="editAccountPassword" placeholder="اتركها فارغة للاحتفاظ بالحالية">
                                    <small class="form-help">أدخل كلمة مرور جديدة فقط إذا كنت تريد تغييرها</small>
                                </div>
                            </div>
                        </div>

                        <div class="form-section">
                            <h4><i class="fas fa-briefcase"></i> المنصب والصلاحيات</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label>المنصب *</label>
                                    <select id="editAccountRole" onchange="updateEditAccountPermissions()" required>
                                        <option value="employee" ${employee.role === 'employee' ? 'selected' : ''}>موظف عادي</option>
                                        <option value="cashier" ${employee.role === 'cashier' ? 'selected' : ''}>كاشير</option>
                                        <option value="sales" ${employee.role === 'sales' ? 'selected' : ''}>مندوب مبيعات</option>
                                        <option value="inventory" ${employee.role === 'inventory' ? 'selected' : ''}>مسؤول مخزون</option>
                                        <option value="manager" ${employee.role === 'manager' ? 'selected' : ''}>مدير فرع</option>
                                        <option value="admin" ${employee.role === 'admin' ? 'selected' : ''}>مدير النظام</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>الراتب (دينار)</label>
                                    <input type="number" id="editAccountSalary" value="${employee.salary}" min="0">
                                </div>
                            </div>

                            <div class="form-group">
                                <label>الصلاحيات *</label>
                                <div class="permissions-grid-advanced" id="editAccountPermissions">
                                    <label class="permission-item-advanced">
                                        <input type="checkbox" value="cashier" ${employee.permissions.includes('cashier') ? 'checked' : ''}>
                                        <span class="permission-label">
                                            <i class="fas fa-cash-register"></i>
                                            نظام الكاشير
                                        </span>
                                        <small>إدارة المبيعات والفواتير</small>
                                    </label>
                                    <label class="permission-item-advanced">
                                        <input type="checkbox" value="sales" ${employee.permissions.includes('sales') ? 'checked' : ''}>
                                        <span class="permission-label">
                                            <i class="fas fa-chart-line"></i>
                                            إدارة المبيعات
                                        </span>
                                        <small>عرض وتتبع المبيعات</small>
                                    </label>
                                    <label class="permission-item-advanced">
                                        <input type="checkbox" value="products" ${employee.permissions.includes('products') ? 'checked' : ''}>
                                        <span class="permission-label">
                                            <i class="fas fa-box"></i>
                                            إدارة المنتجات
                                        </span>
                                        <small>إضافة وتعديل المنتجات</small>
                                    </label>
                                    <label class="permission-item-advanced">
                                        <input type="checkbox" value="inventory" ${employee.permissions.includes('inventory') ? 'checked' : ''}>
                                        <span class="permission-label">
                                            <i class="fas fa-warehouse"></i>
                                            إدارة المخزون
                                        </span>
                                        <small>تتبع الكميات والمخزون</small>
                                    </label>
                                    <label class="permission-item-advanced">
                                        <input type="checkbox" value="reports" ${employee.permissions.includes('reports') ? 'checked' : ''}>
                                        <span class="permission-label">
                                            <i class="fas fa-chart-bar"></i>
                                            التقارير
                                        </span>
                                        <small>عرض التقارير والإحصائيات</small>
                                    </label>
                                    <label class="permission-item-advanced">
                                        <input type="checkbox" value="settings" ${employee.permissions.includes('settings') ? 'checked' : ''}>
                                        <span class="permission-label">
                                            <i class="fas fa-cog"></i>
                                            الإعدادات
                                        </span>
                                        <small>تعديل إعدادات النظام</small>
                                    </label>
                                    <label class="permission-item-advanced">
                                        <input type="checkbox" value="admin" ${employee.permissions.includes('admin') ? 'checked' : ''}>
                                        <span class="permission-label">
                                            <i class="fas fa-crown"></i>
                                            صلاحيات المدير
                                        </span>
                                        <small>وصول كامل للنظام</small>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="form-section">
                            <h4><i class="fas fa-info-circle"></i> معلومات إضافية</h4>
                            <div class="info-grid">
                                <div class="info-item">
                                    <label>تاريخ التوظيف:</label>
                                    <input type="date" id="editAccountHireDate" value="${employee.hireDate}">
                                </div>
                                <div class="info-item">
                                    <label>حالة الحساب:</label>
                                    <select id="editAccountStatus">
                                        <option value="active" ${employee.status === 'active' ? 'selected' : ''}>نشط</option>
                                        <option value="inactive" ${employee.status === 'inactive' ? 'selected' : ''}>معطل</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="closeModal()">إلغاء</button>
                    <button class="btn btn-success" onclick="updateEmployeeAccount('${employeeId}')">
                        <i class="fas fa-save"></i>
                        حفظ التغييرات
                    </button>
                </div>
            </div>
        </div>
    `;

    document.getElementById('modalContainer').innerHTML = modalHTML;
    document.getElementById('modalOverlay').style.display = 'flex';
}

// Update permissions for edit form
function updateEditAccountPermissions() {
    const role = document.getElementById('editAccountRole').value;
    const checkboxes = document.querySelectorAll('#editAccountPermissions input[type="checkbox"]');

    // Don't auto-change permissions when editing, just update role
    // User can manually adjust permissions
}

// Update Employee Account
function updateEmployeeAccount(employeeId) {
    const employee = employees.find(emp => emp.id === employeeId);
    if (!employee) return;

    const name = document.getElementById('editAccountName').value.trim();
    const phone = document.getElementById('editAccountPhone').value.trim();
    const email = document.getElementById('editAccountEmail').value.trim();
    const username = document.getElementById('editAccountUsername').value.trim();
    const password = document.getElementById('editAccountPassword').value;
    const role = document.getElementById('editAccountRole').value;
    const salary = parseInt(document.getElementById('editAccountSalary').value) || employee.salary;
    const hireDate = document.getElementById('editAccountHireDate').value;
    const status = document.getElementById('editAccountStatus').value;

    // Validation
    if (!name || !phone || !username || !role) {
        alert('يرجى ملء جميع الحقول المطلوبة (*)');
        return;
    }

    // Check username format
    if (!/^[a-zA-Z0-9_]+$/.test(username)) {
        alert('اسم المستخدم يجب أن يحتوي على حروف وأرقام فقط بدون مسافات');
        return;
    }

    // Check if username already exists (excluding current employee)
    if (employees.find(emp => emp.username === username && emp.id !== employeeId)) {
        alert('اسم المستخدم موجود بالفعل، يرجى اختيار اسم آخر');
        return;
    }

    // Get selected permissions
    const permissions = Array.from(document.querySelectorAll('#editAccountPermissions input[type="checkbox"]:checked'))
        .map(cb => cb.value);

    if (permissions.length === 0) {
        alert('يرجى اختيار صلاحية واحدة على الأقل');
        return;
    }

    // Update employee data
    employee.name = name;
    employee.phone = phone;
    employee.email = email;
    employee.username = username;
    if (password) employee.password = password; // Only update if new password provided
    employee.role = role;
    employee.permissions = permissions;
    employee.salary = salary;
    employee.hireDate = hireDate;
    employee.status = status;
    employee.updatedAt = new Date().toISOString();
    employee.updatedBy = currentUser ? currentUser.name : 'المدير';

    saveToStorage('employees', employees);

    alert(`تم تحديث حساب الموظف ${name} المكون بنجاح! ✅`);

    closeModal();
    showEmployeeManagementModal();
}

// Toggle Employee Account Status
function toggleEmployeeAccount(employeeId) {
    const employee = employees.find(emp => emp.id === employeeId);
    if (!employee) return;

    const newStatus = employee.status === 'active' ? 'inactive' : 'active';
    const action = newStatus === 'active' ? 'تفعيل' : 'تعطيل';

    if (confirm(`هل أنت متأكد من ${action} حساب ${employee.name}؟`)) {
        employee.status = newStatus;
        employee.updatedAt = new Date().toISOString();
        employee.updatedBy = currentUser ? currentUser.name : 'المدير';

        saveToStorage('employees', employees);
        showEmployeeManagementModal();

        alert(`تم ${action} حساب الموظف ${employee.name} المكون بنجاح`);
    }
}

// Delete Employee Account
function deleteEmployeeAccount(employeeId) {
    const employee = employees.find(emp => emp.id === employeeId);
    if (!employee) return;

    if (employee.role === 'admin') {
        alert('لا يمكن حذف حساب المدير');
        return;
    }

    if (confirm(`هل أنت متأكد من حذف حساب ${employee.name}؟\n\n⚠️ تحذير: هذا الإجراء لا يمكن التراجع عنه.\nسيتم حذف جميع بيانات الموظف نهائياً.`)) {
        employees = employees.filter(emp => emp.id !== employeeId);
        saveToStorage('employees', employees);

        showEmployeeManagementModal();
        alert(`تم حذف حساب الموظف ${employee.name} المكون نهائياً`);
    }
}

// Export Employee Accounts
function exportEmployeeAccounts() {
    const exportData = {
        accounts: employees.map(emp => ({
            name: emp.name,
            username: emp.username,
            role: emp.role,
            permissions: emp.permissions,
            phone: emp.phone,
            email: emp.email,
            status: emp.status,
            hireDate: emp.hireDate,
            salary: emp.salary
        })),
        exportDate: new Date().toISOString(),
        totalAccounts: employees.length,
        activeAccounts: employees.filter(emp => emp.status === 'active').length,
        exportedBy: currentUser ? currentUser.name : 'المدير'
    };

    const jsonString = JSON.stringify(exportData, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = `employee-accounts-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    alert('تم تصدير حسابات الموظفين المكونة بنجاح');
}

// Add new functions to global scope
window.editEmployeeAccount = editEmployeeAccount;
window.updateEditAccountPermissions = updateEditAccountPermissions;
window.updateEmployeeAccount = updateEmployeeAccount;
window.toggleEmployeeAccount = toggleEmployeeAccount;
window.deleteEmployeeAccount = deleteEmployeeAccount;
window.exportEmployeeAccounts = exportEmployeeAccounts;

// Helper function to get role display name
function getRoleDisplayName(role) {
    const roleNames = {
        'admin': 'مدير النظام',
        'manager': 'مدير فرع',
        'cashier': 'كاشير',
        'sales': 'مندوب مبيعات',
        'inventory': 'مسؤول مخزون',
        'employee': 'موظف عادي'
    };
    return roleNames[role] || role;
}

window.getRoleDisplayName = getRoleDisplayName;

// Enhanced closeModal function
function closeModal() {
    console.log('🔒 Closing modal...');

    const modal = document.getElementById('modalOverlay');
    if (modal) {
        modal.style.display = 'none';
        setTimeout(() => {
            modal.remove();
        }, 100);
    }

    // Also clear modal container
    const container = document.getElementById('modalContainer');
    if (container) {
        container.innerHTML = '';
    }

    console.log('✅ Modal closed');
}

window.closeModal = closeModal;

// Real-time Save Status Monitor
function createSaveStatusIndicator() {
    if (document.getElementById('saveStatus')) return;

    const indicator = document.createElement('div');
    indicator.id = 'saveStatus';
    indicator.className = 'save-status-indicator';
    indicator.innerHTML = `
        <div class="save-status-content">
            <i class="fas fa-check-circle"></i>
        </div>
    `;
    document.body.appendChild(indicator);
}

function updateSaveStatus(status, message) {
    const indicator = document.getElementById('saveStatus');
    if (!indicator) {
        createSaveStatusIndicator();
        return updateSaveStatus(status, message);
    }

    const content = indicator.querySelector('.save-status-content');

    // Remove existing classes
    indicator.className = 'save-status-indicator';

    switch (status) {
        case 'saving':
            indicator.classList.add('saving');
            content.innerHTML = `
                <i class="fas fa-spinner fa-spin"></i>
                <span>جاري الحفظ...</span>
            `;
            break;
        case 'success':
            indicator.classList.add('success');
            content.innerHTML = `
                <i class="fas fa-check-circle"></i>
                <span>تم الحفظ بنجاح</span>
            `;
            setTimeout(() => {
                indicator.classList.add('fade-out');
                setTimeout(() => {
                    indicator.classList.remove('fade-out', 'success');
                    content.innerHTML = `
                        <i class="fas fa-check-circle"></i>
                    `;
                }, 2000);
            }, 3000);
            break;
        case 'error':
            indicator.classList.add('error');
            content.innerHTML = `
                <i class="fas fa-exclamation-triangle"></i>
                <span>${message || 'فشل في الحفظ'}</span>
            `;
            break;
        case 'warning':
            indicator.classList.add('warning');
            content.innerHTML = `
                <i class="fas fa-exclamation-circle"></i>
                <span>${message || 'تحذير في الحفظ'}</span>
            `;
            break;
    }

    // Show indicator
    indicator.style.display = 'block';
}

// Auto-save functionality
let autoSaveTimeout;
function scheduleAutoSave(key, data) {
    clearTimeout(autoSaveTimeout);

    updateSaveStatus('saving');

    autoSaveTimeout = setTimeout(() => {
        const success = saveToStorage(key, data);
        if (success) {
            updateSaveStatus('success');
        } else {
            updateSaveStatus('error', 'فشل في الحفظ التلقائي');
        }
    }, 1000); // Save after 1 second of inactivity
}

// Enhanced save functions with status updates
function saveProductWithStatus(product) {
    updateSaveStatus('saving');

    const success = saveProductSafely(product);
    if (success) {
        updateSaveStatus('success');
        return true;
    } else {
        updateSaveStatus('error', 'فشل في حفظ المنتج');
        return false;
    }
}

function saveCategoryWithStatus(category) {
    updateSaveStatus('saving');

    const success = saveCategorySafely(category);
    if (success) {
        updateSaveStatus('success');
        return true;
    } else {
        updateSaveStatus('error', 'فشل في حفظ الفئة');
        return false;
    }
}

// Initialize save status on page load
document.addEventListener('DOMContentLoaded', function() {
    createSaveStatusIndicator();

    // Test storage on load
    try {
        localStorage.setItem('test', 'test');
        localStorage.removeItem('test');
        updateSaveStatus('success', 'النظام جاهز');
    } catch (error) {
        updateSaveStatus('warning', 'مشكلة في التخزين - سيتم استخدام النسخ الاحتياطية');
    }
});

function showStorageErrorDialog(error, key) {
    const errorMessage = `
🔧 مشكلة تقنية في التخزين:

• النوع: ${error.name}
• البيانات: ${key}
• السبب: ${error.message}

💡 الحلول المقترحة:
• أعد تحميل الصفحة
• امسح ذاكرة التخزين المؤقت
• استخدم متصفح حديث
• تحقق من مساحة القرص الصلب

النظام سيحاول الحفظ بطريقة بديلة...`;

    console.warn(errorMessage);

    // Show user-friendly message
    const userMessage = `
حدثت مشكلة تقنية بسيطة في التخزين.

النظام سيحاول حل المشكلة تلقائياً.
إذا استمرت المشكلة، يرجى إعادة تحميل الصفحة.

هل تريد المتابعة؟`;

    return confirm(userMessage);
}

async function tryAdvancedStorage(key, data) {
    try {
        console.log('🚀 Using advanced storage for large data...');

        // If we have IndexedDB available, use it
        if (advancedStorage.useIndexedDB && advancedStorage.db) {
            const transaction = advancedStorage.db.transaction(['products'], 'readwrite');
            const store = transaction.objectStore('products');

            // Store data in IndexedDB instead of localStorage
            const record = {
                id: key,
                data: data,
                size: JSON.stringify(data).length,
                createdAt: new Date().toISOString()
            };

            await store.put(record);
            console.log(`✅ Successfully saved ${key} to IndexedDB`);

            // Keep a small reference in localStorage
            localStorage.setItem(`${key}_ref`, JSON.stringify({
                type: 'indexeddb',
                id: key,
                size: record.size,
                timestamp: record.createdAt
            }));

            return true;
        } else {
            // Fallback: try to compress data
            console.log('🗜️ Attempting data compression...');
            const compressedData = compressData(data);

            if (compressedData.length < JSON.stringify(data).length * 0.8) {
                localStorage.setItem(key, compressedData);
                console.log(`✅ Successfully saved compressed ${key}`);
                return true;
            }
        }

        console.warn('⚠️ Could not save with advanced storage');
        return false;
    } catch (error) {
        console.error('❌ Advanced storage failed:', error);
        return false;
    }
}

function compressData(data) {
    try {
        // Simple compression: remove unnecessary whitespace and optimize structure
        const optimized = JSON.stringify(data, null, 0);

        // Remove redundant data if it's an array of products
        if (Array.isArray(data) && data.length > 0 && data[0].id) {
            const compressed = data.map(item => {
                const compressed = { ...item };

                // Remove large image data if present
                if (compressed.image && compressed.image.length > 1000) {
                    compressed.image = 'compressed_' + compressed.id;
                }

                return compressed;
            });

            return JSON.stringify(compressed);
        }

        return optimized;
    } catch (error) {
        console.error('Compression failed:', error);
        return JSON.stringify(data);
    }
}

// Loading indicator functions
function showLoadingIndicator(message = 'جاري التحميل...') {
    const existingLoader = document.getElementById('loadingIndicator');
    if (existingLoader) {
        existingLoader.remove();
    }

    const loader = document.createElement('div');
    loader.id = 'loadingIndicator';
    loader.innerHTML = `
        <div class="loading-overlay">
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <p>${message}</p>
            </div>
        </div>
    `;
    document.body.appendChild(loader);
}

function hideLoadingIndicator() {
    const loader = document.getElementById('loadingIndicator');
    if (loader) {
        loader.remove();
    }
}

// Ultra-enhanced product loading with virtual scrolling for 50000+ products
async function loadProductsWithImages() {
    const products = loadFromStorage('adminProducts', getDefaultProducts());
    const productsGrid = document.getElementById('productsGrid');

    if (products.length === 0) {
        productsGrid.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-box"></i>
                <p>لا توجد منتجات حتى الآن</p>
            </div>
        `;
        return;
    }

    showLoadingIndicator(`جاري تحميل ${products.length} منتج مع التحسين الفائق...`);

    try {
        // Sort products by creation date (newest first)
        const sortedProducts = products.sort((a, b) => {
            const dateA = new Date(a.createdAt || 0);
            const dateB = new Date(b.createdAt || 0);
            return dateB - dateA;
        });

        // Prepare products for virtual scrolling
        const preparedProducts = sortedProducts.map(product => ({
            ...product,
            imageId: product.imageType === 'advanced' && product.image && product.image.startsWith('img_')
                ? product.image
                : null
        }));

        // Use virtual scrolling for large datasets
        if (products.length > 100) {
            console.log(`🚀 Using virtual scrolling for ${products.length} products`);

            // Destroy existing virtual scroll manager
            if (virtualScrollManager) {
                virtualScrollManager.destroy();
            }

            // Setup container for virtual scrolling
            productsGrid.innerHTML = '';
            productsGrid.className = 'products-grid virtual-scroll';

            // Initialize virtual scroll manager
            virtualScrollManager = new VirtualScrollManager(productsGrid, 320, 15);
            virtualScrollManager.setItems(preparedProducts);

            hideLoadingIndicator();

            // Preload first batch of thumbnails
            const firstBatch = preparedProducts.slice(0, 20).filter(p => p.imageId);
            if (firstBatch.length > 0) {
                console.log('🖼️ Preloading first batch of thumbnails...');
                advancedStorage.preloadImages(firstBatch.map(p => p.imageId), 'thumbnail');
            }

        } else {
            // Use regular loading for smaller datasets
            console.log(`📦 Using regular loading for ${products.length} products`);
            await loadProductsRegular(preparedProducts);
        }

        // Update performance metrics
        updatePerformanceMetrics(products.length);

    } catch (error) {
        console.error('Error loading ultra products:', error);
        hideLoadingIndicator();
        // Fallback to legacy loading
        loadProductsLegacy();
    }
}

// Regular loading for smaller datasets
async function loadProductsRegular(products) {
    const productsGrid = document.getElementById('productsGrid');
    productsGrid.className = 'products-grid regular';

    const productCards = await Promise.all(products.slice(0, 50).map(async (product) => {
        let imageUrl = product.image;

        // Load thumbnail for advanced storage images
        if (product.imageId) {
            try {
                const thumbnail = await advancedStorage.loadImage(product.imageId, 'thumbnail');
                if (thumbnail) {
                    imageUrl = thumbnail;
                } else {
                    imageUrl = 'https://via.placeholder.com/300x200/f8f9fa/6c757d?text=Image+Not+Found';
                }
            } catch (error) {
                console.warn('Failed to load thumbnail:', product.imageId, error);
                imageUrl = 'https://via.placeholder.com/300x200/f8f9fa/6c757d?text=Image+Error';
            }
        }

        const stockStatus = product.stock <= product.minStock ? 'low-stock' : 'in-stock';
        const stockIcon = product.stock <= product.minStock ? 'fa-exclamation-triangle' : 'fa-check-circle';

        return `
            <div class="product-card ${stockStatus}">
                <div class="product-image">
                    <img src="${imageUrl}" alt="${product.name}" onerror="this.src='https://via.placeholder.com/300x200/f8f9fa/6c757d?text=No+Image'">
                    <div class="product-actions">
                        <button class="btn-icon" onclick="editProduct('${product.id}')" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-icon btn-danger" onclick="deleteProduct('${product.id}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    ${product.imageType === 'advanced' ? '<div class="advanced-storage-badge"><i class="fas fa-rocket"></i> Ultra</div>' : ''}
                </div>
                <div class="product-info">
                    <h3 class="product-name">${product.name}</h3>
                    <p class="product-category">${product.category}</p>
                    <p class="product-price">${product.price} دينار</p>
                    <div class="product-stock">
                        <i class="fas ${stockIcon}"></i>
                        <span>المخزون: ${product.stock}</span>
                    </div>
                    ${product.description ? `<p class="product-description">${product.description}</p>` : ''}
                </div>
            </div>
        `;
    }));

    productsGrid.innerHTML = productCards.join('');

    // Show load more button if there are more products
    if (products.length > 50) {
        const loadMoreBtn = document.createElement('div');
        loadMoreBtn.className = 'load-more-container';
        loadMoreBtn.innerHTML = `
            <button class="btn btn-primary" onclick="loadMoreProducts()">
                <i class="fas fa-plus"></i>
                تحميل المزيد (${products.length - 50} منتج متبقي)
            </button>
        `;
        productsGrid.appendChild(loadMoreBtn);
    }

    hideLoadingIndicator();
}

// Smart storage statistics with accurate monitoring
async function updateStorageStats() {
    try {
        const storageInfo = await monitorStorage();
        const stats = await advancedStorage.getStorageStats();
        const performance = await getPerformanceMetrics();

        const usedMB = (storageInfo.used / 1024 / 1024).toFixed(2);
        const availableGB = (storageInfo.available / 1024 / 1024 / 1024).toFixed(1);

        console.log(`📊 Smart Storage Stats: ${stats.totalImages} images, ${usedMB} MB used of ${availableGB} GB available`);

        // Update dashboard if visible
        const statsElement = document.getElementById('storageStats');
        if (statsElement) {
            // Determine status based on actual usage, not arbitrary limits
            let statusClass = 'excellent';
            let statusText = 'ممتاز';

            if (storageInfo.percentage > 70) {
                statusClass = 'good';
                statusText = 'جيد';
            }
            if (storageInfo.percentage > 90) {
                statusClass = 'normal';
                statusText = 'محدود';
            }

            statsElement.innerHTML = `
                <div class="ultra-stats-grid">
                    <div class="stat-item primary">
                        <i class="fas fa-images"></i>
                        <div>
                            <span class="stat-number">${stats.totalImages.toLocaleString()}</span>
                            <span class="stat-label">صورة محفوظة</span>
                        </div>
                    </div>
                    <div class="stat-item success">
                        <i class="fas fa-hdd"></i>
                        <div>
                            <span class="stat-number">${usedMB} MB</span>
                            <span class="stat-label">مساحة مستخدمة</span>
                        </div>
                    </div>
                    <div class="stat-item info">
                        <i class="fas fa-database"></i>
                        <div>
                            <span class="stat-number">${availableGB} GB</span>
                            <span class="stat-label">مساحة متاحة</span>
                        </div>
                    </div>
                    <div class="stat-item warning">
                        <i class="fas fa-percentage"></i>
                        <div>
                            <span class="stat-number">${storageInfo.percentage.toFixed(2)}%</span>
                            <span class="stat-label">نسبة الاستخدام</span>
                        </div>
                    </div>
                </div>
                <div class="performance-indicators">
                    <div class="indicator ${statusClass}">
                        <span>حالة التخزين: ${statusText}</span>
                    </div>
                    <div class="indicator ${stats.totalImages > 10000 ? 'excellent' : stats.totalImages > 1000 ? 'good' : 'normal'}">
                        <span>سعة النظام: ${getCapacityLevel(stats.totalImages)}</span>
                    </div>
                    <div class="indicator excellent">
                        <span>النظام: فائق التطور 🚀</span>
                    </div>
                </div>
                <div class="storage-health">
                    <small style="color: #27ae60; font-weight: 600;">
                        ✅ النظام يعمل بكفاءة عالية ويدعم آلاف الصور بأمان
                    </small>
                </div>
            `;
        }
    } catch (error) {
        console.error('Error updating smart storage stats:', error);

        // Show fallback stats
        const statsElement = document.getElementById('storageStats');
        if (statsElement) {
            statsElement.innerHTML = `
                <div class="storage-error">
                    <i class="fas fa-exclamation-circle"></i>
                    <span>لا يمكن قراءة إحصائيات التخزين حالياً</span>
                    <button class="btn btn-sm btn-primary" onclick="updateStorageStats()">
                        <i class="fas fa-sync-alt"></i> إعادة المحاولة
                    </button>
                </div>
            `;
        }
    }
}

function getCapacityLevel(imageCount) {
    if (imageCount >= 40000) return 'فائقة (40K+)';
    if (imageCount >= 20000) return 'عالية (20K+)';
    if (imageCount >= 10000) return 'متوسطة (10K+)';
    if (imageCount >= 5000) return 'منخفضة (5K+)';
    return 'بداية';
}

async function getPerformanceMetrics() {
    try {
        if (!advancedStorage.db) return {};

        const transaction = advancedStorage.db.transaction(['performance'], 'readonly');
        const store = transaction.objectStore('performance');
        const request = store.get('metrics');

        return new Promise((resolve) => {
            request.onsuccess = () => {
                const result = request.result;
                resolve(result ? result.data : {});
            };
            request.onerror = () => resolve({});
        });
    } catch (error) {
        console.error('Error getting performance metrics:', error);
        return {};
    }
}

async function updatePerformanceMetrics(productCount) {
    try {
        if (!advancedStorage.db) return;

        const metrics = {
            id: 'metrics',
            data: {
                productCount,
                lastUpdate: new Date().toISOString(),
                cacheHitRate: Math.round((advancedStorage.imageCache.size / Math.max(productCount, 1)) * 100),
                avgLoadTime: performance.now() % 1000, // Simplified metric
                memoryUsage: navigator.deviceMemory || 'Unknown'
            }
        };

        const transaction = advancedStorage.db.transaction(['performance'], 'readwrite');
        const store = transaction.objectStore('performance');
        await store.put(metrics);

        console.log('📈 Performance metrics updated:', metrics.data);
    } catch (error) {
        console.error('Error updating performance metrics:', error);
    }
}

// Batch image operations
async function optimizeAllImages() {
    if (!confirm('هل تريد تحسين جميع الصور؟ قد يستغرق هذا وقتاً طويلاً.')) {
        return;
    }

    showLoadingIndicator('جاري تحسين الصور...');

    try {
        const products = loadFromStorage('adminProducts', []);
        let optimizedCount = 0;

        for (const product of products) {
            if (product.imageType === 'url' && product.image && product.image.startsWith('data:')) {
                // Convert base64 to optimized format
                const imageId = await advancedStorage.saveImage(product.image, product.id);
                product.image = imageId;
                product.imageType = 'advanced';
                optimizedCount++;
            }
        }

        if (optimizedCount > 0) {
            saveToStorage('adminProducts', products);
            await loadProductsWithImages();
            alert(`تم تحسين ${optimizedCount} صورة بنجاح!`);
        } else {
            alert('جميع الصور محسنة بالفعل!');
        }

        hideLoadingIndicator();
    } catch (error) {
        console.error('Error optimizing images:', error);
        hideLoadingIndicator();
        alert('حدث خطأ في تحسين الصور.');
    }
}

function loadFromStorage(key, defaultValue = []) {
    console.log(`📖 Attempting to load ${key}...`);

    // Method 1: Try localStorage first
    try {
        const data = localStorage.getItem(key);
        if (data) {
            const parsed = JSON.parse(data);
            console.log(`✅ SUCCESS: Loaded ${key} from localStorage (${parsed.length || Object.keys(parsed).length} items)`);
            return parsed;
        }
    } catch (error) {
        console.warn(`⚠️ localStorage failed for ${key}:`, error.message);
    }

    // Method 2: Try sessionStorage as backup
    try {
        const data = sessionStorage.getItem(key);
        if (data) {
            const parsed = JSON.parse(data);
            console.log(`✅ SUCCESS: Loaded ${key} from sessionStorage (${parsed.length || Object.keys(parsed).length} items)`);
            return parsed;
        }
    } catch (error) {
        console.warn(`⚠️ sessionStorage failed for ${key}:`, error.message);
    }

    // Method 3: Try IndexedDB
    try {
        const data = loadFromIndexedDBSimple(key);
        if (data) {
            console.log(`✅ SUCCESS: Loaded ${key} from IndexedDB`);
            return data;
        }
    } catch (error) {
        console.warn(`⚠️ IndexedDB failed for ${key}:`, error.message);
    }

    // Method 4: Try emergency storage
    if (window.emergencyStorage && window.emergencyStorage[key]) {
        console.log(`🆘 EMERGENCY: Loaded ${key} from memory`);
        return window.emergencyStorage[key];
    }

    console.log(`📝 Using default value for ${key}`);
    return defaultValue;
}

function clearStorage() {
    if (confirm('هل أنت متأكد من حذف جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه!')) {
        localStorage.clear();
        alert('تم حذف جميع البيانات بنجاح!');
        location.reload();
    }
}

// Reliable save functions for each section
function saveProductSafely(product) {
    console.log(`💾 Saving product: ${product.name}`);

    try {
        const products = loadFromStorage('adminProducts', []);
        products.push(product);

        const success = saveToStorage('adminProducts', products);
        if (success) {
            console.log(`✅ Product saved successfully: ${product.name}`);
            return true;
        } else {
            console.error(`❌ Failed to save product: ${product.name}`);
            return forceSave('adminProducts', products);
        }
    } catch (error) {
        console.error(`💥 Error in saveProductSafely:`, error);
        return false;
    }
}

function updateProductSafely(productId, updatedProduct) {
    console.log(`🔄 Updating product: ${productId}`);

    try {
        const products = loadFromStorage('adminProducts', []);
        const index = products.findIndex(p => p.id === productId);

        if (index !== -1) {
            products[index] = updatedProduct;

            const success = saveToStorage('adminProducts', products);
            if (success) {
                console.log(`✅ Product updated successfully: ${productId}`);
                return true;
            } else {
                console.error(`❌ Failed to update product: ${productId}`);
                return forceSave('adminProducts', products);
            }
        } else {
            console.error(`❌ Product not found: ${productId}`);
            return false;
        }
    } catch (error) {
        console.error(`💥 Error in updateProductSafely:`, error);
        return false;
    }
}

function saveCategorySafely(category) {
    console.log(`💾 Saving category: ${category.nameAr}`);

    try {
        const categories = loadFromStorage('categories', []);
        categories.push(category);

        const success = saveToStorage('categories', categories);
        if (success) {
            console.log(`✅ Category saved successfully: ${category.nameAr}`);
            return true;
        } else {
            console.error(`❌ Failed to save category: ${category.nameAr}`);
            return forceSave('categories', categories);
        }
    } catch (error) {
        console.error(`💥 Error in saveCategorySafely:`, error);
        return false;
    }
}

function updateCategorySafely(categoryId, updatedCategory) {
    console.log(`🔄 Updating category: ${categoryId}`);

    try {
        const categories = loadFromStorage('categories', []);
        const index = categories.findIndex(c => c.id === categoryId);

        if (index !== -1) {
            categories[index] = updatedCategory;

            const success = saveToStorage('categories', categories);
            if (success) {
                console.log(`✅ Category updated successfully: ${categoryId}`);
                return true;
            } else {
                console.error(`❌ Failed to update category: ${categoryId}`);
                return forceSave('categories', categories);
            }
        } else {
            console.error(`❌ Category not found: ${categoryId}`);
            return false;
        }
    } catch (error) {
        console.error(`💥 Error in updateCategorySafely:`, error);
        return false;
    }
}

// Notification system for main site
function notifyMainSite(action) {
    try {
        // Send message to main site window if it exists
        if (window.opener && !window.opener.closed) {
            window.opener.postMessage({
                type: 'ADMIN_UPDATE',
                action: action,
                timestamp: new Date().toISOString()
            }, '*');
            console.log(`📢 Notified main site: ${action}`);
        }

        // Also try to notify any other windows
        if (window.parent && window.parent !== window) {
            window.parent.postMessage({
                type: 'ADMIN_UPDATE',
                action: action,
                timestamp: new Date().toISOString()
            }, '*');
        }
    } catch (error) {
        console.log('Could not notify main site:', error);
    }
}

// Smart Storage monitoring and cleanup
async function monitorStorage() {
    try {
        let totalUsed = 0;
        let breakdown = {};

        // Check IndexedDB usage (more accurate for our system)
        if (advancedStorage.useIndexedDB && advancedStorage.db) {
            const stats = await advancedStorage.getStorageStats();
            totalUsed += stats.totalSize || 0;
            breakdown.indexedDB = stats.totalSize || 0;
        }

        // Check localStorage usage (only for metadata)
        try {
            const localStorageSize = JSON.stringify(localStorage).length;
            totalUsed += localStorageSize;
            breakdown.localStorage = localStorageSize;
        } catch (e) {
            breakdown.localStorage = 0;
        }

        // Estimate available storage (modern browsers support this)
        let availableStorage = 50 * 1024 * 1024 * 1024; // Default 50GB estimate
        if ('storage' in navigator && 'estimate' in navigator.storage) {
            try {
                const estimate = await navigator.storage.estimate();
                availableStorage = estimate.quota || availableStorage;
                console.log(`📊 Browser storage quota: ${(availableStorage / 1024 / 1024 / 1024).toFixed(2)}GB`);
            } catch (e) {
                console.log('📊 Using default storage estimate');
            }
        }

        const percentage = (totalUsed / availableStorage) * 100;

        console.log(`💾 Smart Storage usage: ${(totalUsed / 1024 / 1024).toFixed(2)}MB of ${(availableStorage / 1024 / 1024 / 1024).toFixed(2)}GB (${percentage.toFixed(3)}%)`);

        // Only warn if actually approaching limits (much more generous)
        if (percentage > 70) {
            console.warn('⚠️ Storage usage is getting high, but still plenty of space available.');
            if (percentage > 90) {
                console.warn('🚨 Storage usage is very high. Consider optimizing old images.');
                // Only show alert if really critical
                if (percentage > 95) {
                    showSmartStorageWarning(totalUsed, availableStorage, breakdown);
                }
            }
        }

        return {
            used: totalUsed,
            available: availableStorage,
            percentage: percentage,
            breakdown: breakdown
        };
    } catch (error) {
        console.error('Error monitoring storage:', error);
        return { used: 0, available: 0, percentage: 0, breakdown: {} };
    }
}

function showSmartStorageWarning(used, available, breakdown) {
    const usedMB = (used / 1024 / 1024).toFixed(2);
    const availableGB = (available / 1024 / 1024 / 1024).toFixed(2);
    const indexedDBMB = (breakdown.indexedDB / 1024 / 1024).toFixed(2);

    const message = `
📊 معلومات التخزين:
• المستخدم: ${usedMB} MB من ${availableGB} GB
• الصور: ${indexedDBMB} MB
• البيانات: ${((breakdown.localStorage || 0) / 1024).toFixed(2)} KB

💡 اقتراحات:
• استخدم "تحسين جميع الصور" لتوفير مساحة
• احذف المنتجات غير المستخدمة
• النظام يدعم آلاف الصور بأمان

هل تريد تحسين الصور الآن؟`;

    if (confirm(message)) {
        optimizeAllImages();
    }
}

// Smart storage information modal
async function showStorageInfo() {
    showLoadingIndicator('جاري تحليل مساحة التخزين...');

    try {
        const storageInfo = await monitorStorage();

        if (!storageInfo) {
            hideLoadingIndicator();
            alert('لا يمكن قراءة معلومات التخزين');
            return;
        }

        const { used, available, percentage, breakdown } = storageInfo;

        // Get detailed breakdown
        const detailedBreakdown = await getDetailedStorageBreakdown();

        hideLoadingIndicator();

        const usedMB = (used / 1024 / 1024).toFixed(2);
        const availableGB = (available / 1024 / 1024 / 1024).toFixed(2);
        const indexedDBMB = (breakdown.indexedDB / 1024 / 1024).toFixed(2);
        const localStorageKB = (breakdown.localStorage / 1024).toFixed(2);

        // Determine status color and message
        let statusColor = '#27ae60';
        let statusMessage = 'ممتاز - مساحة كافية جداً';
        let statusIcon = 'fa-check-circle';

        if (percentage > 70) {
            statusColor = '#f39c12';
            statusMessage = 'جيد - مساحة كافية';
            statusIcon = 'fa-info-circle';
        }
        if (percentage > 90) {
            statusColor = '#e74c3c';
            statusMessage = 'تحذير - مساحة محدودة';
            statusIcon = 'fa-exclamation-triangle';
        }

        const modal = createModal('📊 تحليل مساحة التخزين الذكي', `
            <div class="smart-storage-info">
                <div class="storage-status" style="background: ${statusColor}">
                    <i class="fas ${statusIcon}"></i>
                    <span>${statusMessage}</span>
                </div>

                <div class="storage-summary">
                    <h4>📈 الاستخدام الإجمالي</h4>
                    <div class="storage-bar">
                        <div class="storage-fill" style="width: ${Math.min(percentage, 100)}%; background: ${statusColor}"></div>
                    </div>
                    <p><strong>${usedMB} MB</strong> من <strong>${availableGB} GB</strong> متاحة (${percentage.toFixed(3)}%)</p>
                </div>

                <div class="storage-breakdown">
                    <h4>🗂️ تفصيل البيانات</h4>
                    <div class="breakdown-grid">
                        <div class="breakdown-item">
                            <i class="fas fa-images" style="color: #3498db;"></i>
                            <div>
                                <span class="item-name">الصور (IndexedDB)</span>
                                <span class="item-size">${indexedDBMB} MB</span>
                            </div>
                        </div>
                        <div class="breakdown-item">
                            <i class="fas fa-database" style="color: #9b59b6;"></i>
                            <div>
                                <span class="item-name">البيانات (localStorage)</span>
                                <span class="item-size">${localStorageKB} KB</span>
                            </div>
                        </div>
                        ${detailedBreakdown.map(item => `
                            <div class="breakdown-item">
                                <i class="fas fa-${item.icon}" style="color: ${item.color};"></i>
                                <div>
                                    <span class="item-name">${item.name}</span>
                                    <span class="item-size">${item.size} (${item.count} عنصر)</span>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <div class="storage-tips">
                    <h4>💡 نصائح التحسين</h4>
                    <ul>
                        <li>✅ النظام يدعم آلاف الصور بأمان</li>
                        <li>🗜️ الصور مضغوطة تلقائياً لتوفير المساحة</li>
                        <li>🚀 استخدم "تحسين جميع الصور" لتوفير مساحة إضافية</li>
                        <li>🧹 احذف المنتجات غير المستخدمة عند الحاجة</li>
                    </ul>
                </div>

                ${percentage > 85 ? `
                    <div class="storage-actions">
                        <h4>🔧 إجراءات مقترحة</h4>
                        <button class="btn btn-warning" onclick="optimizeAllImages(); closeModal();">
                            <i class="fas fa-compress-alt"></i>
                            تحسين جميع الصور
                        </button>
                        <button class="btn btn-info" onclick="showCleanupOptions();">
                            <i class="fas fa-broom"></i>
                            خيارات التنظيف
                        </button>
                    </div>
                ` : ''}
            </div>
        `, [
            { text: 'إغلاق', class: 'btn-secondary', onclick: 'closeModal()' },
            { text: 'تحديث الإحصائيات', class: 'btn-primary', onclick: 'updateStorageStats(); showStorageInfo();' }
        ]);

        showModal(modal);

    } catch (error) {
        hideLoadingIndicator();
        console.error('Error showing storage info:', error);
        alert('حدث خطأ في عرض معلومات التخزين');
    }
}

async function getDetailedStorageBreakdown() {
    const breakdown = [];

    try {
        const keys = ['adminProducts', 'categories', 'adminCoupons', 'storeSettings', 'whatsappSettings'];

        keys.forEach(key => {
            const data = localStorage.getItem(key);
            if (data) {
                const sizeKB = (data.length / 1024).toFixed(2);
                const parsed = JSON.parse(data);
                const count = Array.isArray(parsed) ? parsed.length : Object.keys(parsed).length;

                breakdown.push({
                    name: getKeyDisplayName(key),
                    size: `${sizeKB} KB`,
                    count: count,
                    icon: getKeyIcon(key),
                    color: getKeyColor(key)
                });
            }
        });

        // Add IndexedDB info if available
        if (advancedStorage.useIndexedDB && advancedStorage.db) {
            try {
                const stats = await advancedStorage.getStorageStats();
                if (stats.totalImages > 0) {
                    breakdown.push({
                        name: 'الصور المتقدمة',
                        size: `${(stats.totalSize / 1024 / 1024).toFixed(2)} MB`,
                        count: stats.totalImages,
                        icon: 'rocket',
                        color: '#8e44ad'
                    });
                }
            } catch (e) {
                console.warn('Could not get IndexedDB stats:', e);
            }
        }

        // Sort by actual size (convert to bytes for comparison)
        breakdown.sort((a, b) => {
            const sizeA = parseFloat(a.size) * (a.size.includes('MB') ? 1024 : 1);
            const sizeB = parseFloat(b.size) * (b.size.includes('MB') ? 1024 : 1);
            return sizeB - sizeA;
        });

    } catch (error) {
        console.error('Error getting detailed storage breakdown:', error);
    }

    return breakdown;
}

function getKeyDisplayName(key) {
    const names = {
        'adminProducts': 'المنتجات',
        'categories': 'الفئات',
        'adminCoupons': 'الكوبونات',
        'storeSettings': 'إعدادات المتجر',
        'whatsappSettings': 'إعدادات واتساب'
    };
    return names[key] || key;
}

function getKeyIcon(key) {
    const icons = {
        'adminProducts': 'box',
        'categories': 'th-large',
        'adminCoupons': 'ticket-alt',
        'storeSettings': 'cog',
        'whatsappSettings': 'whatsapp'
    };
    return icons[key] || 'file';
}

function getKeyColor(key) {
    const colors = {
        'adminProducts': '#3498db',
        'categories': '#e74c3c',
        'adminCoupons': '#f39c12',
        'storeSettings': '#95a5a6',
        'whatsappSettings': '#25d366'
    };
    return colors[key] || '#7f8c8d';
}

function showCleanupOptions() {
    const modal = createModal('تنظيف البيانات', `
        <div class="cleanup-options">
            <p>اختر البيانات التي تريد حذفها:</p>
            <div class="cleanup-list">
                <label>
                    <input type="checkbox" id="cleanProducts"> حذف جميع المنتجات
                </label>
                <label>
                    <input type="checkbox" id="cleanCategories"> حذف جميع الفئات
                </label>
                <label>
                    <input type="checkbox" id="cleanCoupons"> حذف جميع الكوبونات
                </label>
                <label>
                    <input type="checkbox" id="cleanSettings"> إعادة تعيين الإعدادات
                </label>
            </div>
            <div class="cleanup-warning">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه!
            </div>
        </div>
    `, [
        { text: 'إلغاء', class: 'btn-secondary', onclick: 'closeModal()' },
        { text: 'حذف البيانات المحددة', class: 'btn-danger', onclick: 'performCleanup()' }
    ]);

    showModal(modal);
}

// وظيفة تحميل بيانات إدارة الموظفين
function loadEmployeeManagement() {
    console.log('📋 تحميل بيانات إدارة الموظفين...');

    // التحقق من وجود الوظائف المطلوبة في admin.html
    if (typeof openEmployeeManagement === 'function') {
        openEmployeeManagement();
    } else if (typeof initializeEmployeeSystem === 'function') {
        initializeEmployeeSystem();
        if (typeof updateEmployeeStats === 'function') {
            updateEmployeeStats();
        }
        if (typeof loadAndDisplayEmployees === 'function') {
            loadAndDisplayEmployees();
        }
    } else {
        console.log('⚠️ وظائف إدارة الموظفين غير متاحة');
    }
}

// Export employee functions globally
window.showSimpleEmployeesModal = showSimpleEmployeesModal;
window.showAdminVerificationModal = showAdminVerificationModal;
window.verifyAdminCredentials = verifyAdminCredentials;
window.showEmployeeManagementModal = showEmployeeManagementModal;
window.showCreateAccountModal = showCreateAccountModal;
window.createEmployeeAccount = createEmployeeAccount;
window.editEmployeeAccount = editEmployeeAccount;
window.updateEmployeeAccount = updateEmployeeAccount;
window.toggleEmployeeAccount = toggleEmployeeAccount;
window.deleteEmployeeAccount = deleteEmployeeAccount;
window.exportEmployeeAccounts = exportEmployeeAccounts;
window.getRoleDisplayName = getRoleDisplayName;
window.loadEmployeeManagement = loadEmployeeManagement;
