<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم الإدارية</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #333;
            font-size: 2rem;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logout-btn {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 0.8rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .logout-btn:hover {
            background: #c0392b;
            transform: translateY(-2px);
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .dashboard-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            text-align: center;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .card-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #667eea;
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 1rem;
        }

        .card-description {
            color: #666;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }

        .card-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .card-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            text-decoration: none;
            color: white;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #666;
            font-weight: 600;
        }

        .quick-actions {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .quick-actions h2 {
            color: #333;
            margin-bottom: 1.5rem;
            text-align: center;
        }

        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
        }

        .action-btn {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            text-decoration: none;
            color: #333;
        }

        .action-btn:hover {
            background: #667eea;
            color: white;
            border-color: #667eea;
            transform: translateY(-2px);
            text-decoration: none;
        }

        .action-btn i {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            display: block;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .header {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div>
                <h1><i class="fas fa-tachometer-alt"></i> لوحة التحكم الإدارية</h1>
                <p id="userWelcome">مرحباً بك في نظام الإدارة</p>
            </div>
            <div class="user-info">
                <span id="currentUser">المستخدم</span>
                <button class="logout-btn" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                </button>
            </div>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="totalProducts">0</div>
                <div class="stat-label">إجمالي المنتجات</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalCategories">0</div>
                <div class="stat-label">الفئات</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalEmployees">0</div>
                <div class="stat-label">الموظفين</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalSales">0</div>
                <div class="stat-label">المبيعات اليوم</div>
            </div>
        </div>

        <!-- Main Dashboard -->
        <div class="dashboard-grid">
            <div class="dashboard-card">
                <div class="card-icon">
                    <i class="fas fa-box"></i>
                </div>
                <h3 class="card-title">إدارة المنتجات</h3>
                <p class="card-description">إضافة وتعديل وحذف المنتجات، إدارة المخزون والأسعار</p>
                <a href="#" class="card-btn" onclick="openProductsModal()">
                    <i class="fas fa-arrow-left"></i> إدارة المنتجات
                </a>
            </div>

            <div class="dashboard-card">
                <div class="card-icon">
                    <i class="fas fa-tags"></i>
                </div>
                <h3 class="card-title">إدارة الفئات</h3>
                <p class="card-description">تنظيم المنتجات في فئات، إضافة وتعديل الفئات</p>
                <a href="#" class="card-btn" onclick="openCategoriesModal()">
                    <i class="fas fa-arrow-left"></i> إدارة الفئات
                </a>
            </div>

            <div class="dashboard-card">
                <div class="card-icon">
                    <i class="fas fa-users"></i>
                </div>
                <h3 class="card-title">إدارة الموظفين</h3>
                <p class="card-description">إضافة موظفين جدد، تحديد الصلاحيات، إدارة الحسابات</p>
                <a href="#" class="card-btn" onclick="openEmployeesModal()">
                    <i class="fas fa-arrow-left"></i> إدارة الموظفين
                </a>
            </div>

            <div class="dashboard-card">
                <div class="card-icon">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <h3 class="card-title">التقارير</h3>
                <p class="card-description">عرض تقارير المبيعات والمخزون والأرباح</p>
                <a href="#" class="card-btn" onclick="openReportsModal()">
                    <i class="fas fa-arrow-left"></i> عرض التقارير
                </a>
            </div>

            <div class="dashboard-card">
                <div class="card-icon">
                    <i class="fas fa-cog"></i>
                </div>
                <h3 class="card-title">الإعدادات</h3>
                <p class="card-description">إعدادات النظام العامة، إدارة الموقع والمعلومات</p>
                <a href="#" class="card-btn" onclick="openSettingsModal()">
                    <i class="fas fa-arrow-left"></i> الإعدادات
                </a>
            </div>

            <div class="dashboard-card">
                <div class="card-icon">
                    <i class="fas fa-cash-register"></i>
                </div>
                <h3 class="card-title">نظام الكاشير</h3>
                <p class="card-description">الانتقال إلى نظام نقاط البيع والكاشير</p>
                <a href="cashier.html" class="card-btn">
                    <i class="fas fa-arrow-left"></i> فتح الكاشير
                </a>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <h2><i class="fas fa-bolt"></i> إجراءات سريعة</h2>
            <div class="actions-grid">
                <a href="#" class="action-btn" onclick="addProduct()">
                    <i class="fas fa-plus"></i>
                    إضافة منتج
                </a>
                <a href="#" class="action-btn" onclick="addEmployee()">
                    <i class="fas fa-user-plus"></i>
                    إضافة موظف
                </a>
                <a href="#" class="action-btn" onclick="viewSales()">
                    <i class="fas fa-chart-line"></i>
                    مبيعات اليوم
                </a>
                <a href="#" class="action-btn" onclick="checkStock()">
                    <i class="fas fa-warehouse"></i>
                    فحص المخزون
                </a>
                <a href="index.html" class="action-btn">
                    <i class="fas fa-globe"></i>
                    الموقع الرئيسي
                </a>
                <a href="#" class="action-btn" onclick="backup()">
                    <i class="fas fa-download"></i>
                    نسخ احتياطي
                </a>
            </div>
        </div>
    </div>

    <script>
        // التحقق من تسجيل الدخول
        document.addEventListener('DOMContentLoaded', function() {
            checkAuth();
            loadStats();
            updateUserInfo();
        });

        function checkAuth() {
            // تم إزالة التحقق من تسجيل الدخول - الوصول مباشر
            console.log('✅ الوصول مباشر للوحة التحكم');
        }

        function updateUserInfo() {
            const userSession = sessionStorage.getItem('userSession');
            if (userSession) {
                try {
                    const user = JSON.parse(userSession);
                    document.getElementById('currentUser').textContent = user.name || user.username;
                    document.getElementById('userWelcome').textContent = `مرحباً ${user.name || user.username}`;
                } catch (error) {
                    console.error('خطأ في قراءة معلومات المستخدم');
                }
            }
        }

        function loadStats() {
            // تحميل الإحصائيات
            const products = JSON.parse(localStorage.getItem('adminProducts')) || [];
            const categories = JSON.parse(localStorage.getItem('adminCategories')) || [];
            const employees = JSON.parse(localStorage.getItem('employees')) || [];
            const sales = JSON.parse(localStorage.getItem('cashierSales')) || [];

            document.getElementById('totalProducts').textContent = products.length;
            document.getElementById('totalCategories').textContent = categories.length;
            document.getElementById('totalEmployees').textContent = employees.length;
            
            // حساب مبيعات اليوم
            const today = new Date().toDateString();
            const todaySales = sales.filter(sale => new Date(sale.date).toDateString() === today);
            document.getElementById('totalSales').textContent = todaySales.length;
        }

        function logout() {
            if (confirm('هل تريد الانتقال إلى الموقع الرئيسي؟')) {
                window.location.href = 'index.html';
            }
        }

        // وظائف النوافذ المنبثقة (يمكن تطويرها لاحقاً)
        function openProductsModal() {
            alert('ميزة إدارة المنتجات قيد التطوير');
        }

        function openCategoriesModal() {
            alert('ميزة إدارة الفئات قيد التطوير');
        }

        function openEmployeesModal() {
            alert('ميزة إدارة الموظفين قيد التطوير');
        }

        function openReportsModal() {
            alert('ميزة التقارير قيد التطوير');
        }

        function openSettingsModal() {
            alert('ميزة الإعدادات قيد التطوير');
        }

        function addProduct() {
            alert('ميزة إضافة منتج قيد التطوير');
        }

        function addEmployee() {
            alert('ميزة إضافة موظف قيد التطوير');
        }

        function viewSales() {
            alert('ميزة عرض المبيعات قيد التطوير');
        }

        function checkStock() {
            alert('ميزة فحص المخزون قيد التطوير');
        }

        function backup() {
            // تصدير البيانات كنسخة احتياطية
            const data = {
                products: JSON.parse(localStorage.getItem('adminProducts')) || [],
                categories: JSON.parse(localStorage.getItem('adminCategories')) || [],
                employees: JSON.parse(localStorage.getItem('employees')) || [],
                sales: JSON.parse(localStorage.getItem('cashierSales')) || []
            };

            const dataStr = JSON.stringify(data, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `backup_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            
            alert('تم تصدير النسخة الاحتياطية بنجاح');
        }
    </script>
</body>
</html>
