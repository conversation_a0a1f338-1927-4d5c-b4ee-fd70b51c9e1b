/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: #f5f6fa;
    direction: rtl;
    overflow-x: hidden;
}

/* Sidebar Styles */
.sidebar {
    position: fixed;
    top: 0;
    right: 0;
    width: 280px;
    height: 100vh;
    background: #2c3e50;
    color: white;
    z-index: 1000;
    transition: transform 0.3s ease;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid #34495e;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.8rem;
}

.logo i {
    font-size: 1.8rem;
    color: #3498db;
}

.logo h2 {
    font-size: 1.2rem;
    font-weight: 700;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: background 0.3s ease;
}

.sidebar-toggle:hover {
    background: #34495e;
}

.sidebar-nav {
    flex: 1;
    padding: 0;
    display: flex;
    flex-direction: column;
    min-height: 0;
    overflow: hidden;
}

.nav-scroll-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 0;
}

.nav-scroll-up,
.nav-scroll-down {
    background: #34495e;
    color: #bdc3c7;
    padding: 0.6rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    font-size: 0.9rem;
    flex-shrink: 0;
}

.nav-scroll-up:hover,
.nav-scroll-down:hover {
    background: #2c3e50;
    color: white;
}

.nav-scroll-up {
    border-bottom: 1px solid #2c3e50;
}

.nav-scroll-down {
    border-top: 1px solid #2c3e50;
}

.nav-items-container {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    scroll-behavior: smooth;
    min-height: 0;
    max-height: calc(100vh - 200px);
}

.nav-items-container::-webkit-scrollbar {
    width: 4px;
}

.nav-items-container::-webkit-scrollbar-track {
    background: #34495e;
}

.nav-items-container::-webkit-scrollbar-thumb {
    background: #7f8c8d;
    border-radius: 2px;
}

.nav-items-container::-webkit-scrollbar-thumb:hover {
    background: #95a5a6;
}

.sidebar-nav ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    margin-bottom: 0.5rem;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    color: #bdc3c7;
    text-decoration: none;
    transition: all 0.3s ease;
    border-right: 3px solid transparent;
}

.nav-link:hover,
.nav-item.active .nav-link {
    background: #34495e;
    color: white;
    border-right-color: #3498db;
}

.nav-link i {
    font-size: 1.1rem;
    width: 20px;
}

.sidebar-footer {
    padding: 1rem;
    border-top: 1px solid #34495e;
    flex-shrink: 0;
    background: #2c3e50;
    margin-top: auto;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.storage-btn {
    width: 100%;
    background: #f39c12;
    color: white;
    border: none;
    padding: 0.7rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
    font-size: 0.85rem;
    white-space: nowrap;
    min-height: 40px;
    box-sizing: border-box;
}

.storage-btn:hover {
    background: #e67e22;
    transform: translateY(-1px);
}

.logout-btn {
    width: 100%;
    background: #e74c3c;
    color: white;
    border: none;
    padding: 0.8rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    transition: background 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
    font-size: 0.9rem;
    white-space: nowrap;
    min-height: 44px;
    box-sizing: border-box;
}

.logout-btn:hover {
    background: #c0392b;
}

.logout-btn i {
    font-size: 1rem;
}

.logout-btn span {
    display: inline-block;
}

/* Main Content */
.main-content {
    margin-right: 280px;
    min-height: 100vh;
    transition: margin-right 0.3s ease;
}

.admin-header {
    background: white;
    padding: 1rem 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #2c3e50;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
    flex-wrap: wrap;
}

.admin-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.admin-name {
    font-weight: 600;
    color: #2c3e50;
}

.admin-avatar {
    width: 40px;
    height: 40px;
    background: #3498db;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

/* Content Sections */
.content-section {
    display: none;
    padding: 2rem;
}

.content-section.active {
    display: block;
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.section-header h2 {
    color: #2c3e50;
    font-size: 1.8rem;
    font-weight: 700;
}

/* Buttons */
.btn-primary,
.btn-secondary {
    padding: 0.8rem 1.5rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-primary:hover {
    background: #2980b9;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background: #7f8c8d;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-card:nth-child(1) .stat-icon { background: #3498db; }
.stat-card:nth-child(2) .stat-icon { background: #f39c12; }
.stat-card:nth-child(3) .stat-icon { background: #e74c3c; }
.stat-card:nth-child(4) .stat-icon { background: #27ae60; }
.stat-card:nth-child(5) .stat-icon { background: #9b59b6; }

.stat-info h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.3rem;
}

.stat-info p {
    color: #7f8c8d;
    font-size: 0.9rem;
}

/* Dashboard Widgets */
.dashboard-widgets {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.widget {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.widget-header {
    padding: 1.5rem;
    border-bottom: 1px solid #ecf0f1;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.widget-header h3 {
    color: #2c3e50;
    font-weight: 600;
}

.widget-content {
    padding: 1.5rem;
}

.empty-state {
    text-align: center;
    color: #7f8c8d;
    padding: 2rem;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Products Grid */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.product-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.product-card:hover {
    transform: translateY(-2px);
}

.product-image {
    height: 200px;
    background: #ecf0f1;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-image .placeholder {
    font-size: 3rem;
    color: #bdc3c7;
}

.product-info {
    padding: 1.5rem;
}

.product-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.product-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: #27ae60;
    margin-bottom: 0.5rem;
}

.product-stock {
    margin-bottom: 1rem;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 4px;
    font-size: 0.9rem;
}

.stock-label {
    font-weight: 600;
    color: #2c3e50;
}

.stock-value {
    font-weight: 700;
    margin-right: 0.5rem;
}

.stock-value.in-stock {
    color: #27ae60;
}

.stock-value.low-stock {
    color: #f39c12;
}

.stock-value.out-of-stock {
    color: #e74c3c;
}

.stock-warning {
    font-size: 0.8rem;
    margin-top: 0.3rem;
    padding: 0.3rem;
    border-radius: 3px;
    font-weight: 600;
}

.low-stock .stock-warning {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.out-of-stock .stock-warning {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Stock badges */
.stock-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    padding: 0.3rem 0.6rem;
    border-radius: 15px;
    font-size: 0.7rem;
    font-weight: 700;
    color: white;
    z-index: 2;
}

.stock-badge.low-stock {
    background: #f39c12;
}

.stock-badge.out-of-stock {
    background: #e74c3c;
}

/* Product card states */
.product-card.out-of-stock {
    opacity: 0.7;
    border: 2px solid #e74c3c;
}

.product-card.low-stock {
    border: 2px solid #f39c12;
}

.product-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-edit,
.btn-delete {
    flex: 1;
    padding: 0.6rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-edit {
    background: #f39c12;
    color: white;
}

.btn-edit:hover {
    background: #e67e22;
}

.btn-delete {
    background: #e74c3c;
    color: white;
}

.btn-delete:hover {
    background: #c0392b;
}

/* Featured Products */
.featured-info {
    margin-bottom: 2rem;
}

.info-card {
    background: #e8f4fd;
    border: 1px solid #bee5eb;
    border-radius: 8px;
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    color: #0c5460;
}

.info-card i {
    font-size: 1.5rem;
    color: #17a2b8;
}

.info-card p {
    margin: 0;
    font-weight: 500;
}

.featured-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.featured-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
    position: relative;
}

.featured-card:hover {
    transform: translateY(-2px);
}

.featured-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 700;
    z-index: 2;
}

.featured-image {
    height: 200px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.featured-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.featured-image .placeholder {
    font-size: 3rem;
    color: #bdc3c7;
}

.featured-info-card {
    padding: 1.5rem;
}

.featured-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.featured-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: #27ae60;
    margin-bottom: 0.5rem;
}

.featured-category {
    font-size: 0.9rem;
    color: #7f8c8d;
    margin-bottom: 1rem;
}

.featured-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-remove-featured {
    flex: 1;
    background: #e74c3c;
    color: white;
    border: none;
    padding: 0.6rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-remove-featured:hover {
    background: #c0392b;
}

/* Featured Preview */
.featured-preview {
    margin-top: 1.5rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e1e1e1;
}

.featured-preview h4 {
    margin-bottom: 1rem;
    color: #2c3e50;
    font-weight: 600;
}

.preview-card {
    display: flex;
    gap: 1rem;
    background: white;
    padding: 1rem;
    border-radius: 6px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.preview-image {
    width: 80px;
    height: 80px;
    border-radius: 6px;
    overflow: hidden;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.preview-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.preview-info {
    flex: 1;
}

.preview-name {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.3rem;
}

.preview-price {
    font-weight: 700;
    color: #27ae60;
    font-size: 1.1rem;
    margin-bottom: 0.3rem;
}

.preview-category {
    color: #7f8c8d;
    font-size: 0.9rem;
    margin-bottom: 0.3rem;
}

.preview-stock {
    color: #34495e;
    font-size: 0.9rem;
    font-weight: 600;
}

/* Categories Grid */
.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.category-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.category-card:hover {
    transform: translateY(-2px);
}

.category-image {
    height: 150px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.category-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.category-image .placeholder {
    font-size: 3rem;
    color: #bdc3c7;
}

.category-info {
    padding: 1.5rem;
}

.category-name {
    font-size: 1.2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.category-description {
    color: #7f8c8d;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    line-height: 1.5;
}

.category-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 6px;
}

.category-stat {
    text-align: center;
}

.category-stat .number {
    font-size: 1.2rem;
    font-weight: 700;
    color: #3498db;
}

.category-stat .label {
    font-size: 0.8rem;
    color: #7f8c8d;
}

.category-actions {
    display: flex;
    gap: 0.5rem;
}

.category-actions button {
    flex: 1;
    padding: 0.6rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    font-size: 0.85rem;
}

.btn-edit-category {
    background: #3498db;
    color: white;
}

.btn-edit-category:hover {
    background: #2980b9;
}

.btn-delete-category {
    background: #e74c3c;
    color: white;
}

.btn-delete-category:hover {
    background: #c0392b;
}

.btn-view-products {
    background: #27ae60;
    color: white;
}

.btn-view-products:hover {
    background: #229954;
}

/* Contact Settings */
.contact-settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.settings-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.card-header {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 1rem 1.5rem;
    font-weight: 600;
}

.card-header h3 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.1rem;
}

.card-header i {
    font-size: 1.2rem;
}

.card-body {
    padding: 1.5rem;
}

.card-body .form-group {
    margin-bottom: 1rem;
}

.card-body .form-group:last-child {
    margin-bottom: 0;
}

.card-body label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.9rem;
}

.card-body input,
.card-body textarea {
    width: 100%;
    padding: 0.8rem;
    border: 2px solid #e1e1e1;
    border-radius: 6px;
    font-family: 'Cairo', sans-serif;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.card-body input:focus,
.card-body textarea:focus {
    outline: none;
    border-color: #3498db;
}

.settings-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    padding: 2rem 0;
}

.settings-actions button {
    padding: 0.8rem 2rem;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.btn-primary {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
    border: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
}

.btn-secondary {
    background: #95a5a6;
    color: white;
    border: none;
}

.btn-secondary:hover {
    background: #7f8c8d;
    transform: translateY(-2px);
}

.btn-info {
    background: #3498db;
    color: white;
    border: none;
}

.btn-info:hover {
    background: #2980b9;
    transform: translateY(-2px);
}

/* Logo Settings */
.logo-upload-section {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.current-logo h4 {
    margin-bottom: 1rem;
    color: #2c3e50;
    font-weight: 600;
}

.logo-preview {
    width: 120px;
    height: 80px;
    border: 2px dashed #bdc3c7;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    margin-bottom: 1rem;
    position: relative;
    overflow: hidden;
}

.logo-preview i {
    font-size: 2rem;
    color: #7f8c8d;
}

.logo-preview img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 6px;
}

.logo-upload {
    border-top: 1px solid #e1e1e1;
    padding-top: 1.5rem;
}

.form-help {
    display: block;
    margin-top: 0.3rem;
    font-size: 0.8rem;
    color: #7f8c8d;
    font-style: italic;
}

.logo-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.logo-actions button {
    padding: 0.6rem 1.2rem;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    border: none;
}

/* File input styling */
input[type="file"] {
    width: 100%;
    padding: 0.8rem;
    border: 2px solid #e1e1e1;
    border-radius: 6px;
    background: white;
    cursor: pointer;
    transition: border-color 0.3s ease;
}

input[type="file"]:hover {
    border-color: #3498db;
}

input[type="file"]:focus {
    outline: none;
    border-color: #3498db;
}

/* Offers Grid */
.offers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
}

.offer-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
    position: relative;
}

.offer-card:hover {
    transform: translateY(-2px);
}

.offer-card.expired {
    opacity: 0.6;
    border: 2px solid #e74c3c;
}

.offer-header {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    padding: 1rem;
    position: relative;
}

.offer-discount {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.offer-title {
    font-size: 1.1rem;
    opacity: 0.9;
}

.offer-status {
    position: absolute;
    top: 1rem;
    left: 1rem;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.offer-status.active {
    background: rgba(39, 174, 96, 0.9);
}

.offer-status.expired {
    background: rgba(231, 76, 60, 0.9);
}

.offer-body {
    padding: 1.5rem;
}

.offer-product {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.offer-product-image {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.offer-product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.offer-product-info h4 {
    margin: 0 0 0.3rem 0;
    color: #2c3e50;
    font-size: 1rem;
}

.offer-product-price {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.offer-original-price {
    text-decoration: line-through;
    color: #7f8c8d;
    font-size: 0.9rem;
}

.offer-sale-price {
    color: #e74c3c;
    font-weight: 700;
    font-size: 1.1rem;
}

.offer-details {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 6px;
    margin-bottom: 1rem;
}

.offer-detail {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.offer-detail:last-child {
    margin-bottom: 0;
}

.offer-detail-label {
    color: #7f8c8d;
}

.offer-detail-value {
    font-weight: 600;
    color: #2c3e50;
}

.offer-actions {
    display: flex;
    gap: 0.5rem;
}

/* Coupons List */
.coupons-list {
    display: grid;
    gap: 1rem;
}

.coupon-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: transform 0.3s ease;
}

.coupon-card:hover {
    transform: translateY(-2px);
}

.coupon-info {
    flex: 1;
}

.coupon-code {
    font-size: 1.2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.coupon-details {
    display: flex;
    gap: 1rem;
    margin-bottom: 0.5rem;
}

.coupon-detail {
    font-size: 0.9rem;
    color: #7f8c8d;
}

.coupon-status {
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.coupon-status.active {
    background: #d4edda;
    color: #155724;
}

.coupon-status.expired {
    background: #f8d7da;
    color: #721c24;
}

.coupon-actions {
    display: flex;
    gap: 0.5rem;
}

/* Settings */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.settings-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 2rem;
}

.settings-card h3 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-weight: 600;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #2c3e50;
    font-weight: 600;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 0.8rem;
    border: 2px solid #e1e1e1;
    border-radius: 6px;
    font-family: 'Cairo', sans-serif;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #3498db;
}

.form-help {
    display: block;
    margin-top: 0.3rem;
    font-size: 0.8rem;
    color: #7f8c8d;
}

/* Image Preview Styles */
.image-preview {
    margin-top: 1rem;
    position: relative;
    display: inline-block;
    border: 2px dashed #e1e1e1;
    border-radius: 8px;
    padding: 1rem;
    background: #f8f9fa;
}

.image-preview img {
    max-width: 200px;
    max-height: 200px;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.remove-image-btn {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    transition: background 0.3s ease;
}

.remove-image-btn:hover {
    background: #c0392b;
}

/* File Input Styling */
input[type="file"] {
    padding: 0.6rem !important;
    background: white;
    cursor: pointer;
}

input[type="file"]::-webkit-file-upload-button {
    background: #3498db;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    margin-left: 0.5rem;
    font-family: 'Cairo', sans-serif;
    font-weight: 600;
}

input[type="file"]::-webkit-file-upload-button:hover {
    background: #2980b9;
}

/* Current Image Display */
.current-image {
    margin-bottom: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e1e1e1;
}

.current-image img {
    display: block;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.current-image p {
    margin: 0;
    text-align: center;
    padding: 2rem;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 2000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal {
    background: white;
    border-radius: 10px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.8);
    transition: transform 0.3s ease;
}

.modal-overlay.active .modal {
    transform: scale(1);
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e1e1e1;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h3 {
    color: #2c3e50;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #7f8c8d;
    padding: 0.5rem;
    border-radius: 4px;
    transition: background 0.3s ease;
}

.modal-close:hover {
    background: #ecf0f1;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid #e1e1e1;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(100%);
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .main-content {
        margin-right: 0;
    }

    .mobile-menu-btn {
        display: block;
    }

    .admin-header {
        padding: 1rem;
    }

    .content-section {
        padding: 1rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .dashboard-widgets {
        grid-template-columns: 1fr;
    }

    .section-header {
        flex-direction: column;
        align-items: stretch;
    }

    .settings-grid {
        grid-template-columns: 1fr;
    }

    .modal {
        width: 95%;
        margin: 1rem;
    }

    /* Mobile navigation adjustments */
    .nav-scroll-up,
    .nav-scroll-down {
        padding: 0.6rem;
        font-size: 0.9rem;
    }

    .nav-items-container {
        max-height: calc(100vh - 180px);
    }

    .sidebar-footer {
        padding: 0.8rem;
    }

    .logout-btn {
        padding: 0.7rem;
        font-size: 0.85rem;
        min-height: 40px;
    }

    .storage-btn {
        padding: 0.6rem;
        font-size: 0.8rem;
        min-height: 36px;
    }
}

/* Storage Info Styles */
.storage-info {
    padding: 1rem 0;
}

.storage-summary {
    margin-bottom: 2rem;
    text-align: center;
}

.storage-bar {
    width: 100%;
    height: 20px;
    background: #ecf0f1;
    border-radius: 10px;
    overflow: hidden;
    margin: 1rem 0;
}

.storage-fill {
    height: 100%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

.storage-breakdown ul {
    list-style: none;
    padding: 0;
}

.storage-breakdown li {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem 0;
    border-bottom: 1px solid #ecf0f1;
}

.item-name {
    font-weight: 600;
}

.item-size {
    color: #7f8c8d;
    font-size: 0.9rem;
}

.storage-warning {
    background: #fdf2f2;
    border: 1px solid #f5c6cb;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
    color: #721c24;
}

.storage-warning i {
    color: #e74c3c;
    margin-left: 0.5rem;
}

.cleanup-options {
    padding: 1rem 0;
}

.cleanup-list {
    margin: 1rem 0;
}

.cleanup-list label {
    display: block;
    padding: 0.5rem 0;
    cursor: pointer;
}

.cleanup-list input[type="checkbox"] {
    margin-left: 0.5rem;
}

.cleanup-warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
    color: #856404;
}

.cleanup-warning i {
    color: #f39c12;
    margin-left: 0.5rem;
}

/* Loading Indicator Styles */
#loadingIndicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-content {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    min-width: 200px;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #2c5530;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-content p {
    margin: 0;
    color: #2c3e50;
    font-weight: 600;
    font-size: 1.1rem;
}

/* Enhanced Product Cards */
.product-card {
    position: relative;
    overflow: hidden;
}

.product-card.low-stock {
    border-left: 4px solid #e74c3c;
}

.product-card.in-stock {
    border-left: 4px solid #27ae60;
}

.product-image {
    position: relative;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-actions {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    gap: 0.5rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.product-card:hover .product-actions {
    opacity: 1;
}

.btn-icon {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
    color: #2c3e50;
}

.btn-icon:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.btn-icon.btn-danger {
    background: rgba(231, 76, 60, 0.9);
    color: white;
}

.btn-icon.btn-danger:hover {
    background: rgba(192, 57, 43, 0.9);
}

.product-stock {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.product-stock i.fa-exclamation-triangle {
    color: #e74c3c;
}

.product-stock i.fa-check-circle {
    color: #27ae60;
}

.product-description {
    font-size: 0.9rem;
    color: #7f8c8d;
    margin-top: 0.5rem;
    line-height: 1.4;
    max-height: 3em;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Storage Stats */
#storageStats {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 8px;
    flex: 1;
}

.stat-item i {
    font-size: 1.5rem;
    color: #2c5530;
}

.stat-number {
    font-size: 1.2rem;
    font-weight: 700;
    color: #2c3e50;
    display: block;
}

.stat-label {
    font-size: 0.8rem;
    color: #7f8c8d;
    display: block;
}

/* Advanced Storage Indicators */
.advanced-storage-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background: linear-gradient(135deg, #2c5530, #4CAF50);
    color: white;
    padding: 0.3rem 0.6rem;
    border-radius: 15px;
    font-size: 0.7rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.advanced-storage-badge i {
    font-size: 0.8rem;
}

/* Batch Operations */
.batch-operations {
    margin: 1rem 0;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
    border: 1px solid #e9ecef;
}

.batch-operations h4 {
    margin: 0 0 1rem 0;
    color: #2c3e50;
}

.batch-operations .btn {
    margin-left: 0.5rem;
}

/* Ultra Advanced Storage Styles for 50000+ Images */
.virtual-scroll {
    height: 80vh !important;
    overflow-y: auto;
    scroll-behavior: smooth;
}

.virtual-item {
    margin-bottom: 1rem;
    transition: transform 0.2s ease;
}

.virtual-item:hover {
    transform: translateY(-2px);
}

.lazy-image {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.lazy-image.loaded {
    opacity: 1;
}

.ultra-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.stat-item.primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.stat-item.success {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
}

.stat-item.info {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
}

.stat-item.warning {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
}

.performance-indicators {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.indicator {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    flex: 1;
    text-align: center;
}

.indicator.excellent {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
}

.indicator.good {
    background: linear-gradient(135deg, #f39c12, #f1c40f);
    color: white;
}

.indicator.normal {
    background: linear-gradient(135deg, #95a5a6, #bdc3c7);
    color: white;
}

.advanced-storage-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background: linear-gradient(135deg, #8e44ad, #9b59b6);
    color: white;
    padding: 0.3rem 0.6rem;
    border-radius: 15px;
    font-size: 0.7rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.3rem;
    z-index: 5;
    box-shadow: 0 2px 8px rgba(142, 68, 173, 0.3);
}

.advanced-storage-badge i {
    font-size: 0.8rem;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.load-more-container {
    grid-column: 1 / -1;
    text-align: center;
    padding: 2rem;
}

.load-more-container .btn {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    border-radius: 25px;
    background: linear-gradient(135deg, #2c5530, #4CAF50);
    border: none;
    color: white;
    transition: all 0.3s ease;
}

.load-more-container .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(44, 85, 48, 0.3);
}

/* Virtual Scrolling Performance Optimizations */
.products-grid.virtual-scroll .product-card {
    contain: layout style paint;
    will-change: transform;
}

.products-grid.virtual-scroll .product-image img {
    content-visibility: auto;
    contain-intrinsic-size: 300px 200px;
}

/* Ultra Performance Indicators */
.ultra-performance-bar {
    width: 100%;
    height: 4px;
    background: #ecf0f1;
    border-radius: 2px;
    overflow: hidden;
    margin: 0.5rem 0;
}

.ultra-performance-fill {
    height: 100%;
    background: linear-gradient(90deg, #e74c3c, #f39c12, #f1c40f, #27ae60);
    transition: width 0.3s ease;
}

/* Responsive Design for Ultra System */
@media (max-width: 768px) {
    .ultra-stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
    }

    .performance-indicators {
        flex-direction: column;
        gap: 0.5rem;
    }

    .virtual-scroll {
        height: 60vh !important;
    }

    .advanced-storage-badge {
        font-size: 0.6rem;
        padding: 0.2rem 0.4rem;
    }
}

@media (max-width: 480px) {
    .ultra-stats-grid {
        grid-template-columns: 1fr;
    }

    .stat-item {
        padding: 0.8rem;
    }

    .stat-number {
        font-size: 1rem;
    }
}

/* Ultra Advanced Button Styles */
.btn-purple {
    background: linear-gradient(135deg, #8e44ad, #9b59b6);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-family: 'Cairo', sans-serif;
}

.btn-purple:hover {
    background: linear-gradient(135deg, #7d3c98, #8e44ad);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(142, 68, 173, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-family: 'Cairo', sans-serif;
}

.btn-success:hover {
    background: linear-gradient(135deg, #229954, #27ae60);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

/* Smart Storage Info Styles */
.smart-storage-info {
    padding: 1rem 0;
}

.storage-status {
    padding: 1rem;
    border-radius: 10px;
    color: white;
    text-align: center;
    margin-bottom: 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.storage-status i {
    font-size: 1.2rem;
}

.storage-summary {
    margin-bottom: 2rem;
}

.storage-summary h4 {
    margin-bottom: 1rem;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.storage-bar {
    width: 100%;
    height: 25px;
    background: #ecf0f1;
    border-radius: 12px;
    overflow: hidden;
    margin: 1rem 0;
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
}

.storage-fill {
    height: 100%;
    transition: width 0.5s ease;
    border-radius: 12px;
    position: relative;
}

.storage-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.breakdown-grid {
    display: grid;
    gap: 1rem;
    margin-top: 1rem;
}

.breakdown-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #3498db;
    transition: all 0.3s ease;
}

.breakdown-item:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.breakdown-item i {
    font-size: 1.5rem;
    width: 30px;
    text-align: center;
}

.breakdown-item > div {
    flex: 1;
}

.item-name {
    display: block;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.2rem;
}

.item-size {
    display: block;
    color: #7f8c8d;
    font-size: 0.9rem;
}

.storage-tips {
    background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
    padding: 1.5rem;
    border-radius: 10px;
    margin: 1.5rem 0;
    border-left: 4px solid #27ae60;
}

.storage-tips h4 {
    color: #27ae60;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.storage-tips ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.storage-tips li {
    padding: 0.5rem 0;
    color: #2c3e50;
    font-size: 0.95rem;
}

.storage-actions {
    background: #fff3cd;
    padding: 1.5rem;
    border-radius: 10px;
    border-left: 4px solid #f39c12;
    margin-top: 1.5rem;
}

.storage-actions h4 {
    color: #856404;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.storage-actions .btn {
    margin-left: 0.5rem;
    margin-bottom: 0.5rem;
}

/* Responsive Design for Storage Info */
@media (max-width: 768px) {
    .breakdown-grid {
        gap: 0.5rem;
    }

    .breakdown-item {
        padding: 0.8rem;
        gap: 0.8rem;
    }

    .breakdown-item i {
        font-size: 1.2rem;
        width: 25px;
    }

    .storage-tips,
    .storage-actions {
        padding: 1rem;
    }

    .storage-status {
        padding: 0.8rem;
        font-size: 0.9rem;
    }
}

/* Storage Health and Error Styles */
.storage-health {
    margin-top: 1rem;
    padding: 1rem;
    background: linear-gradient(135deg, #d5f4e6, #e8f5e8);
    border-radius: 8px;
    border-left: 4px solid #27ae60;
    text-align: center;
}

.storage-error {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    padding: 2rem;
    background: #fff3cd;
    border-radius: 10px;
    border-left: 4px solid #f39c12;
    color: #856404;
    text-align: center;
    flex-wrap: wrap;
}

.storage-error i {
    font-size: 1.5rem;
    color: #f39c12;
}

.storage-error .btn {
    margin-top: 0.5rem;
}

/* Enhanced Performance Indicators */
.performance-indicators {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
    flex-wrap: wrap;
}

.indicator {
    padding: 0.8rem 1.2rem;
    border-radius: 25px;
    font-size: 0.9rem;
    font-weight: 600;
    flex: 1;
    text-align: center;
    min-width: 200px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.indicator::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s ease;
}

.indicator:hover::before {
    left: 100%;
}

.indicator.excellent {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
    box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
}

.indicator.good {
    background: linear-gradient(135deg, #f39c12, #f1c40f);
    color: white;
    box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
}

.indicator.normal {
    background: linear-gradient(135deg, #95a5a6, #bdc3c7);
    color: white;
    box-shadow: 0 4px 15px rgba(149, 165, 166, 0.3);
}

/* Smart Storage Animations */
@keyframes storageSuccess {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.storage-health {
    animation: storageSuccess 2s ease-in-out;
}

/* Improved Ultra Stats Grid */
.ultra-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-item {
    padding: 1.5rem;
    border-radius: 15px;
    color: white;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.stat-item:hover::before {
    transform: translateX(100%);
}

.stat-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.stat-item i {
    font-size: 2rem;
    opacity: 0.9;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: 800;
    display: block;
    line-height: 1;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
    display: block;
    margin-top: 0.3rem;
}

/* Import/Export Styles */
.import-data-form {
    padding: 1rem 0;
}

.import-warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    color: #856404;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.import-warning i {
    color: #f39c12;
    font-size: 1.2rem;
}

.import-options {
    margin-top: 1rem;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
}

.import-options label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 5px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.import-options label:hover {
    background: #e9ecef;
}

.import-options input[type="checkbox"] {
    margin: 0;
}

/* Enhanced Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 600;
    font-family: 'Cairo', sans-serif;
    transition: all 0.3s ease;
    text-decoration: none;
    font-size: 0.9rem;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.btn-success {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
}

.btn-success:hover {
    background: linear-gradient(135deg, #229954, #27ae60);
}

.btn-warning {
    background: linear-gradient(135deg, #f39c12, #f1c40f);
    color: white;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #e67e22, #f39c12);
}

.btn-danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #c0392b, #a93226);
}

.btn-secondary {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
    color: white;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #7f8c8d, #6c7b7d);
}

.btn-info {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.btn-info:hover {
    background: linear-gradient(135deg, #2980b9, #21618c);
}

.btn-cashier {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-family: 'Cairo', sans-serif;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn-cashier:hover {
    background: linear-gradient(135deg, #c0392b, #a93226);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.btn-cashier::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s ease;
}

.btn-cashier:hover::before {
    left: 100%;
}



/* Employee Management Button - Professional Design */
.employee-management-btn {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    border: none;
    border-radius: 15px;
    padding: 0;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: 1rem;
    min-width: 280px;
    height: 60px;
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);
    font-family: 'Cairo', sans-serif;
    margin-right: 1rem;
}

.employee-management-btn:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 15px 40px rgba(231, 76, 60, 0.5);
    background: linear-gradient(135deg, #c0392b, #a93226);
}

.employee-management-btn:active {
    transform: translateY(-1px) scale(0.98);
    transition: all 0.1s ease;
}

.employee-management-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.6s ease;
}

.employee-management-btn:hover::before {
    left: 100%;
}

.employee-management-btn .btn-icon {
    position: relative;
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 0.8rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.employee-management-btn .btn-icon i {
    font-size: 1.5rem;
    color: white;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
}

.employee-management-btn .security-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 20px;
    height: 20px;
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid white;
    box-shadow: 0 2px 8px rgba(39, 174, 96, 0.4);
}

.employee-management-btn .security-badge i {
    font-size: 0.7rem !important;
    color: white;
}

.employee-management-btn .btn-content {
    flex: 1;
    text-align: right;
    color: white;
}

.employee-management-btn .btn-title {
    display: block;
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: 0.2rem;
    text-shadow: 0 1px 3px rgba(0,0,0,0.3);
}

.employee-management-btn .btn-subtitle {
    display: block;
    font-size: 0.85rem;
    opacity: 0.9;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

.employee-management-btn .btn-arrow {
    width: 30px;
    height: 30px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.8rem;
    transition: all 0.3s ease;
}

.employee-management-btn .btn-arrow i {
    font-size: 0.9rem;
    color: white;
    transition: transform 0.3s ease;
}

.employee-management-btn:hover .btn-arrow {
    background: rgba(255, 255, 255, 0.25);
    transform: translateX(-3px);
}

.employee-management-btn:hover .btn-arrow i {
    transform: translateX(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .employee-management-btn {
        min-width: 200px;
        height: 50px;
        gap: 0.5rem;
    }

    .employee-management-btn .btn-icon {
        width: 40px;
        height: 40px;
        margin-left: 0.5rem;
    }

    .employee-management-btn .btn-icon i {
        font-size: 1.2rem;
    }

    .employee-management-btn .btn-title {
        font-size: 1rem;
    }

    .employee-management-btn .btn-subtitle {
        font-size: 0.75rem;
    }

    .employee-management-btn .btn-arrow {
        margin-right: 0.5rem;
    }
}

/* Website Button Styles */
.btn-website {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-family: 'Cairo', sans-serif;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn-website:hover {
    background: linear-gradient(135deg, #2ecc71, #27ae60);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
    text-decoration: none;
    color: white;
}

.btn-website::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s ease;
}

.btn-website:hover::before {
    left: 100%;
}

/* Header Website Button */
.header-website-btn {
    margin-left: 1rem;
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(39, 174, 96, 0.2);
    transition: all 0.3s ease;
}

.header-website-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(39, 174, 96, 0.3);
    text-decoration: none;
    color: white;
}

.header-website-btn i {
    margin-left: 0.5rem;
    font-size: 1rem;
}

.header-website-btn span {
    font-weight: 600;
}

/* Header Employees Button */
.header-employees-btn {
    margin-left: 1rem;
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(155, 89, 182, 0.2);
    transition: all 0.3s ease;
}

.header-employees-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(155, 89, 182, 0.3);
}

.header-employees-btn i {
    margin-left: 0.5rem;
    font-size: 1rem;
}

.header-employees-btn span {
    font-weight: 600;
}

/* Header Cashier Button */
.header-cashier-btn {
    margin-left: 1rem;
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.2);
    transition: all 0.3s ease;
}

.header-cashier-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.3);
}

.header-cashier-btn i {
    margin-left: 0.5rem;
    font-size: 1rem;
}

.header-cashier-btn span {
    font-weight: 600;
}

/* Responsive Header Buttons */
@media (max-width: 768px) {
    .header-website-btn span,
    .header-employees-btn span,
    .header-cashier-btn span {
        display: none;
    }

    .header-website-btn,
    .header-employees-btn,
    .header-cashier-btn {
        padding: 0.6rem;
        margin-left: 0.5rem;
    }

    .header-website-btn i,
    .header-employees-btn i,
    .header-cashier-btn i {
        margin: 0;
        font-size: 1.1rem;
    }
}

@media (max-width: 480px) {
    .header-website-btn,
    .header-employees-btn,
    .header-cashier-btn {
        padding: 0.5rem;
        margin-left: 0.3rem;
        border-radius: 6px;
    }

    .header-right {
        gap: 0.3rem;
    }
}

/* Employee Management Styles */
.employees-management {
    max-width: 100%;
}

.employees-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.employees-stats {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.stat-card {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 10px;
    padding: 1rem;
    display: flex;
    align-items: center;
    gap: 0.8rem;
    min-width: 150px;
    border-left: 4px solid #3498db;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.stat-card i {
    font-size: 2rem;
    color: #3498db;
    width: 40px;
    text-align: center;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
    display: block;
    line-height: 1;
}

.stat-label {
    font-size: 0.8rem;
    color: #7f8c8d;
    display: block;
    margin-top: 0.2rem;
}

.employees-list {
    display: grid;
    gap: 1rem;
    max-height: 500px;
    overflow-y: auto;
}

.employee-card {
    background: white;
    border: 2px solid #ecf0f1;
    border-radius: 12px;
    padding: 1.5rem;
    display: grid;
    grid-template-columns: auto 1fr auto auto;
    gap: 1rem;
    align-items: center;
    transition: all 0.3s ease;
    position: relative;
}

.employee-card:hover {
    border-color: #3498db;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(52, 152, 219, 0.15);
}

.employee-card.inactive {
    opacity: 0.6;
    border-color: #bdc3c7;
}

.employee-avatar {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.employee-info h4 {
    margin: 0 0 0.3rem 0;
    color: #2c3e50;
    font-size: 1.1rem;
    font-weight: 600;
}

.employee-role {
    color: #3498db;
    font-weight: 600;
    font-size: 0.9rem;
    margin: 0 0 0.2rem 0;
}

.employee-username {
    color: #7f8c8d;
    font-size: 0.8rem;
    margin: 0 0 0.2rem 0;
    font-family: 'Courier New', monospace;
}

.employee-phone {
    color: #27ae60;
    font-size: 0.8rem;
    margin: 0;
}

.employee-details {
    text-align: center;
}

.employee-salary {
    font-size: 1rem;
    font-weight: 700;
    color: #27ae60;
    margin-bottom: 0.3rem;
}

.employee-status {
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.employee-status.active {
    background: #d5f4e6;
    color: #27ae60;
}

.employee-status.inactive {
    background: #fadbd8;
    color: #e74c3c;
}

.employee-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

/* Employee Form Styles */
.add-employee-form,
.edit-employee-form {
    padding: 1rem 0;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.permissions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.permission-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.permission-item:hover {
    background: #e9ecef;
}

.permission-item input[type="checkbox"] {
    margin: 0;
}

.permission-item span {
    font-size: 0.9rem;
    color: #2c3e50;
}

/* Responsive Employee Management */
@media (max-width: 768px) {
    .employees-header {
        flex-direction: column;
        align-items: stretch;
    }

    .employees-stats {
        justify-content: center;
    }

    .stat-card {
        min-width: 120px;
    }

    .employee-card {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 1rem;
    }

    .employee-actions {
        justify-content: center;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .permissions-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .employees-stats {
        flex-direction: column;
    }

    .stat-card {
        min-width: auto;
    }

    .employee-card {
        padding: 1rem;
    }

    .employee-actions {
        flex-direction: column;
    }

    .employee-actions .btn {
        width: 100%;
    }
}

/* Simple Employees Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.modal {
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    background: linear-gradient(135deg, #9b59b6, #8e44ad);
    color: white;
    padding: 1.5rem;
    border-radius: 15px 15px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    padding: 1rem 2rem 2rem;
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

.employees-simple h4 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.employees-list-simple {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    max-height: 300px;
    overflow-y: auto;
}

.employee-item-simple {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.employee-item-simple:hover {
    border-color: #9b59b6;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(155, 89, 182, 0.1);
}

.employee-item-simple:last-child {
    margin-bottom: 0;
}

.employee-item-simple strong {
    color: #2c3e50;
    font-size: 1rem;
}

.employee-item-simple small {
    color: #7f8c8d;
    font-size: 0.85rem;
}

/* Add Employee Form Styles */
.add-employee-simple .form-group {
    margin-bottom: 1rem;
}

.add-employee-simple label {
    display: block;
    margin-bottom: 0.5rem;
    color: #2c3e50;
    font-weight: 600;
    font-size: 0.9rem;
}

.add-employee-simple input,
.add-employee-simple select {
    width: 100%;
    padding: 0.8rem;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    font-family: 'Cairo', sans-serif;
}

.add-employee-simple input:focus,
.add-employee-simple select:focus {
    outline: none;
    border-color: #9b59b6;
    box-shadow: 0 0 0 3px rgba(155, 89, 182, 0.1);
}

.permissions-simple {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.permissions-simple label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.85rem;
    font-weight: 500;
}

.permissions-simple label:hover {
    background: #e9ecef;
}

.permissions-simple input[type="checkbox"] {
    width: auto;
    margin: 0;
}

/* Enhanced Modal Styles */
.modal-overlay {
    animation: fadeIn 0.3s ease;
}

.modal {
    animation: slideInUp 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInUp {
    from {
        transform: translateY(50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Responsive Design for Employee Forms */
@media (max-width: 768px) {
    .permissions-simple {
        grid-template-columns: 1fr;
    }

    .modal {
        width: 95%;
        margin: 1rem;
    }

    .modal-body {
        padding: 1rem;
    }

    .modal-footer {
        flex-direction: column;
        gap: 0.5rem;
    }

    .modal-footer .btn {
        width: 100%;
    }
}

/* Sidebar Website Link */
.website-nav {
    margin-top: 1rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 1rem;
}

.website-link {
    background: linear-gradient(135deg, #27ae60, #2ecc71) !important;
    color: white !important;
    border-radius: 8px;
    margin: 0.5rem 0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    text-decoration: none;
}

.website-link:hover {
    background: linear-gradient(135deg, #2ecc71, #27ae60) !important;
    transform: translateX(5px);
    box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
    text-decoration: none;
    color: white !important;
}

.website-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.website-link:hover::before {
    left: 100%;
}

.website-link i {
    color: white !important;
    margin-left: 0.8rem;
    font-size: 1.1rem;
}

.website-link span {
    color: white !important;
    font-weight: 600;
}

/* Sidebar Employees Link */
.employees-nav {
    margin-top: 0.5rem;
}

.employees-link {
    background: linear-gradient(135deg, #9b59b6, #8e44ad) !important;
    color: white !important;
    border-radius: 8px;
    margin: 0.5rem 0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.employees-link:hover {
    background: linear-gradient(135deg, #8e44ad, #7d3c98) !important;
    transform: translateX(5px);
    box-shadow: 0 4px 15px rgba(155, 89, 182, 0.3);
}

.employees-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.employees-link:hover::before {
    left: 100%;
}

.employees-link i {
    color: white !important;
    margin-left: 0.8rem;
    font-size: 1.1rem;
}

.employees-link span {
    color: white !important;
    font-weight: 600;
}

/* Sidebar Cashier Link */
.cashier-nav {
    margin-top: 0.5rem;
}

.cashier-link {
    background: linear-gradient(135deg, #e74c3c, #c0392b) !important;
    color: white !important;
    border-radius: 8px;
    margin: 0.5rem 0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.cashier-link:hover {
    background: linear-gradient(135deg, #c0392b, #a93226) !important;
    transform: translateX(5px);
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.cashier-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s ease;
}

.cashier-link:hover::before {
    left: 100%;
}

.cashier-link i {
    color: white !important;
    margin-left: 0.8rem;
    font-size: 1.1rem;
}

.cashier-link span {
    color: white !important;
    font-weight: 600;
}

/* Responsive Sidebar Links */
@media (max-width: 768px) {
    .website-nav,
    .employees-nav,
    .cashier-nav {
        margin-top: 0.5rem;
        padding-top: 0.5rem;
    }

    .website-link,
    .employees-link,
    .cashier-link {
        margin: 0.3rem 0;
        padding: 0.8rem 1rem;
    }
}

/* Responsive Import/Export */
@media (max-width: 768px) {
    .import-options {
        grid-template-columns: 1fr;
    }

    .batch-operations {
        padding: 1rem;
    }

    .batch-operations > div {
        flex-direction: column;
        gap: 0.5rem;
    }

    .btn {
        width: 100%;
        justify-content: center;
    }
}

/* Save Status Indicator */
.save-status-indicator {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 10000;
    display: none;
    animation: slideInUp 0.3s ease;
}

.save-status-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.9rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
}

.save-status-indicator.saving .save-status-content {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.save-status-indicator.success .save-status-content {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
}

.save-status-indicator.error .save-status-content {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.save-status-indicator.warning .save-status-content {
    background: linear-gradient(135deg, #f39c12, #f1c40f);
    color: white;
}

.save-status-indicator.fade-out {
    animation: fadeOut 2s ease forwards;
}

@keyframes slideInUp {
    from {
        transform: translateY(100px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0.3;
    }
}

/* Pulse animation for saving */
.save-status-indicator.saving .save-status-content {
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* Success checkmark animation */
.save-status-indicator.success .fa-check-circle {
    animation: checkmark 0.6s ease;
}

@keyframes checkmark {
    0% {
        transform: scale(0);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

/* Error shake animation */
.save-status-indicator.error .save-status-content {
    animation: shake 0.5s ease;
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-5px);
    }
    75% {
        transform: translateX(5px);
    }
}

/* Mobile responsive */
@media (max-width: 768px) {
    .save-status-indicator {
        bottom: 10px;
        right: 10px;
        left: 10px;
    }

    .save-status-content {
        padding: 0.8rem 1rem;
        font-size: 0.85rem;
        justify-content: center;
    }
}
}

/* Advanced Employee Management Styles */
.admin-verification {
    text-align: center;
    padding: 2rem 1rem;
}

.verification-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
}

.verification-form {
    max-width: 300px;
    margin: 0 auto;
    text-align: left;
}

.error-message {
    background: #fadbd8;
    color: #e74c3c;
    padding: 0.8rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    text-align: center;
    border-left: 4px solid #e74c3c;
}

.employees-management-advanced {
    padding: 1rem 0;
}

.management-stats {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.management-actions {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.accounts-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.accounts-table th {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
    padding: 1rem;
    text-align: right;
    font-weight: 600;
}

.accounts-table td {
    padding: 1rem;
    border-bottom: 1px solid #ecf0f1;
    vertical-align: middle;
}

.account-row:hover {
    background: #f8f9fa;
}

.account-row.inactive {
    opacity: 0.6;
}

.employee-info strong {
    display: block;
    color: #2c3e50;
    font-size: 1rem;
}

.employee-info small {
    color: #7f8c8d;
    font-size: 0.8rem;
}

.username {
    background: #ecf0f1;
    padding: 0.3rem 0.6rem;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: #2c3e50;
}

.role-badge {
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.role-badge.admin {
    background: #e74c3c;
    color: white;
}

.role-badge.manager {
    background: #9b59b6;
    color: white;
}

.role-badge.cashier {
    background: #3498db;
    color: white;
}

.role-badge.sales {
    background: #27ae60;
    color: white;
}

.role-badge.inventory {
    background: #f39c12;
    color: white;
}

.role-badge.employee {
    background: #95a5a6;
    color: white;
}

.permissions-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.3rem;
}

.permission-tag {
    background: #3498db;
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: 500;
}

.more-permissions {
    background: #95a5a6;
    color: white;
    padding: 0.2rem 0.5rem;
    border-radius: 10px;
    font-size: 0.7rem;
}

.status-badge {
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-badge.active {
    background: #d5f4e6;
    color: #27ae60;
}

.status-badge.inactive {
    background: #fadbd8;
    color: #e74c3c;
}

.action-buttons {
    display: flex;
    gap: 0.3rem;
}

.btn-small {
    padding: 0.4rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-edit {
    background: #3498db;
    color: white;
}

.btn-edit:hover {
    background: #2980b9;
}

.btn-toggle {
    background: #f39c12;
    color: white;
}

.btn-toggle:hover {
    background: #e67e22;
}

.btn-delete {
    background: #e74c3c;
    color: white;
}

.btn-delete:hover {
    background: #c0392b;
}

/* Create/Edit Account Form Styles */
.create-account-form,
.edit-account-form {
    max-height: 70vh;
    overflow-y: auto;
    padding: 1rem;
}

.form-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #3498db;
}

.form-section h4 {
    color: #2c3e50;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-help {
    font-size: 0.8rem;
    color: #7f8c8d;
    margin-top: 0.3rem;
    display: block;
}

.permissions-grid-advanced {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-top: 1rem;
}

.permission-item-advanced {
    background: white;
    border: 2px solid #ecf0f1;
    border-radius: 10px;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: block;
}

.permission-item-advanced:hover {
    border-color: #3498db;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.1);
}

.permission-item-advanced input[type="checkbox"] {
    margin-bottom: 0.5rem;
}

.permission-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.3rem;
}

.permission-label i {
    color: #3498db;
    width: 20px;
}

.permission-item-advanced small {
    color: #7f8c8d;
    font-size: 0.8rem;
    display: block;
}

.info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.info-item label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2c3e50;
}

/* Responsive Design */
@media (max-width: 768px) {
    .management-stats {
        flex-direction: column;
    }

    .accounts-table {
        font-size: 0.8rem;
    }

    .accounts-table th,
    .accounts-table td {
        padding: 0.5rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .permissions-grid-advanced {
        grid-template-columns: 1fr;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }

    .action-buttons {
        flex-direction: column;
    }
}