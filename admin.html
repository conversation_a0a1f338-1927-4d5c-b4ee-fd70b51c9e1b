<!DOCTYPE html>
<!-- Updated: حسابات دخول الموظفين - Employee Login Accounts System -->
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - حسابات دخول الموظفين محدثة</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="admin-styles.css?v=2024">

    <!-- SheetJS for Excel processing -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

    <style>




        /* نظام إدارة الموظفين */
        .header-employee-btn {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            border: none;
            color: white;
            padding: 0.8rem 1.5rem;
            border-radius: 8px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
            position: relative;
            cursor: pointer;
            font-family: 'Cairo', sans-serif;
        }

        .header-employee-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
            text-decoration: none;
            color: white;
        }

        .security-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: #f39c12;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        /* أنماط النوافذ المنبثقة المحسنة لنظام الموظفين */
        .employee-modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.8), rgba(44, 62, 80, 0.9));
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            backdrop-filter: blur(15px);
            font-family: 'Cairo', sans-serif;
            animation: overlayFadeIn 0.4s ease-out;
        }

        @keyframes overlayFadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .employee-modal {
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border-radius: 25px;
            box-shadow:
                0 30px 100px rgba(0, 0, 0, 0.5),
                0 10px 40px rgba(52, 152, 219, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            border: 2px solid rgba(255, 255, 255, 0.4);
            overflow: hidden;
            animation: modalSlideIn 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
            position: relative;
        }

        .employee-modal::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #3498db, #e74c3c, #f39c12, #27ae60, #9b59b6);
            background-size: 300% 100%;
            animation: gradientShift 3s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.8) rotateX(10deg);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1) rotateX(0deg);
            }
        }

        .employee-modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem 2.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .employee-modal-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .employee-modal-body {
            padding: 2.5rem;
            max-height: 75vh;
            overflow-y: auto;
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
        }

        .employee-modal-body::-webkit-scrollbar {
            width: 8px;
        }

        .employee-modal-body::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        .employee-modal-body::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #3498db, #2980b9);
            border-radius: 10px;
        }

        .employee-modal-body::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #2980b9, #3498db);
        }

        /* تحسين الأزرار */
        .employee-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 1rem 1.5rem;
            border-radius: 15px;
            cursor: pointer;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-family: 'Cairo', sans-serif;
            text-decoration: none;
            justify-content: center;
            position: relative;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .employee-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .employee-btn:hover::before {
            left: 100%;
        }

        .employee-btn:hover {
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
            text-decoration: none;
            color: white;
        }

        .employee-btn:active {
            transform: translateY(-1px) scale(0.98);
        }

        .employee-btn.success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            box-shadow: 0 8px 25px rgba(86, 171, 47, 0.3);
        }

        .employee-btn.success:hover {
            box-shadow: 0 15px 35px rgba(86, 171, 47, 0.4);
        }

        .employee-btn.warning {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            box-shadow: 0 8px 25px rgba(240, 147, 251, 0.3);
        }

        .employee-btn.warning:hover {
            box-shadow: 0 15px 35px rgba(240, 147, 251, 0.4);
        }

        .employee-btn.danger {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
            box-shadow: 0 8px 25px rgba(255, 65, 108, 0.3);
        }

        .employee-btn.danger:hover {
            box-shadow: 0 15px 35px rgba(255, 65, 108, 0.4);
        }

        .employee-btn.secondary {
            background: linear-gradient(135deg, #bdc3c7 0%, #2c3e50 100%);
            box-shadow: 0 8px 25px rgba(189, 195, 199, 0.3);
        }

        .employee-btn.secondary:hover {
            box-shadow: 0 15px 35px rgba(189, 195, 199, 0.4);
        }

        /* تحسين النماذج */
        .employee-form-group {
            margin-bottom: 1.8rem;
            position: relative;
        }

        .employee-form-group label {
            display: block;
            margin-bottom: 0.8rem;
            font-weight: 700;
            color: #2c3e50;
            font-size: 1rem;
            position: relative;
            padding-left: 2rem;
        }

        .employee-form-group label i {
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.1rem;
        }

        .employee-form-group input,
        .employee-form-group select,
        .employee-form-group textarea {
            width: 100%;
            padding: 1rem 1.2rem;
            border: 2px solid #e8ecef;
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.05);
        }

        .employee-form-group input:focus,
        .employee-form-group select:focus,
        .employee-form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow:
                0 0 0 4px rgba(102, 126, 234, 0.1),
                inset 0 2px 4px rgba(0,0,0,0.05);
            transform: translateY(-2px);
        }

        /* تحسين الجداول */
        .employee-table {
            width: 100%;
            border-collapse: collapse;
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border-radius: 20px;
            overflow: hidden;
            box-shadow:
                0 20px 60px rgba(0, 0, 0, 0.1),
                0 8px 25px rgba(0, 0, 0, 0.05);
        }

        .employee-table th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem;
            text-align: right;
            font-weight: 700;
            font-size: 1rem;
            position: relative;
        }

        .employee-table th::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #3498db, #e74c3c, #f39c12, #27ae60);
        }

        .employee-table td {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            position: relative;
        }

        .employee-table tr:hover {
            background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
            transform: scale(1.01);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        /* تحسين البطاقات الإحصائية */
        .employee-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 2rem;
            margin-bottom: 2.5rem;
        }

        .employee-stat-card {
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            padding: 2rem;
            border-radius: 20px;
            text-align: center;
            box-shadow:
                0 15px 35px rgba(0, 0, 0, 0.1),
                0 5px 15px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.8);
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;
            overflow: hidden;
        }

        .employee-stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #3498db, #e74c3c, #f39c12, #27ae60, #9b59b6);
            background-size: 300% 100%;
            animation: gradientShift 3s ease-in-out infinite;
        }

        .employee-stat-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.15),
                0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .employee-stat-icon {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: white;
            font-size: 1.8rem;
            box-shadow: 0 8px 25px rgba(0,0,0,0.2);
            position: relative;
        }

        .employee-stat-icon::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border-radius: 50%;
            background: linear-gradient(45deg, rgba(255,255,255,0.3), transparent);
            z-index: -1;
        }

        .employee-stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
            color: #2c3e50;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .employee-stat-label {
            color: #7f8c8d;
            font-size: 1rem;
            font-weight: 600;
        }

        /* تحسين الرسائل */
        .employee-message {
            padding: 1.2rem 1.5rem;
            border-radius: 12px;
            margin-bottom: 1.5rem;
            font-size: 1rem;
            font-weight: 600;
            position: relative;
            border-left: 4px solid;
            animation: messageSlideIn 0.3s ease-out;
        }

        @keyframes messageSlideIn {
            from {
                opacity: 0;
                transform: translateX(-20px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .employee-message.success {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
            border-left-color: #28a745;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.2);
        }

        .employee-message.error {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
            border-left-color: #dc3545;
            box-shadow: 0 4px 15px rgba(220, 53, 69, 0.2);
        }

        /* تحسين الشارات */
        .employee-status-badge,
        .employee-role-badge {
            padding: 0.6rem 1.2rem;
            border-radius: 25px;
            font-size: 0.9rem;
            font-weight: 700;
            color: white;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            position: relative;
            overflow: hidden;
        }

        .employee-status-badge::before,
        .employee-role-badge::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .employee-status-badge:hover::before,
        .employee-role-badge:hover::before {
            left: 100%;
        }

        .employee-status-badge.active {
            background: linear-gradient(135deg, #56ab2f, #a8e6cf);
        }

        .employee-status-badge.inactive {
            background: linear-gradient(135deg, #ff416c, #ff4b2b);
        }

        .employee-role-badge.admin {
            background: linear-gradient(135deg, #ff416c, #ff4b2b);
        }

        .employee-role-badge.supervisor {
            background: linear-gradient(135deg, #f093fb, #f5576c);
        }

        .employee-role-badge.cashier {
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .employee-role-badge.employee {
            background: linear-gradient(135deg, #56ab2f, #a8e6cf);
        }

        /* تحسين الأفاتار */
        .employee-avatar {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 800;
            font-size: 1.1rem;
            box-shadow:
                0 8px 25px rgba(102, 126, 234, 0.3),
                inset 0 2px 4px rgba(255,255,255,0.2);
            position: relative;
            overflow: hidden;
        }

        .employee-avatar::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            animation: avatarShine 3s infinite;
        }

        @keyframes avatarShine {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* تحسين أزرار الإجراءات */
        .employee-actions {
            display: flex;
            gap: 0.8rem;
            justify-content: center;
        }

        .employee-action-btn {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .employee-action-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255,255,255,0.3);
            border-radius: 50%;
            transition: all 0.3s ease;
            transform: translate(-50%, -50%);
        }

        .employee-action-btn:hover::before {
            width: 100%;
            height: 100%;
        }

        .employee-action-btn:hover {
            transform: scale(1.15) rotate(5deg);
        }

        .employee-action-btn:active {
            transform: scale(0.95);
        }

        .employee-action-btn.edit {
            background: linear-gradient(135deg, #f093fb, #f5576c);
            box-shadow: 0 4px 15px rgba(240, 147, 251, 0.3);
        }

        .employee-action-btn.delete {
            background: linear-gradient(135deg, #ff416c, #ff4b2b);
            box-shadow: 0 4px 15px rgba(255, 65, 108, 0.3);
        }

        .employee-action-btn.protected {
            background: linear-gradient(135deg, #bdc3c7, #2c3e50);
            cursor: not-allowed;
            box-shadow: 0 4px 15px rgba(189, 195, 199, 0.3);
        }

        /* تحسين بطاقات الصلاحيات */
        .employee-permissions-grid {
            display: grid;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .employee-permission-card {
            padding: 2.5rem;
            border-radius: 20px;
            color: white;
            position: relative;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .employee-permission-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: permissionGlow 4s ease-in-out infinite;
        }

        @keyframes permissionGlow {
            0%, 100% { transform: scale(0.8) rotate(0deg); opacity: 0.5; }
            50% { transform: scale(1.2) rotate(180deg); opacity: 0.8; }
        }

        .employee-permission-card:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 30px 80px rgba(0, 0, 0, 0.4);
        }

        .employee-permission-header {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            margin-bottom: 1.5rem;
            position: relative;
            z-index: 2;
        }

        .employee-permission-icon {
            width: 70px;
            height: 70px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255,255,255,0.3);
        }

        .employee-permission-title {
            font-size: 1.5rem;
            font-weight: 800;
            margin: 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .employee-permission-subtitle {
            font-size: 1rem;
            opacity: 0.9;
            margin: 0;
            font-weight: 500;
        }

        .employee-permission-list {
            list-style: none;
            padding: 0;
            margin: 0;
            display: grid;
            gap: 0.8rem;
            position: relative;
            z-index: 2;
        }

        .employee-permission-item {
            display: flex;
            align-items: center;
            gap: 0.8rem;
            font-size: 1rem;
            font-weight: 600;
            padding: 0.5rem;
            border-radius: 8px;
            background: rgba(255,255,255,0.1);
            backdrop-filter: blur(5px);
            transition: all 0.3s ease;
        }

        .employee-permission-item:hover {
            background: rgba(255,255,255,0.2);
            transform: translateX(5px);
        }

        .employee-permission-item i {
            width: 20px;
            font-size: 1.1rem;
        }

        /* تحسين زر الإغلاق */
        .employee-close-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            width: 45px;
            height: 45px;
            border-radius: 50%;
            cursor: pointer;
            color: white;
            font-size: 1.3rem;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255,255,255,0.3);
            position: relative;
            overflow: hidden;
        }

        .employee-close-btn::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(231, 76, 60, 0.8);
            border-radius: 50%;
            transition: all 0.3s ease;
            transform: translate(-50%, -50%);
        }

        .employee-close-btn:hover::before {
            width: 120%;
            height: 120%;
        }

        .employee-close-btn:hover {
            transform: scale(1.1) rotate(90deg);
            border-color: rgba(231, 76, 60, 0.5);
        }

        /* تحسين الحالة الفارغة */
        .employee-empty-state {
            text-align: center;
            padding: 4rem 2rem;
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .employee-empty-state::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #bdc3c7, #95a5a6);
        }

        .employee-empty-icon {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, #bdc3c7, #95a5a6);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            color: white;
            font-size: 3.5rem;
            box-shadow: 0 15px 35px rgba(189, 195, 199, 0.3);
            animation: emptyIconFloat 3s ease-in-out infinite;
        }

        @keyframes emptyIconFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        /* تحسين التحكم */
        .employee-controls {
            display: flex;
            gap: 1.5rem;
            margin-bottom: 2.5rem;
            flex-wrap: wrap;
        }

        .employee-grid {
            display: grid;
            gap: 1.5rem;
        }

        /* تحسين الاستجابة */
        @media (max-width: 768px) {
            .employee-modal {
                width: 95%;
                margin: 1rem;
                border-radius: 20px;
            }

            .employee-modal-body {
                padding: 1.5rem;
            }

            .employee-stats-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .employee-controls {
                flex-direction: column;
            }

            .employee-table {
                font-size: 0.9rem;
            }

            .employee-table th,
            .employee-table td {
                padding: 1rem;
            }

            .employee-permission-card {
                padding: 1.5rem;
            }

            .employee-permission-header {
                flex-direction: column;
                text-align: center;
                gap: 1rem;
            }
        }

        @media (max-width: 480px) {
            .employee-modal-header {
                padding: 1.5rem;
            }

            .employee-modal-body {
                padding: 1rem;
            }

            .employee-btn {
                padding: 0.8rem 1rem;
                font-size: 0.9rem;
            }

            .employee-form-group input,
            .employee-form-group select {
                padding: 0.8rem;
            }
        }
    </style>
</head>
<body>


    <!-- Sidebar -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <div class="logo">
                <i class="fas fa-store"></i>
                <h2>هايبر ماركت</h2>
            </div>
            <button class="sidebar-toggle" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>

        <nav class="sidebar-nav">
            <div class="nav-scroll-container">
                <div class="nav-scroll-up" onclick="scrollNavUp()">
                    <i class="fas fa-chevron-up"></i>
                </div>
                <div class="nav-items-container" id="navItemsContainer">
                    <ul>
                        <!-- لوحة المعلومات - متاحة لجميع الصفات -->
                        <li class="nav-item active" data-role-required="admin,supervisor,cashier,employee">
                            <a href="#dashboard" class="nav-link" data-section="dashboard">
                                <i class="fas fa-tachometer-alt"></i>
                                <span>لوحة المعلومات</span>
                            </a>
                        </li>

                        <!-- إدارة الفئات - المدير والمشرف فقط -->
                        <li class="nav-item" data-role-required="admin,supervisor">
                            <a href="#categories" class="nav-link" data-section="categories">
                                <i class="fas fa-th-large"></i>
                                <span>إدارة الفئات</span>
                            </a>
                        </li>

                        <!-- إدارة المنتجات - المدير والمشرف فقط -->
                        <li class="nav-item" data-role-required="admin,supervisor">
                            <a href="#products" class="nav-link" data-section="products">
                                <i class="fas fa-box"></i>
                                <span>إدارة المنتجات</span>
                            </a>
                        </li>

                        <!-- المنتجات المميزة - المدير والمشرف فقط -->
                        <li class="nav-item" data-role-required="admin,supervisor">
                            <a href="#featured" class="nav-link" data-section="featured">
                                <i class="fas fa-star"></i>
                                <span>المنتجات المميزة</span>
                            </a>
                        </li>

                        <!-- إدارة العروض - المدير والمشرف فقط -->
                        <li class="nav-item" data-role-required="admin,supervisor">
                            <a href="#offers" class="nav-link" data-section="offers">
                                <i class="fas fa-tags"></i>
                                <span>إدارة العروض</span>
                            </a>
                        </li>

                        <!-- إدارة الكوبونات - المدير والمشرف فقط -->
                        <li class="nav-item" data-role-required="admin,supervisor">
                            <a href="#coupons" class="nav-link" data-section="coupons">
                                <i class="fas fa-ticket-alt"></i>
                                <span>إدارة الكوبونات</span>
                            </a>
                        </li>

                        <!-- إدارة مناطق التوصيل - المدير والمشرف فقط -->
                        <li class="nav-item" data-role-required="admin,supervisor">
                            <a href="#delivery-zones" class="nav-link" data-section="delivery-zones">
                                <i class="fas fa-map-marked-alt"></i>
                                <span>مناطق التوصيل</span>
                            </a>
                        </li>

                        <!-- إدارة السلايدرات - المدير والمشرف فقط -->
                        <li class="nav-item" data-role-required="admin,supervisor">
                            <a href="#sliders" class="nav-link" data-section="sliders">
                                <i class="fas fa-images"></i>
                                <span>السلايدرات</span>
                            </a>
                        </li>

                        <!-- إدارة ألوان الموقع - المدير والمشرف فقط -->
                        <li class="nav-item" data-role-required="admin,supervisor">
                            <a href="#theme-colors" class="nav-link" data-section="theme-colors">
                                <i class="fas fa-palette"></i>
                                <span>ألوان الموقع</span>
                            </a>
                        </li>

                        <!-- رفع المنتجات من Excel - المدير والمشرف فقط -->
                        <li class="nav-item" data-role-required="admin,supervisor">
                            <a href="#excel-upload" class="nav-link" data-section="excel-upload">
                                <i class="fas fa-file-excel"></i>
                                <span>رفع من Excel</span>
                            </a>
                        </li>

                        <!-- الطلبات - المدير والمشرف والكاشير -->
                        <li class="nav-item" data-role-required="admin,supervisor,cashier">
                            <a href="#orders" class="nav-link" data-section="orders">
                                <i class="fas fa-shopping-cart"></i>
                                <span>الطلبات</span>
                            </a>
                        </li>

                        <!-- إعدادات الاتصال - المدير فقط -->
                        <li class="nav-item" data-role-required="admin">
                            <a href="#contact-settings" class="nav-link" data-section="contact-settings">
                                <i class="fas fa-address-book"></i>
                                <span>إعدادات الاتصال</span>
                            </a>
                        </li>

                        <!-- إعدادات الموقع - المدير فقط -->
                        <li class="nav-item" data-role-required="admin">
                            <a href="#settings" class="nav-link" data-section="settings">
                                <i class="fas fa-cog"></i>
                                <span>إعدادات الموقع</span>
                            </a>
                        </li>

                        <!-- إدارة الموظفين - المدير فقط -->
                        <li class="nav-item" data-role-required="admin">
                            <a href="#employee-management" class="nav-link" data-section="employee-management">
                                <i class="fas fa-users-cog"></i>
                                <span>إدارة الموظفين</span>
                            </a>
                        </li>

                        <!-- الموقع الإلكتروني - متاح لجميع الصفات -->
                        <li class="nav-item website-nav" data-role-required="admin,supervisor,cashier,employee">
                            <a href="index.html" class="nav-link website-link" target="_blank" onclick="openWebsite(event)">
                                <i class="fas fa-globe"></i>
                                <span>الموقع الإلكتروني</span>
                            </a>
                        </li>

                        <!-- نظام الكاشير - المدير والمشرف والكاشير -->
                        <li class="nav-item cashier-nav" data-role-required="admin,supervisor,cashier">
                            <a href="cashier.html" class="nav-link cashier-link" onclick="openCashier(event)">
                                <i class="fas fa-cash-register"></i>
                                <span>نظام الكاشير</span>
                            </a>
                        </li>

                        <!-- التقارير - متاح لجميع الصفات -->
                        <li class="nav-item" data-role-required="admin,supervisor,cashier,employee">
                            <a href="#reports" class="nav-link" data-section="reports">
                                <i class="fas fa-chart-bar"></i>
                                <span>التقارير والإحصائيات</span>
                            </a>
                        </li>
                    </ul>
                </div>
                <div class="nav-scroll-down" onclick="scrollNavDown()">
                    <i class="fas fa-chevron-down"></i>
                </div>
            </div>
        </nav>

        <div class="sidebar-footer">
            <button class="storage-btn" onclick="showStorageInfo()" title="مراقبة مساحة التخزين">
                <i class="fas fa-hdd"></i>
                <span>مساحة التخزين</span>
            </button>
            <button class="logout-btn" onclick="logout()">
                <i class="fas fa-sign-out-alt"></i>
                <span>تسجيل الخروج</span>
            </button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content" id="mainContent">
        <!-- Header -->
        <header class="admin-header">
            <div class="header-left">
                <button class="mobile-menu-btn" id="mobileMenuBtn">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 id="pageTitle">لوحة المعلومات</h1>
            </div>
            <div class="header-right">
                <a href="index.html" class="btn btn-website header-website-btn" target="_blank" onclick="openWebsite(event)">
                    <i class="fas fa-globe"></i>
                    <span>الموقع الإلكتروني</span>
                </a>

                <a href="cashier.html" class="btn btn-cashier header-cashier-btn" onclick="openCashier(event)">
                    <i class="fas fa-cash-register"></i>
                    <span>نظام الكاشير</span>
                </a>

                <!-- نظام إدارة الموظفين -->
                <button class="btn btn-employee header-employee-btn" onclick="showSection('employee-management')">
                    <i class="fas fa-users-cog"></i>
                    <span>إدارة الموظفين</span>
                    <div class="security-badge">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                </button>
                <div class="admin-info">
                    <div class="admin-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                </div>
            </div>
        </header>

        <!-- Dashboard Section -->
        <section id="dashboard" class="content-section active">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-box"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="totalProducts">8</h3>
                        <p>إجمالي المنتجات</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="totalFeatured">0</h3>
                        <p>المنتجات المميزة</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-tags"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="totalOffers">0</h3>
                        <p>العروض النشطة</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-ticket-alt"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="totalCoupons">3</h3>
                        <p>الكوبونات النشطة</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="totalOrders">0</h3>
                        <p>الطلبات اليوم</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stat-info">
                        <h3 id="totalRevenue">0</h3>
                        <p>المبيعات اليوم (ريال)</p>
                    </div>
                </div>
            </div>

            <div class="dashboard-widgets">
                <div class="widget">
                    <div class="widget-header">
                        <h3>الطلبات الأخيرة</h3>
                        <button class="btn-secondary">عرض الكل</button>
                    </div>
                    <div class="widget-content">
                        <div class="empty-state">
                            <i class="fas fa-inbox"></i>
                            <p>لا توجد طلبات حتى الآن</p>
                        </div>
                    </div>
                </div>

                <div class="widget">
                    <div class="widget-header">
                        <h3>المنتجات الأكثر مبيعاً</h3>
                        <button class="btn-secondary">عرض التقرير</button>
                    </div>
                    <div class="widget-content">
                        <div class="empty-state">
                            <i class="fas fa-chart-bar"></i>
                            <p>لا توجد بيانات مبيعات حتى الآن</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Categories Section -->
        <section id="categories" class="content-section">
            <div class="section-header">
                <h2>إدارة الفئات</h2>
                <button class="btn-primary" onclick="showAddCategoryModal()">
                    <i class="fas fa-plus"></i>
                    إضافة فئة جديدة
                </button>
            </div>

            <div class="categories-grid" id="categoriesGrid">
                <!-- Categories will be loaded here -->
            </div>
        </section>

        <!-- Products Section -->
        <section id="products" class="content-section">
            <div class="section-header">
                <h2>إدارة المنتجات</h2>
                <button class="btn-primary" onclick="showAddProductModal()">
                    <i class="fas fa-plus"></i>
                    إضافة منتج جديد
                </button>
            </div>

            <div class="batch-operations">
                <h4><i class="fas fa-tools"></i> العمليات المجمعة والإحصائيات</h4>
                <div style="display: flex; gap: 0.5rem; flex-wrap: wrap; margin-bottom: 1rem;">
                    <button class="btn btn-info" onclick="updateStorageStats()">
                        <i class="fas fa-chart-bar"></i>
                        تحديث الإحصائيات
                    </button>
                    <button class="btn btn-warning" onclick="optimizeAllImages()">
                        <i class="fas fa-compress-alt"></i>
                        تحسين جميع الصور
                    </button>
                    <button class="btn btn-success" onclick="loadProductsWithImages()">
                        <i class="fas fa-sync-alt"></i>
                        إعادة تحميل المنتجات
                    </button>
                    <button class="btn btn-purple" onclick="showBulkUploadModal()">
                        <i class="fas fa-upload"></i>
                        رفع متعدد (50K)
                    </button>
                    <button class="btn btn-success" onclick="exportAllData()">
                        <i class="fas fa-download"></i>
                        تصدير البيانات
                    </button>
                    <button class="btn btn-warning" onclick="showImportModal()">
                        <i class="fas fa-upload"></i>
                        استيراد البيانات
                    </button>

                </div>
                <div id="storageStats"></div>
            </div>

            <div class="products-grid" id="productsGrid">
                <!-- Products will be loaded here -->
            </div>
        </section>

        <!-- Featured Products Section -->
        <section id="featured" class="content-section">
            <div class="section-header">
                <h2>إدارة المنتجات المميزة</h2>
                <button class="btn-primary" onclick="showAddFeaturedModal()">
                    <i class="fas fa-plus"></i>
                    إضافة منتج مميز
                </button>
            </div>

            <div class="featured-info">
                <div class="info-card">
                    <i class="fas fa-info-circle"></i>
                    <p>المنتجات المميزة هي التي تظهر في القسم الرئيسي للموقع. يمكنك إضافة حتى 8 منتجات مميزة.</p>
                </div>
            </div>

            <div class="featured-grid" id="featuredGrid">
                <!-- Featured products will be loaded here -->
            </div>
        </section>

        <!-- Contact Settings Section -->
        <section id="contact-settings" class="content-section">
            <div class="section-header">
                <h2>إعدادات معلومات الاتصال</h2>
                <p>إدارة معلومات التواصل التي تظهر في الموقع</p>
            </div>

            <div class="contact-settings-grid">
                <!-- Basic Info -->
                <div class="settings-card">
                    <div class="card-header">
                        <h3><i class="fas fa-info-circle"></i> المعلومات الأساسية</h3>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label>عنوان المتجر (عربي)</label>
                            <input type="text" id="storeNameAr" placeholder="برومت هايبر ماركت">
                        </div>
                        <div class="form-group">
                            <label>عنوان المتجر (إنجليزي)</label>
                            <input type="text" id="storeNameEn" placeholder="Bromet Hypermarket">
                        </div>
                        <div class="form-group">
                            <label>وصف المتجر (عربي)</label>
                            <textarea id="storeDescAr" rows="3" placeholder="متجرك المفضل لجميع احتياجاتك اليومية"></textarea>
                        </div>
                        <div class="form-group">
                            <label>وصف المتجر (إنجليزي)</label>
                            <textarea id="storeDescEn" rows="3" placeholder="Your favorite store for all daily needs"></textarea>
                        </div>
                    </div>
                </div>

                <!-- Logo Settings -->
                <div class="settings-card">
                    <div class="card-header">
                        <h3><i class="fas fa-image"></i> إعدادات الشعار</h3>
                    </div>
                    <div class="card-body">
                        <div class="logo-upload-section">
                            <div class="current-logo">
                                <h4>الشعار الحالي:</h4>
                                <div class="logo-preview" id="logoPreview">
                                    <i class="fas fa-store" id="defaultIcon"></i>
                                    <img id="logoImage" src="" alt="شعار المتجر" style="display: none;">
                                </div>
                            </div>

                            <div class="logo-upload">
                                <div class="form-group">
                                    <label>رفع شعار جديد</label>
                                    <input type="file" id="logoUpload" accept="image/*" onchange="handleLogoUpload(event)">
                                    <small class="form-help">يُفضل أن يكون الشعار بصيغة PNG أو JPG وحجم لا يزيد عن 2MB</small>
                                </div>

                                <div class="form-group">
                                    <label>أو أدخل رابط الشعار</label>
                                    <input type="url" id="logoUrl" placeholder="https://example.com/logo.png" onchange="handleLogoUrl()">
                                    <small class="form-help">يمكنك إدخال رابط مباشر للشعار من الإنترنت</small>
                                </div>

                                <div class="logo-actions">
                                    <button type="button" class="btn-secondary" onclick="resetLogo()">
                                        <i class="fas fa-undo"></i>
                                        استخدام الأيقونة الافتراضية
                                    </button>
                                    <button type="button" class="btn-primary" onclick="previewLogo()">
                                        <i class="fas fa-eye"></i>
                                        معاينة
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Details -->
                <div class="settings-card">
                    <div class="card-header">
                        <h3><i class="fas fa-phone"></i> معلومات التواصل</h3>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label>العنوان (عربي)</label>
                            <input type="text" id="addressAr" placeholder="الرياض، المملكة العربية السعودية">
                        </div>
                        <div class="form-group">
                            <label>العنوان (إنجليزي)</label>
                            <input type="text" id="addressEn" placeholder="Riyadh, Saudi Arabia">
                        </div>
                        <div class="form-group">
                            <label>الهاتف الأول</label>
                            <input type="tel" id="phone1" placeholder="+966 11 123 4567">
                        </div>
                        <div class="form-group">
                            <label>الهاتف الثاني</label>
                            <input type="tel" id="phone2" placeholder="+966 11 123 4568">
                        </div>
                        <div class="form-group">
                            <label>البريد الإلكتروني الرئيسي</label>
                            <input type="email" id="email1" placeholder="<EMAIL>">
                        </div>
                        <div class="form-group">
                            <label>بريد الدعم الفني</label>
                            <input type="email" id="email2" placeholder="<EMAIL>">
                        </div>
                    </div>
                </div>

                <!-- Working Hours -->
                <div class="settings-card">
                    <div class="card-header">
                        <h3><i class="fas fa-clock"></i> ساعات العمل</h3>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label>ساعات العمل (عربي)</label>
                            <textarea id="workingHoursAr" rows="3" placeholder="السبت - الخميس: 8:00 ص - 12:00 م&#10;الجمعة: 2:00 م - 12:00 م"></textarea>
                        </div>
                        <div class="form-group">
                            <label>ساعات العمل (إنجليزي)</label>
                            <textarea id="workingHoursEn" rows="3" placeholder="Saturday - Thursday: 8:00 AM - 12:00 PM&#10;Friday: 2:00 PM - 12:00 PM"></textarea>
                        </div>
                    </div>
                </div>

                <!-- Social Media -->
                <div class="settings-card">
                    <div class="card-header">
                        <h3><i class="fas fa-share-alt"></i> وسائل التواصل الاجتماعي</h3>
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <label>رابط الفيسبوك</label>
                            <input type="url" id="facebookUrl" placeholder="https://facebook.com/brometmarket">
                        </div>
                        <div class="form-group">
                            <label>رابط تويتر</label>
                            <input type="url" id="twitterUrl" placeholder="https://twitter.com/brometmarket">
                        </div>
                        <div class="form-group">
                            <label>رابط إنستغرام</label>
                            <input type="url" id="instagramUrl" placeholder="https://instagram.com/brometmarket">
                        </div>
                        <div class="form-group">
                            <label>رقم الواتس اب</label>
                            <input type="tel" id="whatsappNumber" placeholder="966111234567">
                        </div>
                    </div>
                </div>
            </div>

            <div class="settings-actions">
                <button class="btn-primary" onclick="saveContactSettings()">
                    <i class="fas fa-save"></i>
                    حفظ الإعدادات
                </button>
                <button class="btn-secondary" onclick="resetContactSettings()">
                    <i class="fas fa-undo"></i>
                    إعادة تعيين
                </button>
                <button class="btn-info" onclick="testLogoUpdate()">
                    <i class="fas fa-sync"></i>
                    اختبار تحديث الشعار
                </button>
            </div>
        </section>

        <!-- Offers Section -->
        <section id="offers" class="content-section">
            <div class="section-header">
                <h2>إدارة العروض والخصومات</h2>
                <button class="btn-primary" onclick="showAddOfferModal()">
                    <i class="fas fa-plus"></i>
                    إضافة عرض جديد
                </button>
            </div>

            <div class="offers-grid" id="offersGrid">
                <!-- Offers will be loaded here -->
            </div>
        </section>

        <!-- Coupons Section -->
        <section id="coupons" class="content-section">
            <div class="section-header">
                <h2>إدارة الكوبونات</h2>
                <button class="btn-primary" onclick="showAddCouponModal()">
                    <i class="fas fa-plus"></i>
                    إنشاء كوبون جديد
                </button>
            </div>

            <div class="coupons-list" id="couponsList">
                <!-- Coupons will be loaded here -->
            </div>
        </section>

        <!-- Orders Section -->
        <section id="orders" class="content-section">
            <div class="section-header">
                <h2>إدارة الطلبات</h2>
                <div class="filters">
                    <select id="orderFilter">
                        <option value="all">جميع الطلبات</option>
                        <option value="pending">قيد الانتظار</option>
                        <option value="completed">مكتملة</option>
                        <option value="cancelled">ملغية</option>
                    </select>
                </div>
            </div>

            <div class="orders-list" id="ordersList">
                <div class="empty-state">
                    <i class="fas fa-shopping-cart"></i>
                    <p>لا توجد طلبات حتى الآن</p>
                </div>
            </div>
        </section>

        <!-- Settings Section -->
        <section id="settings" class="content-section">
            <div class="section-header">
                <h2>إعدادات الموقع</h2>
            </div>

            <div class="settings-grid">
                <div class="settings-card">
                    <h3>معلومات المتجر</h3>
                    <form id="storeInfoForm">
                        <div class="form-group">
                            <label>اسم المتجر</label>
                            <input type="text" id="storeName" value="برومت هايبر ماركت">
                        </div>
                        <div class="form-group">
                            <label>رقم الهاتف</label>
                            <input type="tel" id="storePhone" value="+966 11 123 4567">
                        </div>
                        <div class="form-group">
                            <label>العنوان</label>
                            <input type="text" id="storeAddress" value="الرياض، المملكة العربية السعودية">
                        </div>
                        <button type="submit" class="btn-primary">حفظ التغييرات</button>
                    </form>
                </div>

                <div class="settings-card">
                    <h3>إعدادات الواتس اب</h3>
                    <form id="whatsappForm">
                        <div class="form-group">
                            <label>رقم الواتس اب</label>
                            <input type="tel" id="whatsappNumber" value="966501234567">
                        </div>
                        <div class="form-group">
                            <label>رسالة الترحيب</label>
                            <textarea id="welcomeMessage" rows="3">مرحباً بكم في برومت هايبر ماركت</textarea>
                        </div>
                        <button type="submit" class="btn-primary">حفظ التغييرات</button>
                    </form>
                </div>
            </div>
        </section>

        <!-- Delivery Zones Management Section -->
        <section id="delivery-zones" class="content-section" style="display: none;">
            <div class="section-header">
                <h2>
                    <i class="fas fa-map-marked-alt"></i>
                    إدارة مناطق التوصيل
                </h2>
                <p>إدارة المناطق وعمولات التوصيل للمنزل</p>
            </div>

            <!-- Statistics Cards -->
            <div class="delivery-stats">
                <div class="stat-card-modern primary">
                    <div class="stat-icon-modern">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <div class="stat-content-modern">
                        <div class="stat-number" id="totalZonesCount">0</div>
                        <div class="stat-label">إجمالي المناطق</div>
                        <div class="stat-trend">
                            <i class="fas fa-chart-line"></i>
                            <span>جميع المناطق المضافة</span>
                        </div>
                    </div>
                    <div class="stat-decoration">
                        <div class="decoration-circle"></div>
                        <div class="decoration-circle"></div>
                    </div>
                </div>

                <div class="stat-card-modern success">
                    <div class="stat-icon-modern">
                        <i class="fas fa-truck"></i>
                    </div>
                    <div class="stat-content-modern">
                        <div class="stat-number" id="activeZonesCount">0</div>
                        <div class="stat-label">المناطق النشطة</div>
                        <div class="stat-trend">
                            <i class="fas fa-check-circle"></i>
                            <span>متاحة للتوصيل</span>
                        </div>
                    </div>
                    <div class="stat-decoration">
                        <div class="decoration-circle"></div>
                        <div class="decoration-circle"></div>
                    </div>
                </div>

                <div class="stat-card-modern warning">
                    <div class="stat-icon-modern">
                        <i class="fas fa-coins"></i>
                    </div>
                    <div class="stat-content-modern">
                        <div class="stat-number" id="avgDeliveryFee">0</div>
                        <div class="stat-label">متوسط عمولة التوصيل</div>
                        <div class="stat-trend">
                            <i class="fas fa-calculator"></i>
                            <span>بالدينار العراقي</span>
                        </div>
                    </div>
                    <div class="stat-decoration">
                        <div class="decoration-circle"></div>
                        <div class="decoration-circle"></div>
                    </div>
                </div>

                <div class="stat-card-modern info">
                    <div class="stat-icon-modern">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content-modern">
                        <div class="stat-number" id="avgDeliveryTime">0</div>
                        <div class="stat-label">متوسط وقت التوصيل</div>
                        <div class="stat-trend">
                            <i class="fas fa-stopwatch"></i>
                            <span>بالدقائق</span>
                        </div>
                    </div>
                    <div class="stat-decoration">
                        <div class="decoration-circle"></div>
                        <div class="decoration-circle"></div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="delivery-actions-modern">
                <button class="btn-action-modern primary" onclick="showAddZoneForm()">
                    <div class="btn-icon">
                        <i class="fas fa-plus"></i>
                    </div>
                    <div class="btn-content">
                        <span class="btn-title">إضافة منطقة جديدة</span>
                        <span class="btn-subtitle">إنشاء منطقة توصيل جديدة</span>
                    </div>
                </button>

                <button class="btn-action-modern secondary" onclick="refreshZonesList()">
                    <div class="btn-icon">
                        <i class="fas fa-sync-alt"></i>
                    </div>
                    <div class="btn-content">
                        <span class="btn-title">تحديث القائمة</span>
                        <span class="btn-subtitle">إعادة تحميل البيانات</span>
                    </div>
                </button>

                <button class="btn-action-modern success" onclick="exportZonesData()">
                    <div class="btn-icon">
                        <i class="fas fa-download"></i>
                    </div>
                    <div class="btn-content">
                        <span class="btn-title">تصدير البيانات</span>
                        <span class="btn-subtitle">حفظ ملف CSV</span>
                    </div>
                </button>

                <button class="btn-action-modern info" onclick="showZonesAnalytics()">
                    <div class="btn-icon">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                    <div class="btn-content">
                        <span class="btn-title">تحليل البيانات</span>
                        <span class="btn-subtitle">إحصائيات مفصلة</span>
                    </div>
                </button>
            </div>

            <!-- Zones List -->
            <div class="zones-list-container">
                <div class="zones-list-header">
                    <h3>قائمة مناطق التوصيل</h3>
                    <div class="search-filter">
                        <input type="text" id="zoneSearchInput" placeholder="البحث عن منطقة..." onkeyup="filterZones()">
                        <select id="statusFilterSelect" onchange="filterZones()">
                            <option value="">جميع الحالات</option>
                            <option value="active">نشطة</option>
                            <option value="inactive">غير نشطة</option>
                        </select>
                    </div>
                </div>

                <div class="zones-table-container-modern">
                    <div class="table-wrapper">
                        <table class="zones-table-modern" id="zonesTable">
                            <thead>
                                <tr>
                                    <th class="th-zone-info">
                                        <i class="fas fa-map-marker-alt"></i>
                                        معلومات المنطقة
                                    </th>
                                    <th class="th-delivery-fee">
                                        <i class="fas fa-coins"></i>
                                        عمولة التوصيل
                                    </th>
                                    <th class="th-delivery-time">
                                        <i class="fas fa-clock"></i>
                                        وقت التوصيل
                                    </th>
                                    <th class="th-status">
                                        <i class="fas fa-toggle-on"></i>
                                        الحالة
                                    </th>
                                    <th class="th-date">
                                        <i class="fas fa-calendar-alt"></i>
                                        تاريخ الإضافة
                                    </th>
                                    <th class="th-actions">
                                        <i class="fas fa-cogs"></i>
                                        الإجراءات
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="zonesTableBody">
                                <!-- سيتم ملء البيانات هنا -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="no-zones-message" id="noZonesMessage" style="display: none;">
                    <div class="empty-state">
                        <i class="fas fa-map"></i>
                        <h3>لا توجد مناطق توصيل</h3>
                        <p>لم يتم إضافة أي مناطق توصيل بعد. انقر على "إضافة منطقة جديدة" لإضافة أول منطقة.</p>
                        <button class="btn-primary" onclick="showAddZoneForm()">
                            <i class="fas fa-plus"></i>
                            إضافة أول منطقة
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Sliders Management Section -->
        <section id="sliders" class="content-section" style="display: none;">
            <div class="section-header">
                <h2>
                    <i class="fas fa-images"></i>
                    إدارة السلايدرات
                </h2>
                <p>إدارة السلايدرات والعروض المرئية للموقع الرئيسي</p>
            </div>

            <!-- Statistics Cards -->
            <div class="sliders-stats">
                <div class="stat-card-modern primary">
                    <div class="stat-icon-modern">
                        <i class="fas fa-images"></i>
                    </div>
                    <div class="stat-content-modern">
                        <div class="stat-number" id="totalSlidersCount">0</div>
                        <div class="stat-label">إجمالي السلايدرات</div>
                        <div class="stat-trend">
                            <i class="fas fa-chart-line"></i>
                            <span>جميع السلايدرات المضافة</span>
                        </div>
                    </div>
                    <div class="stat-decoration">
                        <div class="decoration-circle"></div>
                        <div class="decoration-circle"></div>
                    </div>
                </div>

                <div class="stat-card-modern success">
                    <div class="stat-icon-modern">
                        <i class="fas fa-eye"></i>
                    </div>
                    <div class="stat-content-modern">
                        <div class="stat-number" id="activeSlidersCount">0</div>
                        <div class="stat-label">السلايدرات النشطة</div>
                        <div class="stat-trend">
                            <i class="fas fa-check-circle"></i>
                            <span>معروضة في الموقع</span>
                        </div>
                    </div>
                    <div class="stat-decoration">
                        <div class="decoration-circle"></div>
                        <div class="decoration-circle"></div>
                    </div>
                </div>

                <div class="stat-card-modern warning">
                    <div class="stat-icon-modern">
                        <i class="fas fa-palette"></i>
                    </div>
                    <div class="stat-content-modern">
                        <div class="stat-number" id="slidersThemesCount">0</div>
                        <div class="stat-label">الثيمات المختلفة</div>
                        <div class="stat-trend">
                            <i class="fas fa-paint-brush"></i>
                            <span>أنماط التصميم</span>
                        </div>
                    </div>
                    <div class="stat-decoration">
                        <div class="decoration-circle"></div>
                        <div class="decoration-circle"></div>
                    </div>
                </div>

                <div class="stat-card-modern info">
                    <div class="stat-icon-modern">
                        <i class="fas fa-play-circle"></i>
                    </div>
                    <div class="stat-content-modern">
                        <div class="stat-number" id="slidersAnimationsCount">0</div>
                        <div class="stat-label">الحركات المتاحة</div>
                        <div class="stat-trend">
                            <i class="fas fa-magic"></i>
                            <span>تأثيرات الانتقال</span>
                        </div>
                    </div>
                    <div class="stat-decoration">
                        <div class="decoration-circle"></div>
                        <div class="decoration-circle"></div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="sliders-actions-modern">
                <button class="btn-action-modern primary" onclick="showAddSliderForm()">
                    <div class="btn-icon">
                        <i class="fas fa-plus"></i>
                    </div>
                    <div class="btn-content">
                        <span class="btn-title">إضافة سلايدر جديد</span>
                        <span class="btn-subtitle">إنشاء سلايدر احترافي</span>
                    </div>
                </button>

                <button class="btn-action-modern secondary" onclick="refreshSlidersList()">
                    <div class="btn-icon">
                        <i class="fas fa-sync-alt"></i>
                    </div>
                    <div class="btn-content">
                        <span class="btn-title">تحديث القائمة</span>
                        <span class="btn-subtitle">إعادة تحميل البيانات</span>
                    </div>
                </button>

                <button class="btn-action-modern success" onclick="previewSliders()">
                    <div class="btn-icon">
                        <i class="fas fa-eye"></i>
                    </div>
                    <div class="btn-content">
                        <span class="btn-title">معاينة السلايدرات</span>
                        <span class="btn-subtitle">عرض في نافذة جديدة</span>
                    </div>
                </button>

                <button class="btn-action-modern info" onclick="exportSlidersData()">
                    <div class="btn-icon">
                        <i class="fas fa-download"></i>
                    </div>
                    <div class="btn-content">
                        <span class="btn-title">تصدير البيانات</span>
                        <span class="btn-subtitle">حفظ ملف JSON</span>
                    </div>
                </button>
            </div>

            <!-- Sliders List -->
            <div class="sliders-list-container">
                <div class="sliders-list-header">
                    <h3>قائمة السلايدرات</h3>
                    <div class="search-filter">
                        <input type="text" id="sliderSearchInput" placeholder="البحث عن سلايدر..." onkeyup="filterSliders()">
                        <select id="sliderStatusFilter" onchange="filterSliders()">
                            <option value="">جميع الحالات</option>
                            <option value="active">نشطة</option>
                            <option value="inactive">غير نشطة</option>
                        </select>
                    </div>
                </div>

                <div class="sliders-grid-container" id="slidersGridContainer">
                    <!-- سيتم ملء السلايدرات هنا -->
                </div>

                <div class="no-sliders-message" id="noSlidersMessage" style="display: none;">
                    <div class="empty-state">
                        <i class="fas fa-images"></i>
                        <h3>لا توجد سلايدرات</h3>
                        <p>لم يتم إضافة أي سلايدرات بعد. انقر على "إضافة سلايدر جديد" لإضافة أول سلايدر.</p>
                        <button class="btn-primary" onclick="showAddSliderForm()">
                            <i class="fas fa-plus"></i>
                            إضافة أول سلايدر
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Theme Colors Management Section -->
        <section id="theme-colors" class="content-section" style="display: none;">
            <div class="section-header">
                <h2>
                    <i class="fas fa-palette"></i>
                    إدارة ألوان الموقع
                </h2>
                <p>تخصيص ألوان وثيمات الموقع الإلكتروني</p>
            </div>

            <!-- Statistics Cards -->
            <div class="theme-stats">
                <div class="stat-card-modern primary">
                    <div class="stat-icon-modern">
                        <i class="fas fa-palette"></i>
                    </div>
                    <div class="stat-content-modern">
                        <div class="stat-number" id="totalThemesCount">0</div>
                        <div class="stat-label">إجمالي الثيمات</div>
                        <div class="stat-trend">
                            <i class="fas fa-paint-brush"></i>
                            <span>جميع الثيمات المحفوظة</span>
                        </div>
                    </div>
                    <div class="stat-decoration">
                        <div class="decoration-circle"></div>
                        <div class="decoration-circle"></div>
                    </div>
                </div>

                <div class="stat-card-modern success">
                    <div class="stat-icon-modern">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-content-modern">
                        <div class="stat-number" id="activeThemeCount">1</div>
                        <div class="stat-label">الثيم النشط</div>
                        <div class="stat-trend">
                            <i class="fas fa-eye"></i>
                            <span>معروض في الموقع</span>
                        </div>
                    </div>
                    <div class="stat-decoration">
                        <div class="decoration-circle"></div>
                        <div class="decoration-circle"></div>
                    </div>
                </div>

                <div class="stat-card-modern warning">
                    <div class="stat-icon-modern">
                        <i class="fas fa-swatchbook"></i>
                    </div>
                    <div class="stat-content-modern">
                        <div class="stat-number" id="customColorsCount">0</div>
                        <div class="stat-label">الألوان المخصصة</div>
                        <div class="stat-trend">
                            <i class="fas fa-droplet"></i>
                            <span>ألوان مخصصة</span>
                        </div>
                    </div>
                    <div class="stat-decoration">
                        <div class="decoration-circle"></div>
                        <div class="decoration-circle"></div>
                    </div>
                </div>

                <div class="stat-card-modern info">
                    <div class="stat-icon-modern">
                        <i class="fas fa-magic"></i>
                    </div>
                    <div class="stat-content-modern">
                        <div class="stat-number" id="lastModifiedCount">0</div>
                        <div class="stat-label">آخر تعديل</div>
                        <div class="stat-trend">
                            <i class="fas fa-clock"></i>
                            <span>منذ أيام</span>
                        </div>
                    </div>
                    <div class="stat-decoration">
                        <div class="decoration-circle"></div>
                        <div class="decoration-circle"></div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="theme-actions-modern">
                <button class="btn-action-modern primary" onclick="showColorCustomizer()">
                    <div class="btn-icon">
                        <i class="fas fa-paint-brush"></i>
                    </div>
                    <div class="btn-content">
                        <span class="btn-title">تخصيص الألوان</span>
                        <span class="btn-subtitle">تعديل ألوان الموقع</span>
                    </div>
                </button>

                <button class="btn-action-modern secondary" onclick="resetToDefaultTheme()">
                    <div class="btn-icon">
                        <i class="fas fa-undo"></i>
                    </div>
                    <div class="btn-content">
                        <span class="btn-title">استعادة الافتراضي</span>
                        <span class="btn-subtitle">العودة للثيم الأصلي</span>
                    </div>
                </button>

                <button class="btn-action-modern success" onclick="previewTheme()">
                    <div class="btn-icon">
                        <i class="fas fa-eye"></i>
                    </div>
                    <div class="btn-content">
                        <span class="btn-title">معاينة الموقع</span>
                        <span class="btn-subtitle">عرض في نافذة جديدة</span>
                    </div>
                </button>

                <button class="btn-action-modern info" onclick="exportThemeData()">
                    <div class="btn-icon">
                        <i class="fas fa-download"></i>
                    </div>
                    <div class="btn-content">
                        <span class="btn-title">تصدير الثيم</span>
                        <span class="btn-subtitle">حفظ ملف JSON</span>
                    </div>
                </button>
            </div>

            <!-- Color Customizer Panel -->
            <div class="color-customizer-panel">
                <div class="customizer-header">
                    <h3>
                        <i class="fas fa-palette"></i>
                        تخصيص ألوان الموقع
                    </h3>
                    <p>اختر الألوان المناسبة لموقعك الإلكتروني</p>
                </div>

                <div class="color-sections">
                    <!-- ألوان الهيدر -->
                    <div class="color-section">
                        <div class="section-title">
                            <i class="fas fa-window-maximize"></i>
                            <span>ألوان الهيدر والقائمة</span>
                        </div>
                        <div class="color-controls">
                            <div class="color-control">
                                <label>لون خلفية الهيدر</label>
                                <input type="color" id="headerBgColor" value="#2c3e50" onchange="updateThemeColor('headerBg', this.value)">
                                <span class="color-value">#2c3e50</span>
                            </div>
                            <div class="color-control">
                                <label>لون نص القائمة</label>
                                <input type="color" id="navTextColor" value="#ffffff" onchange="updateThemeColor('navText', this.value)">
                                <span class="color-value">#ffffff</span>
                            </div>
                            <div class="color-control">
                                <label>لون الرابط النشط</label>
                                <input type="color" id="navActiveColor" value="#3498db" onchange="updateThemeColor('navActive', this.value)">
                                <span class="color-value">#3498db</span>
                            </div>
                        </div>
                    </div>

                    <!-- ألوان الأزرار -->
                    <div class="color-section">
                        <div class="section-title">
                            <i class="fas fa-mouse-pointer"></i>
                            <span>ألوان الأزرار</span>
                        </div>
                        <div class="color-controls">
                            <div class="color-control">
                                <label>الزر الأساسي</label>
                                <input type="color" id="primaryBtnColor" value="#3498db" onchange="updateThemeColor('primaryBtn', this.value)">
                                <span class="color-value">#3498db</span>
                            </div>
                            <div class="color-control">
                                <label>الزر الثانوي</label>
                                <input type="color" id="secondaryBtnColor" value="#95a5a6" onchange="updateThemeColor('secondaryBtn', this.value)">
                                <span class="color-value">#95a5a6</span>
                            </div>
                            <div class="color-control">
                                <label>زر النجاح</label>
                                <input type="color" id="successBtnColor" value="#27ae60" onchange="updateThemeColor('successBtn', this.value)">
                                <span class="color-value">#27ae60</span>
                            </div>
                        </div>
                    </div>

                    <!-- ألوان المحتوى -->
                    <div class="color-section">
                        <div class="section-title">
                            <i class="fas fa-file-alt"></i>
                            <span>ألوان المحتوى</span>
                        </div>
                        <div class="color-controls">
                            <div class="color-control">
                                <label>خلفية الصفحة</label>
                                <input type="color" id="bodyBgColor" value="#ffffff" onchange="updateThemeColor('bodyBg', this.value)">
                                <span class="color-value">#ffffff</span>
                            </div>
                            <div class="color-control">
                                <label>لون النص الأساسي</label>
                                <input type="color" id="textColor" value="#2c3e50" onchange="updateThemeColor('text', this.value)">
                                <span class="color-value">#2c3e50</span>
                            </div>
                            <div class="color-control">
                                <label>لون النص الثانوي</label>
                                <input type="color" id="secondaryTextColor" value="#7f8c8d" onchange="updateThemeColor('secondaryText', this.value)">
                                <span class="color-value">#7f8c8d</span>
                            </div>
                        </div>
                    </div>

                    <!-- ألوان البطاقات -->
                    <div class="color-section">
                        <div class="section-title">
                            <i class="fas fa-id-card"></i>
                            <span>ألوان البطاقات</span>
                        </div>
                        <div class="color-controls">
                            <div class="color-control">
                                <label>خلفية البطاقة</label>
                                <input type="color" id="cardBgColor" value="#ffffff" onchange="updateThemeColor('cardBg', this.value)">
                                <span class="color-value">#ffffff</span>
                            </div>
                            <div class="color-control">
                                <label>حدود البطاقة</label>
                                <input type="color" id="cardBorderColor" value="#e9ecef" onchange="updateThemeColor('cardBorder', this.value)">
                                <span class="color-value">#e9ecef</span>
                            </div>
                            <div class="color-control">
                                <label>ظل البطاقة</label>
                                <input type="color" id="cardShadowColor" value="#000000" onchange="updateThemeColor('cardShadow', this.value)">
                                <span class="color-value">#000000</span>
                            </div>
                        </div>
                    </div>

                    <!-- ألوان الفوتر -->
                    <div class="color-section">
                        <div class="section-title">
                            <i class="fas fa-window-minimize"></i>
                            <span>ألوان الفوتر</span>
                        </div>
                        <div class="color-controls">
                            <div class="color-control">
                                <label>خلفية الفوتر</label>
                                <input type="color" id="footerBgColor" value="#2c3e50" onchange="updateThemeColor('footerBg', this.value)">
                                <span class="color-value">#2c3e50</span>
                            </div>
                            <div class="color-control">
                                <label>نص الفوتر</label>
                                <input type="color" id="footerTextColor" value="#ffffff" onchange="updateThemeColor('footerText', this.value)">
                                <span class="color-value">#ffffff</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معاينة مباشرة -->
                <div class="live-preview">
                    <div class="preview-header">
                        <h4>
                            <i class="fas fa-eye"></i>
                            معاينة مباشرة
                        </h4>
                    </div>
                    <div class="preview-content" id="livePreview">
                        <!-- سيتم إنشاء المعاينة هنا -->
                    </div>
                </div>

                <!-- أزرار الحفظ -->
                <div class="theme-save-actions">
                    <button class="btn-save-theme" onclick="saveThemeChanges()">
                        <i class="fas fa-save"></i>
                        حفظ التغييرات
                    </button>
                    <button class="btn-cancel-theme" onclick="cancelThemeChanges()">
                        <i class="fas fa-times"></i>
                        إلغاء التغييرات
                    </button>
                    <button class="btn-apply-theme" onclick="applyThemeToSite()">
                        <i class="fas fa-check"></i>
                        تطبيق على الموقع
                    </button>
                </div>
            </div>
        </section>

        <!-- Excel Upload Section -->
        <section id="excel-upload" class="content-section" style="display: none;">
            <div class="section-header">
                <h2>
                    <i class="fas fa-file-excel"></i>
                    رفع المنتجات من Excel أو CSV
                </h2>
                <p>استيراد المنتجات بكميات كبيرة من ملفات Excel أو CSV</p>
            </div>

            <!-- Statistics Cards -->
            <div class="excel-stats">
                <div class="stat-card-modern primary">
                    <div class="stat-icon-modern">
                        <i class="fas fa-file-excel"></i>
                    </div>
                    <div class="stat-content-modern">
                        <div class="stat-number" id="totalUploadsCount">0</div>
                        <div class="stat-label">إجمالي الرفعات</div>
                        <div class="stat-trend">
                            <i class="fas fa-upload"></i>
                            <span>ملفات تم رفعها</span>
                        </div>
                    </div>
                    <div class="stat-decoration">
                        <div class="decoration-circle"></div>
                        <div class="decoration-circle"></div>
                    </div>
                </div>

                <div class="stat-card-modern success">
                    <div class="stat-icon-modern">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-content-modern">
                        <div class="stat-number" id="successfulProductsCount">0</div>
                        <div class="stat-label">منتجات مضافة</div>
                        <div class="stat-trend">
                            <i class="fas fa-plus"></i>
                            <span>تم إضافتها بنجاح</span>
                        </div>
                    </div>
                    <div class="stat-decoration">
                        <div class="decoration-circle"></div>
                        <div class="decoration-circle"></div>
                    </div>
                </div>

                <div class="stat-card-modern warning">
                    <div class="stat-icon-modern">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="stat-content-modern">
                        <div class="stat-number" id="failedProductsCount">0</div>
                        <div class="stat-label">منتجات فاشلة</div>
                        <div class="stat-trend">
                            <i class="fas fa-times"></i>
                            <span>لم يتم إضافتها</span>
                        </div>
                    </div>
                    <div class="stat-decoration">
                        <div class="decoration-circle"></div>
                        <div class="decoration-circle"></div>
                    </div>
                </div>

                <div class="stat-card-modern info">
                    <div class="stat-icon-modern">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-content-modern">
                        <div class="stat-number" id="lastUploadTime">لا يوجد</div>
                        <div class="stat-label">آخر رفعة</div>
                        <div class="stat-trend">
                            <i class="fas fa-history"></i>
                            <span>تاريخ آخر استيراد</span>
                        </div>
                    </div>
                    <div class="stat-decoration">
                        <div class="decoration-circle"></div>
                        <div class="decoration-circle"></div>
                    </div>
                </div>
            </div>

            <!-- Upload Instructions -->
            <div class="upload-instructions">
                <div class="instructions-header">
                    <h3>
                        <i class="fas fa-info-circle"></i>
                        تعليمات رفع ملف Excel أو CSV
                    </h3>
                </div>
                <div class="instructions-content">
                    <div class="instruction-step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h4>تحضير ملف Excel أو CSV</h4>
                            <p>يجب أن يحتوي الملف على الأعمدة التالية بالترتيب:</p>
                            <div class="columns-list">
                                <span class="column-item">اسم المنتج (عربي)</span>
                                <span class="column-item">اسم المنتج (إنجليزي)</span>
                                <span class="column-item">الفئة</span>
                                <span class="column-item">السعر (مفرد)</span>
                                <span class="column-item">سعر الجملة</span>
                                <span class="column-item">السعر القديم</span>
                                <span class="column-item">الوصف (عربي)</span>
                                <span class="column-item">الوصف (إنجليزي)</span>
                                <span class="column-item">رابط الصورة</span>
                                <span class="column-item">الكمية</span>
                                <span class="column-item">الحالة</span>
                            </div>
                        </div>
                    </div>

                    <div class="instruction-step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h4>تنسيق البيانات</h4>
                            <ul>
                                <li>السعر (مفرد): أرقام فقط (مثال: 15000)</li>
                                <li>سعر الجملة: أرقام فقط، يجب أن يكون أقل من سعر المفرد</li>
                                <li>السعر القديم: أرقام أو فارغ للمنتجات بدون خصم</li>
                                <li>رابط الصورة: رابط كامل للصورة</li>
                                <li>الكمية: رقم صحيح</li>
                                <li>الحالة: "متوفر" أو "غير متوفر"</li>
                            </ul>
                        </div>
                    </div>

                    <div class="instruction-step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h4>رفع الملف</h4>
                            <p>اختر ملف Excel (.xlsx أو .xls) أو CSV (.csv) واضغط على "رفع وتحليل"</p>
                        </div>
                    </div>
                </div>

                <div class="download-template">
                    <button class="btn-download-template" onclick="downloadExcelTemplate()">
                        <i class="fas fa-download"></i>
                        تحميل نموذج Excel
                    </button>
                </div>
            </div>

            <!-- Upload Area -->
            <div class="excel-upload-area">
                <div class="upload-header">
                    <h3>
                        <i class="fas fa-cloud-upload-alt"></i>
                        رفع ملف Excel أو CSV
                    </h3>
                </div>

                <div class="upload-zone" id="uploadZone" onclick="document.getElementById('excelFileInput').click()" style="cursor: pointer;">
                    <div class="upload-icon">
                        <i class="fas fa-file-excel"></i>
                    </div>
                    <div class="upload-text">
                        <h4>اسحب ملف Excel أو CSV هنا أو انقر للاختيار</h4>
                        <p>يدعم ملفات .xlsx و .xls و .csv بحجم أقصى 10 ميجابايت</p>
                    </div>
                    <input type="file" id="excelFileInput" accept=".xlsx,.xls,.csv" style="display: none;">
                    <button class="btn-select-file" id="btnSelectFile" type="button" onclick="document.getElementById('excelFileInput').click()">
                        <i class="fas fa-folder-open"></i>
                        اختيار ملف
                    </button>
                </div>

                <div class="upload-progress" id="uploadProgress" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">جاري الرفع...</div>
                </div>

                <div class="upload-actions">
                    <button class="btn-upload-excel" id="btnUploadExcel" onclick="uploadExcelFile()" disabled>
                        <i class="fas fa-upload"></i>
                        رفع وتحليل الملف
                    </button>
                    <button class="btn-clear-file" id="btnClearFile" onclick="clearSelectedFile()" style="display: none;">
                        <i class="fas fa-times"></i>
                        إلغاء الملف
                    </button>
                    <button class="btn-test-upload" id="btnTestUpload" onclick="fullUploadTest()" style="background: #e74c3c; color: white; padding: 0.5rem 1rem; border: none; border-radius: 5px; margin-left: 1rem;">
                        <i class="fas fa-bug"></i>
                        اختبار
                    </button>
                </div>
            </div>

            <!-- Preview Results -->
            <div class="excel-preview" id="excelPreview" style="display: none;">
                <div class="preview-header">
                    <h3>
                        <i class="fas fa-eye"></i>
                        معاينة البيانات المستوردة
                    </h3>
                    <div class="preview-stats">
                        <span class="preview-stat success">
                            <i class="fas fa-check"></i>
                            <span id="validRowsCount">0</span> صحيح
                        </span>
                        <span class="preview-stat error">
                            <i class="fas fa-times"></i>
                            <span id="invalidRowsCount">0</span> خطأ
                        </span>
                    </div>
                </div>

                <div class="preview-table-container">
                    <table class="preview-table" id="previewTable">
                        <thead>
                            <tr>
                                <th>الحالة</th>
                                <th>اسم المنتج</th>
                                <th>الفئة</th>
                                <th>السعر</th>
                                <th>الكمية</th>
                                <th>الصورة</th>
                                <th>ملاحظات</th>
                            </tr>
                        </thead>
                        <tbody id="previewTableBody">
                            <!-- سيتم ملء البيانات هنا -->
                        </tbody>
                    </table>
                </div>

                <div class="preview-actions">
                    <button class="btn-confirm-import" id="btnConfirmImport" onclick="confirmImportProducts()">
                        <i class="fas fa-check"></i>
                        تأكيد الاستيراد
                    </button>
                    <button class="btn-cancel-import" onclick="cancelImport()">
                        <i class="fas fa-times"></i>
                        إلغاء الاستيراد
                    </button>
                </div>
            </div>

            <!-- Import Results -->
            <div class="import-results" id="importResults" style="display: none;">
                <div class="results-header">
                    <h3>
                        <i class="fas fa-chart-pie"></i>
                        نتائج الاستيراد
                    </h3>
                </div>

                <div class="results-summary">
                    <div class="result-card success">
                        <div class="result-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="result-content">
                            <div class="result-number" id="importedCount">0</div>
                            <div class="result-label">تم استيرادها</div>
                        </div>
                    </div>

                    <div class="result-card error">
                        <div class="result-icon">
                            <i class="fas fa-times-circle"></i>
                        </div>
                        <div class="result-content">
                            <div class="result-number" id="failedCount">0</div>
                            <div class="result-label">فشل الاستيراد</div>
                        </div>
                    </div>

                    <div class="result-card info">
                        <div class="result-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="result-content">
                            <div class="result-number" id="processingTime">0</div>
                            <div class="result-label">ثانية</div>
                        </div>
                    </div>
                </div>

                <div class="results-details" id="resultsDetails">
                    <!-- سيتم ملء تفاصيل النتائج هنا -->
                </div>

                <div class="results-actions">
                    <button class="btn-new-upload" onclick="resetUploadForm()">
                        <i class="fas fa-plus"></i>
                        رفع ملف جديد
                    </button>
                    <button class="btn-view-products" onclick="showSection('products')">
                        <i class="fas fa-eye"></i>
                        عرض المنتجات
                    </button>
                </div>
            </div>
        </section>

        <!-- Employee Management Section -->
        <section id="employee-management" class="content-section" style="display: none;">
            <!-- Header Section -->
            <div class="employee-header">
                <div class="header-content">
                    <div class="header-text">
                        <h1 class="page-title">
                            <i class="fas fa-users-cog"></i>
                            إدارة الموظفين
                        </h1>
                        <p class="page-subtitle">إدارة شاملة لحسابات الموظفين والصلاحيات والأدوار</p>
                    </div>
                    <div class="header-actions">
                        <button class="btn-primary-modern" onclick="showAddEmployeeForm()">
                            <i class="fas fa-user-plus"></i>
                            <span>إضافة موظف جديد</span>
                        </button>
                        <button class="btn-secondary-modern" onclick="refreshEmployeesList()">
                            <i class="fas fa-sync-alt"></i>
                            <span>تحديث</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Statistics Cards -->
            <div class="stats-container">
                <div class="stat-card-modern total">
                    <div class="stat-icon-wrapper">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" id="totalEmployeesCount">0</div>
                        <div class="stat-label">إجمالي الموظفين</div>
                        <div class="stat-trend">
                            <i class="fas fa-chart-line"></i>
                            <span>جميع الحسابات</span>
                        </div>
                    </div>
                </div>

                <div class="stat-card-modern active">
                    <div class="stat-icon-wrapper">
                        <div class="stat-icon">
                            <i class="fas fa-user-check"></i>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" id="activeEmployeesCount">0</div>
                        <div class="stat-label">الحسابات النشطة</div>
                        <div class="stat-trend">
                            <i class="fas fa-arrow-up"></i>
                            <span>متاح للعمل</span>
                        </div>
                    </div>
                </div>

                <div class="stat-card-modern admin">
                    <div class="stat-icon-wrapper">
                        <div class="stat-icon">
                            <i class="fas fa-user-shield"></i>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" id="adminEmployeesCount">0</div>
                        <div class="stat-label">المديرين</div>
                        <div class="stat-trend">
                            <i class="fas fa-crown"></i>
                            <span>صلاحيات كاملة</span>
                        </div>
                    </div>
                </div>

                <div class="stat-card-modern inactive">
                    <div class="stat-icon-wrapper">
                        <div class="stat-icon">
                            <i class="fas fa-user-times"></i>
                        </div>
                    </div>
                    <div class="stat-content">
                        <div class="stat-number" id="inactiveEmployeesCount">0</div>
                        <div class="stat-label">غير النشطة</div>
                        <div class="stat-trend">
                            <i class="fas fa-pause"></i>
                            <span>معطلة مؤقتاً</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Employees Management Panel -->
            <div class="employees-panel">
                <!-- Control Bar -->
                <div class="control-bar">
                    <div class="control-left">
                        <h2 class="panel-title">
                            <i class="fas fa-list-ul"></i>
                            قائمة الموظفين
                        </h2>
                        <span class="employees-count">
                            <span id="displayedEmployeesCount">0</span> موظف
                        </span>
                    </div>
                    <div class="control-right">
                        <div class="search-controls">
                            <div class="search-input-wrapper">
                                <i class="fas fa-search"></i>
                                <input type="text" id="employeeSearchInput" placeholder="البحث بالاسم أو اسم المستخدم..." onkeyup="filterEmployees()">
                            </div>
                            <div class="filter-dropdown">
                                <select id="roleFilterSelect" onchange="filterEmployees()">
                                    <option value="">جميع الأدوار</option>
                                    <option value="admin">👑 مدير</option>
                                    <option value="supervisor">👨‍💼 مشرف</option>
                                    <option value="cashier">💰 كاشير</option>
                                    <option value="employee">👤 موظف عادي</option>
                                </select>
                            </div>
                            <button class="btn-export" onclick="exportEmployeesData()" title="تصدير البيانات">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Employees Table -->
                <div class="table-container">
                    <div class="table-wrapper">
                        <table class="modern-table" id="employeesTable">
                            <thead>
                                <tr>
                                    <th class="col-employee">
                                        <i class="fas fa-user"></i>
                                        الموظف
                                    </th>
                                    <th class="col-username">
                                        <i class="fas fa-at"></i>
                                        اسم المستخدم
                                    </th>
                                    <th class="col-role">
                                        <i class="fas fa-user-tag"></i>
                                        الدور
                                    </th>
                                    <th class="col-status">
                                        <i class="fas fa-toggle-on"></i>
                                        الحالة
                                    </th>
                                    <th class="col-date">
                                        <i class="fas fa-calendar"></i>
                                        تاريخ الإنشاء
                                    </th>
                                    <th class="col-actions">
                                        <i class="fas fa-cogs"></i>
                                        الإجراءات
                                    </th>
                                </tr>
                            </thead>
                            <tbody id="employeesTableBody">
                                <!-- سيتم ملء البيانات هنا -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Empty State -->
                <div class="empty-state-modern" id="noEmployeesMessage" style="display: none;">
                    <div class="empty-icon">
                        <i class="fas fa-users-slash"></i>
                    </div>
                    <h3 class="empty-title">لا توجد حسابات موظفين</h3>
                    <p class="empty-description">
                        لم يتم إنشاء أي حسابات موظفين بعد. ابدأ بإضافة أول موظف للنظام.
                    </p>
                    <button class="btn-primary-modern" onclick="showAddEmployeeForm()">
                        <i class="fas fa-user-plus"></i>
                        <span>إضافة أول موظف</span>
                    </button>
                </div>
            </div>

            <!-- Roles Information Panel -->
            <div class="roles-info-panel">
                <div class="info-header">
                    <h3>
                        <i class="fas fa-info-circle"></i>
                        دليل الأدوار والصلاحيات
                    </h3>
                    <p>تعرف على صلاحيات كل دور في النظام</p>
                </div>

                <div class="roles-grid">
                    <div class="role-card admin-role">
                        <div class="role-icon">
                            <i class="fas fa-crown"></i>
                        </div>
                        <div class="role-content">
                            <h4>مدير النظام</h4>
                            <p>صلاحية كاملة وغير محدودة</p>
                            <ul class="permissions-list">
                                <li><i class="fas fa-check"></i> إدارة جميع المنتجات والفئات</li>
                                <li><i class="fas fa-check"></i> إدارة حسابات الموظفين</li>
                                <li><i class="fas fa-check"></i> الوصول لجميع التقارير</li>
                                <li><i class="fas fa-check"></i> إعدادات النظام والأمان</li>
                            </ul>
                        </div>
                    </div>

                    <div class="role-card supervisor-role">
                        <div class="role-icon">
                            <i class="fas fa-user-tie"></i>
                        </div>
                        <div class="role-content">
                            <h4>مشرف</h4>
                            <p>إدارة العمليات اليومية</p>
                            <ul class="permissions-list">
                                <li><i class="fas fa-check"></i> إدارة المنتجات والمخزون</li>
                                <li><i class="fas fa-check"></i> متابعة الطلبات والمبيعات</li>
                                <li><i class="fas fa-check"></i> إدارة الموظفين العاديين</li>
                                <li><i class="fas fa-times"></i> لا يمكن الوصول لإعدادات النظام</li>
                            </ul>
                        </div>
                    </div>

                    <div class="role-card cashier-role">
                        <div class="role-icon">
                            <i class="fas fa-cash-register"></i>
                        </div>
                        <div class="role-content">
                            <h4>كاشير</h4>
                            <p>متخصص في المبيعات والدفع</p>
                            <ul class="permissions-list">
                                <li><i class="fas fa-check"></i> نظام الكاشير والمبيعات</li>
                                <li><i class="fas fa-check"></i> طباعة الفواتير</li>
                                <li><i class="fas fa-check"></i> عرض المنتجات فقط</li>
                                <li><i class="fas fa-times"></i> لا يمكن تعديل المنتجات</li>
                            </ul>
                        </div>
                    </div>

                    <div class="role-card employee-role">
                        <div class="role-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="role-content">
                            <h4>موظف عادي</h4>
                            <p>صلاحيات محدودة حسب التخصص</p>
                            <ul class="permissions-list">
                                <li><i class="fas fa-check"></i> عرض المنتجات والمعلومات</li>
                                <li><i class="fas fa-check"></i> تسجيل الدخول للنظام</li>
                                <li><i class="fas fa-times"></i> لا يمكن تعديل أي بيانات</li>
                                <li><i class="fas fa-times"></i> لا يمكن الوصول للإعدادات</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        </section>

        <!-- Reports Section -->
        <section id="reports" class="content-section">
            <div class="section-header">
                <h2>التقارير والإحصائيات</h2>
                <p>عرض شامل للبيانات والتقارير المختلفة</p>
            </div>

            <div class="reports-dashboard">
                <!-- إحصائيات سريعة -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalSalesReport">0</h3>
                            <p>إجمالي المبيعات</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalOrdersReport">0</h3>
                            <p>إجمالي الطلبات</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-box"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalProductsReport">0</h3>
                            <p>إجمالي المنتجات</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalCustomersReport">0</h3>
                            <p>إجمالي العملاء</p>
                        </div>
                    </div>
                </div>

                <!-- تقارير مفصلة -->
                <div class="reports-grid">
                    <div class="report-card">
                        <div class="report-header">
                            <h3>تقرير المبيعات اليومية</h3>
                            <i class="fas fa-calendar-day"></i>
                        </div>
                        <div class="report-content">
                            <div class="report-chart">
                                <canvas id="dailySalesChart"></canvas>
                            </div>
                            <div class="report-summary">
                                <p>مبيعات اليوم: <span id="todaySales">0 دينار</span></p>
                                <p>مقارنة بالأمس: <span id="salesComparison">+0%</span></p>
                            </div>
                        </div>
                    </div>

                    <div class="report-card">
                        <div class="report-header">
                            <h3>المنتجات الأكثر مبيعاً</h3>
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="report-content">
                            <div class="top-products-list" id="topProductsList">
                                <div class="product-item">
                                    <span class="product-name">تفاح أحمر</span>
                                    <span class="product-sales">150 قطعة</span>
                                </div>
                                <div class="product-item">
                                    <span class="product-name">دجاج طازج</span>
                                    <span class="product-sales">89 قطعة</span>
                                </div>
                                <div class="product-item">
                                    <span class="product-name">أرز بسمتي</span>
                                    <span class="product-sales">67 قطعة</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="report-card">
                        <div class="report-header">
                            <h3>حالة المخزون</h3>
                            <i class="fas fa-warehouse"></i>
                        </div>
                        <div class="report-content">
                            <div class="inventory-status">
                                <div class="status-item">
                                    <span class="status-label">متوفر</span>
                                    <span class="status-count" id="availableProducts">0</span>
                                </div>
                                <div class="status-item warning">
                                    <span class="status-label">ينفد قريباً</span>
                                    <span class="status-count" id="lowStockProducts">0</span>
                                </div>
                                <div class="status-item danger">
                                    <span class="status-label">نفد المخزون</span>
                                    <span class="status-count" id="outOfStockProducts">0</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="report-card">
                        <div class="report-header">
                            <h3>أداء الموظفين</h3>
                            <i class="fas fa-users-cog"></i>
                        </div>
                        <div class="report-content">
                            <div class="employee-performance" id="employeePerformance">
                                <div class="performance-item">
                                    <span class="employee-name">أحمد علي</span>
                                    <span class="employee-sales">25,000 دينار</span>
                                </div>
                                <div class="performance-item">
                                    <span class="employee-name">فاطمة محمد</span>
                                    <span class="employee-sales">18,500 دينار</span>
                                </div>
                                <div class="performance-item">
                                    <span class="employee-name">محمد أحمد</span>
                                    <span class="employee-sales">15,200 دينار</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أزرار التقارير -->
                <div class="reports-actions">
                    <button class="btn-primary" onclick="generateDailyReport()">
                        <i class="fas fa-file-pdf"></i>
                        تقرير يومي
                    </button>
                    <button class="btn-secondary" onclick="generateWeeklyReport()">
                        <i class="fas fa-file-excel"></i>
                        تقرير أسبوعي
                    </button>
                    <button class="btn-success" onclick="generateMonthlyReport()">
                        <i class="fas fa-file-chart-line"></i>
                        تقرير شهري
                    </button>
                    <button class="btn-info" onclick="exportAllData()">
                        <i class="fas fa-download"></i>
                        تصدير البيانات
                    </button>
                </div>
            </div>
        </section>
    </div>

    <!-- Modals will be added here -->
    <div id="modalContainer"></div>

    <script src="admin-script.js?v=2024"></script>
    <script src="employee-management.js?v=2024"></script>

    <!-- نظام إدارة الموظفين -->
    <script>
        // نظام إدارة حسابات دخول الموظفين
        let currentEmployees = [];
        let isEmployeeSystemInitialized = false;

        function initializeEmployeeSystem() {
            if (isEmployeeSystemInitialized) return;
            loadEmployeesFromStorage();
            createDefaultAdminAccount();
            isEmployeeSystemInitialized = true;
            console.log('✅ تم تهيئة نظام إدارة الموظفين');
        }

        function loadEmployeesFromStorage() {
            try {
                const stored = localStorage.getItem('employees');
                currentEmployees = stored ? JSON.parse(stored) : [];
                console.log(`📋 تم تحميل ${currentEmployees.length} موظف`);
            } catch (error) {
                console.error('❌ خطأ في تحميل بيانات الموظفين:', error);
                currentEmployees = [];
            }
        }

        function saveEmployeesToStorage() {
            try {
                localStorage.setItem('employees', JSON.stringify(currentEmployees));
                console.log('💾 تم حفظ بيانات الموظفين');
                return true;
            } catch (error) {
                console.error('❌ خطأ في حفظ بيانات الموظفين:', error);
                return false;
            }
        }

        function createDefaultAdminAccount() {
            // إنشاء حسابات افتراضية للاختبار إذا لم تكن موجودة
            if (currentEmployees.length === 0) {
                const defaultEmployees = [
                    {
                        id: 'emp_001',
                        name: 'علي أحمد المدير',
                        username: 'ali',
                        password: '12',
                        role: 'admin',
                        status: 'active',
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString()
                    },
                    {
                        id: 'emp_002',
                        name: 'فاطمة محمد المشرفة',
                        username: 'fatima',
                        password: '123',
                        role: 'supervisor',
                        status: 'active',
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString()
                    },
                    {
                        id: 'emp_003',
                        name: 'أحمد علي الكاشير',
                        username: 'ahmed',
                        password: '123',
                        role: 'cashier',
                        status: 'active',
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString()
                    },
                    {
                        id: 'emp_004',
                        name: 'سارة محمد الموظفة',
                        username: 'sara',
                        password: '123',
                        role: 'employee',
                        status: 'active',
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString()
                    },
                    {
                        id: 'emp_005',
                        name: 'محمد أحمد (غير نشط)',
                        username: 'mohammed',
                        password: '123',
                        role: 'employee',
                        status: 'inactive',
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString()
                    }
                ];

                currentEmployees = defaultEmployees;
                saveEmployeesToStorage();
                console.log('✅ تم إنشاء الحسابات الافتراضية للاختبار');
            }
        }

        function openEmployeeManagement() {
            console.log('🔐 فتح نظام إدارة الموظفين...');
            initializeEmployeeSystem();
            updateEmployeeStats();
            loadAndDisplayEmployees();
        }

        function updateEmployeeStats() {
            const totalEmployees = currentEmployees.length;
            const activeEmployees = currentEmployees.filter(emp => emp.status === 'active').length;
            const inactiveEmployees = currentEmployees.filter(emp => emp.status === 'inactive').length;
            const adminEmployees = currentEmployees.filter(emp => emp.role === 'admin').length;

            // تحديث الإحصائيات في القسم مع تأثيرات بصرية
            updateStatCard('totalEmployeesCount', totalEmployees);
            updateStatCard('activeEmployeesCount', activeEmployees);
            updateStatCard('inactiveEmployeesCount', inactiveEmployees);
            updateStatCard('adminEmployeesCount', adminEmployees);

            // تحديث عداد الموظفين المعروضين
            updateDisplayedCount();

            console.log(`📊 تم تحديث إحصائيات الموظفين: ${totalEmployees} إجمالي، ${activeEmployees} نشط، ${adminEmployees} مدير`);
        }

        function updateStatCard(elementId, value) {
            const element = document.getElementById(elementId);
            if (element) {
                // إضافة تأثير التحديث
                element.style.transform = 'scale(1.1)';
                element.style.transition = 'transform 0.3s ease';

                setTimeout(() => {
                    element.textContent = value;
                    element.style.transform = 'scale(1)';
                }, 150);
            }
        }

        function loadAndDisplayEmployees() {
            console.log('📋 تحميل وعرض قائمة الموظفين...');

            const tableBody = document.getElementById('employeesTableBody');
            const noEmployeesMessage = document.getElementById('noEmployeesMessage');
            const tableContainer = document.querySelector('.table-container');

            if (!tableBody) {
                console.error('❌ لم يتم العثور على جدول الموظفين');
                return;
            }

            // مسح المحتوى السابق مع تأثير بصري
            tableBody.style.opacity = '0.5';
            tableBody.innerHTML = '';

            if (currentEmployees.length === 0) {
                // إظهار رسالة عدم وجود موظفين
                if (noEmployeesMessage) {
                    noEmployeesMessage.style.display = 'block';
                }
                if (tableContainer) {
                    tableContainer.style.display = 'none';
                }
                return;
            }

            // إخفاء رسالة عدم وجود موظفين
            if (noEmployeesMessage) {
                noEmployeesMessage.style.display = 'none';
            }
            if (tableContainer) {
                tableContainer.style.display = 'block';
            }

            // إضافة الموظفين للجدول مع تأثير تدريجي
            currentEmployees.forEach((employee, index) => {
                setTimeout(() => {
                    const row = createEmployeeRow(employee, index);
                    row.style.opacity = '0';
                    row.style.transform = 'translateY(10px)';
                    tableBody.appendChild(row);

                    // تأثير الظهور التدريجي
                    setTimeout(() => {
                        row.style.transition = 'all 0.3s ease';
                        row.style.opacity = '1';
                        row.style.transform = 'translateY(0)';
                    }, 50);
                }, index * 50);
            });

            // استعادة شفافية الجدول
            setTimeout(() => {
                tableBody.style.transition = 'opacity 0.3s ease';
                tableBody.style.opacity = '1';
            }, 100);

            console.log(`✅ تم عرض ${currentEmployees.length} موظف في الجدول`);
        }

        function createEmployeeRow(employee, index) {
            const row = document.createElement('tr');
            row.className = `employee-row ${employee.status}`;

            // إنشاء شارة الحالة مع أيقونة
            const statusBadge = employee.status === 'active' ?
                '<span class="status-badge active"><i class="fas fa-check-circle"></i> نشط</span>' :
                '<span class="status-badge inactive"><i class="fas fa-times-circle"></i> غير نشط</span>';

            // إنشاء شارة الدور
            const roleBadge = getRoleBadge(employee.role);

            // تنسيق التاريخ الإنجليزي
            const createdDate = formatEnglishDate(employee.createdAt);

            // إنشاء الأفاتار بالحرف الأول من الاسم
            const firstLetter = employee.name.charAt(0).toUpperCase();

            row.innerHTML = `
                <td>
                    <div class="employee-info-cell">
                        <div class="employee-avatar">
                            ${firstLetter}
                        </div>
                        <div class="employee-details">
                            <strong>${employee.name}</strong>
                            <small>ID: ${employee.id}</small>
                        </div>
                    </div>
                </td>
                <td>
                    <code class="username-code">@${employee.username}</code>
                </td>
                <td>${roleBadge}</td>
                <td>${statusBadge}</td>
                <td>
                    <div style="display: flex; flex-direction: column; gap: 0.25rem;">
                        <span style="color: #2c3e50; font-size: 0.85rem; font-weight: 600;"
                              title="تاريخ إنشاء الحساب: ${formatDetailedDate(employee.createdAt)}">
                            <i class="fas fa-calendar-plus" style="margin-left: 0.5rem; color: #56ab2f;"></i>
                            ${createdDate}
                        </span>
                        ${employee.updatedAt ? `
                            <span style="color: #7f8c8d; font-size: 0.75rem;"
                                  title="آخر تحديث: ${formatDetailedDate(employee.updatedAt)}">
                                <i class="fas fa-edit" style="margin-left: 0.5rem; opacity: 0.7;"></i>
                                آخر تحديث: ${formatEnglishDate(employee.updatedAt)}
                            </span>
                        ` : ''}
                    </div>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn-sm btn-primary" onclick="editEmployee(${index})"
                                title="تعديل بيانات ${employee.name}">
                            <i class="fas fa-user-edit"></i>
                        </button>
                        <button class="btn-sm btn-warning" onclick="toggleEmployeeStatus(${index})"
                                title="${employee.status === 'active' ? 'إلغاء تفعيل' : 'تفعيل'} حساب ${employee.name}">
                            <i class="fas fa-toggle-${employee.status === 'active' ? 'off' : 'on'}"></i>
                        </button>
                        ${employee.username !== 'ali' ? `
                            <button class="btn-sm btn-danger" onclick="deleteEmployee(${index})"
                                    title="حذف حساب ${employee.name} نهائياً">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                        ` : `
                            <button class="btn-sm" style="background: #95a5a6; cursor: not-allowed;"
                                    title="حساب المدير الرئيسي محمي من الحذف" disabled>
                                <i class="fas fa-shield-alt"></i>
                            </button>
                        `}
                    </div>
                </td>
            `;

            return row;
        }

        function getRoleBadge(role) {
            const roleBadges = {
                'admin': '<span class="role-badge admin"><i class="fas fa-crown"></i> مدير النظام</span>',
                'supervisor': '<span class="role-badge supervisor"><i class="fas fa-user-tie"></i> مشرف</span>',
                'cashier': '<span class="role-badge cashier"><i class="fas fa-cash-register"></i> كاشير</span>',
                'employee': '<span class="role-badge employee"><i class="fas fa-user"></i> موظف عادي</span>'
            };
            return roleBadges[role] || '<span class="role-badge" style="background: #95a5a6; color: white;"><i class="fas fa-question"></i> غير محدد</span>';
        }

        // وظائف إدارة الموظفين المحسنة
        function refreshEmployeesList() {
            console.log('🔄 تحديث قائمة الموظفين...');
            loadEmployeesFromStorage();
            updateEmployeeStats();
            loadAndDisplayEmployees();
            updateDisplayedCount();
        }

        function updateDisplayedCount() {
            const displayedCount = document.getElementById('displayedEmployeesCount');
            if (displayedCount) {
                displayedCount.textContent = currentEmployees.length;
            }
        }

        function filterEmployees() {
            const searchTerm = document.getElementById('employeeSearchInput').value.toLowerCase();
            const roleFilter = document.getElementById('roleFilterSelect').value;

            const filteredEmployees = currentEmployees.filter(employee => {
                const matchesSearch = employee.name.toLowerCase().includes(searchTerm) ||
                                    employee.username.toLowerCase().includes(searchTerm);
                const matchesRole = !roleFilter || employee.role === roleFilter;

                return matchesSearch && matchesRole;
            });

            displayFilteredEmployees(filteredEmployees);
        }

        function displayFilteredEmployees(employees) {
            const tableBody = document.getElementById('employeesTableBody');
            const noEmployeesMessage = document.getElementById('noEmployeesMessage');
            const tableContainer = document.querySelector('.table-container');

            if (!tableBody) return;

            tableBody.innerHTML = '';

            if (employees.length === 0) {
                if (noEmployeesMessage) {
                    noEmployeesMessage.style.display = 'block';
                }
                if (tableContainer) {
                    tableContainer.style.display = 'none';
                }
                return;
            }

            if (noEmployeesMessage) {
                noEmployeesMessage.style.display = 'none';
            }
            if (tableContainer) {
                tableContainer.style.display = 'block';
            }

            employees.forEach((employee, index) => {
                const originalIndex = currentEmployees.findIndex(emp => emp.id === employee.id);
                const row = createEmployeeRow(employee, originalIndex);
                tableBody.appendChild(row);
            });

            // تحديث عداد الموظفين المعروضين
            const displayedCount = document.getElementById('displayedEmployeesCount');
            if (displayedCount) {
                displayedCount.textContent = employees.length;
            }
        }

        function editEmployee(index) {
            const employee = currentEmployees[index];
            if (!employee) {
                alert('خطأ: لم يتم العثور على الموظف');
                return;
            }

            console.log('✏️ تعديل الموظف:', employee.name);
            showEditEmployeeForm(employee, index);
        }

        // نموذج تعديل الموظف
        function showEditEmployeeForm(employee, index) {
            const modalHTML = `
                <div class="employee-modal-overlay" id="employeeModalOverlay">
                    <div class="employee-modal-modern">
                        <div class="modal-header-modern">
                            <div class="header-icon">
                                <i class="fas fa-user-edit"></i>
                            </div>
                            <div class="header-text">
                                <h3>تعديل بيانات الموظف</h3>
                                <p>تحديث معلومات الموظف "${employee.name}"</p>
                            </div>
                            <button class="close-btn-modern" onclick="closeEmployeeModal()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>

                        <div class="modal-body-modern">
                            <form id="editEmployeeForm" onsubmit="updateEmployee(event, ${index})">
                                <!-- معلومات شخصية -->
                                <div class="form-section">
                                    <div class="section-header">
                                        <i class="fas fa-user"></i>
                                        <span>المعلومات الشخصية</span>
                                    </div>

                                    <div class="form-group-modern">
                                        <label class="form-label-modern">
                                            <i class="fas fa-user"></i>
                                            الاسم الكامل
                                        </label>
                                        <input type="text" id="editEmployeeName" class="form-input-modern" value="${employee.name}" required>
                                    </div>
                                </div>

                                <!-- معلومات الحساب -->
                                <div class="form-section">
                                    <div class="section-header">
                                        <i class="fas fa-key"></i>
                                        <span>معلومات تسجيل الدخول</span>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-at"></i>
                                                اسم المستخدم
                                            </label>
                                            <input type="text" id="editEmployeeUsername" class="form-input-modern" value="${employee.username}" required>
                                        </div>

                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-lock"></i>
                                                كلمة المرور الجديدة
                                            </label>
                                            <input type="password" id="editEmployeePassword" class="form-input-modern" placeholder="اتركها فارغة للاحتفاظ بكلمة المرور الحالية">
                                        </div>
                                    </div>
                                </div>

                                <!-- الصلاحيات -->
                                <div class="form-section">
                                    <div class="section-header">
                                        <i class="fas fa-cogs"></i>
                                        <span>الصلاحيات والحالة</span>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-user-tag"></i>
                                                المنصب
                                            </label>
                                            <select id="editEmployeeRole" class="form-select-modern" required>
                                                <option value="employee" ${employee.role === 'employee' ? 'selected' : ''}>👤 موظف عادي</option>
                                                <option value="cashier" ${employee.role === 'cashier' ? 'selected' : ''}>💰 كاشير</option>
                                                <option value="supervisor" ${employee.role === 'supervisor' ? 'selected' : ''}>👨‍💼 مشرف</option>
                                                <option value="admin" ${employee.role === 'admin' ? 'selected' : ''}>👑 مدير</option>
                                            </select>
                                        </div>

                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-toggle-on"></i>
                                                حالة الحساب
                                            </label>
                                            <select id="editEmployeeStatus" class="form-select-modern" required>
                                                <option value="active" ${employee.status === 'active' ? 'selected' : ''}>✅ نشط</option>
                                                <option value="inactive" ${employee.status === 'inactive' ? 'selected' : ''}>❌ غير نشط</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- معلومات إضافية -->
                                <div class="form-section">
                                    <div class="section-header">
                                        <i class="fas fa-info-circle"></i>
                                        <span>معلومات الحساب</span>
                                    </div>

                                    <div class="info-display">
                                        <div class="info-item">
                                            <span class="info-label">معرف الحساب:</span>
                                            <span class="info-value">${employee.id}</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">تاريخ الإنشاء:</span>
                                            <span class="info-value">${employee.createdAt ? new Date(employee.createdAt).toLocaleDateString('en-US', {
                                                year: 'numeric',
                                                month: 'long',
                                                day: 'numeric',
                                                hour: '2-digit',
                                                minute: '2-digit'
                                            }) : 'غير محدد'}</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- رسالة النتيجة -->
                                <div id="editEmployeeMessage" class="message-modern" style="display: none;"></div>

                                <!-- أزرار التحكم -->
                                <div class="form-actions-modern">
                                    <button type="button" class="btn-cancel-modern" onclick="closeEmployeeModal()">
                                        <i class="fas fa-times"></i>
                                        <span>إلغاء</span>
                                    </button>
                                    <button type="submit" class="btn-submit-modern">
                                        <i class="fas fa-save"></i>
                                        <span>حفظ التغييرات</span>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            `;
            document.getElementById('modalContainer').innerHTML = modalHTML;
        }

        // وظيفة تحديث بيانات الموظف
        function updateEmployee(event, index) {
            event.preventDefault();

            const name = document.getElementById('editEmployeeName').value.trim();
            const username = document.getElementById('editEmployeeUsername').value.trim();
            const password = document.getElementById('editEmployeePassword').value;
            const role = document.getElementById('editEmployeeRole').value;
            const status = document.getElementById('editEmployeeStatus').value;

            if (!name || !username || !role || !status) {
                showEditEmployeeMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
                return;
            }

            // التحقق من عدم تكرار اسم المستخدم
            const existingEmployee = currentEmployees.find((emp, i) =>
                emp.username === username && i !== index
            );

            if (existingEmployee) {
                showEditEmployeeMessage('اسم المستخدم موجود بالفعل لدى موظف آخر', 'error');
                return;
            }

            // تحديث بيانات الموظف
            const employee = currentEmployees[index];
            employee.name = name;
            employee.username = username;
            if (password && password.trim() !== '') {
                employee.password = password;
            }
            employee.role = role;
            employee.status = status;
            employee.updatedAt = new Date().toISOString();

            // حفظ التغييرات
            if (saveEmployeesToStorage()) {
                showEditEmployeeMessage('تم تحديث بيانات الموظف بنجاح!', 'success');
                setTimeout(() => {
                    closeEmployeeModal();
                    refreshEmployeesList();
                    showNotification(`تم تحديث بيانات "${employee.name}" بنجاح`, 'success');
                }, 1500);
                console.log(`✅ تم تحديث بيانات الموظف: ${employee.name}`);
            } else {
                showEditEmployeeMessage('حدث خطأ أثناء حفظ التغييرات', 'error');
            }
        }

        // عرض رسالة في نموذج التعديل
        function showEditEmployeeMessage(message, type) {
            const messageDiv = document.getElementById('editEmployeeMessage');
            if (messageDiv) {
                const icon = type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle';
                messageDiv.innerHTML = `<i class="${icon}"></i> ${message}`;
                messageDiv.className = `message-modern ${type}`;
                messageDiv.style.display = 'block';

                // إضافة تأثير الظهور
                messageDiv.style.opacity = '0';
                messageDiv.style.transform = 'translateY(-10px)';

                setTimeout(() => {
                    messageDiv.style.transition = 'all 0.3s ease';
                    messageDiv.style.opacity = '1';
                    messageDiv.style.transform = 'translateY(0)';
                }, 10);

                // إخفاء الرسالة بعد 5 ثوان
                setTimeout(() => {
                    messageDiv.style.transition = 'all 0.3s ease';
                    messageDiv.style.opacity = '0';
                    messageDiv.style.transform = 'translateY(-10px)';
                    setTimeout(() => {
                        messageDiv.style.display = 'none';
                    }, 300);
                }, 5000);
            }
        }

        function toggleEmployeeStatus(index) {
            const employee = currentEmployees[index];
            if (!employee) return;

            const newStatus = employee.status === 'active' ? 'inactive' : 'active';
            const statusText = newStatus === 'active' ? 'تفعيل' : 'إلغاء تفعيل';
            const statusIcon = newStatus === 'active' ? '✅' : '❌';

            const confirmMessage = `${statusIcon} ${statusText} حساب الموظف\n\n` +
                                 `الاسم: ${employee.name}\n` +
                                 `اسم المستخدم: ${employee.username}\n` +
                                 `الدور: ${getRoleText(employee.role)}\n\n` +
                                 `هل أنت متأكد من هذا الإجراء؟`;

            if (confirm(confirmMessage)) {
                currentEmployees[index].status = newStatus;
                currentEmployees[index].updatedAt = new Date().toISOString();

                if (saveEmployeesToStorage()) {
                    console.log(`✅ تم ${statusText} حساب ${employee.name}`);
                    refreshEmployeesList();

                    // عرض رسالة نجاح
                    showNotification(`تم ${statusText} حساب "${employee.name}" بنجاح`, 'success');
                } else {
                    alert('حدث خطأ أثناء تحديث حالة الموظف');
                }
            }
        }

        function deleteEmployee(index) {
            const employee = currentEmployees[index];
            if (!employee) return;

            if (employee.username === 'ali') {
                alert('🛡️ لا يمكن حذف حساب المدير الرئيسي!\n\nهذا الحساب محمي من الحذف لضمان أمان النظام.');
                return;
            }

            const confirmMessage = `🗑️ حذف حساب الموظف نهائياً\n\n` +
                                 `الاسم: ${employee.name}\n` +
                                 `اسم المستخدم: ${employee.username}\n` +
                                 `الدور: ${getRoleText(employee.role)}\n` +
                                 `تاريخ الإنشاء: ${formatEnglishDate(employee.createdAt)}\n\n` +
                                 `⚠️ تحذير: هذا الإجراء لا يمكن التراجع عنه!\n\n` +
                                 `هل أنت متأكد من حذف هذا الحساب؟`;

            if (confirm(confirmMessage)) {
                currentEmployees.splice(index, 1);

                if (saveEmployeesToStorage()) {
                    console.log(`🗑️ تم حذف حساب ${employee.name}`);
                    refreshEmployeesList();

                    // عرض رسالة نجاح
                    showNotification(`تم حذف حساب "${employee.name}" نهائياً`, 'success');
                } else {
                    alert('حدث خطأ أثناء حذف الموظف');
                }
            }
        }

        function exportEmployeesData() {
            console.log('📤 تصدير بيانات الموظفين...');

            if (currentEmployees.length === 0) {
                alert('لا توجد بيانات موظفين للتصدير');
                return;
            }

            // إنشاء البيانات للتصدير
            const csvContent = generateEmployeesCSV();
            downloadCSV(csvContent, `employees_data_${new Date().toISOString().split('T')[0]}.csv`);
        }

        function generateEmployeesCSV() {
            const headers = ['الاسم', 'اسم المستخدم', 'الدور', 'الحالة', 'تاريخ الإنشاء', 'تاريخ آخر تحديث'];
            const rows = currentEmployees.map(emp => [
                emp.name,
                emp.username,
                getRoleText(emp.role),
                emp.status === 'active' ? 'نشط' : 'غير نشط',
                emp.createdAt ? new Date(emp.createdAt).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                }) : 'غير محدد',
                emp.updatedAt ? new Date(emp.updatedAt).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'short',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                }) : 'لم يتم التحديث'
            ]);

            return [headers, ...rows].map(row => row.join(',')).join('\n');
        }

        function downloadCSV(content, filename) {
            const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');

            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', filename);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                console.log('✅ تم تصدير البيانات بنجاح');
            }
        }

        function getRoleText(role) {
            const roleTexts = {
                'admin': 'مدير النظام',
                'supervisor': 'مشرف',
                'cashier': 'كاشير',
                'employee': 'موظف عادي'
            };
            return roleTexts[role] || 'غير محدد';
        }

        // وظيفة عرض الإشعارات
        function showNotification(message, type = 'info', duration = 3000) {
            // إنشاء عنصر الإشعار
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;

            const icon = type === 'success' ? 'fas fa-check-circle' :
                        type === 'error' ? 'fas fa-exclamation-triangle' :
                        'fas fa-info-circle';

            notification.innerHTML = `
                <div class="notification-content">
                    <i class="${icon}"></i>
                    <span>${message}</span>
                </div>
                <button class="notification-close" onclick="this.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            `;

            // إضافة الإشعار للصفحة
            document.body.appendChild(notification);

            // تأثير الظهور
            setTimeout(() => {
                notification.classList.add('show');
            }, 10);

            // إزالة الإشعار تلقائياً
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    if (notification.parentElement) {
                        notification.remove();
                    }
                }, 300);
            }, duration);
        }

        // وظيفة تنسيق التاريخ الإنجليزي
        function formatEnglishDate(dateString, includeTime = false) {
            if (!dateString) return 'غير محدد';

            const date = new Date(dateString);
            const options = {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            };

            if (includeTime) {
                options.hour = '2-digit';
                options.minute = '2-digit';
            }

            return date.toLocaleDateString('en-US', options);
        }

        // وظيفة تنسيق التاريخ المفصل
        function formatDetailedDate(dateString) {
            if (!dateString) return 'غير محدد';

            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }



        function closeEmployeeModal() {
            const modalOverlay = document.getElementById('employeeModalOverlay');
            if (modalOverlay) {
                // إضافة تأثير الإغلاق
                modalOverlay.style.transition = 'all 0.3s ease';
                modalOverlay.style.opacity = '0';

                const modal = modalOverlay.querySelector('.employee-modal-modern');
                if (modal) {
                    modal.style.transition = 'all 0.3s ease';
                    modal.style.transform = 'translateY(30px) scale(0.95)';
                }

                setTimeout(() => {
                    const modalContainer = document.getElementById('modalContainer');
                    if (modalContainer) {
                        modalContainer.innerHTML = '';
                    }
                }, 300);
            }
            console.log('❌ تم إغلاق النافذة');
        }

        // إضافة مستمع للنقر خارج النموذج لإغلاقه
        document.addEventListener('click', function(e) {
            if (e.target && e.target.classList.contains('employee-modal-overlay')) {
                closeEmployeeModal();
            }
        });

        // إضافة مستمع لمفتاح Escape لإغلاق النموذج
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const modalOverlay = document.getElementById('employeeModalOverlay');
                if (modalOverlay) {
                    closeEmployeeModal();
                }
            }
        });





        // نموذج إضافة موظف جديد احترافي
        function showAddEmployeeForm() {
            const modalHTML = `
                <div class="employee-modal-overlay" id="employeeModalOverlay">
                    <div class="employee-modal-modern">
                        <div class="modal-header-modern">
                            <div class="header-icon">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="header-text">
                                <h3>إضافة موظف جديد</h3>
                                <p>إنشاء حساب دخول جديد مع تحديد الصلاحيات</p>
                            </div>
                            <button class="close-btn-modern" onclick="closeEmployeeModal()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>

                        <div class="modal-body-modern">
                            <form id="addEmployeeForm" onsubmit="createNewEmployee(event)">
                                <!-- معلومات شخصية -->
                                <div class="form-section">
                                    <div class="section-header">
                                        <i class="fas fa-user"></i>
                                        <span>المعلومات الشخصية</span>
                                    </div>

                                    <div class="form-group-modern">
                                        <label class="form-label-modern">
                                            <i class="fas fa-user"></i>
                                            الاسم الكامل
                                        </label>
                                        <input type="text" id="employeeName" class="form-input-modern" placeholder="أدخل الاسم الكامل للموظف" required>
                                    </div>
                                </div>

                                <!-- معلومات الحساب -->
                                <div class="form-section">
                                    <div class="section-header">
                                        <i class="fas fa-key"></i>
                                        <span>معلومات تسجيل الدخول</span>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-at"></i>
                                                اسم المستخدم
                                            </label>
                                            <input type="text" id="employeeUsername" class="form-input-modern" placeholder="اسم المستخدم (بالإنجليزية)" required>
                                        </div>

                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-lock"></i>
                                                كلمة المرور
                                            </label>
                                            <input type="password" id="employeePassword" class="form-input-modern" placeholder="كلمة المرور (3 أحرف على الأقل)" required>
                                        </div>
                                    </div>
                                </div>

                                <!-- الصلاحيات -->
                                <div class="form-section">
                                    <div class="section-header">
                                        <i class="fas fa-cogs"></i>
                                        <span>الصلاحيات والحالة</span>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-user-tag"></i>
                                                المنصب
                                            </label>
                                            <select id="employeeRole" class="form-select-modern" required>
                                                <option value="">اختر المنصب</option>
                                                <option value="employee">👤 موظف عادي</option>
                                                <option value="cashier">💰 كاشير</option>
                                                <option value="supervisor">👨‍💼 مشرف</option>
                                                <option value="admin">👑 مدير</option>
                                            </select>
                                        </div>

                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-toggle-on"></i>
                                                حالة الحساب
                                            </label>
                                            <select id="employeeStatus" class="form-select-modern" required>
                                                <option value="active">✅ نشط</option>
                                                <option value="inactive">❌ غير نشط</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <!-- رسالة النتيجة -->
                                <div id="addEmployeeMessage" class="message-modern" style="display: none;"></div>

                                <!-- أزرار التحكم -->
                                <div class="form-actions-modern">
                                    <button type="button" class="btn-cancel-modern" onclick="closeEmployeeModal()">
                                        <i class="fas fa-times"></i>
                                        <span>إلغاء</span>
                                    </button>
                                    <button type="submit" class="btn-submit-modern">
                                        <i class="fas fa-save"></i>
                                        <span>إنشاء الحساب</span>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            `;
            document.getElementById('modalContainer').innerHTML = modalHTML;
        }

        // إنشاء موظف جديد
        function createNewEmployee(event) {
            event.preventDefault();

            const name = document.getElementById('employeeName').value.trim();
            const username = document.getElementById('employeeUsername').value.trim();
            const password = document.getElementById('employeePassword').value;
            const role = document.getElementById('employeeRole').value;
            const status = document.getElementById('employeeStatus').value;

            if (!name || !username || !password || !role) {
                showEmployeeMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
                return;
            }

            if (currentEmployees.find(emp => emp.username === username)) {
                showEmployeeMessage('اسم المستخدم موجود بالفعل', 'error');
                return;
            }

            const newEmployee = {
                id: 'emp_' + Date.now(),
                name: name,
                username: username,
                password: password,
                role: role,
                status: status,
                createdAt: new Date().toISOString()
            };

            currentEmployees.push(newEmployee);

            if (saveEmployeesToStorage()) {
                showEmployeeMessage('تم إنشاء حساب الموظف بنجاح!', 'success');
                document.getElementById('addEmployeeForm').reset();
                setTimeout(() => {
                    closeEmployeeModal();
                    refreshEmployeesList();
                    showNotification(`تم إنشاء حساب "${name}" بنجاح`, 'success');
                }, 1500);
            } else {
                showEmployeeMessage('حدث خطأ في حفظ بيانات الموظف', 'error');
            }
        }

        // عرض رسالة في النموذج الحديث
        function showEmployeeMessage(message, type) {
            const messageDiv = document.getElementById('addEmployeeMessage');
            if (messageDiv) {
                const icon = type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle';
                messageDiv.innerHTML = `<i class="${icon}"></i> ${message}`;
                messageDiv.className = `message-modern ${type}`;
                messageDiv.style.display = 'block';

                // إضافة تأثير الظهور
                messageDiv.style.opacity = '0';
                messageDiv.style.transform = 'translateY(-10px)';

                setTimeout(() => {
                    messageDiv.style.transition = 'all 0.3s ease';
                    messageDiv.style.opacity = '1';
                    messageDiv.style.transform = 'translateY(0)';
                }, 10);

                // إخفاء الرسالة بعد 5 ثوان
                setTimeout(() => {
                    messageDiv.style.transition = 'all 0.3s ease';
                    messageDiv.style.opacity = '0';
                    messageDiv.style.transform = 'translateY(-10px)';
                    setTimeout(() => {
                        messageDiv.style.display = 'none';
                    }, 300);
                }, 5000);
            }
        }

        // إضافة تحسينات للتفاعل مع الواجهة
        function addEmployeeInteractions() {
            // إضافة تأثيرات hover للبطاقات الإحصائية
            const statCards = document.querySelectorAll('.stat-card-modern');
            statCards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px) scale(1.02)';
                });

                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });

            // إضافة تأثيرات للأزرار
            const buttons = document.querySelectorAll('.btn-primary-modern, .btn-secondary-modern, .btn-export');
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });

            // تحسين البحث المباشر
            const searchInput = document.getElementById('employeeSearchInput');
            if (searchInput) {
                let searchTimeout;
                searchInput.addEventListener('input', function() {
                    clearTimeout(searchTimeout);
                    searchTimeout = setTimeout(() => {
                        filterEmployees();
                    }, 300);
                });
            }
        }

        // تشغيل التحسينات عند تحميل القسم
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                addEmployeeInteractions();
            }, 1000);
        });

        console.log('🚀 تم تحميل نظام إدارة الموظفين المحسن');

        // ===== نظام إدارة السلايدرات =====
        let sliders = [];

        // تهيئة نظام السلايدرات
        function initializeSliders() {
            loadSlidersFromStorage();
            updateSlidersStats();
            console.log('🎬 تم تهيئة نظام السلايدرات');
        }

        // تحميل السلايدرات من التخزين المحلي
        function loadSlidersFromStorage() {
            const storedSliders = localStorage.getItem('sliders');
            if (storedSliders) {
                try {
                    sliders = JSON.parse(storedSliders);
                    console.log(`📦 تم تحميل ${sliders.length} سلايدر من التخزين المحلي`);
                } catch (error) {
                    console.error('❌ خطأ في تحميل السلايدرات:', error);
                    sliders = [];
                }
            } else {
                // إنشاء سلايدرات افتراضية
                createDefaultSliders();
            }
        }

        // إنشاء سلايدرات افتراضية
        function createDefaultSliders() {
            sliders = [
                {
                    id: 'slider_1',
                    title: 'مرحباً بكم في متجرنا',
                    titleEn: 'Welcome to Our Store',
                    subtitle: 'أفضل المنتجات بأفضل الأسعار',
                    subtitleEn: 'Best Products at Best Prices',
                    description: 'اكتشف مجموعتنا الواسعة من المنتجات عالية الجودة',
                    descriptionEn: 'Discover our wide range of high-quality products',
                    image: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=1200&h=600&fit=crop',
                    buttonText: 'تسوق الآن',
                    buttonTextEn: 'Shop Now',
                    buttonLink: '#products',
                    backgroundColor: '#667eea',
                    textColor: '#ffffff',
                    buttonColor: '#56ab2f',
                    animation: 'slideInRight',
                    duration: 5000,
                    status: 'active',
                    order: 1,
                    createdAt: new Date().toISOString()
                },
                {
                    id: 'slider_2',
                    title: 'عروض خاصة',
                    titleEn: 'Special Offers',
                    subtitle: 'خصومات تصل إلى 50%',
                    subtitleEn: 'Discounts up to 50%',
                    description: 'لا تفوت فرصة الحصول على منتجاتك المفضلة بأسعار مذهلة',
                    descriptionEn: 'Don\'t miss the chance to get your favorite products at amazing prices',
                    image: 'https://images.unsplash.com/photo-1607082348824-0a96f2a4b9da?w=1200&h=600&fit=crop',
                    buttonText: 'اكتشف العروض',
                    buttonTextEn: 'Discover Offers',
                    buttonLink: '#offers',
                    backgroundColor: '#56ab2f',
                    textColor: '#ffffff',
                    buttonColor: '#667eea',
                    animation: 'fadeInUp',
                    duration: 6000,
                    status: 'active',
                    order: 2,
                    createdAt: new Date().toISOString()
                },
                {
                    id: 'slider_3',
                    title: 'منتجات جديدة',
                    titleEn: 'New Products',
                    subtitle: 'آخر الإضافات لمتجرنا',
                    subtitleEn: 'Latest additions to our store',
                    description: 'تصفح أحدث المنتجات التي وصلت حديثاً إلى متجرنا',
                    descriptionEn: 'Browse the latest products that have recently arrived at our store',
                    image: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=1200&h=600&fit=crop',
                    buttonText: 'استكشف الجديد',
                    buttonTextEn: 'Explore New',
                    buttonLink: '#featured',
                    backgroundColor: '#764ba2',
                    textColor: '#ffffff',
                    buttonColor: '#f093fb',
                    animation: 'zoomIn',
                    duration: 4000,
                    status: 'active',
                    order: 3,
                    createdAt: new Date().toISOString()
                }
            ];
            saveSlidersToStorage();
            console.log('✅ تم إنشاء السلايدرات الافتراضية');
        }

        // حفظ السلايدرات في التخزين المحلي
        function saveSlidersToStorage() {
            try {
                localStorage.setItem('sliders', JSON.stringify(sliders));
                console.log('💾 تم حفظ السلايدرات');
                return true;
            } catch (error) {
                console.error('❌ خطأ في حفظ السلايدرات:', error);
                return false;
            }
        }

        // تحديث إحصائيات السلايدرات
        function updateSlidersStats() {
            const totalSliders = sliders.length;
            const activeSliders = sliders.filter(slider => slider.status === 'active').length;
            const uniqueThemes = [...new Set(sliders.map(slider => slider.backgroundColor))].length;
            const uniqueAnimations = [...new Set(sliders.map(slider => slider.animation))].length;

            updateStatElement('totalSlidersCount', totalSliders);
            updateStatElement('activeSlidersCount', activeSliders);
            updateStatElement('slidersThemesCount', uniqueThemes);
            updateStatElement('slidersAnimationsCount', uniqueAnimations);

            console.log(`📊 تم تحديث إحصائيات السلايدرات: ${totalSliders} إجمالي، ${activeSliders} نشطة`);
        }

        // عرض وتحميل قائمة السلايدرات
        function loadAndDisplaySliders() {
            console.log('📋 تحميل وعرض قائمة السلايدرات...');

            const gridContainer = document.getElementById('slidersGridContainer');
            const noSlidersMessage = document.getElementById('noSlidersMessage');

            if (!gridContainer) {
                console.error('❌ لم يتم العثور على حاوي السلايدرات');
                return;
            }

            // مسح المحتوى السابق
            gridContainer.innerHTML = '';

            if (sliders.length === 0) {
                // إظهار رسالة عدم وجود سلايدرات
                if (noSlidersMessage) {
                    noSlidersMessage.style.display = 'block';
                }
                gridContainer.style.display = 'none';
                return;
            }

            // إخفاء رسالة عدم وجود سلايدرات
            if (noSlidersMessage) {
                noSlidersMessage.style.display = 'none';
            }
            gridContainer.style.display = 'grid';

            // ترتيب السلايدرات حسب الترتيب
            const sortedSliders = [...sliders].sort((a, b) => (a.order || 0) - (b.order || 0));

            // إضافة السلايدرات للشبكة
            sortedSliders.forEach((slider, index) => {
                const sliderCard = createSliderCard(slider, index);
                gridContainer.appendChild(sliderCard);
            });

            console.log(`✅ تم عرض ${sliders.length} سلايدر في الشبكة`);
        }

        // إنشاء بطاقة سلايدر
        function createSliderCard(slider, index) {
            const card = document.createElement('div');
            card.className = `slider-card-modern ${slider.status}`;

            const statusBadge = slider.status === 'active' ?
                '<span class="status-badge-modern active"><i class="fas fa-eye"></i> نشط</span>' :
                '<span class="status-badge-modern inactive"><i class="fas fa-eye-slash"></i> غير نشط</span>';

            const createdDate = formatEnglishDate(slider.createdAt);

            card.innerHTML = `
                <div class="slider-preview" style="background: linear-gradient(135deg, ${slider.backgroundColor}, ${slider.backgroundColor}88);">
                    <div class="slider-image" style="background-image: url('${slider.image}')"></div>
                    <div class="slider-overlay">
                        <div class="slider-content-preview">
                            <h3 style="color: ${slider.textColor}">${slider.title}</h3>
                            <p style="color: ${slider.textColor}88">${slider.subtitle}</p>
                            <button class="preview-btn" style="background: ${slider.buttonColor}; color: white;">
                                ${slider.buttonText}
                            </button>
                        </div>
                    </div>
                </div>

                <div class="slider-info">
                    <div class="slider-header">
                        <div class="slider-title">
                            <h4>${slider.title}</h4>
                            <small>${slider.titleEn || ''}</small>
                        </div>
                        ${statusBadge}
                    </div>

                    <div class="slider-details">
                        <div class="detail-row">
                            <span class="detail-label">الحركة:</span>
                            <span class="detail-value">${getAnimationName(slider.animation)}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">المدة:</span>
                            <span class="detail-value">${slider.duration / 1000} ثانية</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">الترتيب:</span>
                            <span class="detail-value">#${slider.order || 0}</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">تاريخ الإنشاء:</span>
                            <span class="detail-value">${createdDate}</span>
                        </div>
                    </div>

                    <div class="slider-actions">
                        <button class="btn-action-sm edit" onclick="editSlider(${index})" title="تعديل السلايدر">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-action-sm toggle ${slider.status}" onclick="toggleSliderStatus(${index})"
                                title="${slider.status === 'active' ? 'إلغاء تفعيل' : 'تفعيل'} السلايدر">
                            <i class="fas fa-toggle-${slider.status === 'active' ? 'off' : 'on'}"></i>
                        </button>
                        <button class="btn-action-sm preview" onclick="previewSingleSlider(${index})" title="معاينة السلايدر">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn-action-sm delete" onclick="deleteSlider(${index})" title="حذف السلايدر">
                            <i class="fas fa-trash-alt"></i>
                        </button>
                    </div>
                </div>
            `;

            // إضافة تأثير الظهور
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';

            setTimeout(() => {
                card.style.transition = 'all 0.5s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);

            return card;
        }

        // الحصول على اسم الحركة
        function getAnimationName(animation) {
            const animations = {
                'slideInRight': 'انزلاق من اليمين',
                'slideInLeft': 'انزلاق من اليسار',
                'slideInUp': 'انزلاق من الأعلى',
                'slideInDown': 'انزلاق من الأسفل',
                'fadeIn': 'ظهور تدريجي',
                'fadeInUp': 'ظهور من الأسفل',
                'fadeInDown': 'ظهور من الأعلى',
                'zoomIn': 'تكبير',
                'zoomOut': 'تصغير',
                'rotateIn': 'دوران',
                'bounceIn': 'ارتداد',
                'flipInX': 'قلب أفقي',
                'flipInY': 'قلب عمودي'
            };
            return animations[animation] || animation;
        }

        // تحديث قائمة السلايدرات
        function refreshSlidersList() {
            console.log('🔄 تحديث قائمة السلايدرات...');
            loadSlidersFromStorage();
            updateSlidersStats();
            loadAndDisplaySliders();
        }

        // عرض نموذج إضافة سلايدر جديد
        function showAddSliderForm() {
            const modalHTML = `
                <div class="employee-modal-overlay" id="employeeModalOverlay">
                    <div class="employee-modal-modern slider-modal">
                        <div class="modal-header-modern">
                            <div class="header-icon">
                                <i class="fas fa-images"></i>
                            </div>
                            <div class="header-text">
                                <h3>إضافة سلايدر جديد</h3>
                                <p>إنشاء سلايدر احترافي متحرك للموقع الرئيسي</p>
                            </div>
                            <button class="close-btn-modern" onclick="closeEmployeeModal()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>

                        <div class="modal-body-modern">
                            <form id="addSliderForm" onsubmit="createNewSlider(event)">
                                <!-- النصوص والمحتوى -->
                                <div class="form-section">
                                    <div class="section-header">
                                        <i class="fas fa-font"></i>
                                        <span>النصوص والمحتوى</span>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-heading"></i>
                                                العنوان الرئيسي (عربي)
                                            </label>
                                            <input type="text" id="sliderTitle" class="form-input-modern" placeholder="مثال: مرحباً بكم في متجرنا" required>
                                        </div>

                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-globe"></i>
                                                العنوان الرئيسي (إنجليزي)
                                            </label>
                                            <input type="text" id="sliderTitleEn" class="form-input-modern" placeholder="Welcome to Our Store">
                                        </div>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-text-height"></i>
                                                العنوان الفرعي (عربي)
                                            </label>
                                            <input type="text" id="sliderSubtitle" class="form-input-modern" placeholder="أفضل المنتجات بأفضل الأسعار">
                                        </div>

                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-globe"></i>
                                                العنوان الفرعي (إنجليزي)
                                            </label>
                                            <input type="text" id="sliderSubtitleEn" class="form-input-modern" placeholder="Best Products at Best Prices">
                                        </div>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-align-left"></i>
                                                الوصف (عربي)
                                            </label>
                                            <textarea id="sliderDescription" class="form-input-modern" rows="3" placeholder="وصف تفصيلي للسلايدر"></textarea>
                                        </div>

                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-globe"></i>
                                                الوصف (إنجليزي)
                                            </label>
                                            <textarea id="sliderDescriptionEn" class="form-input-modern" rows="3" placeholder="Detailed description of the slider"></textarea>
                                        </div>
                                    </div>
                                </div>

                                <!-- الصورة والرابط -->
                                <div class="form-section">
                                    <div class="section-header">
                                        <i class="fas fa-image"></i>
                                        <span>الصورة والرابط</span>
                                    </div>

                                    <div class="form-group-modern">
                                        <label class="form-label-modern">
                                            <i class="fas fa-upload"></i>
                                            رفع صورة من الكمبيوتر
                                        </label>
                                        <input type="file" id="sliderImageFile" class="form-input-modern" accept="image/*" onchange="handleSliderImageUpload(this, 'sliderImage')">
                                    </div>

                                    <div class="form-group-modern">
                                        <label class="form-label-modern">
                                            <i class="fas fa-link"></i>
                                            أو رابط الصورة
                                        </label>
                                        <input type="url" id="sliderImage" class="form-input-modern" placeholder="https://example.com/image.jpg" onchange="updateImagePreview('sliderImage')" required>
                                        <small style="color: #6c757d; font-size: 0.8rem;">يفضل استخدام صور بدقة 1200x600 بكسل</small>
                                    </div>

                                    <div class="image-preview" id="imagePreview" style="margin-top: 1rem;">
                                        <!-- معاينة الصورة ستظهر هنا -->
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-mouse-pointer"></i>
                                                نص الزر (عربي)
                                            </label>
                                            <input type="text" id="sliderButtonText" class="form-input-modern" placeholder="تسوق الآن" required>
                                        </div>

                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-globe"></i>
                                                نص الزر (إنجليزي)
                                            </label>
                                            <input type="text" id="sliderButtonTextEn" class="form-input-modern" placeholder="Shop Now">
                                        </div>
                                    </div>

                                    <div class="form-group-modern">
                                        <label class="form-label-modern">
                                            <i class="fas fa-link"></i>
                                            رابط الزر
                                        </label>
                                        <input type="text" id="sliderButtonLink" class="form-input-modern" placeholder="#products" required>
                                    </div>
                                </div>

                                <!-- الألوان والتصميم -->
                                <div class="form-section">
                                    <div class="section-header">
                                        <i class="fas fa-palette"></i>
                                        <span>الألوان والتصميم</span>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-fill-drip"></i>
                                                لون الخلفية
                                            </label>
                                            <input type="color" id="sliderBackgroundColor" class="form-input-modern color-input" value="#667eea">
                                        </div>

                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-font"></i>
                                                لون النص
                                            </label>
                                            <input type="color" id="sliderTextColor" class="form-input-modern color-input" value="#ffffff">
                                        </div>

                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-square"></i>
                                                لون الزر
                                            </label>
                                            <input type="color" id="sliderButtonColor" class="form-input-modern color-input" value="#56ab2f">
                                        </div>
                                    </div>
                                </div>

                                <!-- الحركة والتوقيت -->
                                <div class="form-section">
                                    <div class="section-header">
                                        <i class="fas fa-magic"></i>
                                        <span>الحركة والتوقيت</span>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-play"></i>
                                                نوع الحركة
                                            </label>
                                            <select id="sliderAnimation" class="form-select-modern" required>
                                                <option value="slideInRight">انزلاق من اليمين</option>
                                                <option value="slideInLeft">انزلاق من اليسار</option>
                                                <option value="slideInUp">انزلاق من الأعلى</option>
                                                <option value="slideInDown">انزلاق من الأسفل</option>
                                                <option value="fadeIn">ظهور تدريجي</option>
                                                <option value="fadeInUp">ظهور من الأسفل</option>
                                                <option value="fadeInDown">ظهور من الأعلى</option>
                                                <option value="zoomIn">تكبير</option>
                                                <option value="zoomOut">تصغير</option>
                                                <option value="rotateIn">دوران</option>
                                                <option value="bounceIn">ارتداد</option>
                                                <option value="flipInX">قلب أفقي</option>
                                                <option value="flipInY">قلب عمودي</option>
                                            </select>
                                        </div>

                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-clock"></i>
                                                مدة العرض (ثانية)
                                            </label>
                                            <input type="number" id="sliderDuration" class="form-input-modern" min="2" max="10" value="5" required>
                                        </div>

                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-sort-numeric-up"></i>
                                                ترتيب العرض
                                            </label>
                                            <input type="number" id="sliderOrder" class="form-input-modern" min="1" value="1" required>
                                        </div>
                                    </div>
                                </div>

                                <!-- الحالة -->
                                <div class="form-section">
                                    <div class="section-header">
                                        <i class="fas fa-toggle-on"></i>
                                        <span>حالة السلايدر</span>
                                    </div>

                                    <div class="form-group-modern">
                                        <label class="form-label-modern">
                                            <i class="fas fa-eye"></i>
                                            حالة العرض
                                        </label>
                                        <select id="sliderStatus" class="form-select-modern" required>
                                            <option value="active">✅ نشط - معروض في الموقع</option>
                                            <option value="inactive">❌ غير نشط - مخفي من الموقع</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- رسالة النتيجة -->
                                <div id="addSliderMessage" class="message-modern" style="display: none;"></div>

                                <!-- أزرار التحكم -->
                                <div class="form-actions-modern">
                                    <button type="button" class="btn-cancel-modern" onclick="closeEmployeeModal()">
                                        <i class="fas fa-times"></i>
                                        <span>إلغاء</span>
                                    </button>
                                    <button type="submit" class="btn-submit-modern">
                                        <i class="fas fa-save"></i>
                                        <span>إضافة السلايدر</span>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            `;
            document.getElementById('modalContainer').innerHTML = modalHTML;
        }

        // إنشاء سلايدر جديد
        function createNewSlider(event) {
            event.preventDefault();

            const title = document.getElementById('sliderTitle').value.trim();
            const titleEn = document.getElementById('sliderTitleEn').value.trim();
            const subtitle = document.getElementById('sliderSubtitle').value.trim();
            const subtitleEn = document.getElementById('sliderSubtitleEn').value.trim();
            const description = document.getElementById('sliderDescription').value.trim();
            const descriptionEn = document.getElementById('sliderDescriptionEn').value.trim();
            const image = document.getElementById('sliderImage').value.trim();
            const buttonText = document.getElementById('sliderButtonText').value.trim();
            const buttonTextEn = document.getElementById('sliderButtonTextEn').value.trim();
            const buttonLink = document.getElementById('sliderButtonLink').value.trim();
            const backgroundColor = document.getElementById('sliderBackgroundColor').value;
            const textColor = document.getElementById('sliderTextColor').value;
            const buttonColor = document.getElementById('sliderButtonColor').value;
            const animation = document.getElementById('sliderAnimation').value;
            const duration = parseInt(document.getElementById('sliderDuration').value) * 1000;
            const order = parseInt(document.getElementById('sliderOrder').value);
            const status = document.getElementById('sliderStatus').value;

            if (!title || !image || !buttonText || !buttonLink) {
                showSliderMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
                return;
            }

            // التحقق من صحة رابط الصورة
            if (!isValidImageUrl(image)) {
                showSliderMessage('يرجى إدخال رابط صورة صحيح', 'error');
                return;
            }

            const newSlider = {
                id: 'slider_' + Date.now(),
                title: title,
                titleEn: titleEn || title,
                subtitle: subtitle,
                subtitleEn: subtitleEn || subtitle,
                description: description,
                descriptionEn: descriptionEn || description,
                image: image,
                buttonText: buttonText,
                buttonTextEn: buttonTextEn || buttonText,
                buttonLink: buttonLink,
                backgroundColor: backgroundColor,
                textColor: textColor,
                buttonColor: buttonColor,
                animation: animation,
                duration: duration,
                order: order,
                status: status,
                createdAt: new Date().toISOString()
            };

            sliders.push(newSlider);

            if (saveSlidersToStorage()) {
                showSliderMessage('تم إضافة السلايدر بنجاح!', 'success');
                document.getElementById('addSliderForm').reset();
                setTimeout(() => {
                    closeEmployeeModal();
                    refreshSlidersList();
                    showNotification(`تم إضافة سلايدر "${title}" بنجاح`, 'success');
                }, 1500);
            } else {
                showSliderMessage('حدث خطأ في حفظ بيانات السلايدر', 'error');
            }
        }

        // التحقق من صحة رابط الصورة
        function isValidImageUrl(url) {
            const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'];
            const lowerUrl = url.toLowerCase();
            return imageExtensions.some(ext => lowerUrl.includes(ext)) || url.includes('unsplash.com') || url.includes('images.');
        }

        // عرض رسالة في نموذج السلايدر
        function showSliderMessage(message, type) {
            const messageDiv = document.getElementById('addSliderMessage');
            if (messageDiv) {
                const icon = type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle';
                messageDiv.innerHTML = `<i class="${icon}"></i> ${message}`;
                messageDiv.className = `message-modern ${type}`;
                messageDiv.style.display = 'block';

                messageDiv.style.opacity = '0';
                messageDiv.style.transform = 'translateY(-10px)';

                setTimeout(() => {
                    messageDiv.style.transition = 'all 0.3s ease';
                    messageDiv.style.opacity = '1';
                    messageDiv.style.transform = 'translateY(0)';
                }, 10);

                setTimeout(() => {
                    messageDiv.style.transition = 'all 0.3s ease';
                    messageDiv.style.opacity = '0';
                    messageDiv.style.transform = 'translateY(-10px)';
                    setTimeout(() => {
                        messageDiv.style.display = 'none';
                    }, 300);
                }, 5000);
            }
        }

        // حذف سلايدر
        function deleteSlider(index) {
            if (confirm('هل أنت متأكد من حذف هذا السلايدر؟')) {
                sliders.splice(index, 1);
                saveSlidersToStorage();
                updateSlidersStats();
                loadAndDisplaySliders();
                console.log(`🗑️ تم حذف السلايدر رقم ${index}`);

                // إظهار رسالة نجاح
                showNotification('تم حذف السلايدر بنجاح', 'success');
            }
        }

        // تنشيط/إلغاء تنشيط السلايدر
        function toggleSliderStatus(index) {
            if (sliders[index]) {
                sliders[index].status = sliders[index].status === 'active' ? 'inactive' : 'active';
                saveSlidersToStorage();
                updateSlidersStats();
                loadAndDisplaySliders();

                const statusText = sliders[index].status === 'active' ? 'تم تنشيط' : 'تم إلغاء تنشيط';
                console.log(`🔄 ${statusText} السلايدر رقم ${index}`);

                // إظهار رسالة نجاح
                showNotification(`${statusText} السلايدر بنجاح`, 'success');
            }
        }

        // معاينة سلايدر واحد
        function previewSingleSlider(index) {
            const slider = sliders[index];
            if (!slider) return;

            const previewHTML = `
                <div class="employee-modal-overlay" id="employeeModalOverlay">
                    <div class="employee-modal-modern slider-preview-modal">
                        <div class="modal-header-modern">
                            <div class="header-icon">
                                <i class="fas fa-eye"></i>
                            </div>
                            <div class="header-text">
                                <h3>معاينة السلايدر</h3>
                                <p>${slider.title}</p>
                            </div>
                            <button class="close-btn-modern" onclick="closeEmployeeModal()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>

                        <div class="modal-body-modern">
                            <div class="slider-preview-container" style="
                                background: ${slider.backgroundColor};
                                color: ${slider.textColor};
                                background-image: url('${slider.image}');
                                background-size: cover;
                                background-position: center;
                                min-height: 400px;
                                border-radius: 15px;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                position: relative;
                                overflow: hidden;
                            ">
                                <div style="
                                    background: rgba(0,0,0,0.4);
                                    padding: 3rem;
                                    border-radius: 15px;
                                    text-align: center;
                                    max-width: 600px;
                                ">
                                    <h1 style="font-size: 2.5rem; margin-bottom: 1rem; color: ${slider.textColor};">${slider.title}</h1>
                                    ${slider.subtitle ? `<h2 style="font-size: 1.5rem; margin-bottom: 1rem; color: ${slider.textColor};">${slider.subtitle}</h2>` : ''}
                                    ${slider.description ? `<p style="font-size: 1.1rem; margin-bottom: 2rem; color: ${slider.textColor};">${slider.description}</p>` : ''}
                                    ${slider.buttonText ? `<button style="
                                        background: ${slider.buttonColor};
                                        color: white;
                                        border: none;
                                        padding: 1rem 2rem;
                                        border-radius: 10px;
                                        font-size: 1.1rem;
                                        font-weight: 600;
                                        cursor: pointer;
                                    ">${slider.buttonText}</button>` : ''}
                                </div>
                            </div>

                            <div class="slider-details" style="margin-top: 2rem; padding: 1.5rem; background: #f8f9fa; border-radius: 10px;">
                                <h4>تفاصيل السلايدر:</h4>
                                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-top: 1rem;">
                                    <div><strong>الحالة:</strong> ${slider.status === 'active' ? 'نشط' : 'غير نشط'}</div>
                                    <div><strong>نوع الحركة:</strong> ${getAnimationName(slider.animation)}</div>
                                    <div><strong>مدة العرض:</strong> ${slider.duration / 1000} ثانية</div>
                                    <div><strong>ترتيب العرض:</strong> ${slider.order}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.getElementById('modalContainer').innerHTML = previewHTML;
        }

        // معالجة رفع الصورة من الكمبيوتر
        function handleSliderImageUpload(input, targetInputId) {
            const file = input.files[0];
            if (!file) return;

            // التحقق من نوع الملف
            if (!file.type.startsWith('image/')) {
                alert('يرجى اختيار ملف صورة صحيح');
                return;
            }

            // التحقق من حجم الملف (5MB كحد أقصى)
            if (file.size > 5 * 1024 * 1024) {
                alert('حجم الصورة كبير جداً. يرجى اختيار صورة أصغر من 5 ميجابايت');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                const imageData = e.target.result;

                // تحديث حقل رابط الصورة
                const targetInput = document.getElementById(targetInputId);
                if (targetInput) {
                    targetInput.value = imageData;
                }

                // تحديث معاينة الصورة
                updateImagePreview(targetInputId);

                console.log('✅ تم رفع الصورة بنجاح');
            };

            reader.onerror = function() {
                alert('حدث خطأ أثناء قراءة الصورة');
            };

            reader.readAsDataURL(file);
        }

        // تحديث معاينة الصورة
        function updateImagePreview(inputId) {
            const input = document.getElementById(inputId);
            if (!input || !input.value) return;

            const previewId = inputId.includes('edit') ? 'editImagePreview' : 'imagePreview';
            const previewContainer = document.getElementById(previewId);

            if (previewContainer) {
                previewContainer.innerHTML = `
                    <img src="${input.value}" alt="معاينة الصورة" style="
                        max-width: 100%;
                        height: 200px;
                        object-fit: cover;
                        border-radius: 10px;
                        border: 2px solid #e9ecef;
                    " onerror="this.style.display='none'">
                `;
            }
        }

        // تعديل سلايدر
        function editSlider(index) {
            const slider = sliders[index];
            if (!slider) return;

            const modalHTML = `
                <div class="employee-modal-overlay" id="employeeModalOverlay">
                    <div class="employee-modal-modern slider-modal">
                        <div class="modal-header-modern">
                            <div class="header-icon">
                                <i class="fas fa-edit"></i>
                            </div>
                            <div class="header-text">
                                <h3>تعديل السلايدر</h3>
                                <p>تحديث بيانات السلايدر الاحترافي</p>
                            </div>
                            <button class="close-btn-modern" onclick="closeEmployeeModal()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>

                        <div class="modal-body-modern">
                            <form id="editSliderForm" onsubmit="updateSlider(event, ${index})">
                                <!-- النصوص والمحتوى -->
                                <div class="form-section">
                                    <div class="section-header">
                                        <i class="fas fa-font"></i>
                                        <span>النصوص والمحتوى</span>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-heading"></i>
                                                العنوان الرئيسي (عربي)
                                            </label>
                                            <input type="text" id="editSliderTitle" class="form-input-modern" value="${slider.title}" required>
                                        </div>

                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-globe"></i>
                                                العنوان الرئيسي (إنجليزي)
                                            </label>
                                            <input type="text" id="editSliderTitleEn" class="form-input-modern" value="${slider.titleEn || ''}">
                                        </div>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-text-height"></i>
                                                العنوان الفرعي (عربي)
                                            </label>
                                            <input type="text" id="editSliderSubtitle" class="form-input-modern" value="${slider.subtitle || ''}">
                                        </div>

                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-globe"></i>
                                                العنوان الفرعي (إنجليزي)
                                            </label>
                                            <input type="text" id="editSliderSubtitleEn" class="form-input-modern" value="${slider.subtitleEn || ''}">
                                        </div>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-align-left"></i>
                                                الوصف (عربي)
                                            </label>
                                            <textarea id="editSliderDescription" class="form-input-modern" rows="3">${slider.description || ''}</textarea>
                                        </div>

                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-globe"></i>
                                                الوصف (إنجليزي)
                                            </label>
                                            <textarea id="editSliderDescriptionEn" class="form-input-modern" rows="3">${slider.descriptionEn || ''}</textarea>
                                        </div>
                                    </div>
                                </div>

                                <!-- الصورة -->
                                <div class="form-section">
                                    <div class="section-header">
                                        <i class="fas fa-image"></i>
                                        <span>صورة السلايدر</span>
                                    </div>

                                    <div class="form-group-modern">
                                        <label class="form-label-modern">
                                            <i class="fas fa-upload"></i>
                                            رفع صورة من الكمبيوتر
                                        </label>
                                        <input type="file" id="editSliderImageFile" class="form-input-modern" accept="image/*" onchange="handleSliderImageUpload(this, 'editSliderImage')">
                                    </div>

                                    <div class="form-group-modern">
                                        <label class="form-label-modern">
                                            <i class="fas fa-link"></i>
                                            أو رابط الصورة
                                        </label>
                                        <input type="url" id="editSliderImage" class="form-input-modern" value="${slider.image}" placeholder="https://example.com/image.jpg" onchange="updateImagePreview('editSliderImage')">
                                    </div>

                                    <div class="image-preview" id="editImagePreview">
                                        <img src="${slider.image}" alt="معاينة الصورة" style="max-width: 100%; height: 200px; object-fit: cover; border-radius: 10px;">
                                    </div>
                                </div>

                                <!-- الزر -->
                                <div class="form-section">
                                    <div class="section-header">
                                        <i class="fas fa-mouse-pointer"></i>
                                        <span>زر الإجراء</span>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-tag"></i>
                                                نص الزر (عربي)
                                            </label>
                                            <input type="text" id="editSliderButtonText" class="form-input-modern" value="${slider.buttonText || ''}">
                                        </div>

                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-globe"></i>
                                                نص الزر (إنجليزي)
                                            </label>
                                            <input type="text" id="editSliderButtonTextEn" class="form-input-modern" value="${slider.buttonTextEn || ''}">
                                        </div>
                                    </div>

                                    <div class="form-group-modern">
                                        <label class="form-label-modern">
                                            <i class="fas fa-external-link-alt"></i>
                                            رابط الزر
                                        </label>
                                        <input type="text" id="editSliderButtonLink" class="form-input-modern" value="${slider.buttonLink || ''}" placeholder="#products">
                                    </div>
                                </div>

                                <!-- الألوان والتصميم -->
                                <div class="form-section">
                                    <div class="section-header">
                                        <i class="fas fa-palette"></i>
                                        <span>الألوان والتصميم</span>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-fill-drip"></i>
                                                لون الخلفية
                                            </label>
                                            <input type="color" id="editSliderBackgroundColor" class="form-input-modern color-input" value="${slider.backgroundColor}">
                                        </div>

                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-font"></i>
                                                لون النص
                                            </label>
                                            <input type="color" id="editSliderTextColor" class="form-input-modern color-input" value="${slider.textColor}">
                                        </div>

                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-square"></i>
                                                لون الزر
                                            </label>
                                            <input type="color" id="editSliderButtonColor" class="form-input-modern color-input" value="${slider.buttonColor}">
                                        </div>
                                    </div>
                                </div>

                                <!-- الحركة والتوقيت -->
                                <div class="form-section">
                                    <div class="section-header">
                                        <i class="fas fa-magic"></i>
                                        <span>الحركة والتوقيت</span>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-play"></i>
                                                نوع الحركة
                                            </label>
                                            <select id="editSliderAnimation" class="form-input-modern">
                                                <option value="slideInLeft" ${slider.animation === 'slideInLeft' ? 'selected' : ''}>انزلاق من اليسار</option>
                                                <option value="slideInRight" ${slider.animation === 'slideInRight' ? 'selected' : ''}>انزلاق من اليمين</option>
                                                <option value="slideInUp" ${slider.animation === 'slideInUp' ? 'selected' : ''}>انزلاق من الأسفل</option>
                                                <option value="slideInDown" ${slider.animation === 'slideInDown' ? 'selected' : ''}>انزلاق من الأعلى</option>
                                                <option value="fadeIn" ${slider.animation === 'fadeIn' ? 'selected' : ''}>ظهور تدريجي</option>
                                                <option value="zoomIn" ${slider.animation === 'zoomIn' ? 'selected' : ''}>تكبير</option>
                                                <option value="bounceIn" ${slider.animation === 'bounceIn' ? 'selected' : ''}>ارتداد</option>
                                            </select>
                                        </div>

                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-clock"></i>
                                                مدة العرض (ثانية)
                                            </label>
                                            <input type="number" id="editSliderDuration" class="form-input-modern" value="${slider.duration / 1000}" min="3" max="10" step="0.5">
                                        </div>

                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-sort-numeric-up"></i>
                                                ترتيب العرض
                                            </label>
                                            <input type="number" id="editSliderOrder" class="form-input-modern" value="${slider.order}" min="1">
                                        </div>
                                    </div>
                                </div>

                                <!-- رسالة النتيجة -->
                                <div id="editSliderMessage" class="message-modern" style="display: none;"></div>

                                <!-- أزرار التحكم -->
                                <div class="form-actions-modern">
                                    <button type="button" class="btn-cancel-modern" onclick="closeEmployeeModal()">
                                        <i class="fas fa-times"></i>
                                        <span>إلغاء</span>
                                    </button>
                                    <button type="submit" class="btn-submit-modern">
                                        <i class="fas fa-save"></i>
                                        <span>حفظ التغييرات</span>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            `;
            document.getElementById('modalContainer').innerHTML = modalHTML;
        }

        // تحديث السلايدر
        function updateSlider(event, index) {
            event.preventDefault();

            const title = document.getElementById('editSliderTitle').value.trim();
            const titleEn = document.getElementById('editSliderTitleEn').value.trim();
            const subtitle = document.getElementById('editSliderSubtitle').value.trim();
            const subtitleEn = document.getElementById('editSliderSubtitleEn').value.trim();
            const description = document.getElementById('editSliderDescription').value.trim();
            const descriptionEn = document.getElementById('editSliderDescriptionEn').value.trim();
            const image = document.getElementById('editSliderImage').value.trim();
            const buttonText = document.getElementById('editSliderButtonText').value.trim();
            const buttonTextEn = document.getElementById('editSliderButtonTextEn').value.trim();
            const buttonLink = document.getElementById('editSliderButtonLink').value.trim();
            const backgroundColor = document.getElementById('editSliderBackgroundColor').value;
            const textColor = document.getElementById('editSliderTextColor').value;
            const buttonColor = document.getElementById('editSliderButtonColor').value;
            const animation = document.getElementById('editSliderAnimation').value;
            const duration = parseInt(document.getElementById('editSliderDuration').value) * 1000;
            const order = parseInt(document.getElementById('editSliderOrder').value);

            // التحقق من صحة البيانات
            if (!title) {
                showSliderEditMessage('يرجى إدخال العنوان الرئيسي', 'error');
                return;
            }

            if (!image) {
                showSliderEditMessage('يرجى إدخال رابط الصورة أو رفع صورة', 'error');
                return;
            }

            // تحديث السلايدر
            sliders[index] = {
                ...sliders[index],
                title,
                titleEn,
                subtitle,
                subtitleEn,
                description,
                descriptionEn,
                image,
                buttonText,
                buttonTextEn,
                buttonLink,
                backgroundColor,
                textColor,
                buttonColor,
                animation,
                duration,
                order,
                updatedAt: new Date().toISOString()
            };

            // حفظ التغييرات
            if (saveSlidersToStorage()) {
                showSliderEditMessage('تم تحديث السلايدر بنجاح!', 'success');
                updateSlidersStats();
                loadAndDisplaySliders();

                setTimeout(() => {
                    closeEmployeeModal();
                }, 2000);
            } else {
                showSliderEditMessage('حدث خطأ أثناء حفظ السلايدر', 'error');
            }
        }

        // عرض رسالة في نموذج تعديل السلايدر
        function showSliderEditMessage(message, type) {
            const messageDiv = document.getElementById('editSliderMessage');
            if (messageDiv) {
                const icon = type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle';
                messageDiv.innerHTML = `<i class="${icon}"></i> ${message}`;
                messageDiv.className = `message-modern ${type}`;
                messageDiv.style.display = 'block';

                setTimeout(() => {
                    messageDiv.style.display = 'none';
                }, 5000);
            }
        }

        // الحصول على اسم الحركة بالعربية
        function getAnimationName(animation) {
            const animations = {
                'slideInLeft': 'انزلاق من اليسار',
                'slideInRight': 'انزلاق من اليمين',
                'slideInUp': 'انزلاق من الأسفل',
                'slideInDown': 'انزلاق من الأعلى',
                'fadeIn': 'ظهور تدريجي',
                'fadeInUp': 'ظهور من الأسفل',
                'fadeInDown': 'ظهور من الأعلى',
                'zoomIn': 'تكبير',
                'zoomOut': 'تصغير',
                'rotateIn': 'دوران',
                'bounceIn': 'ارتداد',
                'flipInX': 'قلب أفقي',
                'flipInY': 'قلب عمودي'
            };
            return animations[animation] || animation;
        }

        // ===== نظام إدارة مناطق التوصيل =====
        let deliveryZones = [];

        // تهيئة نظام مناطق التوصيل
        function initializeDeliveryZones() {
            loadZonesFromStorage();
            updateZonesStats();
            console.log('🚚 تم تهيئة نظام مناطق التوصيل');
        }

        // تحميل المناطق من التخزين المحلي
        function loadZonesFromStorage() {
            const storedZones = localStorage.getItem('deliveryZones');
            if (storedZones) {
                try {
                    deliveryZones = JSON.parse(storedZones);
                    console.log(`📦 تم تحميل ${deliveryZones.length} منطقة من التخزين المحلي`);
                } catch (error) {
                    console.error('❌ خطأ في تحميل مناطق التوصيل:', error);
                    deliveryZones = [];
                }
            } else {
                // إنشاء مناطق افتراضية
                createDefaultZones();
            }
        }

        // إنشاء مناطق افتراضية
        function createDefaultZones() {
            deliveryZones = [
                {
                    id: 'zone_1',
                    name: 'وسط المدينة',
                    nameEn: 'City Center',
                    deliveryFee: 2000,
                    deliveryTime: '30-45',
                    status: 'active',
                    description: 'منطقة وسط المدينة والأحياء المجاورة',
                    createdAt: new Date().toISOString()
                },
                {
                    id: 'zone_2',
                    name: 'الأحياء الشمالية',
                    nameEn: 'Northern Districts',
                    deliveryFee: 3000,
                    deliveryTime: '45-60',
                    status: 'active',
                    description: 'الأحياء الشمالية من المدينة',
                    createdAt: new Date().toISOString()
                },
                {
                    id: 'zone_3',
                    name: 'الأحياء الجنوبية',
                    nameEn: 'Southern Districts',
                    deliveryFee: 3500,
                    deliveryTime: '60-75',
                    status: 'active',
                    description: 'الأحياء الجنوبية من المدينة',
                    createdAt: new Date().toISOString()
                }
            ];
            saveZonesToStorage();
            console.log('✅ تم إنشاء مناطق التوصيل الافتراضية');
        }

        // حفظ المناطق في التخزين المحلي
        function saveZonesToStorage() {
            try {
                localStorage.setItem('deliveryZones', JSON.stringify(deliveryZones));
                console.log('💾 تم حفظ مناطق التوصيل');
                return true;
            } catch (error) {
                console.error('❌ خطأ في حفظ مناطق التوصيل:', error);
                return false;
            }
        }

        // تحديث إحصائيات المناطق
        function updateZonesStats() {
            const totalZones = deliveryZones.length;
            const activeZones = deliveryZones.filter(zone => zone.status === 'active').length;
            const avgFee = totalZones > 0 ? Math.round(deliveryZones.reduce((sum, zone) => sum + zone.deliveryFee, 0) / totalZones) : 0;
            const avgTime = totalZones > 0 ? Math.round(deliveryZones.reduce((sum, zone) => {
                const timeRange = zone.deliveryTime.split('-');
                return sum + (parseInt(timeRange[0]) + parseInt(timeRange[1])) / 2;
            }, 0) / totalZones) : 0;

            // تحديث العناصر في الواجهة
            updateStatElement('totalZonesCount', totalZones);
            updateStatElement('activeZonesCount', activeZones);
            updateStatElement('avgDeliveryFee', avgFee + ' دينار');
            updateStatElement('avgDeliveryTime', avgTime + ' دقيقة');

            console.log(`📊 تم تحديث إحصائيات المناطق: ${totalZones} إجمالي، ${activeZones} نشطة`);
        }

        function updateStatElement(elementId, value) {
            const element = document.getElementById(elementId);
            if (element) {
                element.style.transform = 'scale(1.1)';
                element.style.transition = 'transform 0.3s ease';

                setTimeout(() => {
                    element.textContent = value;
                    element.style.transform = 'scale(1)';
                }, 150);
            }
        }

        // عرض وتحميل قائمة المناطق
        function loadAndDisplayZones() {
            console.log('📋 تحميل وعرض قائمة مناطق التوصيل...');

            const tableBody = document.getElementById('zonesTableBody');
            const noZonesMessage = document.getElementById('noZonesMessage');
            const tableContainer = document.querySelector('.zones-table-container');

            if (!tableBody) {
                console.error('❌ لم يتم العثور على جدول المناطق');
                return;
            }

            // مسح المحتوى السابق
            tableBody.innerHTML = '';

            if (deliveryZones.length === 0) {
                // إظهار رسالة عدم وجود مناطق
                if (noZonesMessage) {
                    noZonesMessage.style.display = 'block';
                }
                if (tableContainer) {
                    tableContainer.style.display = 'none';
                }
                return;
            }

            // إخفاء رسالة عدم وجود مناطق
            if (noZonesMessage) {
                noZonesMessage.style.display = 'none';
            }
            if (tableContainer) {
                tableContainer.style.display = 'block';
            }

            // إضافة المناطق للجدول
            deliveryZones.forEach((zone, index) => {
                const row = createZoneRow(zone, index);
                tableBody.appendChild(row);
            });

            console.log(`✅ تم عرض ${deliveryZones.length} منطقة في الجدول`);
        }

        // إنشاء صف منطقة في الجدول
        function createZoneRow(zone, index) {
            const row = document.createElement('tr');
            row.className = `zone-row-modern ${zone.status}`;

            const statusBadge = zone.status === 'active' ?
                '<span class="status-badge-modern active"><i class="fas fa-check-circle"></i> نشطة</span>' :
                '<span class="status-badge-modern inactive"><i class="fas fa-times-circle"></i> غير نشطة</span>';

            const createdDate = formatEnglishDate(zone.createdAt);

            // حساب متوسط وقت التوصيل
            const timeRange = zone.deliveryTime.split('-');
            const avgTime = timeRange.length === 2 ?
                Math.round((parseInt(timeRange[0]) + parseInt(timeRange[1])) / 2) :
                parseInt(zone.deliveryTime);

            row.innerHTML = `
                <td class="zone-info-cell">
                    <div class="zone-card-mini">
                        <div class="zone-avatar">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="zone-details">
                            <div class="zone-name">${zone.name}</div>
                            <div class="zone-name-en">${zone.nameEn || ''}</div>
                            <div class="zone-description">${zone.description || 'لا يوجد وصف'}</div>
                        </div>
                    </div>
                </td>
                <td class="delivery-fee-cell">
                    <div class="fee-display">
                        <span class="fee-amount">${zone.deliveryFee.toLocaleString()}</span>
                        <span class="fee-currency">دينار</span>
                        <div class="fee-indicator ${zone.deliveryFee <= 2000 ? 'low' : zone.deliveryFee <= 3000 ? 'medium' : 'high'}"></div>
                    </div>
                </td>
                <td class="delivery-time-cell">
                    <div class="time-display">
                        <div class="time-range">
                            <i class="fas fa-clock"></i>
                            ${zone.deliveryTime} دقيقة
                        </div>
                        <div class="time-avg">متوسط: ${avgTime} دقيقة</div>
                    </div>
                </td>
                <td class="status-cell">
                    ${statusBadge}
                </td>
                <td class="date-cell">
                    <div class="date-display">
                        <div class="date-main">
                            <i class="fas fa-calendar-plus"></i>
                            ${createdDate}
                        </div>
                        ${zone.updatedAt ? `
                            <div class="date-updated">
                                <i class="fas fa-edit"></i>
                                آخر تحديث: ${formatEnglishDate(zone.updatedAt)}
                            </div>
                        ` : ''}
                    </div>
                </td>
                <td class="actions-cell">
                    <div class="action-buttons-modern">
                        <button class="btn-action-sm edit" onclick="editZone(${index})" title="تعديل منطقة ${zone.name}">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-action-sm toggle ${zone.status}" onclick="toggleZoneStatus(${index})"
                                title="${zone.status === 'active' ? 'إلغاء تفعيل' : 'تفعيل'} منطقة ${zone.name}">
                            <i class="fas fa-toggle-${zone.status === 'active' ? 'off' : 'on'}"></i>
                        </button>
                        <button class="btn-action-sm delete" onclick="deleteZone(${index})" title="حذف منطقة ${zone.name}">
                            <i class="fas fa-trash-alt"></i>
                        </button>
                        <button class="btn-action-sm info" onclick="showZoneDetails(${index})" title="تفاصيل منطقة ${zone.name}">
                            <i class="fas fa-info-circle"></i>
                        </button>
                    </div>
                </td>
            `;

            // إضافة تأثير الظهور
            row.style.opacity = '0';
            row.style.transform = 'translateY(10px)';

            setTimeout(() => {
                row.style.transition = 'all 0.3s ease';
                row.style.opacity = '1';
                row.style.transform = 'translateY(0)';
            }, index * 50);

            return row;
        }

        // تحديث قائمة المناطق
        function refreshZonesList() {
            console.log('🔄 تحديث قائمة مناطق التوصيل...');
            loadZonesFromStorage();
            updateZonesStats();
            loadAndDisplayZones();
        }

        // تصفية المناطق
        function filterZones() {
            const searchTerm = document.getElementById('zoneSearchInput').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilterSelect').value;

            const filteredZones = deliveryZones.filter(zone => {
                const matchesSearch = zone.name.toLowerCase().includes(searchTerm) ||
                                    zone.description.toLowerCase().includes(searchTerm);
                const matchesStatus = !statusFilter || zone.status === statusFilter;

                return matchesSearch && matchesStatus;
            });

            displayFilteredZones(filteredZones);
        }

        function displayFilteredZones(zones) {
            const tableBody = document.getElementById('zonesTableBody');
            const noZonesMessage = document.getElementById('noZonesMessage');
            const tableContainer = document.querySelector('.zones-table-container');

            if (!tableBody) return;

            tableBody.innerHTML = '';

            if (zones.length === 0) {
                if (noZonesMessage) {
                    noZonesMessage.style.display = 'block';
                }
                if (tableContainer) {
                    tableContainer.style.display = 'none';
                }
                return;
            }

            if (noZonesMessage) {
                noZonesMessage.style.display = 'none';
            }
            if (tableContainer) {
                tableContainer.style.display = 'block';
            }

            zones.forEach((zone, index) => {
                const originalIndex = deliveryZones.findIndex(z => z.id === zone.id);
                const row = createZoneRow(zone, originalIndex);
                tableBody.appendChild(row);
            });
        }

        // عرض نموذج إضافة منطقة جديدة
        function showAddZoneForm() {
            const modalHTML = `
                <div class="employee-modal-overlay" id="employeeModalOverlay">
                    <div class="employee-modal-modern">
                        <div class="modal-header-modern">
                            <div class="header-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div class="header-text">
                                <h3>إضافة منطقة توصيل جديدة</h3>
                                <p>إضافة منطقة جديدة مع تحديد عمولة ووقت التوصيل</p>
                            </div>
                            <button class="close-btn-modern" onclick="closeEmployeeModal()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>

                        <div class="modal-body-modern">
                            <form id="addZoneForm" onsubmit="createNewZone(event)">
                                <!-- معلومات المنطقة -->
                                <div class="form-section">
                                    <div class="section-header">
                                        <i class="fas fa-map"></i>
                                        <span>معلومات المنطقة</span>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-map-marker-alt"></i>
                                                اسم المنطقة (عربي)
                                            </label>
                                            <input type="text" id="zoneName" class="form-input-modern" placeholder="مثال: وسط المدينة" required>
                                        </div>

                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-globe"></i>
                                                اسم المنطقة (إنجليزي)
                                            </label>
                                            <input type="text" id="zoneNameEn" class="form-input-modern" placeholder="Example: City Center">
                                        </div>
                                    </div>

                                    <div class="form-group-modern">
                                        <label class="form-label-modern">
                                            <i class="fas fa-info-circle"></i>
                                            وصف المنطقة
                                        </label>
                                        <textarea id="zoneDescription" class="form-input-modern" rows="3" placeholder="وصف تفصيلي للمنطقة والأحياء التي تشملها"></textarea>
                                    </div>
                                </div>

                                <!-- معلومات التوصيل -->
                                <div class="form-section">
                                    <div class="section-header">
                                        <i class="fas fa-truck"></i>
                                        <span>معلومات التوصيل</span>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-coins"></i>
                                                عمولة التوصيل (دينار)
                                            </label>
                                            <input type="number" id="deliveryFee" class="form-input-modern" min="0" step="500" placeholder="2000" required>
                                        </div>

                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-clock"></i>
                                                وقت التوصيل (دقيقة)
                                            </label>
                                            <input type="text" id="deliveryTime" class="form-input-modern" placeholder="30-45" pattern="[0-9]+-[0-9]+" required>
                                            <small style="color: #6c757d; font-size: 0.8rem;">مثال: 30-45 (من 30 إلى 45 دقيقة)</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- الحالة -->
                                <div class="form-section">
                                    <div class="section-header">
                                        <i class="fas fa-toggle-on"></i>
                                        <span>حالة المنطقة</span>
                                    </div>

                                    <div class="form-group-modern">
                                        <label class="form-label-modern">
                                            <i class="fas fa-power-off"></i>
                                            حالة التوصيل
                                        </label>
                                        <select id="zoneStatus" class="form-select-modern" required>
                                            <option value="active">✅ نشطة - متاحة للتوصيل</option>
                                            <option value="inactive">❌ غير نشطة - غير متاحة مؤقتاً</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- رسالة النتيجة -->
                                <div id="addZoneMessage" class="message-modern" style="display: none;"></div>

                                <!-- أزرار التحكم -->
                                <div class="form-actions-modern">
                                    <button type="button" class="btn-cancel-modern" onclick="closeEmployeeModal()">
                                        <i class="fas fa-times"></i>
                                        <span>إلغاء</span>
                                    </button>
                                    <button type="submit" class="btn-submit-modern">
                                        <i class="fas fa-save"></i>
                                        <span>إضافة المنطقة</span>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            `;
            document.getElementById('modalContainer').innerHTML = modalHTML;
        }

        // إنشاء منطقة جديدة
        function createNewZone(event) {
            event.preventDefault();

            const name = document.getElementById('zoneName').value.trim();
            const nameEn = document.getElementById('zoneNameEn').value.trim();
            const description = document.getElementById('zoneDescription').value.trim();
            const deliveryFee = parseInt(document.getElementById('deliveryFee').value);
            const deliveryTime = document.getElementById('deliveryTime').value.trim();
            const status = document.getElementById('zoneStatus').value;

            if (!name || !deliveryFee || !deliveryTime) {
                showZoneMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
                return;
            }

            // التحقق من تكرار اسم المنطقة
            if (deliveryZones.find(zone => zone.name === name)) {
                showZoneMessage('اسم المنطقة موجود بالفعل', 'error');
                return;
            }

            // التحقق من صيغة وقت التوصيل
            const timePattern = /^\d+-\d+$/;
            if (!timePattern.test(deliveryTime)) {
                showZoneMessage('يرجى إدخال وقت التوصيل بالصيغة الصحيحة (مثال: 30-45)', 'error');
                return;
            }

            const newZone = {
                id: 'zone_' + Date.now(),
                name: name,
                nameEn: nameEn || name,
                description: description,
                deliveryFee: deliveryFee,
                deliveryTime: deliveryTime,
                status: status,
                createdAt: new Date().toISOString()
            };

            deliveryZones.push(newZone);

            if (saveZonesToStorage()) {
                showZoneMessage('تم إضافة المنطقة بنجاح!', 'success');
                document.getElementById('addZoneForm').reset();
                setTimeout(() => {
                    closeEmployeeModal();
                    refreshZonesList();
                    showNotification(`تم إضافة منطقة "${name}" بنجاح`, 'success');
                }, 1500);
            } else {
                showZoneMessage('حدث خطأ في حفظ بيانات المنطقة', 'error');
            }
        }

        // عرض رسالة في نموذج المنطقة
        function showZoneMessage(message, type) {
            const messageDiv = document.getElementById('addZoneMessage');
            if (messageDiv) {
                const icon = type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle';
                messageDiv.innerHTML = `<i class="${icon}"></i> ${message}`;
                messageDiv.className = `message-modern ${type}`;
                messageDiv.style.display = 'block';

                messageDiv.style.opacity = '0';
                messageDiv.style.transform = 'translateY(-10px)';

                setTimeout(() => {
                    messageDiv.style.transition = 'all 0.3s ease';
                    messageDiv.style.opacity = '1';
                    messageDiv.style.transform = 'translateY(0)';
                }, 10);

                setTimeout(() => {
                    messageDiv.style.transition = 'all 0.3s ease';
                    messageDiv.style.opacity = '0';
                    messageDiv.style.transform = 'translateY(-10px)';
                    setTimeout(() => {
                        messageDiv.style.display = 'none';
                    }, 300);
                }, 5000);
            }
        }

        // تعديل منطقة
        function editZone(index) {
            const zone = deliveryZones[index];
            if (!zone) {
                alert('خطأ: لم يتم العثور على المنطقة');
                return;
            }

            console.log('✏️ تعديل المنطقة:', zone.name);
            showEditZoneForm(zone, index);
        }

        // نموذج تعديل المنطقة
        function showEditZoneForm(zone, index) {
            const modalHTML = `
                <div class="employee-modal-overlay" id="employeeModalOverlay">
                    <div class="employee-modal-modern zone-edit-modal">
                        <div class="modal-header-modern">
                            <div class="header-icon">
                                <i class="fas fa-map-marker-edit"></i>
                            </div>
                            <div class="header-text">
                                <h3>تعديل منطقة التوصيل</h3>
                                <p>تحديث معلومات منطقة "${zone.name}"</p>
                            </div>
                            <button class="close-btn-modern" onclick="closeEmployeeModal()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>

                        <div class="modal-body-modern">
                            <form id="editZoneForm" onsubmit="updateZone(event, ${index})">
                                <!-- معلومات المنطقة -->
                                <div class="form-section">
                                    <div class="section-header">
                                        <i class="fas fa-map"></i>
                                        <span>معلومات المنطقة</span>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-map-marker-alt"></i>
                                                اسم المنطقة (عربي)
                                            </label>
                                            <input type="text" id="editZoneName" class="form-input-modern" value="${zone.name}" required>
                                        </div>

                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-globe"></i>
                                                اسم المنطقة (إنجليزي)
                                            </label>
                                            <input type="text" id="editZoneNameEn" class="form-input-modern" value="${zone.nameEn || ''}" placeholder="City Center">
                                        </div>
                                    </div>

                                    <div class="form-group-modern">
                                        <label class="form-label-modern">
                                            <i class="fas fa-info-circle"></i>
                                            وصف المنطقة
                                        </label>
                                        <textarea id="editZoneDescription" class="form-input-modern" rows="3" placeholder="وصف تفصيلي للمنطقة">${zone.description || ''}</textarea>
                                    </div>
                                </div>

                                <!-- معلومات التوصيل -->
                                <div class="form-section">
                                    <div class="section-header">
                                        <i class="fas fa-truck"></i>
                                        <span>معلومات التوصيل</span>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-coins"></i>
                                                عمولة التوصيل (دينار)
                                            </label>
                                            <input type="number" id="editDeliveryFee" class="form-input-modern" min="0" step="500" value="${zone.deliveryFee}" required>
                                        </div>

                                        <div class="form-group-modern">
                                            <label class="form-label-modern">
                                                <i class="fas fa-clock"></i>
                                                وقت التوصيل (دقيقة)
                                            </label>
                                            <input type="text" id="editDeliveryTime" class="form-input-modern" value="${zone.deliveryTime}" pattern="[0-9]+-[0-9]+" required>
                                            <small style="color: #6c757d; font-size: 0.8rem;">مثال: 30-45 (من 30 إلى 45 دقيقة)</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- الحالة -->
                                <div class="form-section">
                                    <div class="section-header">
                                        <i class="fas fa-toggle-on"></i>
                                        <span>حالة المنطقة</span>
                                    </div>

                                    <div class="form-group-modern">
                                        <label class="form-label-modern">
                                            <i class="fas fa-power-off"></i>
                                            حالة التوصيل
                                        </label>
                                        <select id="editZoneStatus" class="form-select-modern" required>
                                            <option value="active" ${zone.status === 'active' ? 'selected' : ''}>✅ نشطة - متاحة للتوصيل</option>
                                            <option value="inactive" ${zone.status === 'inactive' ? 'selected' : ''}>❌ غير نشطة - غير متاحة مؤقتاً</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- معلومات إضافية -->
                                <div class="form-section">
                                    <div class="section-header">
                                        <i class="fas fa-info-circle"></i>
                                        <span>معلومات المنطقة</span>
                                    </div>

                                    <div class="info-display">
                                        <div class="info-item">
                                            <span class="info-label">معرف المنطقة:</span>
                                            <span class="info-value">${zone.id}</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">تاريخ الإنشاء:</span>
                                            <span class="info-value">${formatDetailedDate(zone.createdAt)}</span>
                                        </div>
                                        ${zone.updatedAt ? `
                                            <div class="info-item">
                                                <span class="info-label">آخر تحديث:</span>
                                                <span class="info-value">${formatDetailedDate(zone.updatedAt)}</span>
                                            </div>
                                        ` : ''}
                                    </div>
                                </div>

                                <!-- رسالة النتيجة -->
                                <div id="editZoneMessage" class="message-modern" style="display: none;"></div>

                                <!-- أزرار التحكم -->
                                <div class="form-actions-modern">
                                    <button type="button" class="btn-cancel-modern" onclick="closeEmployeeModal()">
                                        <i class="fas fa-times"></i>
                                        <span>إلغاء</span>
                                    </button>
                                    <button type="submit" class="btn-submit-modern">
                                        <i class="fas fa-save"></i>
                                        <span>حفظ التغييرات</span>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            `;
            document.getElementById('modalContainer').innerHTML = modalHTML;
        }

        // وظيفة تحديث المنطقة
        function updateZone(event, index) {
            event.preventDefault();

            const name = document.getElementById('editZoneName').value.trim();
            const nameEn = document.getElementById('editZoneNameEn').value.trim();
            const description = document.getElementById('editZoneDescription').value.trim();
            const deliveryFee = parseInt(document.getElementById('editDeliveryFee').value);
            const deliveryTime = document.getElementById('editDeliveryTime').value.trim();
            const status = document.getElementById('editZoneStatus').value;

            if (!name || !deliveryFee || !deliveryTime) {
                showEditZoneMessage('يرجى ملء جميع الحقول المطلوبة', 'error');
                return;
            }

            // التحقق من تكرار اسم المنطقة (عدا المنطقة الحالية)
            const existingZone = deliveryZones.find((zone, i) =>
                zone.name === name && i !== index
            );

            if (existingZone) {
                showEditZoneMessage('اسم المنطقة موجود بالفعل لدى منطقة أخرى', 'error');
                return;
            }

            // التحقق من صيغة وقت التوصيل
            const timePattern = /^\d+-\d+$/;
            if (!timePattern.test(deliveryTime)) {
                showEditZoneMessage('يرجى إدخال وقت التوصيل بالصيغة الصحيحة (مثال: 30-45)', 'error');
                return;
            }

            // تحديث بيانات المنطقة
            const zone = deliveryZones[index];
            zone.name = name;
            zone.nameEn = nameEn || name;
            zone.description = description;
            zone.deliveryFee = deliveryFee;
            zone.deliveryTime = deliveryTime;
            zone.status = status;
            zone.updatedAt = new Date().toISOString();

            // حفظ التغييرات
            if (saveZonesToStorage()) {
                showEditZoneMessage('تم تحديث بيانات المنطقة بنجاح!', 'success');
                setTimeout(() => {
                    closeEmployeeModal();
                    refreshZonesList();
                    showNotification(`تم تحديث منطقة "${zone.name}" بنجاح`, 'success');
                }, 1500);
                console.log(`✅ تم تحديث بيانات المنطقة: ${zone.name}`);
            } else {
                showEditZoneMessage('حدث خطأ أثناء حفظ التغييرات', 'error');
            }
        }

        // عرض رسالة في نموذج التعديل
        function showEditZoneMessage(message, type) {
            const messageDiv = document.getElementById('editZoneMessage');
            if (messageDiv) {
                const icon = type === 'success' ? 'fas fa-check-circle' : 'fas fa-exclamation-triangle';
                messageDiv.innerHTML = `<i class="${icon}"></i> ${message}`;
                messageDiv.className = `message-modern ${type}`;
                messageDiv.style.display = 'block';

                // إضافة تأثير الظهور
                messageDiv.style.opacity = '0';
                messageDiv.style.transform = 'translateY(-10px)';

                setTimeout(() => {
                    messageDiv.style.transition = 'all 0.3s ease';
                    messageDiv.style.opacity = '1';
                    messageDiv.style.transform = 'translateY(0)';
                }, 10);

                // إخفاء الرسالة بعد 5 ثوان
                setTimeout(() => {
                    messageDiv.style.transition = 'all 0.3s ease';
                    messageDiv.style.opacity = '0';
                    messageDiv.style.transform = 'translateY(-10px)';
                    setTimeout(() => {
                        messageDiv.style.display = 'none';
                    }, 300);
                }, 5000);
            }
        }

        // تغيير حالة المنطقة
        function toggleZoneStatus(index) {
            const zone = deliveryZones[index];
            if (!zone) return;

            const newStatus = zone.status === 'active' ? 'inactive' : 'active';
            const statusText = newStatus === 'active' ? 'تفعيل' : 'إلغاء تفعيل';

            if (confirm(`هل أنت متأكد من ${statusText} منطقة "${zone.name}"؟`)) {
                deliveryZones[index].status = newStatus;
                deliveryZones[index].updatedAt = new Date().toISOString();

                if (saveZonesToStorage()) {
                    console.log(`✅ تم ${statusText} منطقة ${zone.name}`);
                    refreshZonesList();
                    showNotification(`تم ${statusText} منطقة "${zone.name}" بنجاح`, 'success');
                } else {
                    alert('حدث خطأ أثناء تحديث حالة المنطقة');
                }
            }
        }

        // حذف منطقة
        function deleteZone(index) {
            const zone = deliveryZones[index];
            if (!zone) return;

            const confirmMessage = `🗑️ حذف منطقة التوصيل\n\n` +
                                 `المنطقة: ${zone.name}\n` +
                                 `عمولة التوصيل: ${zone.deliveryFee.toLocaleString()} دينار\n` +
                                 `وقت التوصيل: ${zone.deliveryTime} دقيقة\n\n` +
                                 `⚠️ تحذير: سيتم حذف المنطقة نهائياً!\n\n` +
                                 `هل أنت متأكد من حذف هذه المنطقة؟`;

            if (confirm(confirmMessage)) {
                deliveryZones.splice(index, 1);

                if (saveZonesToStorage()) {
                    console.log(`🗑️ تم حذف منطقة ${zone.name}`);
                    refreshZonesList();
                    showNotification(`تم حذف منطقة "${zone.name}" نهائياً`, 'success');
                } else {
                    alert('حدث خطأ أثناء حذف المنطقة');
                }
            }
        }

        // تصدير بيانات المناطق
        function exportZonesData() {
            console.log('📤 تصدير بيانات مناطق التوصيل...');

            if (deliveryZones.length === 0) {
                alert('لا توجد بيانات مناطق للتصدير');
                return;
            }

            const csvContent = generateZonesCSV();
            downloadCSV(csvContent, `delivery-zones-${new Date().toISOString().split('T')[0]}.csv`);
        }

        function generateZonesCSV() {
            const headers = ['اسم المنطقة', 'الاسم الإنجليزي', 'عمولة التوصيل', 'وقت التوصيل', 'الحالة', 'الوصف', 'تاريخ الإضافة'];
            const rows = deliveryZones.map(zone => [
                zone.name,
                zone.nameEn || '',
                zone.deliveryFee + ' دينار',
                zone.deliveryTime + ' دقيقة',
                zone.status === 'active' ? 'نشطة' : 'غير نشطة',
                zone.description || '',
                formatEnglishDate(zone.createdAt, true)
            ]);

            return [headers, ...rows].map(row => row.join(',')).join('\n');
        }

        // عرض تفاصيل المنطقة
        function showZoneDetails(index) {
            const zone = deliveryZones[index];
            if (!zone) return;

            const modalHTML = `
                <div class="employee-modal-overlay" id="employeeModalOverlay">
                    <div class="employee-modal-modern zone-details-modal">
                        <div class="modal-header-modern">
                            <div class="header-icon">
                                <i class="fas fa-map-marked-alt"></i>
                            </div>
                            <div class="header-text">
                                <h3>تفاصيل منطقة التوصيل</h3>
                                <p>معلومات شاملة عن منطقة "${zone.name}"</p>
                            </div>
                            <button class="close-btn-modern" onclick="closeEmployeeModal()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>

                        <div class="modal-body-modern">
                            <div class="zone-details-content">
                                <!-- معلومات أساسية -->
                                <div class="details-section">
                                    <div class="section-header">
                                        <i class="fas fa-info-circle"></i>
                                        <span>المعلومات الأساسية</span>
                                    </div>
                                    <div class="details-grid">
                                        <div class="detail-item">
                                            <span class="detail-label">اسم المنطقة (عربي):</span>
                                            <span class="detail-value">${zone.name}</span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">اسم المنطقة (إنجليزي):</span>
                                            <span class="detail-value">${zone.nameEn || 'غير محدد'}</span>
                                        </div>
                                        <div class="detail-item full-width">
                                            <span class="detail-label">الوصف:</span>
                                            <span class="detail-value">${zone.description || 'لا يوجد وصف'}</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- معلومات التوصيل -->
                                <div class="details-section">
                                    <div class="section-header">
                                        <i class="fas fa-truck"></i>
                                        <span>معلومات التوصيل</span>
                                    </div>
                                    <div class="delivery-info-cards">
                                        <div class="info-card fee">
                                            <div class="card-icon">
                                                <i class="fas fa-coins"></i>
                                            </div>
                                            <div class="card-content">
                                                <div class="card-value">${zone.deliveryFee.toLocaleString()}</div>
                                                <div class="card-label">دينار عراقي</div>
                                            </div>
                                        </div>
                                        <div class="info-card time">
                                            <div class="card-icon">
                                                <i class="fas fa-clock"></i>
                                            </div>
                                            <div class="card-content">
                                                <div class="card-value">${zone.deliveryTime}</div>
                                                <div class="card-label">دقيقة</div>
                                            </div>
                                        </div>
                                        <div class="info-card status ${zone.status}">
                                            <div class="card-icon">
                                                <i class="fas fa-${zone.status === 'active' ? 'check-circle' : 'times-circle'}"></i>
                                            </div>
                                            <div class="card-content">
                                                <div class="card-value">${zone.status === 'active' ? 'نشطة' : 'غير نشطة'}</div>
                                                <div class="card-label">الحالة الحالية</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- معلومات النظام -->
                                <div class="details-section">
                                    <div class="section-header">
                                        <i class="fas fa-database"></i>
                                        <span>معلومات النظام</span>
                                    </div>
                                    <div class="system-info">
                                        <div class="info-row">
                                            <span class="info-label">معرف المنطقة:</span>
                                            <span class="info-value code">${zone.id}</span>
                                        </div>
                                        <div class="info-row">
                                            <span class="info-label">تاريخ الإنشاء:</span>
                                            <span class="info-value">${formatDetailedDate(zone.createdAt)}</span>
                                        </div>
                                        ${zone.updatedAt ? `
                                            <div class="info-row">
                                                <span class="info-label">آخر تحديث:</span>
                                                <span class="info-value">${formatDetailedDate(zone.updatedAt)}</span>
                                            </div>
                                        ` : ''}
                                    </div>
                                </div>

                                <!-- إجراءات سريعة -->
                                <div class="quick-actions">
                                    <button class="btn-quick edit" onclick="closeEmployeeModal(); editZone(${index})">
                                        <i class="fas fa-edit"></i>
                                        تعديل المنطقة
                                    </button>
                                    <button class="btn-quick toggle" onclick="toggleZoneStatus(${index}); closeEmployeeModal()">
                                        <i class="fas fa-toggle-${zone.status === 'active' ? 'off' : 'on'}"></i>
                                        ${zone.status === 'active' ? 'إلغاء التفعيل' : 'تفعيل المنطقة'}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.getElementById('modalContainer').innerHTML = modalHTML;
        }

        // عرض تحليل البيانات
        function showZonesAnalytics() {
            if (deliveryZones.length === 0) {
                alert('لا توجد بيانات مناطق لتحليلها');
                return;
            }

            const totalZones = deliveryZones.length;
            const activeZones = deliveryZones.filter(z => z.status === 'active').length;
            const inactiveZones = totalZones - activeZones;

            const fees = deliveryZones.map(z => z.deliveryFee);
            const minFee = Math.min(...fees);
            const maxFee = Math.max(...fees);
            const avgFee = Math.round(fees.reduce((sum, fee) => sum + fee, 0) / fees.length);

            const modalHTML = `
                <div class="employee-modal-overlay" id="employeeModalOverlay">
                    <div class="employee-modal-modern analytics-modal">
                        <div class="modal-header-modern">
                            <div class="header-icon">
                                <i class="fas fa-chart-pie"></i>
                            </div>
                            <div class="header-text">
                                <h3>تحليل بيانات مناطق التوصيل</h3>
                                <p>إحصائيات وتحليلات شاملة للمناطق</p>
                            </div>
                            <button class="close-btn-modern" onclick="closeEmployeeModal()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>

                        <div class="modal-body-modern">
                            <div class="analytics-content">
                                <!-- إحصائيات عامة -->
                                <div class="analytics-section">
                                    <h4><i class="fas fa-chart-bar"></i> الإحصائيات العامة</h4>
                                    <div class="analytics-grid">
                                        <div class="analytics-card">
                                            <div class="card-header">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span>إجمالي المناطق</span>
                                            </div>
                                            <div class="card-value">${totalZones}</div>
                                        </div>
                                        <div class="analytics-card active">
                                            <div class="card-header">
                                                <i class="fas fa-check-circle"></i>
                                                <span>المناطق النشطة</span>
                                            </div>
                                            <div class="card-value">${activeZones}</div>
                                            <div class="card-percentage">${Math.round((activeZones/totalZones)*100)}%</div>
                                        </div>
                                        <div class="analytics-card inactive">
                                            <div class="card-header">
                                                <i class="fas fa-times-circle"></i>
                                                <span>المناطق غير النشطة</span>
                                            </div>
                                            <div class="card-value">${inactiveZones}</div>
                                            <div class="card-percentage">${Math.round((inactiveZones/totalZones)*100)}%</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- تحليل العمولات -->
                                <div class="analytics-section">
                                    <h4><i class="fas fa-coins"></i> تحليل العمولات</h4>
                                    <div class="fee-analysis">
                                        <div class="fee-stat">
                                            <span class="stat-label">أقل عمولة:</span>
                                            <span class="stat-value min">${minFee.toLocaleString()} دينار</span>
                                        </div>
                                        <div class="fee-stat">
                                            <span class="stat-label">أعلى عمولة:</span>
                                            <span class="stat-value max">${maxFee.toLocaleString()} دينار</span>
                                        </div>
                                        <div class="fee-stat">
                                            <span class="stat-label">متوسط العمولة:</span>
                                            <span class="stat-value avg">${avgFee.toLocaleString()} دينار</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- قائمة المناطق -->
                                <div class="analytics-section">
                                    <h4><i class="fas fa-list"></i> تفاصيل المناطق</h4>
                                    <div class="zones-summary">
                                        ${deliveryZones.map((zone, index) => `
                                            <div class="zone-summary-item ${zone.status}">
                                                <div class="zone-summary-info">
                                                    <span class="zone-summary-name">${zone.name}</span>
                                                    <span class="zone-summary-fee">${zone.deliveryFee.toLocaleString()} دينار</span>
                                                </div>
                                                <div class="zone-summary-status">
                                                    <i class="fas fa-${zone.status === 'active' ? 'check-circle' : 'times-circle'}"></i>
                                                </div>
                                            </div>
                                        `).join('')}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.getElementById('modalContainer').innerHTML = modalHTML;
        }
    </script>

    <!-- تم تنظيف الكود وحذف الوظائف الزائدة -->

    <script>
        // نظام التنقل المباشر
        function showSection(sectionId) {
            console.log(`🔄 عرض القسم: ${sectionId}`);

            // إخفاء جميع الأقسام
            const sections = document.querySelectorAll('.content-section');
            sections.forEach(section => {
                section.classList.remove('active');
                section.style.display = 'none';
            });

            // إظهار القسم المطلوب
            const targetSection = document.getElementById(sectionId);
            if (targetSection) {
                targetSection.classList.add('active');
                targetSection.style.display = 'block';

                // تحديث القائمة الجانبية
                const navItems = document.querySelectorAll('.nav-item');
                navItems.forEach(item => item.classList.remove('active'));

                const targetNavItem = document.querySelector(`[data-section="${sectionId}"]`);
                if (targetNavItem) {
                    targetNavItem.closest('.nav-item').classList.add('active');
                }

                // تحديث عنوان الصفحة
                const titles = {
                    'dashboard': 'لوحة المعلومات',
                    'categories': 'إدارة الفئات',
                    'products': 'إدارة المنتجات',
                    'featured': 'المنتجات المميزة',
                    'offers': 'إدارة العروض',
                    'coupons': 'إدارة الكوبونات',
                    'orders': 'إدارة الطلبات',
                    'contact-settings': 'إعدادات الاتصال',
                    'settings': 'إعدادات الموقع',
                    'employee-management': 'إدارة الموظفين',
                    'reports': 'التقارير والإحصائيات'
                };

                const pageTitle = document.getElementById('pageTitle');
                if (pageTitle) {
                    pageTitle.textContent = titles[sectionId] || 'لوحة التحكم';
                }

                // تحميل بيانات القسم
                if (sectionId === 'employee-management') {
                    setTimeout(() => {
                        console.log('🔄 تحميل بيانات إدارة الموظفين...');
                        if (typeof initializeEmployeeSystem === 'function') {
                            initializeEmployeeSystem();
                        }
                        if (typeof updateEmployeeStats === 'function') {
                            updateEmployeeStats();
                        }
                        if (typeof loadAndDisplayEmployees === 'function') {
                            loadAndDisplayEmployees();
                        }
                    }, 100);
                }

                console.log(`✅ تم عرض القسم: ${sectionId}`);
            } else {
                console.error(`❌ لم يتم العثور على القسم: ${sectionId}`);
                alert(`لم يتم العثور على القسم: ${sectionId}`);
            }
        }



        // تهيئة النظام المبسط
        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من جلسة المستخدم
            checkUserSession();

            // تهيئة نظام إدارة الموظفين
            initializeEmployeeSystem();
            updateEmployeeStats();

            // تهيئة نظام السلايدرات
            initializeSliders();
            loadAndDisplaySliders();

            // إضافة زر مزامنة المنتجات
            setTimeout(() => {
                addSyncButton();
            }, 1000);

            // إضافة مستمعات الأحداث للتنقل
            const navLinks = document.querySelectorAll('.nav-link[data-section]');
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const sectionId = this.getAttribute('data-section');
                    if (sectionId) {
                        showSection(sectionId);

                        // تحميل البيانات حسب القسم
                        if (sectionId === 'employee-management') {
                            setTimeout(() => {
                                if (typeof loadAndDisplayEmployees === 'function') {
                                    loadAndDisplayEmployees();
                                }
                            }, 100);
                        } else if (sectionId === 'delivery-zones') {
                            setTimeout(() => {
                                loadAndDisplayZones();
                            }, 100);
                        } else if (sectionId === 'sliders') {
                            setTimeout(() => {
                                loadAndDisplaySliders();
                            }, 100);
                        } else if (sectionId === 'theme-colors') {
                            setTimeout(() => {
                                updateThemeStats();
                                createLivePreview();
                            }, 100);
                        } else if (sectionId === 'excel-upload') {
                            setTimeout(() => {
                                updateExcelStats();
                            }, 100);
                        }
                    }
                });
            });

            // تهيئة النظام
            if (typeof initializeNavigation === 'function') initializeNavigation();
            if (typeof initializeMobileMenu === 'function') initializeMobileMenu();
            if (typeof initializeNavSlider === 'function') initializeNavSlider();
            if (typeof loadDashboardData === 'function') loadDashboardData();
            if (typeof loadCategories === 'function') loadCategories();
            if (typeof loadProducts === 'function') loadProducts();
            if (typeof loadFeaturedProducts === 'function') loadFeaturedProducts();
            if (typeof loadOffers === 'function') loadOffers();
            if (typeof loadCoupons === 'function') loadCoupons();

            // تهيئة نظام مناطق التوصيل
            initializeDeliveryZones();

            // تهيئة نظام السلايدرات
            initializeSliders();

            // تهيئة نظام إدارة الألوان
            initializeThemeColors();

            // تهيئة نظام رفع Excel
            initializeExcelUpload();
        });

        // وظيفة التحقق من جلسة المستخدم المبسطة
        function checkUserSession() {
            const userSession = sessionStorage.getItem('userSession');
            const isLoggedIn = sessionStorage.getItem('isLoggedIn');

            if (userSession && isLoggedIn === 'true') {
                try {
                    const user = JSON.parse(userSession);
                    if (user && user.isLoggedIn) {
                        window.currentUserRole = user.role;
                        window.currentUser = user;
                        console.log(`✅ مرحباً ${user.name} (${getRoleDisplayName(user.role)})`);
                        applyUserPermissions(user.role);
                        updateUserInterface(user);
                        return;
                    }
                } catch (error) {
                    console.error('خطأ في قراءة جلسة المستخدم:', error);
                }
            }

            console.log('⚠️ لا توجد جلسة صحيحة، إعادة التوجيه لتسجيل الدخول...');
            window.location.href = 'login.html';
        }

        // وظيفة تطبيق الصلاحيات المبسطة
        function applyUserPermissions(role) {
            console.log(`🔧 تطبيق صلاحيات ${getRoleDisplayName(role)}...`);

            const restrictedElements = document.querySelectorAll('[data-role-required]');
            restrictedElements.forEach(element => {
                const requiredRoles = element.getAttribute('data-role-required').split(',');
                if (requiredRoles.includes(role)) {
                    element.style.display = '';
                } else {
                    element.style.display = 'none';
                }
            });

            switch(role) {
                case 'admin':
                    console.log('👑 صلاحيات المدير: كاملة');
                    enableFullAccess();
                    break;
                case 'supervisor':
                    console.log('👨‍💼 صلاحيات المشرف');
                    enableSupervisorAccess();
                    break;
                case 'cashier':
                    console.log('💰 صلاحيات الكاشير');
                    enableCashierAccess();
                    break;
                case 'employee':
                    console.log('👤 صلاحيات الموظف');
                    enableEmployeeAccess();
                    break;
                default:
                    enableEmployeeAccess();
            }
        }

        // وظيفة تحديث واجهة المستخدم
        function updateUserInterface(user) {
            // تحديث اسم المستخدم في الهيدر إذا وجد
            const userNameElements = document.querySelectorAll('.user-name, #userName');
            userNameElements.forEach(element => {
                element.textContent = user.name;
            });

            // تحديث دور المستخدم في الهيدر إذا وجد
            const userRoleElements = document.querySelectorAll('.user-role, #userRole');
            userRoleElements.forEach(element => {
                element.textContent = getRoleDisplayName(user.role);
            });

            // إضافة شارة الدور
            const roleClass = `role-${user.role}`;
            document.body.classList.add(roleClass);
        }

        // وظائف الصلاحيات المبسطة
        function enableFullAccess() {
            console.log('🔓 تفعيل الوصول الكامل للمدير');
        }

        function enableSupervisorAccess() {
            console.log('🔧 تفعيل صلاحيات المشرف');
        }

        function enableCashierAccess() {
            console.log('💰 تفعيل صلاحيات الكاشير');
        }

        function enableEmployeeAccess() {
            console.log('👤 تفعيل صلاحيات الموظف');
        }

        // وظيفة للحصول على اسم الدور للعرض
        function getRoleDisplayName(role) {
            const roleNames = {
                'admin': 'مدير النظام',
                'supervisor': 'مشرف',
                'cashier': 'كاشير',
                'employee': 'موظف عادي'
            };
            return roleNames[role] || 'غير محدد';
        }

        // وظائف إدارة الموظفين المبسطة - تم نقلها للقسم الرئيسي

        // وظائف التقارير المبسطة
        function generateDailyReport() {
            console.log('📊 إنشاء تقرير يومي...');
            alert('سيتم إنشاء التقرير اليومي قريباً');
        }

        function generateWeeklyReport() {
            console.log('📊 إنشاء تقرير أسبوعي...');
            alert('سيتم إنشاء التقرير الأسبوعي قريباً');
        }

        function generateMonthlyReport() {
            console.log('📊 إنشاء تقرير شهري...');
            alert('سيتم إنشاء التقرير الشهري قريباً');
        }

        function exportAllData() {
            console.log('📤 تصدير جميع البيانات...');
            alert('سيتم تصدير البيانات قريباً');
        }

        // ===== نظام إدارة ألوان الموقع =====
        let currentTheme = {};
        let defaultTheme = {};

        // تهيئة نظام الألوان
        function initializeThemeColors() {
            loadDefaultTheme();
            loadCurrentTheme();
            updateThemeStats();
            createLivePreview();
            console.log('🎨 تم تهيئة نظام إدارة الألوان');
        }

        // تحميل الثيم الافتراضي
        function loadDefaultTheme() {
            defaultTheme = {
                headerBg: '#2c3e50',
                navText: '#ffffff',
                navActive: '#3498db',
                primaryBtn: '#3498db',
                secondaryBtn: '#95a5a6',
                successBtn: '#27ae60',
                bodyBg: '#ffffff',
                text: '#2c3e50',
                secondaryText: '#7f8c8d',
                cardBg: '#ffffff',
                cardBorder: '#e9ecef',
                cardShadow: '#000000',
                footerBg: '#2c3e50',
                footerText: '#ffffff'
            };
        }

        // تحميل الثيم الحالي
        function loadCurrentTheme() {
            const storedTheme = localStorage.getItem('siteTheme');
            if (storedTheme) {
                try {
                    currentTheme = JSON.parse(storedTheme);
                    console.log('🎨 تم تحميل الثيم المحفوظ');
                } catch (error) {
                    console.error('❌ خطأ في تحميل الثيم:', error);
                    currentTheme = { ...defaultTheme };
                }
            } else {
                currentTheme = { ...defaultTheme };
            }

            // تحديث واجهة التحكم
            updateColorInputs();
        }

        // تحديث حقول الألوان
        function updateColorInputs() {
            Object.keys(currentTheme).forEach(key => {
                const input = document.getElementById(key + 'Color');
                if (input) {
                    input.value = currentTheme[key];
                    const valueSpan = input.nextElementSibling;
                    if (valueSpan && valueSpan.classList.contains('color-value')) {
                        valueSpan.textContent = currentTheme[key];
                    }
                }
            });
        }

        // تحديث لون في الثيم
        function updateThemeColor(colorKey, colorValue) {
            currentTheme[colorKey] = colorValue;

            // تحديث عرض القيمة
            const input = document.getElementById(colorKey + 'Color');
            if (input) {
                const valueSpan = input.nextElementSibling;
                if (valueSpan && valueSpan.classList.contains('color-value')) {
                    valueSpan.textContent = colorValue;
                }
            }

            // تحديث المعاينة المباشرة
            updateLivePreview();

            console.log(`🎨 تم تحديث ${colorKey} إلى ${colorValue}`);
        }

        // إنشاء المعاينة المباشرة
        function createLivePreview() {
            const previewContainer = document.getElementById('livePreview');
            if (!previewContainer) return;

            previewContainer.innerHTML = `
                <div class="preview-website">
                    <div class="preview-header" id="previewHeader">
                        <div class="preview-nav">
                            <div class="preview-logo">
                                <i class="fas fa-store"></i>
                                <span>متجرنا</span>
                            </div>
                            <div class="preview-menu">
                                <a href="#" class="preview-nav-link active" id="previewNavActive">الرئيسية</a>
                                <a href="#" class="preview-nav-link" id="previewNavLink">المنتجات</a>
                                <a href="#" class="preview-nav-link" id="previewNavLink2">العروض</a>
                            </div>
                        </div>
                    </div>

                    <div class="preview-content" id="previewContent">
                        <div class="preview-section">
                            <h2 id="previewTitle">عنوان القسم</h2>
                            <p id="previewText">هذا نص تجريبي لعرض شكل المحتوى في الموقع</p>
                            <p id="previewSecondaryText">نص ثانوي بلون مختلف</p>
                        </div>

                        <div class="preview-cards">
                            <div class="preview-card" id="previewCard">
                                <h3>بطاقة تجريبية</h3>
                                <p>محتوى البطاقة</p>
                                <button class="preview-btn-primary" id="previewBtnPrimary">زر أساسي</button>
                            </div>
                            <div class="preview-card" id="previewCard2">
                                <h3>بطاقة أخرى</h3>
                                <p>محتوى آخر</p>
                                <button class="preview-btn-secondary" id="previewBtnSecondary">زر ثانوي</button>
                            </div>
                        </div>

                        <div class="preview-buttons">
                            <button class="preview-btn-success" id="previewBtnSuccess">زر النجاح</button>
                        </div>
                    </div>

                    <div class="preview-footer" id="previewFooter">
                        <p id="previewFooterText">© 2024 جميع الحقوق محفوظة</p>
                    </div>
                </div>
            `;

            updateLivePreview();
        }

        // تحديث المعاينة المباشرة
        function updateLivePreview() {
            const elements = {
                previewHeader: currentTheme.headerBg,
                previewNavActive: currentTheme.navActive,
                previewNavLink: currentTheme.navText,
                previewNavLink2: currentTheme.navText,
                previewContent: currentTheme.bodyBg,
                previewTitle: currentTheme.text,
                previewText: currentTheme.text,
                previewSecondaryText: currentTheme.secondaryText,
                previewCard: currentTheme.cardBg,
                previewCard2: currentTheme.cardBg,
                previewBtnPrimary: currentTheme.primaryBtn,
                previewBtnSecondary: currentTheme.secondaryBtn,
                previewBtnSuccess: currentTheme.successBtn,
                previewFooter: currentTheme.footerBg,
                previewFooterText: currentTheme.footerText
            };

            Object.keys(elements).forEach(elementId => {
                const element = document.getElementById(elementId);
                if (element) {
                    if (elementId.includes('Btn')) {
                        element.style.backgroundColor = elements[elementId];
                        element.style.color = '#ffffff';
                    } else if (elementId.includes('Footer') && !elementId.includes('Text')) {
                        element.style.backgroundColor = elements[elementId];
                    } else if (elementId.includes('Text') || elementId.includes('Title') || elementId.includes('Link')) {
                        element.style.color = elements[elementId];
                    } else if (elementId.includes('Card')) {
                        element.style.backgroundColor = elements[elementId];
                        element.style.borderColor = currentTheme.cardBorder;
                        element.style.boxShadow = `0 2px 10px ${currentTheme.cardShadow}20`;
                    } else {
                        element.style.backgroundColor = elements[elementId];
                    }
                }
            });
        }

        // تحديث إحصائيات الثيم
        function updateThemeStats() {
            const totalThemes = localStorage.getItem('savedThemes') ? JSON.parse(localStorage.getItem('savedThemes')).length : 1;
            const customColors = Object.keys(currentTheme).filter(key => currentTheme[key] !== defaultTheme[key]).length;
            const lastModified = localStorage.getItem('themeLastModified');
            const daysSinceModified = lastModified ? Math.floor((new Date() - new Date(lastModified)) / (1000 * 60 * 60 * 24)) : 0;

            updateStatElement('totalThemesCount', totalThemes);
            updateStatElement('customColorsCount', customColors);
            updateStatElement('lastModifiedCount', daysSinceModified);
        }

        // حفظ تغييرات الثيم
        function saveThemeChanges() {
            try {
                localStorage.setItem('siteTheme', JSON.stringify(currentTheme));
                localStorage.setItem('themeLastModified', new Date().toISOString());
                showNotification('تم حفظ تغييرات الثيم بنجاح!', 'success');
                updateThemeStats();
                console.log('💾 تم حفظ الثيم');
            } catch (error) {
                console.error('❌ خطأ في حفظ الثيم:', error);
                showNotification('حدث خطأ في حفظ الثيم', 'error');
            }
        }

        // إلغاء تغييرات الثيم
        function cancelThemeChanges() {
            loadCurrentTheme();
            updateLivePreview();
            showNotification('تم إلغاء التغييرات', 'info');
        }

        // تطبيق الثيم على الموقع
        function applyThemeToSite() {
            saveThemeChanges();

            // إنشاء CSS مخصص
            const customCSS = generateCustomCSS();

            // حفظ CSS في localStorage
            localStorage.setItem('customSiteCSS', customCSS);

            showNotification('تم تطبيق الثيم على الموقع بنجاح!', 'success');
            console.log('✅ تم تطبيق الثيم على الموقع');
        }

        // إنشاء CSS مخصص
        function generateCustomCSS() {
            return `
                /* ثيم مخصص للموقع */
                .header, .navbar {
                    background-color: ${currentTheme.headerBg} !important;
                }

                .nav-link {
                    color: ${currentTheme.navText} !important;
                }

                .nav-link.active, .nav-link:hover {
                    color: ${currentTheme.navActive} !important;
                }

                body {
                    background-color: ${currentTheme.bodyBg} !important;
                    color: ${currentTheme.text} !important;
                }

                .section-title, h1, h2, h3, h4, h5, h6 {
                    color: ${currentTheme.text} !important;
                }

                p, .secondary-text {
                    color: ${currentTheme.secondaryText} !important;
                }

                .btn-primary, .cta-button, .add-to-cart {
                    background-color: ${currentTheme.primaryBtn} !important;
                    border-color: ${currentTheme.primaryBtn} !important;
                }

                .btn-secondary {
                    background-color: ${currentTheme.secondaryBtn} !important;
                    border-color: ${currentTheme.secondaryBtn} !important;
                }

                .btn-success {
                    background-color: ${currentTheme.successBtn} !important;
                    border-color: ${currentTheme.successBtn} !important;
                }

                .product-card, .offer-card, .contact-card, .category-card {
                    background-color: ${currentTheme.cardBg} !important;
                    border-color: ${currentTheme.cardBorder} !important;
                    box-shadow: 0 4px 15px ${currentTheme.cardShadow}20 !important;
                }

                .footer {
                    background-color: ${currentTheme.footerBg} !important;
                    color: ${currentTheme.footerText} !important;
                }

                .footer p, .footer a {
                    color: ${currentTheme.footerText} !important;
                }
            `;
        }

        // استعادة الثيم الافتراضي
        function resetToDefaultTheme() {
            if (confirm('هل أنت متأكد من استعادة الثيم الافتراضي؟ سيتم فقدان جميع التخصيصات الحالية.')) {
                currentTheme = { ...defaultTheme };
                updateColorInputs();
                updateLivePreview();
                showNotification('تم استعادة الثيم الافتراضي', 'success');
            }
        }

        // معاينة الموقع
        function previewTheme() {
            window.open('index.html', '_blank');
        }

        // تصدير بيانات الثيم
        function exportThemeData() {
            const themeData = {
                theme: currentTheme,
                exportDate: new Date().toISOString(),
                version: '1.0'
            };

            const dataStr = JSON.stringify(themeData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `theme-${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            showNotification('تم تصدير بيانات الثيم بنجاح!', 'success');
        }

        // عرض مخصص الألوان
        function showColorCustomizer() {
            const customizerPanel = document.querySelector('.color-customizer-panel');
            if (customizerPanel) {
                customizerPanel.style.display = customizerPanel.style.display === 'none' ? 'block' : 'none';
            }
        }

        // ===== نظام رفع المنتجات من Excel =====
        let selectedExcelFile = null;
        let parsedExcelData = [];
        let validProducts = [];
        let invalidProducts = [];

        // تهيئة نظام رفع Excel
        function initializeExcelUpload() {
            console.log('📊 بدء تهيئة نظام رفع Excel...');

            // التأكد من تحميل العناصر
            const checkElements = () => {
                const fileInput = document.getElementById('excelFileInput');
                const uploadZone = document.getElementById('uploadZone');
                const selectButton = document.getElementById('btnSelectFile');

                if (fileInput && uploadZone && selectButton) {
                    console.log('✅ جميع العناصر محملة، بدء إعداد الأحداث...');
                    setupExcelUploadEvents();
                    updateExcelStats();
                    console.log('📊 تم تهيئة نظام رفع Excel بنجاح');
                } else {
                    console.log('⏳ انتظار تحميل العناصر...');
                    setTimeout(checkElements, 100);
                }
            };

            checkElements();
        }

        // إعداد أحداث رفع Excel
        function setupExcelUploadEvents() {
            const fileInput = document.getElementById('excelFileInput');
            const uploadZone = document.getElementById('uploadZone');
            const uploadButton = document.getElementById('btnUploadExcel');
            const selectButton = document.getElementById('btnSelectFile');

            console.log('🔧 إعداد أحداث رفع Excel...');
            console.log('📁 عنصر اختيار الملف:', fileInput ? '✅ موجود' : '❌ مفقود');
            console.log('📦 منطقة الرفع:', uploadZone ? '✅ موجود' : '❌ مفقود');
            console.log('🔘 زر الرفع:', uploadButton ? '✅ موجود' : '❌ مفقود');
            console.log('🔘 زر اختيار الملف:', selectButton ? '✅ موجود' : '❌ مفقود');

            // ربط حدث اختيار الملف
            if (fileInput) {
                fileInput.addEventListener('change', handleFileSelection);
                console.log('✅ تم ربط حدث اختيار الملف');
            } else {
                console.error('❌ عنصر اختيار الملف غير موجود');
                return;
            }

            // ربط زر اختيار الملف - طريقة أساسية
            if (selectButton) {
                selectButton.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('🖱️ تم النقر على زر اختيار الملف');
                    triggerFileInput();
                });

                // نسخة احتياطية باستخدام onclick
                selectButton.onclick = function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('🖱️ النسخة الاحتياطية - تم النقر على زر اختيار الملف');
                    triggerFileInput();
                };

                console.log('✅ تم ربط زر اختيار الملف (طريقتين)');
            } else {
                console.error('❌ زر اختيار الملف غير موجود');
            }

            // ربط منطقة الرفع للنقر
            if (uploadZone) {
                uploadZone.addEventListener('click', function(e) {
                    // تجنب التفعيل عند النقر على الأزرار
                    if (!e.target.closest('button')) {
                        console.log('🖱️ تم النقر على منطقة الرفع');
                        fileInput.click();
                    }
                });

                // إعداد السحب والإفلات
                uploadZone.addEventListener('dragover', handleDragOver);
                uploadZone.addEventListener('dragleave', handleDragLeave);
                uploadZone.addEventListener('drop', handleFileDrop);
                console.log('✅ تم ربط أحداث السحب والإفلات والنقر');
            } else {
                console.error('❌ منطقة الرفع غير موجودة');
            }
        }

        // تفعيل نافذة اختيار الملف
        function triggerFileInput() {
            const fileInput = document.getElementById('excelFileInput');
            if (fileInput) {
                try {
                    console.log('🔄 محاولة فتح نافذة اختيار الملف...');
                    fileInput.click();
                    console.log('✅ تم تفعيل نافذة اختيار الملف');
                } catch (error) {
                    console.error('❌ خطأ في فتح نافذة اختيار الملف:', error);
                    alert('حدث خطأ في فتح نافذة اختيار الملف. يرجى المحاولة مرة أخرى.');
                }
            } else {
                console.error('❌ عنصر اختيار الملف غير موجود');
                alert('خطأ في النظام: عنصر اختيار الملف غير موجود');
            }
        }

        // معالجة اختيار الملف
        function handleFileSelection(event) {
            console.log('📁 تم تفعيل حدث اختيار الملف');

            const file = event.target.files[0];
            if (file) {
                console.log('📄 تم اختيار الملف:', file.name);
                console.log('📊 نوع الملف:', file.type);
                console.log('📏 حجم الملف:', formatFileSize(file.size));
                validateAndSelectFile(file);
            } else {
                console.log('❌ لم يتم اختيار أي ملف');
            }
        }

        // معالجة السحب فوق المنطقة
        function handleDragOver(event) {
            event.preventDefault();
            event.stopPropagation();
            event.currentTarget.classList.add('drag-over');
            console.log('📦 سحب ملف فوق المنطقة');
        }

        // معالجة مغادرة منطقة السحب
        function handleDragLeave(event) {
            event.preventDefault();
            event.stopPropagation();
            event.currentTarget.classList.remove('drag-over');
            console.log('📦 مغادرة منطقة السحب');
        }

        // معالجة إفلات الملف
        function handleFileDrop(event) {
            event.preventDefault();
            event.stopPropagation();
            event.currentTarget.classList.remove('drag-over');

            console.log('📦 إفلات ملف في المنطقة');

            const files = event.dataTransfer.files;
            console.log('📁 عدد الملفات المسحوبة:', files.length);

            if (files.length > 0) {
                console.log('📄 الملف المسحوب:', files[0].name);
                validateAndSelectFile(files[0]);
            } else {
                console.log('❌ لم يتم العثور على ملفات');
            }
        }

        // التحقق من صحة الملف واختياره
        function validateAndSelectFile(file) {
            console.log('🔍 بدء التحقق من صحة الملف...');
            console.log('📄 اسم الملف:', file.name);
            console.log('📊 نوع الملف:', file.type);
            console.log('📏 حجم الملف:', formatFileSize(file.size));

            // التحقق من نوع الملف
            const allowedTypes = [
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/vnd.ms-excel',
                'text/csv'
            ];

            const fileName = file.name.toLowerCase();
            const hasValidExtension = fileName.endsWith('.xlsx') || fileName.endsWith('.xls') || fileName.endsWith('.csv');
            const hasValidType = allowedTypes.includes(file.type);

            console.log('🔍 امتداد صحيح:', hasValidExtension);
            console.log('🔍 نوع صحيح:', hasValidType);

            if (!hasValidType && !hasValidExtension) {
                console.error('❌ نوع الملف غير مدعوم');
                showNotification('يرجى اختيار ملف Excel أو CSV صحيح (.xlsx, .xls, .csv)', 'error');
                return false;
            }

            // التحقق من حجم الملف (10 ميجابايت)
            const maxSize = 10 * 1024 * 1024;
            if (file.size > maxSize) {
                console.error('❌ حجم الملف كبير جداً');
                showNotification('حجم الملف كبير جداً. الحد الأقصى 10 ميجابايت', 'error');
                return false;
            }

            console.log('✅ تم التحقق من الملف بنجاح');
            selectedExcelFile = file;
            updateUploadUI();
            showNotification(`تم اختيار الملف: ${file.name}`, 'success');
            return true;
        }

        // تحديث واجهة الرفع
        function updateUploadUI() {
            const uploadZone = document.getElementById('uploadZone');
            const btnUpload = document.getElementById('btnUploadExcel');
            const btnClear = document.getElementById('btnClearFile');

            if (selectedExcelFile) {
                uploadZone.classList.add('file-selected');
                uploadZone.querySelector('.upload-text h4').textContent = `تم اختيار: ${selectedExcelFile.name}`;
                uploadZone.querySelector('.upload-text p').textContent = `الحجم: ${formatFileSize(selectedExcelFile.size)}`;

                btnUpload.disabled = false;
                btnClear.style.display = 'inline-flex';
            } else {
                uploadZone.classList.remove('file-selected');
                uploadZone.querySelector('.upload-text h4').textContent = 'اسحب ملف Excel أو CSV هنا أو انقر للاختيار';
                uploadZone.querySelector('.upload-text p').textContent = 'يدعم ملفات .xlsx و .xls و .csv بحجم أقصى 10 ميجابايت';

                btnUpload.disabled = true;
                btnClear.style.display = 'none';
            }
        }

        // تنسيق حجم الملف
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // مسح الملف المختار
        function clearSelectedFile() {
            selectedExcelFile = null;
            document.getElementById('excelFileInput').value = '';
            updateUploadUI();
            hidePreview();
        }

        // رفع وتحليل ملف Excel أو CSV
        function uploadExcelFile() {
            if (!selectedExcelFile) {
                showNotification('يرجى اختيار ملف Excel أو CSV أولاً', 'error');
                return;
            }

            showUploadProgress();

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    // التحقق من نوع الملف
                    const fileName = selectedExcelFile.name.toLowerCase();
                    if (fileName.endsWith('.csv')) {
                        parseCSVData(e.target.result);
                    } else {
                        parseExcelData(e.target.result);
                    }
                } catch (error) {
                    console.error('خطأ في قراءة الملف:', error);
                    showNotification('حدث خطأ في قراءة الملف', 'error');
                    hideUploadProgress();
                }
            };

            // قراءة الملف حسب النوع
            const fileName = selectedExcelFile.name.toLowerCase();
            if (fileName.endsWith('.csv')) {
                reader.readAsText(selectedExcelFile, 'UTF-8');
            } else {
                reader.readAsArrayBuffer(selectedExcelFile);
            }
        }

        // تحليل بيانات CSV
        function parseCSVData(csvText) {
            try {
                console.log('📊 بدء تحليل ملف CSV...');

                if (!csvText || csvText.trim() === '') {
                    throw new Error('الملف فارغ أو لا يحتوي على بيانات');
                }

                // تقسيم النص إلى صفوف
                const lines = csvText.split('\n').filter(line => line.trim() !== '');

                if (lines.length < 2) {
                    throw new Error('الملف يجب أن يحتوي على صف العناوين وصف واحد على الأقل من البيانات');
                }

                // تحويل CSV إلى مصفوفة ثنائية الأبعاد
                const jsonData = lines.map(line => {
                    // تقسيم الصف مع مراعاة الفواصل داخل علامات الاقتباس
                    const result = [];
                    let current = '';
                    let inQuotes = false;

                    for (let i = 0; i < line.length; i++) {
                        const char = line[i];

                        if (char === '"') {
                            inQuotes = !inQuotes;
                        } else if (char === ',' && !inQuotes) {
                            result.push(current.trim());
                            current = '';
                        } else {
                            current += char;
                        }
                    }

                    result.push(current.trim());
                    return result;
                });

                console.log(`📋 تم تحليل ${jsonData.length} صف من ملف CSV`);

                // تحليل البيانات
                processExcelData(jsonData);

            } catch (error) {
                console.error('خطأ في تحليل CSV:', error);
                showNotification('حدث خطأ في تحليل ملف CSV: ' + error.message, 'error');
                hideUploadProgress();
            }
        }

        // تحليل بيانات Excel
        function parseExcelData(arrayBuffer) {
            try {
                // استخدام مكتبة SheetJS لقراءة Excel
                const workbook = XLSX.read(arrayBuffer, { type: 'array' });
                const firstSheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[firstSheetName];

                // تحويل البيانات إلى JSON
                const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

                if (jsonData.length < 2) {
                    throw new Error('الملف فارغ أو لا يحتوي على بيانات كافية');
                }

                // تحليل البيانات
                processExcelData(jsonData);

            } catch (error) {
                console.error('خطأ في تحليل Excel:', error);
                showNotification('حدث خطأ في تحليل ملف Excel: ' + error.message, 'error');
                hideUploadProgress();
            }
        }

        // معالجة بيانات Excel
        function processExcelData(data) {
            validProducts = [];
            invalidProducts = [];

            // تخطي الصف الأول (العناوين)
            for (let i = 1; i < data.length; i++) {
                const row = data[i];

                // تخطي الصفوف الفارغة
                if (!row || row.length === 0 || !row[0]) continue;

                const product = {
                    rowNumber: i + 1,
                    nameAr: row[0] || '',
                    nameEn: row[1] || '',
                    category: row[2] || '',
                    price: row[3] || '',
                    wholesalePrice: row[4] || '',
                    oldPrice: row[5] || '',
                    descriptionAr: row[6] || '',
                    descriptionEn: row[7] || '',
                    image: row[8] || '',
                    quantity: row[9] || '',
                    status: row[10] || ''
                };

                // التحقق من صحة البيانات
                const validation = validateProductData(product);

                if (validation.isValid) {
                    validProducts.push({
                        ...product,
                        id: 'product_' + Date.now() + '_' + i,
                        createdAt: new Date().toISOString()
                    });
                } else {
                    invalidProducts.push({
                        ...product,
                        errors: validation.errors
                    });
                }
            }

            hideUploadProgress();
            showPreviewResults();

            console.log(`✅ تم تحليل ${validProducts.length} منتج صحيح و ${invalidProducts.length} منتج خاطئ`);
        }

        // التحقق من صحة بيانات المنتج
        function validateProductData(product) {
            const errors = [];

            // التحقق من الاسم العربي
            if (!product.nameAr || product.nameAr.trim() === '') {
                errors.push('اسم المنتج العربي مطلوب');
            }

            // التحقق من الفئة
            if (!product.category || product.category.trim() === '') {
                errors.push('فئة المنتج مطلوبة');
            }

            // التحقق من السعر
            if (!product.price || isNaN(parseFloat(product.price)) || parseFloat(product.price) <= 0) {
                errors.push('السعر يجب أن يكون رقم موجب');
            }

            // التحقق من سعر الجملة
            if (!product.wholesalePrice || isNaN(parseFloat(product.wholesalePrice)) || parseFloat(product.wholesalePrice) <= 0) {
                errors.push('سعر الجملة يجب أن يكون رقم موجب');
            } else if (product.price && parseFloat(product.wholesalePrice) >= parseFloat(product.price)) {
                errors.push('سعر الجملة يجب أن يكون أقل من سعر المفرد');
            }

            // التحقق من السعر القديم (اختياري)
            if (product.oldPrice && (isNaN(parseFloat(product.oldPrice)) || parseFloat(product.oldPrice) <= 0)) {
                errors.push('السعر القديم يجب أن يكون رقم موجب أو فارغ');
            }

            // التحقق من رابط الصورة
            if (!product.image || !isValidImageUrl(product.image)) {
                errors.push('رابط الصورة غير صحيح');
            }

            // التحقق من الكمية
            if (!product.quantity || isNaN(parseInt(product.quantity)) || parseInt(product.quantity) < 0) {
                errors.push('الكمية يجب أن تكون رقم صحيح غير سالب');
            }

            // التحقق من الحالة
            const validStatuses = ['متوفر', 'غير متوفر', 'available', 'unavailable'];
            if (!product.status || !validStatuses.includes(product.status.toLowerCase())) {
                errors.push('الحالة يجب أن تكون "متوفر" أو "غير متوفر"');
            }

            return {
                isValid: errors.length === 0,
                errors: errors
            };
        }

        // عرض نتائج المعاينة
        function showPreviewResults() {
            const previewSection = document.getElementById('excelPreview');
            const validCount = document.getElementById('validRowsCount');
            const invalidCount = document.getElementById('invalidRowsCount');
            const tableBody = document.getElementById('previewTableBody');

            if (!previewSection || !tableBody) return;

            // تحديث الإحصائيات
            validCount.textContent = validProducts.length;
            invalidCount.textContent = invalidProducts.length;

            // مسح الجدول
            tableBody.innerHTML = '';

            // إضافة المنتجات الصحيحة
            validProducts.forEach(product => {
                const row = createPreviewRow(product, true);
                tableBody.appendChild(row);
            });

            // إضافة المنتجات الخاطئة
            invalidProducts.forEach(product => {
                const row = createPreviewRow(product, false);
                tableBody.appendChild(row);
            });

            // إظهار قسم المعاينة
            previewSection.style.display = 'block';
            previewSection.scrollIntoView({ behavior: 'smooth' });

            // تفعيل/تعطيل زر التأكيد
            const confirmBtn = document.getElementById('btnConfirmImport');
            if (confirmBtn) {
                confirmBtn.disabled = validProducts.length === 0;
            }
        }

        // إنشاء صف في جدول المعاينة
        function createPreviewRow(product, isValid) {
            const row = document.createElement('tr');
            row.className = isValid ? 'preview-row-valid' : 'preview-row-invalid';

            const statusIcon = isValid ?
                '<i class="fas fa-check-circle text-success"></i>' :
                '<i class="fas fa-times-circle text-danger"></i>';

            const imagePreview = product.image ?
                `<img src="${product.image}" alt="صورة المنتج" class="preview-image" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMCAyNUMyMi43NjE0IDI1IDI1IDIyLjc2MTQgMjUgMjBDMjUgMTcuMjM4NiAyMi43NjE0IDE1IDIwIDE1QzE3LjIzODYgMTUgMTUgMTcuMjM4NiAxNSAyMEMxNSAyMi43NjE0IDE3LjIzODYgMjUgMjAgMjVaIiBmaWxsPSIjOUM5Qzk3Ii8+CjxwYXRoIGQ9Ik0zMCAzMEgxMFYyN0wxNSAyMkwyMCAyN0wyNSAyMkwzMCAyN1YzMFoiIGZpbGw9IiM5QzlDOTciLz4KPC9zdmc+Cg=='">` :
                '<span class="no-image">لا توجد صورة</span>';

            const notes = isValid ?
                '<span class="text-success">جاهز للاستيراد</span>' :
                `<span class="text-danger">${product.errors.join(', ')}</span>`;

            row.innerHTML = `
                <td>${statusIcon}</td>
                <td>
                    <div class="product-name">
                        <strong>${product.nameAr}</strong>
                        ${product.nameEn ? `<br><small>${product.nameEn}</small>` : ''}
                    </div>
                </td>
                <td>${product.category}</td>
                <td>
                    <div class="price-info">
                        <strong>مفرد: ${product.price} IQD</strong>
                        <br><span class="wholesale-price">جملة: ${product.wholesalePrice} IQD</span>
                        ${product.oldPrice ? `<br><small class="old-price">قديم: ${product.oldPrice} IQD</small>` : ''}
                    </div>
                </td>
                <td>${product.quantity}</td>
                <td>${imagePreview}</td>
                <td>${notes}</td>
            `;

            return row;
        }

        // إخفاء المعاينة
        function hidePreview() {
            const previewSection = document.getElementById('excelPreview');
            if (previewSection) {
                previewSection.style.display = 'none';
            }
        }

        // عرض شريط التقدم
        function showUploadProgress() {
            const progressSection = document.getElementById('uploadProgress');
            if (progressSection) {
                progressSection.style.display = 'block';
                animateProgress();
            }
        }

        // إخفاء شريط التقدم
        function hideUploadProgress() {
            const progressSection = document.getElementById('uploadProgress');
            if (progressSection) {
                progressSection.style.display = 'none';
            }
        }

        // تحريك شريط التقدم
        function animateProgress() {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');

            if (!progressFill || !progressText) return;

            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress > 90) progress = 90;

                progressFill.style.width = progress + '%';
                progressText.textContent = `جاري التحليل... ${Math.round(progress)}%`;

                if (progress >= 90) {
                    clearInterval(interval);
                    progressFill.style.width = '100%';
                    progressText.textContent = 'تم التحليل بنجاح!';
                }
            }, 200);
        }

        // تأكيد استيراد المنتجات
        function confirmImportProducts() {
            if (validProducts.length === 0) {
                showNotification('لا توجد منتجات صحيحة للاستيراد', 'error');
                return;
            }

            const startTime = Date.now();
            let importedCount = 0;
            let failedCount = 0;

            // تحميل المنتجات الحالية
            let existingProducts = [];
            try {
                // التحقق من adminProducts أولاً (الأولوية)
                const adminProducts = localStorage.getItem('adminProducts');
                if (adminProducts) {
                    existingProducts = JSON.parse(adminProducts);
                    console.log(`📦 تم تحميل ${existingProducts.length} منتج من adminProducts`);
                } else {
                    // التحقق من products كبديل
                    const storedProducts = localStorage.getItem('products');
                    if (storedProducts) {
                        existingProducts = JSON.parse(storedProducts);
                        console.log(`📦 تم تحميل ${existingProducts.length} منتج من products`);
                    }
                }
            } catch (error) {
                console.error('خطأ في تحميل المنتجات الحالية:', error);
                existingProducts = [];
            }

            // إضافة المنتجات الجديدة
            validProducts.forEach(product => {
                try {
                    // تحويل البيانات لتنسيق المنتج المطلوب
                    const newProduct = {
                        id: product.id,
                        name: product.nameAr,
                        nameEn: product.nameEn || product.nameAr,
                        category: product.category,
                        price: parseFloat(product.price),
                        wholesalePrice: parseFloat(product.wholesalePrice),
                        oldPrice: product.oldPrice ? parseFloat(product.oldPrice) : null,
                        description: product.descriptionAr || '',
                        descriptionEn: product.descriptionEn || product.descriptionAr || '',
                        image: product.image,
                        quantity: parseInt(product.quantity),
                        status: product.status.toLowerCase() === 'متوفر' || product.status.toLowerCase() === 'available' ? 'available' : 'unavailable',
                        featured: false,
                        createdAt: product.createdAt
                    };

                    existingProducts.push(newProduct);
                    importedCount++;
                } catch (error) {
                    console.error('خطأ في إضافة المنتج:', error);
                    failedCount++;
                }
            });

            // حفظ المنتجات في كلا المواقع
            try {
                // حفظ في adminProducts للموقع الرئيسي وواجهة الكاشير
                localStorage.setItem('adminProducts', JSON.stringify(existingProducts));

                // حفظ في products للتوافق مع الأنظمة القديمة
                localStorage.setItem('products', JSON.stringify(existingProducts));

                // تحديث الإحصائيات
                updateExcelStats();

                // تحديث عرض المنتجات في لوحة التحكم
                if (typeof loadProducts === 'function') {
                    loadProducts();
                }

                // إشعار الموقع الرئيسي بالتحديث
                try {
                    // إرسال حدث مخصص للموقع الرئيسي
                    window.dispatchEvent(new CustomEvent('productsUpdated', {
                        detail: { count: importedCount, source: 'excel' }
                    }));
                } catch (error) {
                    console.log('تعذر إرسال إشعار التحديث للموقع الرئيسي');
                }

                // عرض النتائج
                showImportResults(importedCount, failedCount, Date.now() - startTime);

                console.log(`✅ تم استيراد ${importedCount} منتج بنجاح وحفظه في adminProducts و products`);

            } catch (error) {
                console.error('خطأ في حفظ المنتجات:', error);
                showNotification('حدث خطأ في حفظ المنتجات', 'error');
            }
        }

        // عرض نتائج الاستيراد
        function showImportResults(imported, failed, processingTime) {
            const resultsSection = document.getElementById('importResults');
            const importedCountEl = document.getElementById('importedCount');
            const failedCountEl = document.getElementById('failedCount');
            const processingTimeEl = document.getElementById('processingTime');
            const resultsDetails = document.getElementById('resultsDetails');

            if (!resultsSection) return;

            // تحديث الأرقام
            importedCountEl.textContent = imported;
            failedCountEl.textContent = failed;
            processingTimeEl.textContent = (processingTime / 1000).toFixed(1);

            // إنشاء تفاصيل النتائج
            let detailsHTML = '<div class="results-list">';

            if (imported > 0) {
                detailsHTML += `
                    <div class="result-item success">
                        <i class="fas fa-check-circle"></i>
                        <span>تم استيراد ${imported} منتج بنجاح</span>
                    </div>
                `;
            }

            if (failed > 0) {
                detailsHTML += `
                    <div class="result-item error">
                        <i class="fas fa-times-circle"></i>
                        <span>فشل في استيراد ${failed} منتج</span>
                    </div>
                `;
            }

            if (invalidProducts.length > 0) {
                detailsHTML += `
                    <div class="result-item warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>تم تجاهل ${invalidProducts.length} صف بسبب أخطاء في البيانات</span>
                    </div>
                `;
            }

            detailsHTML += '</div>';
            resultsDetails.innerHTML = detailsHTML;

            // إخفاء المعاينة وإظهار النتائج
            hidePreview();
            resultsSection.style.display = 'block';
            resultsSection.scrollIntoView({ behavior: 'smooth' });

            // حفظ إحصائيات الرفع
            saveUploadStats(imported, failed);
        }

        // حفظ إحصائيات الرفع
        function saveUploadStats(imported, failed) {
            try {
                const stats = {
                    totalUploads: (parseInt(localStorage.getItem('totalUploads')) || 0) + 1,
                    totalImported: (parseInt(localStorage.getItem('totalImported')) || 0) + imported,
                    totalFailed: (parseInt(localStorage.getItem('totalFailed')) || 0) + failed,
                    lastUpload: new Date().toISOString()
                };

                localStorage.setItem('totalUploads', stats.totalUploads);
                localStorage.setItem('totalImported', stats.totalImported);
                localStorage.setItem('totalFailed', stats.totalFailed);
                localStorage.setItem('lastUpload', stats.lastUpload);

            } catch (error) {
                console.error('خطأ في حفظ إحصائيات الرفع:', error);
            }
        }

        // تحديث إحصائيات Excel
        function updateExcelStats() {
            const totalUploads = parseInt(localStorage.getItem('totalUploads')) || 0;
            const totalImported = parseInt(localStorage.getItem('totalImported')) || 0;
            const totalFailed = parseInt(localStorage.getItem('totalFailed')) || 0;
            const lastUpload = localStorage.getItem('lastUpload');

            updateStatElement('totalUploadsCount', totalUploads);
            updateStatElement('successfulProductsCount', totalImported);
            updateStatElement('failedProductsCount', totalFailed);

            if (lastUpload) {
                const lastUploadDate = new Date(lastUpload);
                const now = new Date();
                const diffDays = Math.floor((now - lastUploadDate) / (1000 * 60 * 60 * 24));

                let timeText = 'اليوم';
                if (diffDays === 1) timeText = 'أمس';
                else if (diffDays > 1) timeText = `منذ ${diffDays} أيام`;

                updateStatElement('lastUploadTime', timeText);
            }
        }

        // إلغاء الاستيراد
        function cancelImport() {
            hidePreview();
            clearSelectedFile();
            showNotification('تم إلغاء عملية الاستيراد', 'info');
        }

        // إعادة تعيين نموذج الرفع
        function resetUploadForm() {
            clearSelectedFile();
            hidePreview();

            const resultsSection = document.getElementById('importResults');
            if (resultsSection) {
                resultsSection.style.display = 'none';
            }

            // التمرير إلى أعلى القسم
            const uploadSection = document.getElementById('excel-upload');
            if (uploadSection) {
                uploadSection.scrollIntoView({ behavior: 'smooth' });
            }
        }

        // مزامنة المنتجات بين جميع الأنظمة
        function syncProductsAcrossSystems() {
            try {
                // الحصول على المنتجات من adminProducts
                const adminProducts = JSON.parse(localStorage.getItem('adminProducts')) || [];

                if (adminProducts.length > 0) {
                    // مزامنة مع products
                    localStorage.setItem('products', JSON.stringify(adminProducts));

                    console.log(`🔄 تم مزامنة ${adminProducts.length} منتج عبر جميع الأنظمة`);
                    showNotification(`تم مزامنة ${adminProducts.length} منتج بنجاح`, 'success');
                } else {
                    console.log('⚠️ لا توجد منتجات في adminProducts للمزامنة');
                    showNotification('لا توجد منتجات للمزامنة', 'warning');
                }

            } catch (error) {
                console.error('❌ خطأ في مزامنة المنتجات:', error);
                showNotification('حدث خطأ في مزامنة المنتجات', 'error');
            }
        }

        // إضافة زر المزامنة إلى واجهة المستخدم
        function addSyncButton() {
            const batchOperations = document.querySelector('.batch-operations');
            if (batchOperations && !document.getElementById('syncProductsBtn')) {
                const syncButton = document.createElement('button');
                syncButton.id = 'syncProductsBtn';
                syncButton.className = 'btn btn-info';
                syncButton.onclick = syncProductsAcrossSystems;
                syncButton.innerHTML = `
                    <i class="fas fa-sync-alt"></i>
                    مزامنة المنتجات
                `;

                // إدراج الزر بعد زر "إعادة تحميل المنتجات"
                const reloadBtn = batchOperations.querySelector('button[onclick="loadProductsWithImages()"]');
                if (reloadBtn && reloadBtn.parentNode) {
                    reloadBtn.parentNode.insertBefore(syncButton, reloadBtn.nextSibling);
                }
            }
        }

        // تحميل نموذج Excel
        function downloadExcelTemplate() {
            // إنشاء بيانات نموذجية
            const templateData = [
                [
                    'اسم المنتج (عربي)',
                    'اسم المنتج (إنجليزي)',
                    'الفئة',
                    'السعر (مفرد)',
                    'سعر الجملة',
                    'السعر القديم',
                    'الوصف (عربي)',
                    'الوصف (إنجليزي)',
                    'رابط الصورة',
                    'الكمية',
                    'الحالة'
                ],
                [
                    'هاتف ذكي',
                    'Smartphone',
                    'إلكترونيات',
                    '500000',
                    '450000',
                    '600000',
                    'هاتف ذكي بمواصفات عالية',
                    'High-spec smartphone',
                    'https://example.com/phone.jpg',
                    '10',
                    'متوفر'
                ],
                [
                    'لابتوب',
                    'Laptop',
                    'إلكترونيات',
                    '1200000',
                    '1100000',
                    '',
                    'لابتوب للألعاب والعمل',
                    'Gaming and work laptop',
                    'https://example.com/laptop.jpg',
                    '5',
                    'متوفر'
                ]
            ];

            // إنشاء ملف Excel
            const ws = XLSX.utils.aoa_to_sheet(templateData);
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'المنتجات');

            // تحميل الملف
            XLSX.writeFile(wb, 'نموذج_المنتجات.xlsx');

            showNotification('تم تحميل نموذج Excel بنجاح!', 'success');
        }

        // وظيفة اختبار رفع الملف (للتطوير)
        function testExcelUpload() {
            console.log('🧪 اختبار وظيفة رفع الملف...');

            const fileInput = document.getElementById('excelFileInput');
            const uploadZone = document.getElementById('uploadZone');
            const uploadButton = document.getElementById('btnUploadExcel');
            const clearButton = document.getElementById('btnClearFile');

            console.log('📋 نتائج الاختبار:');
            console.log('📁 عنصر اختيار الملف:', fileInput ? '✅ موجود' : '❌ مفقود');
            console.log('📦 منطقة الرفع:', uploadZone ? '✅ موجود' : '❌ مفقود');
            console.log('🔘 زر الرفع:', uploadButton ? '✅ موجود' : '❌ مفقود');
            console.log('🗑️ زر المسح:', clearButton ? '✅ موجود' : '❌ مفقود');

            if (fileInput) {
                console.log('📄 أنواع الملفات المقبولة:', fileInput.accept);
            }

            // اختبار النقر على زر اختيار الملف
            const selectButton = uploadZone?.querySelector('.btn-select-file');
            if (selectButton) {
                console.log('🖱️ اختبار النقر على زر اختيار الملف...');
                selectButton.click();
            }
        }

        // وظيفة اختبار سريع للنقر
        function quickClickTest() {
            console.log('🧪 اختبار سريع للنقر...');
            triggerFileInput();
        }

        // وظيفة اختبار شاملة
        function fullUploadTest() {
            console.log('🧪 اختبار شامل لنظام رفع الملف...');

            // اختبار العناصر
            testExcelUpload();

            // اختبار النقر
            setTimeout(() => {
                console.log('🖱️ اختبار النقر التلقائي...');
                quickClickTest();
            }, 1000);
        }

        // إضافة نسخة احتياطية عالمية لتفعيل اختيار الملف
        window.openFileDialog = function() {
            console.log('🌍 تفعيل النسخة الاحتياطية العالمية...');
            triggerFileInput();
        };

        // تفعيل الاختبار (إلغاء التعليق للاختبار)
        // setTimeout(() => testExcelUpload(), 2000);
        // setTimeout(() => quickClickTest(), 3000);

    </script>

    <style>
        /* أنماط إدارة الموظفين الحديثة */

        /* Header Section */
        .employee-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .employee-header::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            z-index: 1;
        }

        .header-text .page-title {
            font-size: 2.5rem;
            font-weight: 800;
            margin: 0 0 0.5rem 0;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .header-text .page-title i {
            font-size: 2rem;
            opacity: 0.9;
        }

        .header-text .page-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin: 0;
            font-weight: 400;
        }

        .header-actions {
            display: flex;
            gap: 1rem;
        }

        /* Modern Buttons */
        .btn-primary-modern, .btn-secondary-modern {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 12px;
            font-weight: 600;
            font-size: 0.95rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
            position: relative;
            overflow: hidden;
        }

        .btn-primary-modern {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .btn-primary-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
        }

        .btn-secondary-modern {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }

        .btn-secondary-modern:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        /* Statistics Cards */
        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card-modern {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid #f0f0f0;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stat-card-modern::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .stat-card-modern.total::before {
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .stat-card-modern.active::before {
            background: linear-gradient(90deg, #56ab2f, #a8e6cf);
        }

        .stat-card-modern.admin::before {
            background: linear-gradient(90deg, #ff6b6b, #ee5a24);
        }

        .stat-card-modern.inactive::before {
            background: linear-gradient(90deg, #ffa726, #ff7043);
        }

        .stat-card-modern:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .stat-card-modern .stat-icon-wrapper {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 60px;
            border-radius: 15px;
            margin-bottom: 1.5rem;
        }

        .stat-card-modern.total .stat-icon-wrapper {
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .stat-card-modern.active .stat-icon-wrapper {
            background: linear-gradient(135deg, #56ab2f, #a8e6cf);
        }

        .stat-card-modern.admin .stat-icon-wrapper {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        }

        .stat-card-modern.inactive .stat-icon-wrapper {
            background: linear-gradient(135deg, #ffa726, #ff7043);
        }

        .stat-card-modern .stat-icon {
            color: white;
            font-size: 1.5rem;
        }

        .stat-card-modern .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .stat-card-modern .stat-label {
            font-size: 1rem;
            color: #7f8c8d;
            font-weight: 600;
            margin-bottom: 0.75rem;
        }

        .stat-card-modern .stat-trend {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.85rem;
            color: #95a5a6;
        }

        .stat-card-modern .stat-trend i {
            font-size: 0.8rem;
        }

        /* Employees Panel */
        .employees-panel {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-bottom: 2rem;
        }

        /* Control Bar */
        .control-bar {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 1.5rem 2rem;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .control-left .panel-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin: 0 0 0.25rem 0;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .control-left .employees-count {
            font-size: 0.9rem;
            color: #7f8c8d;
            font-weight: 500;
        }

        .search-controls {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .search-input-wrapper {
            position: relative;
            min-width: 300px;
        }

        .search-input-wrapper i {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #95a5a6;
            font-size: 0.9rem;
        }

        .search-input-wrapper input {
            width: 100%;
            padding: 0.75rem 1rem 0.75rem 2.5rem;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            background: white;
        }

        .search-input-wrapper input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .filter-dropdown select {
            padding: 0.75rem 1rem;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            font-size: 0.9rem;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 150px;
        }

        .filter-dropdown select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn-export {
            padding: 0.75rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 45px;
            height: 45px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .btn-export:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        /* Modern Table */
        .table-container {
            padding: 0;
        }

        .table-wrapper {
            overflow-x: auto;
        }

        .modern-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }

        .modern-table thead tr {
            background: linear-gradient(135deg, #2c3e50, #34495e);
        }

        .modern-table th {
            padding: 1.25rem 1rem;
            text-align: right;
            font-weight: 600;
            font-size: 0.9rem;
            color: white;
            border: none;
            position: relative;
        }

        .modern-table th i {
            margin-left: 0.5rem;
            opacity: 0.8;
        }

        .modern-table tbody tr {
            transition: all 0.3s ease;
            border-bottom: 1px solid #f8f9fa;
        }

        .modern-table tbody tr:hover {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            transform: scale(1.01);
        }

        .modern-table td {
            padding: 1.25rem 1rem;
            vertical-align: middle;
            border: none;
        }

        /* Employee Info Cell */
        .employee-info-cell {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .employee-avatar {
            width: 45px;
            height: 45px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.1rem;
            font-weight: 600;
        }

        .employee-details strong {
            display: block;
            color: #2c3e50;
            font-weight: 600;
            font-size: 0.95rem;
            margin-bottom: 0.25rem;
        }

        .employee-details small {
            color: #7f8c8d;
            font-size: 0.8rem;
            font-family: 'Courier New', monospace;
        }

        /* Username Code */
        .username-code {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 0.5rem 0.75rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            color: #495057;
            border: 1px solid #dee2e6;
            font-weight: 600;
            font-size: 0.85rem;
        }

        /* Status and Role Badges */
        .status-badge, .role-badge {
            padding: 0.4rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
        }

        .status-badge.active {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-badge.inactive {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .role-badge.admin {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
        }

        .role-badge.supervisor {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .role-badge.cashier {
            background: linear-gradient(135deg, #56ab2f, #a8e6cf);
            color: white;
        }

        .role-badge.employee {
            background: linear-gradient(135deg, #f093fb, #f5576c);
            color: white;
        }

        /* Action Buttons */
        .action-buttons {
            display: flex;
            gap: 0.5rem;
        }

        .btn-sm {
            padding: 0.5rem;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.8rem;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .btn-sm.btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-sm.btn-warning {
            background: linear-gradient(135deg, #ffa726, #ff7043);
            color: white;
        }

        .btn-sm.btn-danger {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
        }

        .btn-sm:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        /* Empty State */
        .empty-state-modern {
            text-align: center;
            padding: 4rem 2rem;
            color: #7f8c8d;
        }

        .empty-state-modern .empty-icon {
            font-size: 4rem;
            margin-bottom: 1.5rem;
            color: #bdc3c7;
        }

        .empty-state-modern .empty-title {
            font-size: 1.5rem;
            color: #2c3e50;
            margin-bottom: 1rem;
            font-weight: 600;
        }

        .empty-state-modern .empty-description {
            font-size: 1rem;
            margin-bottom: 2rem;
            line-height: 1.6;
            max-width: 400px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Roles Information Panel */
        .roles-info-panel {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .info-header {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .info-header h3 {
            font-size: 1.5rem;
            font-weight: 700;
            margin: 0 0 0.5rem 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
        }

        .info-header p {
            margin: 0;
            opacity: 0.9;
            font-size: 1rem;
        }

        .roles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            padding: 2rem;
        }

        .role-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            border: 2px solid #f0f0f0;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .role-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
        }

        .role-card.admin-role::before {
            background: linear-gradient(90deg, #ff6b6b, #ee5a24);
        }

        .role-card.supervisor-role::before {
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .role-card.cashier-role::before {
            background: linear-gradient(90deg, #56ab2f, #a8e6cf);
        }

        .role-card.employee-role::before {
            background: linear-gradient(90deg, #f093fb, #f5576c);
        }

        .role-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            border-color: #e0e0e0;
        }

        .role-card .role-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 1.5rem;
        }

        .role-card.admin-role .role-icon {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        }

        .role-card.supervisor-role .role-icon {
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .role-card.cashier-role .role-icon {
            background: linear-gradient(135deg, #56ab2f, #a8e6cf);
        }

        .role-card.employee-role .role-icon {
            background: linear-gradient(135deg, #f093fb, #f5576c);
        }

        .role-card h4 {
            font-size: 1.25rem;
            font-weight: 700;
            color: #2c3e50;
            margin: 0 0 0.5rem 0;
        }

        .role-card p {
            color: #7f8c8d;
            margin: 0 0 1.5rem 0;
            font-size: 0.95rem;
        }

        .permissions-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .permissions-list li {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.5rem 0;
            font-size: 0.9rem;
            color: #2c3e50;
        }

        .permissions-list li i.fa-check {
            color: #27ae60;
            font-weight: 600;
        }

        .permissions-list li i.fa-times {
            color: #e74c3c;
            font-weight: 600;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .stats-container {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            }

            .roles-grid {
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            }
        }

        @media (max-width: 768px) {
            .employee-header {
                padding: 1.5rem;
            }

            .header-content {
                flex-direction: column;
                gap: 1.5rem;
                text-align: center;
            }

            .header-text .page-title {
                font-size: 2rem;
            }

            .header-actions {
                justify-content: center;
            }

            .stats-container {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .stat-card-modern {
                padding: 1.5rem;
            }

            .control-bar {
                flex-direction: column;
                align-items: stretch;
                gap: 1.5rem;
            }

            .search-controls {
                flex-direction: column;
                gap: 1rem;
            }

            .search-input-wrapper {
                min-width: auto;
            }

            .modern-table th,
            .modern-table td {
                padding: 0.75rem 0.5rem;
                font-size: 0.8rem;
            }

            .employee-info-cell {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .action-buttons {
                flex-direction: column;
                gap: 0.25rem;
            }

            .roles-grid {
                grid-template-columns: 1fr;
                padding: 1rem;
            }

            .role-card {
                padding: 1rem;
            }
        }

        @media (max-width: 480px) {
            .employee-header {
                padding: 1rem;
            }

            .header-text .page-title {
                font-size: 1.5rem;
            }

            .btn-primary-modern,
            .btn-secondary-modern {
                padding: 0.5rem 1rem;
                font-size: 0.85rem;
            }

            .stat-card-modern {
                padding: 1rem;
            }

            .stat-card-modern .stat-number {
                font-size: 2rem;
            }

            .control-bar {
                padding: 1rem;
            }

            .employees-panel {
                border-radius: 15px;
            }

            .table-wrapper {
                font-size: 0.75rem;
            }
        }

        /* أنماط النموذج الحديث */
        .employee-modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(5px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            animation: fadeIn 0.3s ease;
        }

        .employee-modal-modern {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            max-width: 700px;
            width: 95%;
            max-height: 90vh;
            overflow-y: auto;
            animation: slideUp 0.3s ease;
        }

        .modal-header-modern {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 2rem;
            border-radius: 20px 20px 0 0;
            display: flex;
            align-items: center;
            gap: 1.5rem;
            position: relative;
        }

        .modal-header-modern .header-icon {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            backdrop-filter: blur(10px);
        }

        .modal-header-modern .header-text h3 {
            margin: 0 0 0.5rem 0;
            font-size: 1.5rem;
            font-weight: 700;
        }

        .modal-header-modern .header-text p {
            margin: 0;
            opacity: 0.9;
            font-size: 0.95rem;
        }

        .close-btn-modern {
            position: absolute;
            top: 1rem;
            left: 1rem;
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 10px;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .close-btn-modern:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .modal-body-modern {
            padding: 2rem;
        }

        .form-section {
            margin-bottom: 2rem;
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            border: 1px solid #e9ecef;
        }

        .section-header {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 1.5rem;
            color: #2c3e50;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .section-header i {
            width: 30px;
            height: 30px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9rem;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .form-group-modern {
            margin-bottom: 1rem;
        }

        .form-label-modern {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
            color: #2c3e50;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .form-label-modern i {
            color: #667eea;
            font-size: 0.8rem;
        }

        .form-input-modern, .form-select-modern {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-input-modern:focus, .form-select-modern:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }

        .form-select-modern {
            cursor: pointer;
        }

        .message-modern {
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1.5rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .message-modern.success {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message-modern.error {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .form-actions-modern {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 2rem;
        }

        .btn-cancel-modern, .btn-submit-modern {
            padding: 0.75rem 2rem;
            border: none;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.95rem;
        }

        .btn-cancel-modern {
            background: linear-gradient(135deg, #95a5a6, #7f8c8d);
            color: white;
        }

        .btn-cancel-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(149, 165, 166, 0.3);
        }

        .btn-submit-modern {
            background: linear-gradient(135deg, #56ab2f, #a8e6cf);
            color: white;
        }

        .btn-submit-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(86, 171, 47, 0.3);
        }

        /* أنماط عرض المعلومات */
        .info-display {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            padding: 1rem;
            border: 1px solid #dee2e6;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #e9ecef;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: 600;
            color: #495057;
            font-size: 0.9rem;
        }

        .info-value {
            color: #6c757d;
            font-size: 0.9rem;
            font-family: 'Courier New', monospace;
            background: white;
            padding: 0.25rem 0.5rem;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }

        /* أنماط الإشعارات */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            padding: 1rem 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 1rem;
            z-index: 10001;
            transform: translateX(400px);
            opacity: 0;
            transition: all 0.3s ease;
            min-width: 300px;
            max-width: 500px;
            border-left: 4px solid #667eea;
        }

        .notification.show {
            transform: translateX(0);
            opacity: 1;
        }

        .notification-success {
            border-left-color: #28a745;
        }

        .notification-error {
            border-left-color: #dc3545;
        }

        .notification-info {
            border-left-color: #17a2b8;
        }

        .notification-content {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            flex: 1;
        }

        .notification-content i {
            font-size: 1.2rem;
        }

        .notification-success .notification-content i {
            color: #28a745;
        }

        .notification-error .notification-content i {
            color: #dc3545;
        }

        .notification-info .notification-content i {
            color: #17a2b8;
        }

        .notification-content span {
            color: #2c3e50;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .notification-close {
            background: none;
            border: none;
            color: #6c757d;
            cursor: pointer;
            padding: 0.25rem;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .notification-close:hover {
            background: #f8f9fa;
            color: #495057;
        }

        /* أنماط إدارة مناطق التوصيل المحسنة */
        .delivery-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
            padding: 1rem;
            background: linear-gradient(135deg, #f8f9ff 0%, #fff8f8 100%);
            border-radius: 20px;
            position: relative;
            overflow: hidden;
        }

        .delivery-stats::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2, #56ab2f, #17a2b8);
            border-radius: 20px 20px 0 0;
        }

        /* بطاقات الإحصائيات الحديثة */
        .stat-card-modern {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 24px;
            padding: 2.5rem;
            color: white;
            position: relative;
            overflow: hidden;
            transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            cursor: pointer;
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .stat-card-modern::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.8s ease;
        }

        .stat-card-modern:hover::before {
            left: 100%;
        }

        .stat-card-modern:hover {
            transform: translateY(-12px) scale(1.03) rotateY(5deg);
            box-shadow: 0 25px 50px rgba(102, 126, 234, 0.5);
        }

        .stat-card-modern.primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
        }

        .stat-card-modern.primary:hover {
            box-shadow: 0 25px 50px rgba(102, 126, 234, 0.5);
        }

        .stat-card-modern.success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            box-shadow: 0 15px 35px rgba(86, 171, 47, 0.3);
        }

        .stat-card-modern.success:hover {
            box-shadow: 0 25px 50px rgba(86, 171, 47, 0.5);
        }

        .stat-card-modern.warning {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            box-shadow: 0 15px 35px rgba(255, 154, 158, 0.3);
        }

        .stat-card-modern.warning:hover {
            box-shadow: 0 25px 50px rgba(255, 154, 158, 0.5);
        }

        .stat-card-modern.info {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            box-shadow: 0 15px 35px rgba(168, 237, 234, 0.3);
        }

        .stat-card-modern.info:hover {
            box-shadow: 0 25px 50px rgba(168, 237, 234, 0.5);
        }

        .stat-icon-modern {
            font-size: 3.5rem;
            margin-bottom: 1.5rem;
            opacity: 0.9;
            filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
            transition: all 0.3s ease;
        }

        .stat-card-modern:hover .stat-icon-modern {
            transform: scale(1.1) rotateY(15deg);
            filter: drop-shadow(0 6px 12px rgba(0, 0, 0, 0.3));
        }

        .stat-content-modern {
            position: relative;
            z-index: 2;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 0.75rem;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            letter-spacing: -1px;
            transition: all 0.3s ease;
        }

        .stat-card-modern:hover .stat-number {
            transform: scale(1.05);
            text-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
        }

        .stat-label {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 0.75rem;
            opacity: 0.95;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .stat-trend {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 1rem;
            opacity: 0.85;
            background: rgba(255, 255, 255, 0.1);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .stat-decoration {
            position: absolute;
            top: -30px;
            right: -30px;
            opacity: 0.15;
            transition: all 0.5s ease;
        }

        .stat-card-modern:hover .stat-decoration {
            opacity: 0.25;
            transform: rotate(15deg) scale(1.1);
        }

        .decoration-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
            position: absolute;
            animation: float 6s ease-in-out infinite;
        }

        .decoration-circle:nth-child(2) {
            width: 80px;
            height: 80px;
            top: 50px;
            right: 50px;
            animation: float 6s ease-in-out infinite reverse;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
            }
            50% {
                transform: translateY(-10px) rotate(5deg);
            }
        }

        /* أزرار الإجراءات الحديثة */
        .delivery-actions-modern {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        .btn-action-modern {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border: 2px solid #e9ecef;
            border-radius: 20px;
            padding: 2rem;
            display: flex;
            align-items: center;
            gap: 1.5rem;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            cursor: pointer;
            text-decoration: none;
            color: inherit;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .btn-action-modern::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            transition: left 0.6s ease;
        }

        .btn-action-modern:hover::before {
            left: 100%;
        }

        .btn-action-modern:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
        }

        .btn-action-modern.primary {
            border-color: #667eea;
            background: linear-gradient(135deg, #ffffff 0%, #f0f2ff 100%);
        }

        .btn-action-modern.primary:hover {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: #667eea;
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
        }

        .btn-action-modern.secondary {
            border-color: #6c757d;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        }

        .btn-action-modern.secondary:hover {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            color: white;
            border-color: #6c757d;
            box-shadow: 0 15px 40px rgba(108, 117, 125, 0.4);
        }

        .btn-action-modern.success {
            border-color: #28a745;
            background: linear-gradient(135deg, #ffffff 0%, #f0fff4 100%);
        }

        .btn-action-modern.success:hover {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
            border-color: #28a745;
            box-shadow: 0 15px 40px rgba(86, 171, 47, 0.4);
        }

        .btn-action-modern.info {
            border-color: #17a2b8;
            background: linear-gradient(135deg, #ffffff 0%, #f0fdff 100%);
        }

        .btn-action-modern.info:hover {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            color: white;
            border-color: #17a2b8;
            box-shadow: 0 15px 40px rgba(168, 237, 234, 0.4);
        }

        .btn-icon {
            font-size: 2.5rem;
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .btn-icon::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.8), transparent);
            transition: all 0.4s ease;
            border-radius: 50%;
            transform: translate(-50%, -50%);
        }

        .btn-action-modern:hover .btn-icon::before {
            width: 100px;
            height: 100px;
        }

        .btn-action-modern:hover .btn-icon {
            transform: scale(1.1) rotateY(10deg);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .btn-content {
            flex: 1;
            position: relative;
            z-index: 2;
        }

        .btn-title {
            display: block;
            font-weight: 700;
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
        }

        .btn-action-modern:hover .btn-title {
            transform: translateX(5px);
        }

        .btn-subtitle {
            display: block;
            font-size: 1rem;
            color: #6c757d;
            transition: all 0.3s ease;
            opacity: 0.8;
        }

        .btn-action-modern:hover .btn-subtitle {
            color: rgba(255, 255, 255, 0.9);
            transform: translateX(5px);
        }

        /* حاوي الجدول الحديث */
        .zones-list-container {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
            border-radius: 24px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-bottom: 3rem;
            border: 1px solid rgba(102, 126, 234, 0.1);
            position: relative;
        }

        .zones-list-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, #667eea, #764ba2, #56ab2f, #17a2b8);
        }

        .zones-table-container-modern {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
            border-radius: 24px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            border: 1px solid rgba(102, 126, 234, 0.1);
            position: relative;
        }

        .zones-table-container-modern::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, #667eea, #764ba2, #56ab2f, #17a2b8);
        }

        .table-wrapper {
            overflow-x: auto;
        }

        /* الجدول الحديث */
        .zones-table-modern {
            width: 100%;
            border-collapse: collapse;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .zones-table-modern thead {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #56ab2f 100%);
            color: white;
            position: relative;
        }

        .zones-table-modern thead::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
            animation: headerShine 4s infinite;
        }

        @keyframes headerShine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .zones-table-modern th {
            padding: 2rem 1.5rem;
            text-align: right;
            font-weight: 700;
            font-size: 1rem;
            border: none;
            position: relative;
            z-index: 2;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
        }

        .zones-table-modern th:hover {
            transform: translateY(-2px);
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        .zones-table-modern th i {
            margin-left: 0.75rem;
            opacity: 0.9;
            font-size: 1.1rem;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
        }

        .zones-table-modern th::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
        }

        /* صفوف الجدول */
        .zone-row-modern {
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border-bottom: 1px solid rgba(102, 126, 234, 0.1);
            position: relative;
            background: linear-gradient(135deg, #ffffff 0%, #fafbff 100%);
        }

        .zone-row-modern::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            transform: scaleY(0);
            transition: transform 0.3s ease;
        }

        .zone-row-modern:hover::before {
            transform: scaleY(1);
        }

        .zone-row-modern:hover {
            background: linear-gradient(135deg, #f0f2ff 0%, #fff0f8 100%);
            transform: translateX(8px) scale(1.01);
            box-shadow: 0 8px 30px rgba(102, 126, 234, 0.15);
            border-radius: 12px;
        }

        .zone-row-modern.inactive {
            opacity: 0.6;
            filter: grayscale(20%);
        }

        .zone-row-modern.inactive:hover {
            opacity: 0.8;
            filter: grayscale(0%);
        }

        .zone-row-modern td {
            padding: 2rem 1.5rem;
            border: none;
            vertical-align: middle;
            position: relative;
            z-index: 2;
        }

        /* خلايا الجدول المخصصة */
        .zone-info-cell {
            min-width: 250px;
        }

        .zone-card-mini {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            transition: all 0.3s ease;
        }

        .zone-row-modern:hover .zone-card-mini {
            transform: translateX(5px);
        }

        .zone-avatar {
            width: 60px;
            height: 60px;
            border-radius: 16px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.4rem;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .zone-avatar::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transform: rotate(45deg);
            transition: all 0.5s ease;
            opacity: 0;
        }

        .zone-row-modern:hover .zone-avatar::before {
            opacity: 1;
            animation: avatarShine 1s ease;
        }

        @keyframes avatarShine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .zone-row-modern:hover .zone-avatar {
            transform: scale(1.1) rotateY(10deg);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5);
        }

        .zone-details {
            flex: 1;
            transition: all 0.3s ease;
        }

        .zone-row-modern:hover .zone-details {
            transform: translateY(-2px);
        }

        .zone-name {
            font-weight: 700;
            font-size: 1.1rem;
            color: #2c3e50;
            margin-bottom: 0.5rem;
            transition: all 0.3s ease;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .zone-row-modern:hover .zone-name {
            color: #667eea;
            transform: translateX(3px);
        }

        .zone-name-en {
            font-size: 0.9rem;
            color: #7f8c8d;
            margin-bottom: 0.5rem;
            font-style: italic;
            transition: all 0.3s ease;
        }

        .zone-row-modern:hover .zone-name-en {
            color: #764ba2;
            transform: translateX(3px);
        }

        .zone-description {
            font-size: 0.85rem;
            color: #95a5a6;
            line-height: 1.4;
            transition: all 0.3s ease;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .zone-row-modern:hover .zone-description {
            color: #6c757d;
            white-space: normal;
            max-width: none;
        }

        /* عرض العمولة */
        .delivery-fee-cell {
            min-width: 180px;
        }

        .fee-display {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.75rem;
            position: relative;
            padding: 1rem;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 16px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .zone-row-modern:hover .fee-display {
            transform: scale(1.05);
            border-color: #667eea;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
        }

        .fee-amount {
            font-size: 1.4rem;
            font-weight: 800;
            color: #2c3e50;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .zone-row-modern:hover .fee-amount {
            color: #667eea;
            transform: scale(1.1);
        }

        .fee-currency {
            font-size: 0.9rem;
            color: #7f8c8d;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .zone-row-modern:hover .fee-currency {
            color: #764ba2;
        }

        .fee-indicator {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            margin-top: 0.5rem;
            position: relative;
            overflow: hidden;
        }

        .fee-indicator::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
            transition: left 0.5s ease;
        }

        .zone-row-modern:hover .fee-indicator::before {
            left: 100%;
        }

        .fee-indicator.low {
            background: linear-gradient(90deg, #56ab2f, #a8e6cf);
            box-shadow: 0 2px 8px rgba(86, 171, 47, 0.3);
        }

        .fee-indicator.medium {
            background: linear-gradient(90deg, #f093fb, #f5576c);
            box-shadow: 0 2px 8px rgba(240, 147, 251, 0.3);
        }

        .fee-indicator.high {
            background: linear-gradient(90deg, #ff6b6b, #ee5a24);
            box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
        }

        /* عرض الوقت */
        .delivery-time-cell {
            min-width: 140px;
        }

        .time-display {
            text-align: center;
        }

        .time-range {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.25rem;
        }

        .time-range i {
            margin-left: 0.5rem;
            color: #17a2b8;
        }

        .time-avg {
            font-size: 0.8rem;
            color: #7f8c8d;
        }

        /* شارات الحالة الحديثة */
        .status-badge-modern {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-badge-modern.active {
            background: linear-gradient(135deg, #56ab2f, #a8e6cf);
            color: white;
        }

        .status-badge-modern.inactive {
            background: linear-gradient(135deg, #bdc3c7, #95a5a6);
            color: white;
        }

        /* عرض التاريخ */
        .date-cell {
            min-width: 160px;
        }

        .date-display {
            text-align: center;
        }

        .date-main {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 0.25rem;
        }

        .date-main i {
            margin-left: 0.5rem;
            color: #56ab2f;
        }

        .date-updated {
            font-size: 0.75rem;
            color: #7f8c8d;
        }

        .date-updated i {
            margin-left: 0.25rem;
            color: #f39c12;
        }

        /* أزرار الإجراءات الحديثة */
        .actions-cell {
            min-width: 180px;
        }

        .action-buttons-modern {
            display: flex;
            gap: 0.5rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn-action-sm {
            width: 36px;
            height: 36px;
            border-radius: 8px;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
            position: relative;
            overflow: hidden;
        }

        .btn-action-sm::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.5s ease;
        }

        .btn-action-sm:hover::before {
            left: 100%;
        }

        .btn-action-sm.edit {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-action-sm.edit:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-action-sm.toggle {
            background: linear-gradient(135deg, #f39c12, #f1c40f);
            color: white;
        }

        .btn-action-sm.toggle:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(243, 156, 18, 0.4);
        }

        .btn-action-sm.toggle.active {
            background: linear-gradient(135deg, #56ab2f, #a8e6cf);
        }

        .btn-action-sm.delete {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        .btn-action-sm.delete:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.4);
        }

        .btn-action-sm.info {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
        }

        .btn-action-sm.info:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(23, 162, 184, 0.4);
        }

        /* أنماط النوافذ المنبثقة المحسنة */
        .zone-edit-modal .employee-modal-modern {
            max-width: 800px;
            width: 90%;
        }

        .zone-details-modal .employee-modal-modern {
            max-width: 900px;
            width: 95%;
        }

        .analytics-modal .employee-modal-modern {
            max-width: 1000px;
            width: 95%;
        }

        /* محتوى تفاصيل المنطقة */
        .zone-details-content {
            padding: 1rem 0;
        }

        .details-section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: #f8f9fa;
            border-radius: 12px;
            border-left: 4px solid #667eea;
        }

        .details-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            background: white;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .detail-item.full-width {
            grid-column: 1 / -1;
            flex-direction: column;
            align-items: flex-start;
            gap: 0.5rem;
        }

        .detail-label {
            font-weight: 600;
            color: #495057;
        }

        .detail-value {
            color: #6c757d;
            font-family: 'Courier New', monospace;
        }

        /* بطاقات معلومات التوصيل */
        .delivery-info-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .info-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .info-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .info-card.fee {
            border-color: #f39c12;
        }

        .info-card.time {
            border-color: #17a2b8;
        }

        .info-card.status.active {
            border-color: #28a745;
        }

        .info-card.status.inactive {
            border-color: #dc3545;
        }

        .card-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        .info-card.fee .card-icon {
            color: #f39c12;
        }

        .info-card.time .card-icon {
            color: #17a2b8;
        }

        .info-card.status.active .card-icon {
            color: #28a745;
        }

        .info-card.status.inactive .card-icon {
            color: #dc3545;
        }

        .card-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .card-label {
            font-size: 0.9rem;
            color: #6c757d;
        }

        /* معلومات النظام */
        .system-info {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem 0;
            border-bottom: 1px solid #f1f3f4;
        }

        .info-row:last-child {
            border-bottom: none;
        }

        .info-label {
            font-weight: 600;
            color: #495057;
        }

        .info-value {
            color: #6c757d;
        }

        .info-value.code {
            font-family: 'Courier New', monospace;
            background: #f8f9fa;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.85rem;
        }

        /* إجراءات سريعة */
        .quick-actions {
            display: flex;
            gap: 1rem;
            margin-top: 2rem;
            justify-content: center;
        }

        .btn-quick {
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            border: none;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-quick.edit {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-quick.toggle {
            background: linear-gradient(135deg, #f39c12, #f1c40f);
            color: white;
        }

        .btn-quick:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        /* أنماط التحليلات */
        .analytics-content {
            padding: 1rem 0;
        }

        .analytics-section {
            margin-bottom: 2rem;
        }

        .analytics-section h4 {
            color: #2c3e50;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #e9ecef;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .analytics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .analytics-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .analytics-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .analytics-card.active {
            border-color: #28a745;
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
        }

        .analytics-card.inactive {
            border-color: #dc3545;
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
        }

        .analytics-card .card-header {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
            font-weight: 600;
            color: #495057;
        }

        .analytics-card .card-value {
            font-size: 2rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .analytics-card .card-percentage {
            font-size: 0.9rem;
            color: #6c757d;
        }

        /* تحليل العمولات */
        .fee-analysis {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .fee-stat {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
            border: 1px solid #e9ecef;
        }

        .stat-label {
            display: block;
            font-size: 0.9rem;
            color: #6c757d;
            margin-bottom: 0.5rem;
        }

        .stat-value {
            font-size: 1.2rem;
            font-weight: 700;
        }

        .stat-value.min {
            color: #28a745;
        }

        .stat-value.max {
            color: #dc3545;
        }

        .stat-value.avg {
            color: #17a2b8;
        }

        /* ملخص المناطق */
        .zones-summary {
            max-height: 300px;
            overflow-y: auto;
            margin-top: 1rem;
        }

        .zone-summary-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            margin-bottom: 0.5rem;
            background: white;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .zone-summary-item:hover {
            transform: translateX(4px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .zone-summary-item.active {
            border-left: 4px solid #28a745;
        }

        .zone-summary-item.inactive {
            border-left: 4px solid #dc3545;
            opacity: 0.7;
        }

        .zone-summary-info {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .zone-summary-name {
            font-weight: 600;
            color: #2c3e50;
        }

        .zone-summary-fee {
            font-size: 0.9rem;
            color: #6c757d;
        }

        .zone-summary-status i {
            font-size: 1.2rem;
        }

        .zone-summary-item.active .zone-summary-status i {
            color: #28a745;
        }

        .zone-summary-item.inactive .zone-summary-status i {
            color: #dc3545;
        }

        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .delivery-stats {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .delivery-actions-modern {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .btn-action-modern {
                padding: 1rem;
            }

            .btn-icon {
                width: 50px;
                height: 50px;
                font-size: 1.5rem;
            }

            .zones-table-modern th,
            .zones-table-modern td {
                padding: 1rem 0.5rem;
                font-size: 0.85rem;
            }

            .zone-card-mini {
                flex-direction: column;
                text-align: center;
                gap: 0.5rem;
            }

            .action-buttons-modern {
                gap: 0.25rem;
            }

            .btn-action-sm {
                width: 32px;
                height: 32px;
                font-size: 0.8rem;
            }

            .delivery-info-cards {
                grid-template-columns: 1fr;
            }

            .analytics-grid {
                grid-template-columns: 1fr;
            }

            .fee-analysis {
                grid-template-columns: 1fr;
            }

            .quick-actions {
                flex-direction: column;
            }
        }

        .zones-list-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #56ab2f 100%);
            color: white;
            padding: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1.5rem;
            position: relative;
            overflow: hidden;
        }

        .zones-list-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .zones-list-header h3 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            position: relative;
            z-index: 2;
        }

        .search-filter {
            display: flex;
            gap: 1.5rem;
            align-items: center;
            position: relative;
            z-index: 2;
        }

        .search-filter input,
        .search-filter select {
            padding: 0.75rem 1.5rem;
            border: 2px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            background: rgba(255, 255, 255, 0.15);
            color: white;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .search-filter input:focus,
        .search-filter select:focus {
            outline: none;
            border-color: rgba(255, 255, 255, 0.5);
            background: rgba(255, 255, 255, 0.25);
            box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
            transform: scale(1.02);
        }

        .search-filter input::placeholder {
            color: rgba(255, 255, 255, 0.8);
            font-weight: 400;
        }

        .zones-table-container {
            overflow-x: auto;
        }

        .zones-table {
            width: 100%;
            border-collapse: collapse;
        }

        .zones-table th {
            background: #f8f9fa;
            padding: 1rem;
            text-align: right;
            font-weight: 600;
            color: #495057;
            border-bottom: 2px solid #dee2e6;
        }

        .zones-table td {
            padding: 1rem;
            border-bottom: 1px solid #dee2e6;
            vertical-align: middle;
        }

        .zone-row {
            transition: all 0.3s ease;
        }

        .zone-row:hover {
            background: #f8f9fa;
        }

        .zone-row.inactive {
            opacity: 0.7;
        }

        .zone-info strong {
            display: block;
            color: #2c3e50;
            font-size: 1rem;
            margin-bottom: 0.25rem;
        }

        .zone-info small {
            color: #6c757d;
            font-size: 0.85rem;
        }

        .delivery-fee {
            background: linear-gradient(135deg, #56ab2f, #a8e6cf);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
            display: inline-block;
        }

        .delivery-time {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .delivery-time i {
            margin-left: 0.5rem;
            color: #17a2b8;
        }

        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .delivery-stats {
                grid-template-columns: 1fr;
            }

            .delivery-actions {
                flex-direction: column;
            }

            .zones-list-header {
                flex-direction: column;
                align-items: stretch;
            }

            .search-filter {
                flex-direction: column;
                gap: 0.5rem;
            }

            .zones-table {
                font-size: 0.85rem;
            }

            .zones-table th,
            .zones-table td {
                padding: 0.75rem 0.5rem;
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px) scale(0.95);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @media (max-width: 768px) {
            .employee-modal-modern {
                width: 95%;
                margin: 1rem;
            }

            .modal-header-modern {
                padding: 1.5rem;
                flex-direction: column;
                text-align: center;
                gap: 1rem;
            }

            .modal-body-modern {
                padding: 1.5rem;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .form-actions-modern {
                flex-direction: column;
            }

            .btn-cancel-modern, .btn-submit-modern {
                width: 100%;
                justify-content: center;
            }
        }

        /* أنماط إدارة السلايدرات */
        .sliders-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
            padding: 1rem;
            background: linear-gradient(135deg, #f8f9ff 0%, #fff8f8 100%);
            border-radius: 20px;
            position: relative;
            overflow: hidden;
        }

        .sliders-stats::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2, #56ab2f, #17a2b8);
            border-radius: 20px 20px 0 0;
        }

        .sliders-actions-modern {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        /* حاوي السلايدرات */
        .sliders-list-container {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
            border-radius: 24px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-bottom: 3rem;
            border: 1px solid rgba(102, 126, 234, 0.1);
            position: relative;
        }

        .sliders-list-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, #667eea, #764ba2, #56ab2f, #17a2b8);
        }

        .sliders-list-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #56ab2f 100%);
            color: white;
            padding: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1.5rem;
            position: relative;
            overflow: hidden;
        }

        .sliders-list-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            animation: shimmer 3s infinite;
        }

        .sliders-list-header h3 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            position: relative;
            z-index: 2;
        }

        /* شبكة السلايدرات */
        .sliders-grid-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            padding: 2rem;
        }

        /* بطاقة السلايدر */
        .slider-card-modern {
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border: 1px solid #e9ecef;
            position: relative;
        }

        .slider-card-modern:hover {
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
        }

        .slider-card-modern.inactive {
            opacity: 0.7;
            filter: grayscale(30%);
        }

        /* معاينة السلايدر */
        .slider-preview {
            height: 200px;
            position: relative;
            overflow: hidden;
        }

        .slider-image {
            width: 100%;
            height: 100%;
            background-size: cover;
            background-position: center;
            transition: transform 0.5s ease;
        }

        .slider-card-modern:hover .slider-image {
            transform: scale(1.1);
        }

        .slider-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.1));
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .slider-card-modern:hover .slider-overlay {
            opacity: 1;
        }

        .slider-content-preview {
            text-align: center;
            padding: 1rem;
        }

        .slider-content-preview h3 {
            font-size: 1.2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .slider-content-preview p {
            font-size: 0.9rem;
            margin-bottom: 1rem;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .preview-btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 20px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .preview-btn:hover {
            transform: scale(1.05);
        }

        /* معلومات السلايدر */
        .slider-info {
            padding: 1.5rem;
        }

        .slider-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .slider-title h4 {
            margin: 0 0 0.25rem 0;
            font-size: 1.1rem;
            font-weight: 700;
            color: #2c3e50;
        }

        .slider-title small {
            color: #7f8c8d;
            font-style: italic;
        }

        .slider-details {
            margin-bottom: 1.5rem;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #f1f3f4;
        }

        .detail-row:last-child {
            border-bottom: none;
        }

        .detail-label {
            font-weight: 600;
            color: #495057;
            font-size: 0.9rem;
        }

        .detail-value {
            color: #6c757d;
            font-size: 0.9rem;
        }

        /* أزرار إجراءات السلايدر */
        .slider-actions {
            display: flex;
            gap: 0.5rem;
            justify-content: center;
        }

        .btn-action-sm.preview {
            background: linear-gradient(135deg, #17a2b8, #138496);
            color: white;
        }

        .btn-action-sm.preview:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(23, 162, 184, 0.4);
        }

        /* نموذج السلايدر */
        .slider-modal .employee-modal-modern {
            max-width: 1000px;
            width: 95%;
            max-height: 90vh;
            overflow-y: auto;
        }

        .color-input {
            height: 50px !important;
            padding: 0.25rem !important;
            border-radius: 10px !important;
            cursor: pointer;
        }

        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .sliders-stats {
                grid-template-columns: 1fr;
                gap: 1rem;
                padding: 0.5rem;
            }

            .sliders-actions-modern {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .sliders-grid-container {
                grid-template-columns: 1fr;
                gap: 1rem;
                padding: 1rem;
            }

            .slider-card-modern {
                margin-bottom: 1rem;
            }

            .slider-preview {
                height: 150px;
            }

            .slider-info {
                padding: 1rem;
            }

            .slider-actions {
                gap: 0.25rem;
            }

            .btn-action-sm {
                width: 32px;
                height: 32px;
                font-size: 0.8rem;
            }
        }

        /* أنماط إدارة ألوان الموقع */
        .theme-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
            padding: 1rem;
            background: linear-gradient(135deg, #f8f9ff 0%, #fff8f8 100%);
            border-radius: 20px;
            position: relative;
            overflow: hidden;
        }

        .theme-stats::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2, #56ab2f, #17a2b8);
            border-radius: 20px 20px 0 0;
        }

        .theme-actions-modern {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 3rem;
        }

        /* لوحة تخصيص الألوان */
        .color-customizer-panel {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
            border-radius: 24px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-bottom: 3rem;
            border: 1px solid rgba(102, 126, 234, 0.1);
            position: relative;
        }

        .color-customizer-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, #667eea, #764ba2, #56ab2f, #17a2b8);
        }

        .customizer-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #56ab2f 100%);
            color: white;
            padding: 2rem;
            position: relative;
            overflow: hidden;
        }

        .customizer-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            animation: shimmer 3s infinite;
        }

        .customizer-header h3 {
            margin: 0 0 0.5rem 0;
            font-size: 1.5rem;
            font-weight: 700;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            position: relative;
            z-index: 2;
        }

        .customizer-header p {
            margin: 0;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }

        /* أقسام الألوان */
        .color-sections {
            padding: 2rem;
            display: grid;
            gap: 2rem;
        }

        .color-section {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            border: 1px solid #f0f0f0;
            transition: all 0.3s ease;
        }

        .color-section:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 1.5rem;
            font-size: 1.1rem;
            font-weight: 600;
            color: #2c3e50;
        }

        .section-title i {
            color: #667eea;
            font-size: 1.2rem;
        }

        .color-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .color-control {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .color-control label {
            font-weight: 600;
            color: #495057;
            font-size: 0.9rem;
        }

        .color-control input[type="color"] {
            width: 100%;
            height: 50px;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .color-control input[type="color"]:hover {
            border-color: #667eea;
            transform: scale(1.02);
        }

        .color-value {
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
            color: #6c757d;
            background: #f8f9fa;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            text-align: center;
        }

        /* المعاينة المباشرة */
        .live-preview {
            margin: 2rem 0;
            background: white;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .preview-header h4 {
            margin: 0;
            padding: 1rem 1.5rem;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            color: #2c3e50;
            font-size: 1rem;
            font-weight: 600;
        }

        .preview-content {
            padding: 1.5rem;
        }

        .preview-website {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
            font-size: 0.8rem;
        }

        .preview-header {
            padding: 0.75rem 1rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .preview-nav {
            display: flex;
            align-items: center;
            gap: 1rem;
            width: 100%;
        }

        .preview-logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 600;
        }

        .preview-menu {
            display: flex;
            gap: 1rem;
        }

        .preview-nav-link {
            text-decoration: none;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            transition: all 0.3s ease;
        }

        .preview-nav-link.active {
            font-weight: 600;
        }

        .preview-content {
            padding: 1rem;
        }

        .preview-section {
            margin-bottom: 1rem;
        }

        .preview-section h2 {
            margin: 0 0 0.5rem 0;
            font-size: 1rem;
            font-weight: 600;
        }

        .preview-section p {
            margin: 0 0 0.5rem 0;
            font-size: 0.8rem;
            line-height: 1.4;
        }

        .preview-cards {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.75rem;
            margin: 1rem 0;
        }

        .preview-card {
            padding: 0.75rem;
            border: 1px solid;
            border-radius: 6px;
        }

        .preview-card h3 {
            margin: 0 0 0.5rem 0;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .preview-card p {
            margin: 0 0 0.5rem 0;
            font-size: 0.7rem;
        }

        .preview-card button {
            padding: 0.25rem 0.5rem;
            border: none;
            border-radius: 4px;
            font-size: 0.7rem;
            cursor: pointer;
        }

        .preview-buttons {
            text-align: center;
            margin: 1rem 0;
        }

        .preview-btn-success {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 6px;
            font-size: 0.8rem;
            cursor: pointer;
            color: white;
        }

        .preview-footer {
            padding: 0.75rem 1rem;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }

        .preview-footer p {
            margin: 0;
            font-size: 0.7rem;
        }

        /* أزرار الحفظ */
        .theme-save-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            padding: 2rem;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
        }

        .btn-save-theme, .btn-cancel-theme, .btn-apply-theme {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-save-theme {
            background: linear-gradient(135deg, #56ab2f, #a8e6cf);
            color: white;
        }

        .btn-cancel-theme {
            background: linear-gradient(135deg, #95a5a6, #bdc3c7);
            color: white;
        }

        .btn-apply-theme {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-save-theme:hover, .btn-cancel-theme:hover, .btn-apply-theme:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .theme-stats {
                grid-template-columns: 1fr;
                gap: 1rem;
                padding: 0.5rem;
            }

            .theme-actions-modern {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .color-controls {
                grid-template-columns: 1fr;
            }

            .preview-cards {
                grid-template-columns: 1fr;
            }

            .theme-save-actions {
                flex-direction: column;
                align-items: center;
            }

            .btn-save-theme, .btn-cancel-theme, .btn-apply-theme {
                width: 100%;
                justify-content: center;
            }
        }

        /* أنماط رفع المنتجات من Excel */
        .excel-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
            padding: 1rem;
            background: linear-gradient(135deg, #f8f9ff 0%, #fff8f8 100%);
            border-radius: 20px;
            position: relative;
            overflow: hidden;
        }

        .excel-stats::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2, #56ab2f, #17a2b8);
            border-radius: 20px 20px 0 0;
        }

        /* تعليمات الرفع */
        .upload-instructions {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(102, 126, 234, 0.1);
        }

        .instructions-header {
            margin-bottom: 2rem;
        }

        .instructions-header h3 {
            color: #2c3e50;
            font-size: 1.3rem;
            font-weight: 700;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .instructions-header i {
            color: #667eea;
            font-size: 1.4rem;
        }

        .instruction-step {
            display: flex;
            gap: 1.5rem;
            margin-bottom: 2rem;
            align-items: flex-start;
        }

        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 1.1rem;
            flex-shrink: 0;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .step-content h4 {
            margin: 0 0 0.75rem 0;
            color: #2c3e50;
            font-weight: 600;
        }

        .step-content p {
            margin: 0 0 1rem 0;
            color: #6c757d;
            line-height: 1.6;
        }

        .step-content ul {
            margin: 0;
            padding-right: 1.5rem;
            color: #6c757d;
        }

        .step-content li {
            margin-bottom: 0.5rem;
            line-height: 1.5;
        }

        .columns-list {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .column-item {
            background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
            color: #2c3e50;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 600;
            border: 1px solid rgba(102, 126, 234, 0.2);
        }

        .download-template {
            text-align: center;
            margin-top: 2rem;
        }

        .btn-download-template {
            background: linear-gradient(135deg, #56ab2f, #a8e6cf);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 15px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.75rem;
            box-shadow: 0 4px 15px rgba(86, 171, 47, 0.3);
        }

        .btn-download-template:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(86, 171, 47, 0.4);
        }

        /* منطقة رفع الملفات */
        .excel-upload-area {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(102, 126, 234, 0.1);
        }

        .upload-header {
            margin-bottom: 2rem;
        }

        .upload-header h3 {
            color: #2c3e50;
            font-size: 1.3rem;
            font-weight: 700;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .upload-header i {
            color: #667eea;
            font-size: 1.4rem;
        }

        .upload-zone {
            border: 3px dashed #e9ecef;
            border-radius: 20px;
            padding: 3rem 2rem;
            text-align: center;
            transition: all 0.3s ease;
            background: linear-gradient(135deg, #fafbff 0%, #f8f9fa 100%);
            position: relative;
            overflow: hidden;
        }

        .upload-zone::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(102, 126, 234, 0.05) 50%, transparent 70%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .upload-zone:hover::before {
            opacity: 1;
        }

        .upload-zone.drag-over {
            border-color: #667eea;
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            transform: scale(1.02);
        }

        .upload-zone.file-selected {
            border-color: #56ab2f;
            background: linear-gradient(135deg, #f0fff4 0%, #e8f5e8 100%);
        }

        .upload-icon {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
        }

        .upload-zone:hover .upload-icon {
            transform: scale(1.1);
            color: #764ba2;
        }

        .upload-text h4 {
            margin: 0 0 0.75rem 0;
            color: #2c3e50;
            font-weight: 600;
            font-size: 1.2rem;
        }

        .upload-text p {
            margin: 0 0 1.5rem 0;
            color: #6c757d;
            font-size: 0.95rem;
        }

        .btn-select-file {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 15px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.75rem;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-select-file:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        /* شريط التقدم */
        .upload-progress {
            margin: 2rem 0;
            text-align: center;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 1rem;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
            border-radius: 4px;
        }

        .progress-text {
            color: #6c757d;
            font-weight: 600;
        }

        /* أزرار الإجراءات */
        .upload-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 2rem;
        }

        .btn-upload-excel, .btn-clear-file {
            padding: 1rem 2rem;
            border: none;
            border-radius: 15px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.75rem;
        }

        .btn-upload-excel {
            background: linear-gradient(135deg, #56ab2f, #a8e6cf);
            color: white;
            box-shadow: 0 4px 15px rgba(86, 171, 47, 0.3);
        }

        .btn-upload-excel:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(86, 171, 47, 0.4);
        }

        .btn-upload-excel:disabled {
            background: #95a5a6;
            cursor: not-allowed;
            box-shadow: none;
        }

        .btn-clear-file {
            background: linear-gradient(135deg, #95a5a6, #bdc3c7);
            color: white;
            box-shadow: 0 4px 15px rgba(149, 165, 166, 0.3);
        }

        .btn-clear-file:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(149, 165, 166, 0.4);
        }

        /* معاينة البيانات */
        .excel-preview {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
            border-radius: 20px;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(102, 126, 234, 0.1);
            overflow: hidden;
        }

        .preview-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1.5rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .preview-header h3 {
            margin: 0;
            font-size: 1.2rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .preview-stats {
            display: flex;
            gap: 1rem;
        }

        .preview-stat {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .preview-stat.success {
            background: rgba(86, 171, 47, 0.2);
            color: #ffffff;
        }

        .preview-stat.error {
            background: rgba(220, 53, 69, 0.2);
            color: #ffffff;
        }

        .preview-table-container {
            max-height: 400px;
            overflow: auto;
            padding: 1rem;
        }

        .preview-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .preview-table th {
            background: #f8f9fa;
            padding: 1rem;
            text-align: right;
            font-weight: 600;
            color: #495057;
            border-bottom: 2px solid #dee2e6;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .preview-table td {
            padding: 1rem;
            border-bottom: 1px solid #dee2e6;
            vertical-align: middle;
        }

        .preview-row-valid {
            background: rgba(86, 171, 47, 0.05);
        }

        .preview-row-invalid {
            background: rgba(220, 53, 69, 0.05);
        }

        .preview-image {
            width: 40px;
            height: 40px;
            object-fit: cover;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }

        .no-image {
            display: inline-block;
            width: 40px;
            height: 40px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            text-align: center;
            line-height: 38px;
            font-size: 0.7rem;
            color: #6c757d;
        }

        .product-name strong {
            color: #2c3e50;
            font-weight: 600;
        }

        .product-name small {
            color: #6c757d;
            font-style: italic;
        }

        .price-info strong {
            color: #2c3e50;
            font-weight: 600;
        }

        .old-price {
            text-decoration: line-through;
            color: #dc3545;
        }

        .wholesale-price {
            color: #28a745;
            font-weight: 600;
        }

        .text-success {
            color: #28a745 !important;
        }

        .text-danger {
            color: #dc3545 !important;
        }

        /* أزرار المعاينة */
        .preview-actions {
            padding: 1.5rem 2rem;
            background: #f8f9fa;
            display: flex;
            gap: 1rem;
            justify-content: center;
        }

        .btn-confirm-import, .btn-cancel-import {
            padding: 1rem 2rem;
            border: none;
            border-radius: 15px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.75rem;
        }

        .btn-confirm-import {
            background: linear-gradient(135deg, #56ab2f, #a8e6cf);
            color: white;
            box-shadow: 0 4px 15px rgba(86, 171, 47, 0.3);
        }

        .btn-confirm-import:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(86, 171, 47, 0.4);
        }

        .btn-confirm-import:disabled {
            background: #95a5a6;
            cursor: not-allowed;
            box-shadow: none;
        }

        .btn-cancel-import {
            background: linear-gradient(135deg, #95a5a6, #bdc3c7);
            color: white;
            box-shadow: 0 4px 15px rgba(149, 165, 166, 0.3);
        }

        .btn-cancel-import:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(149, 165, 166, 0.4);
        }

        /* نتائج الاستيراد */
        .import-results {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
            border-radius: 20px;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(102, 126, 234, 0.1);
            overflow: hidden;
        }

        .results-header {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
            color: white;
            padding: 1.5rem 2rem;
        }

        .results-header h3 {
            margin: 0;
            font-size: 1.2rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .results-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            padding: 2rem;
        }

        .result-card {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            border: 2px solid;
            transition: all 0.3s ease;
        }

        .result-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .result-card.success {
            border-color: #28a745;
        }

        .result-card.error {
            border-color: #dc3545;
        }

        .result-card.info {
            border-color: #17a2b8;
        }

        .result-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            font-size: 1.5rem;
            color: white;
        }

        .result-card.success .result-icon {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .result-card.error .result-icon {
            background: linear-gradient(135deg, #dc3545, #e74c3c);
        }

        .result-card.info .result-icon {
            background: linear-gradient(135deg, #17a2b8, #20c997);
        }

        .result-number {
            font-size: 2rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
            color: #2c3e50;
        }

        .result-label {
            color: #6c757d;
            font-weight: 600;
        }

        .results-details {
            padding: 0 2rem 2rem;
        }

        .results-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .result-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            border-radius: 12px;
            font-weight: 600;
        }

        .result-item.success {
            background: rgba(40, 167, 69, 0.1);
            color: #155724;
            border-left: 4px solid #28a745;
        }

        .result-item.error {
            background: rgba(220, 53, 69, 0.1);
            color: #721c24;
            border-left: 4px solid #dc3545;
        }

        .result-item.warning {
            background: rgba(255, 193, 7, 0.1);
            color: #856404;
            border-left: 4px solid #ffc107;
        }

        .result-item i {
            font-size: 1.2rem;
        }

        .results-actions {
            padding: 1.5rem 2rem;
            background: #f8f9fa;
            display: flex;
            gap: 1rem;
            justify-content: center;
        }

        .btn-new-upload, .btn-view-products {
            padding: 1rem 2rem;
            border: none;
            border-radius: 15px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 0.75rem;
        }

        .btn-new-upload {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-new-upload:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn-view-products {
            background: linear-gradient(135deg, #17a2b8, #20c997);
            color: white;
            box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
        }

        .btn-view-products:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(23, 162, 184, 0.4);
        }

        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .excel-stats {
                grid-template-columns: 1fr;
                gap: 1rem;
                padding: 0.5rem;
            }

            .upload-instructions {
                padding: 1rem;
            }

            .instruction-step {
                flex-direction: column;
                gap: 1rem;
            }

            .columns-list {
                flex-direction: column;
                gap: 0.25rem;
            }

            .excel-upload-area {
                padding: 1rem;
            }

            .upload-zone {
                padding: 2rem 1rem;
            }

            .upload-actions {
                flex-direction: column;
                align-items: center;
            }

            .btn-upload-excel, .btn-clear-file {
                width: 100%;
                justify-content: center;
            }

            .preview-header {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }

            .preview-stats {
                justify-content: center;
            }

            .preview-table-container {
                overflow-x: auto;
            }

            .preview-table {
                min-width: 600px;
            }

            .preview-actions {
                flex-direction: column;
                align-items: center;
            }

            .btn-confirm-import, .btn-cancel-import {
                width: 100%;
                justify-content: center;
            }

            .results-summary {
                grid-template-columns: 1fr;
                gap: 1rem;
                padding: 1rem;
            }

            .results-actions {
                flex-direction: column;
                align-items: center;
            }

            .btn-new-upload, .btn-view-products {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</body>
</html>
