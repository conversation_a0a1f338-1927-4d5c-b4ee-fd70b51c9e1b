// Cashier System JavaScript

// Global Variables
let cart = [];
let currentSaleType = 'retail';
let currentPaymentMethod = 'cash';
let currentDiscount = 0;
let products = [];
let categories = [];
let sales = [];
let heldSales = [];
let currentCashier = null;

// تهيئة النظام
document.addEventListener('DOMContentLoaded', function() {
    initializeCashierSystem();
});

function initializeCashierSystem() {
    // الحصول على بيانات الكاشير الحالي
    const cashierData = sessionStorage.getItem('cashierUser');
    if (cashierData) {
        currentCashier = JSON.parse(cashierData);
        console.log('✅ تم تحميل بيانات الكاشير:', currentCashier.name);
    }

    // تحميل البيانات الأساسية
    loadProducts();
    loadCategories();
    updateDateTime();

    // تحديث الوقت كل ثانية
    setInterval(updateDateTime, 1000);
}

// تحديث التاريخ والوقت
function updateDateTime() {
    const now = new Date();
    const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: true
    };

    const dateTimeString = now.toLocaleDateString('ar-SA', options);
    const dateTimeElement = document.getElementById('currentDateTime');
    if (dateTimeElement) {
        dateTimeElement.textContent = dateTimeString;
    }
}

// التحقق من صلاحيات الكاشير للعمليات الحساسة
function checkCashierPermission(action) {
    if (!currentCashier) {
        showNotification('خطأ: لم يتم العثور على بيانات الكاشير', 'error');
        return false;
    }

    // المديرون لديهم صلاحية كاملة
    if (currentCashier.role === 'admin') {
        return true;
    }

    // الكاشير لديه صلاحيات محدودة
    const allowedActions = ['sale', 'print', 'calculator', 'search'];

    if (!allowedActions.includes(action)) {
        showNotification('عذراً، ليس لديك صلاحية لهذا الإجراء', 'warning');
        return false;
    }

    return true;
}

// حفظ عملية البيع مع بيانات الكاشير
function saveSaleRecord(saleData) {
    if (!currentCashier) {
        console.warn('⚠️ لا توجد بيانات كاشير لحفظ العملية');
        return;
    }

    const sale = {
        ...saleData,
        cashierId: currentCashier.id,
        cashierName: currentCashier.name,
        cashierUsername: currentCashier.username,
        cashierRole: currentCashier.role,
        timestamp: new Date().toISOString(),
        saleId: 'SALE_' + Date.now(),
        workShift: getCurrentShift()
    };

    // حفظ في localStorage
    const sales = JSON.parse(localStorage.getItem('sales')) || [];
    sales.push(sale);
    localStorage.setItem('sales', JSON.stringify(sales));

    console.log('💾 تم حفظ عملية البيع بواسطة:', currentCashier.name, sale);
    return sale;
}

// تحديد الوردية الحالية
function getCurrentShift() {
    const hour = new Date().getHours();
    if (hour >= 6 && hour < 14) {
        return 'صباحية';
    } else if (hour >= 14 && hour < 22) {
        return 'مسائية';
    } else {
        return 'ليلية';
    }
}

// الحصول على معلومات الكاشير الحالي
function getCurrentCashierInfo() {
    if (!currentCashier) {
        return 'غير محدد';
    }

    const roleText = currentCashier.role === 'admin' ? 'المدير' :
                    currentCashier.role === 'supervisor' ? 'المشرف' : 'الكاشير';

    return `${roleText}: ${currentCashier.name}`;
}
let currentEmployee = null;

// Initialize the system
document.addEventListener('DOMContentLoaded', function() {
    initializeCashier();
    updateDateTime();
    setInterval(updateDateTime, 1000);
});

function initializeCashier() {
    console.log('🛒 Initializing Cashier System...');

    // Load data from localStorage
    loadProducts();
    loadCategories();
    loadSales();

    // Initialize UI
    displayCategories();
    displayProducts();
    updateCartDisplay();
    updateTotals();
    updateEmployeeInfo();

    console.log('✅ Cashier System initialized successfully');
}

// تم حذف وظيفة التحقق من تسجيل الدخول

function updateEmployeeInfo() {
    if (currentEmployee) {
        // Update cashier name in header
        const cashierInfo = document.querySelector('.cashier-info span');
        if (cashierInfo) {
            cashierInfo.textContent = `الكاشير: ${currentEmployee.name}`;
        }

        // Update any other employee-specific UI elements
        console.log(`🔄 Updated UI for employee: ${currentEmployee.name}`);
    }
}

function updateDateTime() {
    const now = new Date();
    const options = {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
    };
    
    document.getElementById('currentDateTime').textContent = 
        now.toLocaleDateString('ar-SA', options);
}

// Data Loading Functions
function loadProducts() {
    try {
        const adminProducts = JSON.parse(localStorage.getItem('adminProducts')) || [];
        products = adminProducts.filter(product => product.stock > 0);
        console.log(`📦 Loaded ${products.length} products`);
    } catch (error) {
        console.error('Error loading products:', error);
        products = [];
    }
}

function loadCategories() {
    try {
        categories = JSON.parse(localStorage.getItem('categories')) || [];
        console.log(`📂 Loaded ${categories.length} categories`);
    } catch (error) {
        console.error('Error loading categories:', error);
        categories = [];
    }
}

function loadSales() {
    try {
        sales = JSON.parse(localStorage.getItem('cashierSales')) || [];
        heldSales = JSON.parse(localStorage.getItem('heldSales')) || [];
        console.log(`📊 Loaded ${sales.length} sales, ${heldSales.length} held sales`);
    } catch (error) {
        console.error('Error loading sales:', error);
        sales = [];
        heldSales = [];
    }
}

// Display Functions
function displayCategories() {
    const categoriesContainer = document.getElementById('categoriesTabs');
    
    let categoriesHTML = `
        <div class="category-tab active" onclick="filterByCategory('all')">
            <i class="fas fa-th"></i>
            الكل
        </div>
    `;
    
    categories.forEach(category => {
        categoriesHTML += `
            <div class="category-tab" onclick="filterByCategory('${category.id}')">
                <i class="fas fa-tag"></i>
                ${category.nameAr}
            </div>
        `;
    });
    
    categoriesContainer.innerHTML = categoriesHTML;
}

function displayProducts(filteredProducts = null) {
    const productsContainer = document.getElementById('productsGrid');
    const productsToShow = filteredProducts || products;
    
    if (productsToShow.length === 0) {
        productsContainer.innerHTML = `
            <div class="no-products">
                <i class="fas fa-box-open"></i>
                <p>لا توجد منتجات متاحة</p>
            </div>
        `;
        return;
    }
    
    let productsHTML = '';
    
    productsToShow.forEach(product => {
        const isOutOfStock = product.stock <= 0;
        const stockClass = isOutOfStock ? 'out-of-stock' : '';
        const stockBadgeClass = product.stock > product.minStock ? 'in-stock' : '';
        
        // Determine price based on sale type
        const price = currentSaleType === 'wholesale' && product.wholesalePrice 
            ? product.wholesalePrice 
            : product.price;
        
        productsHTML += `
            <div class="product-card ${stockClass}" onclick="addToCart('${product.id}')">
                <div class="product-image">
                    ${product.image && product.image.startsWith('data:') 
                        ? `<img src="${product.image}" alt="${product.name}">` 
                        : `<i class="fas fa-box"></i>`
                    }
                </div>
                <div class="product-name">${product.name}</div>
                <div class="product-price">${price} دينار</div>
                <div class="product-stock ${stockBadgeClass}">${product.stock}</div>
            </div>
        `;
    });
    
    productsContainer.innerHTML = productsHTML;
}

// Cart Functions
function addToCart(productId) {
    const product = products.find(p => p.id === productId);
    if (!product || product.stock <= 0) {
        showNotification('المنتج غير متوفر', 'error');
        return;
    }
    
    const existingItem = cart.find(item => item.id === productId);
    
    if (existingItem) {
        if (existingItem.quantity < product.stock) {
            existingItem.quantity++;
            existingItem.total = existingItem.quantity * existingItem.price;
        } else {
            showNotification('الكمية المطلوبة غير متوفرة', 'warning');
            return;
        }
    } else {
        const price = currentSaleType === 'wholesale' && product.wholesalePrice 
            ? product.wholesalePrice 
            : product.price;
            
        cart.push({
            id: product.id,
            name: product.name,
            price: price,
            quantity: 1,
            total: price,
            image: product.image,
            maxStock: product.stock
        });
    }
    
    updateCartDisplay();
    updateTotals();
    showNotification(`تم إضافة ${product.name} للسلة`, 'success');
}

function removeFromCart(productId) {
    cart = cart.filter(item => item.id !== productId);
    updateCartDisplay();
    updateTotals();
    showNotification('تم حذف المنتج من السلة', 'info');
}

function updateQuantity(productId, newQuantity) {
    const item = cart.find(item => item.id === productId);
    if (!item) return;
    
    if (newQuantity <= 0) {
        removeFromCart(productId);
        return;
    }
    
    if (newQuantity > item.maxStock) {
        showNotification('الكمية المطلوبة غير متوفرة', 'warning');
        return;
    }
    
    item.quantity = newQuantity;
    item.total = item.quantity * item.price;
    
    updateCartDisplay();
    updateTotals();
}

function updateCartDisplay() {
    const cartContainer = document.getElementById('cartItems');
    
    if (cart.length === 0) {
        cartContainer.innerHTML = `
            <div class="empty-cart">
                <i class="fas fa-shopping-cart"></i>
                <p>السلة فارغة</p>
                <small>أضف منتجات للبدء في البيع</small>
            </div>
        `;
        return;
    }
    
    let cartHTML = '';
    
    cart.forEach(item => {
        cartHTML += `
            <div class="cart-item">
                <div class="cart-item-image">
                    ${item.image && item.image.startsWith('data:') 
                        ? `<img src="${item.image}" alt="${item.name}">` 
                        : `<i class="fas fa-box"></i>`
                    }
                </div>
                <div class="cart-item-info">
                    <div class="cart-item-name">${item.name}</div>
                    <div class="cart-item-price">${item.price} دينار × ${item.quantity}</div>
                </div>
                <div class="cart-item-controls">
                    <button class="qty-btn" onclick="updateQuantity('${item.id}', ${item.quantity - 1})">
                        <i class="fas fa-minus"></i>
                    </button>
                    <input type="number" class="qty-input" value="${item.quantity}" 
                           onchange="updateQuantity('${item.id}', parseInt(this.value))" min="1" max="${item.maxStock}">
                    <button class="qty-btn" onclick="updateQuantity('${item.id}', ${item.quantity + 1})">
                        <i class="fas fa-plus"></i>
                    </button>
                    <button class="remove-btn" onclick="removeFromCart('${item.id}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
    });
    
    cartContainer.innerHTML = cartHTML;
}

function updateTotals() {
    const subtotal = cart.reduce((sum, item) => sum + item.total, 0);
    const discountAmount = currentDiscount;
    const taxableAmount = subtotal - discountAmount;
    const tax = taxableAmount * 0.15; // 15% VAT
    const total = taxableAmount + tax;
    
    document.getElementById('subtotal').textContent = `${subtotal.toFixed(0)} دينار`;
    document.getElementById('discount').textContent = `${discountAmount.toFixed(0)} دينار`;
    document.getElementById('tax').textContent = `${tax.toFixed(0)} دينار`;
    document.getElementById('total').textContent = `${total.toFixed(0)} دينار`;
    
    // Enable/disable complete sale button
    const completeSaleBtn = document.getElementById('completeSaleBtn');
    completeSaleBtn.disabled = cart.length === 0;
}

function clearCart() {
    if (cart.length === 0) return;
    
    if (confirm('هل أنت متأكد من مسح السلة؟')) {
        cart = [];
        currentDiscount = 0;
        document.getElementById('discountAmount').value = '';
        document.getElementById('discountPercent').value = '';
        updateCartDisplay();
        updateTotals();
        showNotification('تم مسح السلة', 'info');
    }
}

// Search and Filter Functions
function searchProducts() {
    const searchTerm = document.getElementById('productSearch').value.toLowerCase();
    
    if (searchTerm === '') {
        displayProducts();
        return;
    }
    
    const filteredProducts = products.filter(product => 
        product.name.toLowerCase().includes(searchTerm) ||
        product.category.toLowerCase().includes(searchTerm)
    );
    
    displayProducts(filteredProducts);
}

function filterByCategory(categoryId) {
    // Update active tab
    document.querySelectorAll('.category-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    event.target.classList.add('active');
    
    if (categoryId === 'all') {
        displayProducts();
        return;
    }
    
    const category = categories.find(c => c.id === categoryId);
    if (!category) return;
    
    const filteredProducts = products.filter(product => 
        product.category === category.nameAr
    );
    
    displayProducts(filteredProducts);
}

function filterProducts(type) {
    // Update active filter button
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
    
    switch(type) {
        case 'all':
            displayProducts();
            break;
        case 'category':
            // Show category filter
            break;
        case 'barcode':
            // Show barcode scanner
            break;
    }
}

// Sale Type Functions
function updateSaleType() {
    const saleType = document.querySelector('input[name="saleType"]:checked').value;
    currentSaleType = saleType;
    
    // Update cart prices based on sale type
    cart.forEach(item => {
        const product = products.find(p => p.id === item.id);
        if (product) {
            const newPrice = saleType === 'wholesale' && product.wholesalePrice 
                ? product.wholesalePrice 
                : product.price;
            item.price = newPrice;
            item.total = item.quantity * newPrice;
        }
    });
    
    updateCartDisplay();
    updateTotals();
    displayProducts(); // Refresh product display with new prices
    
    showNotification(`تم التبديل إلى البيع ${saleType === 'retail' ? 'بالمفرد' : 'بالجملة'}`, 'info');
}

// Payment Functions
function selectPaymentMethod(method) {
    currentPaymentMethod = method;
    
    // Update active payment button
    document.querySelectorAll('.payment-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-method="${method}"]`).classList.add('active');
    
    // Show/hide payment amount section based on method
    const paymentAmountSection = document.getElementById('paymentAmount');
    if (method === 'cash') {
        paymentAmountSection.style.display = 'block';
    } else {
        paymentAmountSection.style.display = 'none';
    }
}

function calculateChange() {
    const total = parseFloat(document.getElementById('total').textContent.replace(' ريال', ''));
    const paidAmount = parseFloat(document.getElementById('paidAmount').value) || 0;
    const change = paidAmount - total;
    
    document.getElementById('changeAmount').textContent = `${change.toFixed(0)} دينار`;
    
    if (change < 0) {
        document.getElementById('changeAmount').style.color = '#e74c3c';
    } else {
        document.getElementById('changeAmount').style.color = '#27ae60';
    }
}

// Discount Functions
function applyDiscount() {
    const discountAmount = parseFloat(document.getElementById('discountAmount').value) || 0;
    currentDiscount = discountAmount;
    document.getElementById('discountPercent').value = '';
    updateTotals();
}

function applyDiscountPercent() {
    const discountPercent = parseFloat(document.getElementById('discountPercent').value) || 0;
    const subtotal = cart.reduce((sum, item) => sum + item.total, 0);
    currentDiscount = (subtotal * discountPercent) / 100;
    document.getElementById('discountAmount').value = currentDiscount.toFixed(2);
    updateTotals();
}

// Notification System
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${getNotificationIcon(type)}"></i>
        <span>${message}</span>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

function getNotificationIcon(type) {
    switch(type) {
        case 'success': return 'check-circle';
        case 'error': return 'exclamation-circle';
        case 'warning': return 'exclamation-triangle';
        default: return 'info-circle';
    }
}

// Utility Functions
function holdSale() {
    if (cart.length === 0) {
        showNotification('السلة فارغة', 'warning');
        return;
    }
    
    const heldSale = {
        id: 'held_' + Date.now(),
        cart: [...cart],
        discount: currentDiscount,
        saleType: currentSaleType,
        timestamp: new Date().toISOString(),
        cashier: currentEmployee ? currentEmployee.name : 'غير محدد',
        cashierId: currentEmployee ? currentEmployee.id : null
    };
    
    heldSales.push(heldSale);
    localStorage.setItem('heldSales', JSON.stringify(heldSales));
    
    clearCart();
    showNotification('تم تعليق البيع', 'success');
}

function openCashDrawer() {
    showNotification('تم فتح درج النقود', 'info');
    // Here you would integrate with actual cash drawer hardware
}

// متغيرات الآلة الحاسبة
let calculatorDisplay = '';
let calculatorMemory = 0;
let calculatorLastOperation = '';
let calculatorOperand = 0;
let calculatorWaitingForOperand = false;

function showCalculator() {
    const modalHTML = `
        <div class="modal-overlay" onclick="closeCalculator()">
            <div class="calculator-modal" onclick="event.stopPropagation()">
                <div class="calculator-header">
                    <h3>
                        <i class="fas fa-calculator"></i>
                        الآلة الحاسبة
                    </h3>
                    <button class="close-btn" onclick="closeCalculator()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <div class="calculator-body">
                    <!-- شاشة العرض -->
                    <div class="calculator-display">
                        <div class="display-memory" id="calculatorMemory">M: ${calculatorMemory}</div>
                        <div class="display-main" id="calculatorDisplay">0</div>
                    </div>

                    <!-- أزرار الآلة الحاسبة -->
                    <div class="calculator-buttons">
                        <!-- الصف الأول -->
                        <button class="calc-btn memory-btn" onclick="calculatorMemoryClear()">MC</button>
                        <button class="calc-btn memory-btn" onclick="calculatorMemoryRecall()">MR</button>
                        <button class="calc-btn memory-btn" onclick="calculatorMemoryAdd()">M+</button>
                        <button class="calc-btn memory-btn" onclick="calculatorMemorySubtract()">M-</button>

                        <!-- الصف الثاني -->
                        <button class="calc-btn clear-btn" onclick="calculatorClear()">C</button>
                        <button class="calc-btn clear-btn" onclick="calculatorClearEntry()">CE</button>
                        <button class="calc-btn operation-btn" onclick="calculatorBackspace()">⌫</button>
                        <button class="calc-btn operation-btn" onclick="calculatorOperation('/')" title="قسمة">÷</button>

                        <!-- الصف الثالث -->
                        <button class="calc-btn number-btn" onclick="calculatorInputNumber('7')">7</button>
                        <button class="calc-btn number-btn" onclick="calculatorInputNumber('8')">8</button>
                        <button class="calc-btn number-btn" onclick="calculatorInputNumber('9')">9</button>
                        <button class="calc-btn operation-btn" onclick="calculatorOperation('*')" title="ضرب">×</button>

                        <!-- الصف الرابع -->
                        <button class="calc-btn number-btn" onclick="calculatorInputNumber('4')">4</button>
                        <button class="calc-btn number-btn" onclick="calculatorInputNumber('5')">5</button>
                        <button class="calc-btn number-btn" onclick="calculatorInputNumber('6')">6</button>
                        <button class="calc-btn operation-btn" onclick="calculatorOperation('-')" title="طرح">-</button>

                        <!-- الصف الخامس -->
                        <button class="calc-btn number-btn" onclick="calculatorInputNumber('1')">1</button>
                        <button class="calc-btn number-btn" onclick="calculatorInputNumber('2')">2</button>
                        <button class="calc-btn number-btn" onclick="calculatorInputNumber('3')">3</button>
                        <button class="calc-btn operation-btn" onclick="calculatorOperation('+')" title="جمع">+</button>

                        <!-- الصف السادس -->
                        <button class="calc-btn number-btn zero-btn" onclick="calculatorInputNumber('0')">0</button>
                        <button class="calc-btn number-btn" onclick="calculatorInputDecimal()">.</button>
                        <button class="calc-btn equals-btn" onclick="calculatorEquals()" title="يساوي">=</button>

                        <!-- وظائف إضافية -->
                        <button class="calc-btn function-btn" onclick="calculatorSquareRoot()" title="جذر تربيعي">√</button>
                        <button class="calc-btn function-btn" onclick="calculatorSquare()" title="تربيع">x²</button>
                        <button class="calc-btn function-btn" onclick="calculatorPercent()" title="نسبة مئوية">%</button>
                        <button class="calc-btn function-btn" onclick="calculatorInverse()" title="مقلوب">1/x</button>
                    </div>

                    <!-- أزرار سريعة للكاشير -->
                    <div class="calculator-quick-actions">
                        <button class="quick-action-btn" onclick="copyToTotal()" title="نسخ إلى المجموع">
                            <i class="fas fa-copy"></i>
                            نسخ للمجموع
                        </button>
                        <button class="quick-action-btn" onclick="copyToDiscount()" title="نسخ إلى الخصم">
                            <i class="fas fa-percent"></i>
                            نسخ للخصم
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <style>
            .calculator-modal {
                background: linear-gradient(145deg, #ffffff, #f8f9fa);
                border-radius: 20px;
                box-shadow: 0 25px 80px rgba(0,0,0,0.3);
                width: 350px;
                max-width: 90vw;
                overflow: hidden;
                animation: calculatorSlideIn 0.3s ease-out;
            }

            @keyframes calculatorSlideIn {
                from { opacity: 0; transform: scale(0.8) translateY(-50px); }
                to { opacity: 1; transform: scale(1) translateY(0); }
            }

            .calculator-header {
                background: linear-gradient(135deg, #667eea, #764ba2);
                color: white;
                padding: 1rem 1.5rem;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .calculator-header h3 {
                margin: 0;
                font-size: 1.2rem;
                font-weight: 600;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .calculator-body {
                padding: 1.5rem;
            }

            .calculator-display {
                background: linear-gradient(145deg, #2c3e50, #34495e);
                border-radius: 10px;
                padding: 1rem;
                margin-bottom: 1rem;
                text-align: right;
                direction: ltr;
            }

            .display-memory {
                color: #95a5a6;
                font-size: 0.8rem;
                margin-bottom: 0.5rem;
                min-height: 1rem;
            }

            .display-main {
                color: white;
                font-size: 2rem;
                font-weight: 600;
                font-family: 'Courier New', monospace;
                min-height: 2.5rem;
                overflow: hidden;
                word-break: break-all;
            }

            .calculator-buttons {
                display: grid;
                grid-template-columns: repeat(4, 1fr);
                gap: 0.5rem;
                margin-bottom: 1rem;
            }

            .calc-btn {
                height: 50px;
                border: none;
                border-radius: 8px;
                font-size: 1.1rem;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.2s ease;
                font-family: 'Cairo', sans-serif;
            }

            .calc-btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            }

            .calc-btn:active {
                transform: translateY(0);
            }

            .number-btn {
                background: linear-gradient(145deg, #ecf0f1, #bdc3c7);
                color: #2c3e50;
            }

            .number-btn:hover {
                background: linear-gradient(145deg, #d5dbdb, #a6acaf);
            }

            .operation-btn {
                background: linear-gradient(145deg, #3498db, #2980b9);
                color: white;
            }

            .operation-btn:hover {
                background: linear-gradient(145deg, #2980b9, #21618c);
            }

            .equals-btn {
                background: linear-gradient(145deg, #e74c3c, #c0392b);
                color: white;
                grid-column: span 2;
            }

            .equals-btn:hover {
                background: linear-gradient(145deg, #c0392b, #a93226);
            }

            .clear-btn {
                background: linear-gradient(145deg, #e67e22, #d35400);
                color: white;
            }

            .clear-btn:hover {
                background: linear-gradient(145deg, #d35400, #ba4a00);
            }

            .memory-btn {
                background: linear-gradient(145deg, #9b59b6, #8e44ad);
                color: white;
                font-size: 0.9rem;
            }

            .memory-btn:hover {
                background: linear-gradient(145deg, #8e44ad, #7d3c98);
            }

            .function-btn {
                background: linear-gradient(145deg, #27ae60, #229954);
                color: white;
                font-size: 0.9rem;
            }

            .function-btn:hover {
                background: linear-gradient(145deg, #229954, #1e8449);
            }

            .zero-btn {
                grid-column: span 2;
            }

            .calculator-quick-actions {
                display: flex;
                gap: 0.5rem;
                margin-top: 1rem;
            }

            .quick-action-btn {
                flex: 1;
                padding: 0.8rem;
                background: linear-gradient(145deg, #f39c12, #e67e22);
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 0.9rem;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.2s ease;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 0.3rem;
            }

            .quick-action-btn:hover {
                background: linear-gradient(145deg, #e67e22, #d35400);
                transform: translateY(-2px);
            }
        </style>
    `;

    document.getElementById('modalContainer').innerHTML = modalHTML;
    calculatorUpdateDisplay();
    // إضافة دعم لوحة المفاتيح
    addCalculatorKeyboardSupport();
}

// وظائف الآلة الحاسبة
function calculatorUpdateDisplay() {
    const display = document.getElementById('calculatorDisplay');
    const memoryDisplay = document.getElementById('calculatorMemory');

    if (display) {
        // تنسيق الأرقام الكبيرة
        let displayValue = calculatorDisplay || '0';

        // إذا كان الرقم كبيراً جداً، استخدم التدوين العلمي
        if (displayValue.length > 12) {
            const num = parseFloat(displayValue);
            displayValue = num.toExponential(6);
        }
        // إذا كان الرقم عشرياً، قم بتقريبه
        else if (displayValue.includes('.') && displayValue.length > 12) {
            const num = parseFloat(displayValue);
            displayValue = num.toFixed(8).replace(/\.?0+$/, '');
        }

        display.textContent = displayValue;
    }

    if (memoryDisplay) {
        if (calculatorMemory !== 0) {
            let memoryValue = calculatorMemory.toString();
            if (memoryValue.length > 10) {
                memoryValue = calculatorMemory.toExponential(3);
            }
            memoryDisplay.textContent = `M: ${memoryValue}`;
        } else {
            memoryDisplay.textContent = '';
        }
    }
}

function calculatorInputNumber(number) {
    if (calculatorWaitingForOperand) {
        calculatorDisplay = number;
        calculatorWaitingForOperand = false;
    } else {
        calculatorDisplay = calculatorDisplay === '0' ? number : calculatorDisplay + number;
    }
    calculatorUpdateDisplay();
}

function calculatorInputDecimal() {
    if (calculatorWaitingForOperand) {
        calculatorDisplay = '0.';
        calculatorWaitingForOperand = false;
    } else if (calculatorDisplay.indexOf('.') === -1) {
        calculatorDisplay += '.';
    }
    calculatorUpdateDisplay();
}

function calculatorClear() {
    calculatorDisplay = '0';
    calculatorLastOperation = '';
    calculatorOperand = 0;
    calculatorWaitingForOperand = false;
    calculatorUpdateDisplay();
}

function calculatorClearEntry() {
    calculatorDisplay = '0';
    calculatorUpdateDisplay();
}

function calculatorBackspace() {
    if (calculatorDisplay.length > 1) {
        calculatorDisplay = calculatorDisplay.slice(0, -1);
    } else {
        calculatorDisplay = '0';
    }
    calculatorUpdateDisplay();
}

function calculatorOperation(nextOperation) {
    const inputValue = parseFloat(calculatorDisplay);

    if (calculatorLastOperation && calculatorWaitingForOperand) {
        calculatorLastOperation = nextOperation;
        return;
    }

    if (calculatorOperand === 0) {
        calculatorOperand = inputValue;
    } else if (calculatorLastOperation) {
        const currentValue = calculatorOperand || 0;
        const newValue = calculatorCalculate(currentValue, inputValue, calculatorLastOperation);

        calculatorDisplay = String(newValue);
        calculatorOperand = newValue;
        calculatorUpdateDisplay();
    }

    calculatorWaitingForOperand = true;
    calculatorLastOperation = nextOperation;
}

function calculatorEquals() {
    const inputValue = parseFloat(calculatorDisplay);

    if (calculatorLastOperation && !calculatorWaitingForOperand) {
        const currentValue = calculatorOperand || 0;
        const newValue = calculatorCalculate(currentValue, inputValue, calculatorLastOperation);

        calculatorDisplay = String(newValue);
        calculatorOperand = 0;
        calculatorLastOperation = '';
        calculatorWaitingForOperand = true;
        calculatorUpdateDisplay();
    }
}

function calculatorCalculate(firstOperand, secondOperand, operation) {
    switch (operation) {
        case '+':
            return firstOperand + secondOperand;
        case '-':
            return firstOperand - secondOperand;
        case '*':
            return firstOperand * secondOperand;
        case '/':
            return secondOperand !== 0 ? firstOperand / secondOperand : 0;
        default:
            return secondOperand;
    }
}

// وظائف الذاكرة
function calculatorMemoryClear() {
    calculatorMemory = 0;
    calculatorUpdateDisplay();
}

function calculatorMemoryRecall() {
    calculatorDisplay = String(calculatorMemory);
    calculatorWaitingForOperand = true;
    calculatorUpdateDisplay();
}

function calculatorMemoryAdd() {
    calculatorMemory += parseFloat(calculatorDisplay);
    calculatorWaitingForOperand = true;
    calculatorUpdateDisplay();
}

function calculatorMemorySubtract() {
    calculatorMemory -= parseFloat(calculatorDisplay);
    calculatorWaitingForOperand = true;
    calculatorUpdateDisplay();
}

// وظائف رياضية إضافية
function calculatorSquareRoot() {
    const value = parseFloat(calculatorDisplay);
    if (value >= 0) {
        calculatorDisplay = String(Math.sqrt(value));
        calculatorWaitingForOperand = true;
        calculatorUpdateDisplay();
    } else {
        showNotification('لا يمكن حساب الجذر التربيعي لرقم سالب', 'error');
    }
}

function calculatorSquare() {
    const value = parseFloat(calculatorDisplay);
    calculatorDisplay = String(value * value);
    calculatorWaitingForOperand = true;
    calculatorUpdateDisplay();
}

function calculatorPercent() {
    const value = parseFloat(calculatorDisplay);
    calculatorDisplay = String(value / 100);
    calculatorWaitingForOperand = true;
    calculatorUpdateDisplay();
}

function calculatorInverse() {
    const value = parseFloat(calculatorDisplay);
    if (value !== 0) {
        calculatorDisplay = String(1 / value);
        calculatorWaitingForOperand = true;
        calculatorUpdateDisplay();
    } else {
        showNotification('لا يمكن قسمة 1 على صفر', 'error');
    }
}

// وظائف سريعة للكاشير
function copyToTotal() {
    const value = parseFloat(calculatorDisplay);
    if (!isNaN(value) && value > 0) {
        // البحث عن عنصر المجموع في الواجهة
        const totalElement = document.querySelector('.total-amount') ||
                           document.getElementById('totalAmount') ||
                           document.querySelector('[data-total]');

        if (totalElement) {
            totalElement.textContent = value.toFixed(2) + ' د.ع';
            showNotification(`تم نسخ ${value.toFixed(2)} د.ع إلى المجموع`, 'success');
        } else {
            // إذا لم نجد عنصر المجموع، نحفظ القيمة في متغير عام
            window.calculatedTotal = value;
            showNotification(`تم حفظ القيمة: ${value.toFixed(2)} د.ع`, 'success');
        }
        closeCalculator();
    } else {
        showNotification('يرجى إدخال قيمة صحيحة أكبر من صفر', 'error');
    }
}

function copyToDiscount() {
    const value = parseFloat(calculatorDisplay);
    if (!isNaN(value) && value >= 0 && value <= 100) {
        // البحث عن حقل الخصم في الواجهة
        const discountInput = document.getElementById('discountInput') ||
                            document.querySelector('input[name="discount"]') ||
                            document.querySelector('.discount-input');

        if (discountInput) {
            discountInput.value = value;
            // محاولة تطبيق الخصم إذا كانت الوظيفة موجودة
            if (typeof applyDiscount === 'function') {
                applyDiscount();
            }
            showNotification(`تم تطبيق خصم ${value}%`, 'success');
        } else {
            // إذا لم نجد حقل الخصم، نحفظ القيمة في متغير عام
            window.calculatedDiscount = value;
            showNotification(`تم حفظ خصم: ${value}%`, 'success');
        }
        closeCalculator();
    } else {
        showNotification('قيمة الخصم يجب أن تكون بين 0 و 100', 'error');
    }
}

function closeCalculator() {
    document.getElementById('modalContainer').innerHTML = '';
    // إزالة مستمع لوحة المفاتيح
    document.removeEventListener('keydown', calculatorKeyHandler);
    // إعادة تعيين متغيرات الآلة الحاسبة
    calculatorDisplay = '0';
    calculatorLastOperation = '';
    calculatorOperand = 0;
    calculatorWaitingForOperand = false;
}

// دعم لوحة المفاتيح للآلة الحاسبة
function calculatorKeyHandler(event) {
    const key = event.key;

    // منع السلوك الافتراضي للمفاتيح
    if ('0123456789+-*/.=Enter'.includes(key) || key === 'Backspace' || key === 'Delete' || key === 'Escape') {
        event.preventDefault();
    }

    // التعامل مع الأرقام
    if ('0123456789'.includes(key)) {
        calculatorInputNumber(key);
    }
    // التعامل مع العمليات
    else if (key === '+') {
        calculatorOperation('+');
    }
    else if (key === '-') {
        calculatorOperation('-');
    }
    else if (key === '*') {
        calculatorOperation('*');
    }
    else if (key === '/') {
        calculatorOperation('/');
    }
    // التعامل مع النقطة العشرية
    else if (key === '.') {
        calculatorInputDecimal();
    }
    // التعامل مع يساوي
    else if (key === '=' || key === 'Enter') {
        calculatorEquals();
    }
    // التعامل مع المسح
    else if (key === 'Backspace') {
        calculatorBackspace();
    }
    else if (key === 'Delete') {
        calculatorClearEntry();
    }
    else if (key === 'Escape') {
        calculatorClear();
    }
}

// إضافة مستمع لوحة المفاتيح عند فتح الآلة الحاسبة
function addCalculatorKeyboardSupport() {
    document.addEventListener('keydown', calculatorKeyHandler);
}

function goToMainSite() {
    if (confirm('هل تريد الانتقال إلى الموقع الرئيسي؟')) {
        window.location.href = 'index.html';
    }
}

// Complete Sale Function
function completeSale() {
    if (cart.length === 0) {
        showNotification('السلة فارغة', 'warning');
        return;
    }

    const subtotal = cart.reduce((sum, item) => sum + item.total, 0);
    const discountAmount = currentDiscount;
    const taxableAmount = subtotal - discountAmount;
    const tax = taxableAmount * 0.15;
    const total = taxableAmount + tax;

    // Validate payment for cash method
    if (currentPaymentMethod === 'cash') {
        const paidAmount = parseFloat(document.getElementById('paidAmount').value) || 0;
        if (paidAmount < total) {
            showNotification('المبلغ المدفوع أقل من المطلوب', 'error');
            return;
        }
    }

    // Create sale record
    const sale = {
        id: 'sale_' + Date.now(),
        invoiceNumber: generateInvoiceNumber(),
        items: [...cart],
        subtotal: subtotal,
        discount: discountAmount,
        tax: tax,
        total: total,
        saleType: currentSaleType,
        paymentMethod: currentPaymentMethod,
        customerName: document.getElementById('customerName').value || 'عميل نقدي',
        customerPhone: document.getElementById('customerPhone').value || '',
        cashier: currentEmployee ? currentEmployee.name : 'غير محدد',
        cashierId: currentEmployee ? currentEmployee.id : null,
        cashierUsername: currentEmployee ? currentEmployee.username : null,
        timestamp: new Date().toISOString(),
        date: new Date().toLocaleDateString('ar-SA'),
        time: new Date().toLocaleTimeString('ar-SA')
    };

    if (currentPaymentMethod === 'cash') {
        sale.paidAmount = parseFloat(document.getElementById('paidAmount').value);
        sale.changeAmount = sale.paidAmount - total;
    }

    // Update product stock
    updateProductStock();

    // Save sale
    sales.push(sale);
    localStorage.setItem('cashierSales', JSON.stringify(sales));

    // Show success and print invoice
    showSaleCompleteModal(sale);

    // Clear cart
    clearCart();
    document.getElementById('customerName').value = '';
    document.getElementById('customerPhone').value = '';
    document.getElementById('paidAmount').value = '';
    document.getElementById('changeAmount').textContent = '0.00 ريال';

    showNotification('تم إتمام البيع بنجاح', 'success');
}

function generateInvoiceNumber() {
    const today = new Date();
    const year = today.getFullYear().toString().slice(-2);
    const month = (today.getMonth() + 1).toString().padStart(2, '0');
    const day = today.getDate().toString().padStart(2, '0');
    const sequence = (sales.length + 1).toString().padStart(4, '0');

    return `INV${year}${month}${day}${sequence}`;
}

function updateProductStock() {
    const adminProducts = JSON.parse(localStorage.getItem('adminProducts')) || [];

    cart.forEach(cartItem => {
        const productIndex = adminProducts.findIndex(p => p.id === cartItem.id);
        if (productIndex !== -1) {
            adminProducts[productIndex].stock -= cartItem.quantity;
        }
    });

    localStorage.setItem('adminProducts', JSON.stringify(adminProducts));
    loadProducts(); // Reload products with updated stock
    displayProducts(); // Refresh display
}

function showSaleCompleteModal(sale) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal sale-complete-modal">
            <div class="modal-header">
                <h3><i class="fas fa-check-circle"></i> تم إتمام البيع بنجاح</h3>
            </div>
            <div class="modal-body">
                <div class="sale-summary">
                    <div class="summary-item">
                        <span>رقم الفاتورة:</span>
                        <span class="invoice-number">${sale.invoiceNumber}</span>
                    </div>
                    <div class="summary-item">
                        <span>المجموع الكلي:</span>
                        <span class="total-amount">${sale.total.toFixed(0)} دينار</span>
                    </div>
                    <div class="summary-item">
                        <span>طريقة الدفع:</span>
                        <span>${getPaymentMethodName(sale.paymentMethod)}</span>
                    </div>
                    ${sale.changeAmount ? `
                        <div class="summary-item">
                            <span>الباقي:</span>
                            <span class="change-amount">${sale.changeAmount.toFixed(0)} دينار</span>
                        </div>
                    ` : ''}
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" onclick="printInvoice('${sale.id}')">
                    <i class="fas fa-print"></i>
                    طباعة الفاتورة
                </button>
                <button class="btn btn-info" onclick="sendWhatsAppInvoice('${sale.id}')">
                    <i class="fab fa-whatsapp"></i>
                    إرسال واتساب
                </button>
                <button class="btn btn-secondary" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                    إغلاق
                </button>
            </div>
        </div>
    `;

    document.getElementById('modalContainer').appendChild(modal);
    setTimeout(() => modal.classList.add('show'), 100);
}

function getPaymentMethodName(method) {
    const methods = {
        'cash': 'نقدي',
        'card': 'بطاقة',
        'transfer': 'تحويل',
        'mixed': 'مختلط'
    };
    return methods[method] || method;
}

// Print Invoice Function
function printInvoice(saleId = null) {
    let sale;

    if (saleId) {
        sale = sales.find(s => s.id === saleId);
        if (!sale) {
            showNotification('لم يتم العثور على الفاتورة', 'error');
            return;
        }
    } else {
        // Create temporary sale for current cart
        if (cart.length === 0) {
            showNotification('السلة فارغة', 'warning');
            return;
        }

        const subtotal = cart.reduce((sum, item) => sum + item.total, 0);
        const tax = (subtotal - currentDiscount) * 0.15;

        sale = {
            invoiceNumber: 'PREVIEW',
            items: [...cart],
            subtotal: subtotal,
            discount: currentDiscount,
            tax: tax,
            total: subtotal - currentDiscount + tax,
            saleType: currentSaleType,
            paymentMethod: currentPaymentMethod,
            customerName: document.getElementById('customerName').value || 'عميل نقدي',
            customerPhone: document.getElementById('customerPhone').value || '',
            cashier: getCurrentCashierInfo(),
            date: new Date().toLocaleDateString('ar-SA'),
            time: new Date().toLocaleTimeString('ar-SA')
        };
    }

    generateInvoicePrint(sale);
}

function generateInvoicePrint(sale) {
    const storeSettings = JSON.parse(localStorage.getItem('storeSettings')) || {};

    // معلومات الكاشير الافتراضية
    const cashierInfo = {
        name: 'الكاشير',
        role: 'cashier',
        roleText: 'الكاشير',
        roleIcon: '💰'
    };

    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>فاتورة ${sale.invoiceNumber}</title>
            <style>
                /* إعدادات الصفحة A4 */
                @page {
                    size: A4;
                    margin: 15mm;
                }

                * {
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }

                body {
                    font-family: 'Arial', 'Tahoma', sans-serif;
                    font-size: 14px;
                    line-height: 1.6;
                    color: #333;
                    background: white;
                    width: 210mm;
                    min-height: 297mm;
                    margin: 0 auto;
                    padding: 20mm;
                    position: relative;
                }

                /* تخطيط الفاتورة A4 */
                .invoice-container {
                    max-width: 100%;
                    margin: 0 auto;
                    background: white;
                    box-shadow: 0 0 20px rgba(0,0,0,0.1);
                    border-radius: 8px;
                    overflow: hidden;
                }

                .invoice-header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    text-align: center;
                    padding: 30px 20px;
                    margin-bottom: 30px;
                }

                .store-name {
                    font-size: 28px;
                    font-weight: bold;
                    margin-bottom: 10px;
                    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
                }

                .store-info {
                    font-size: 14px;
                    margin-bottom: 5px;
                    opacity: 0.9;
                }

                .invoice-details {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 30px;
                    margin-bottom: 30px;
                    padding: 0 20px;
                }

                .invoice-info, .customer-info {
                    background: #f8f9fa;
                    padding: 20px;
                    border-radius: 8px;
                    border-left: 4px solid #667eea;
                }

                .info-title {
                    font-size: 16px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin-bottom: 15px;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                }

                .info-row {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 8px;
                    padding: 5px 0;
                    border-bottom: 1px solid #e9ecef;
                }

                .info-label {
                    font-weight: 600;
                    color: #6c757d;
                }

                .info-value {
                    font-weight: 500;
                    color: #2c3e50;
                }

                .sale-type {
                    background: linear-gradient(135deg, #28a745, #20c997);
                    color: white;
                    padding: 15px;
                    text-align: center;
                    margin: 0 20px 30px 20px;
                    border-radius: 8px;
                    font-weight: bold;
                    font-size: 16px;
                    text-transform: uppercase;
                    letter-spacing: 1px;
                }

                .items-section {
                    margin: 0 20px 30px 20px;
                }

                .items-title {
                    font-size: 18px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin-bottom: 15px;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                }

                .items-table {
                    width: 100%;
                    border-collapse: collapse;
                    background: white;
                    border-radius: 8px;
                    overflow: hidden;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }

                .items-table th {
                    background: linear-gradient(135deg, #667eea, #764ba2);
                    color: white;
                    padding: 15px 12px;
                    text-align: right;
                    font-weight: bold;
                    font-size: 14px;
                }

                .items-table td {
                    padding: 12px;
                    text-align: right;
                    border-bottom: 1px solid #e9ecef;
                    font-size: 13px;
                }

                .items-table tr:nth-child(even) {
                    background: #f8f9fa;
                }

                .items-table tr:hover {
                    background: #e3f2fd;
                }

                .totals-section {
                    margin: 0 20px 30px 20px;
                    background: #f8f9fa;
                    padding: 25px;
                    border-radius: 8px;
                    border-top: 4px solid #667eea;
                }

                .totals-title {
                    font-size: 18px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin-bottom: 15px;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                }

                .total-row {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 10px;
                    padding: 8px 0;
                    font-size: 14px;
                }

                .total-label {
                    font-weight: 600;
                    color: #6c757d;
                }

                .total-value {
                    font-weight: 600;
                    color: #2c3e50;
                }

                .total-final {
                    background: linear-gradient(135deg, #28a745, #20c997);
                    color: white;
                    padding: 15px;
                    border-radius: 8px;
                    margin-top: 15px;
                    font-size: 18px;
                    font-weight: bold;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }

                .payment-info {
                    margin: 0 20px 30px 20px;
                    background: #e3f2fd;
                    padding: 20px;
                    border-radius: 8px;
                    border-left: 4px solid #2196f3;
                }

                .footer {
                    background: #2c3e50;
                    color: white;
                    text-align: center;
                    padding: 25px 20px;
                    margin-top: 30px;
                }

                .footer-message {
                    font-size: 16px;
                    font-weight: 600;
                    margin-bottom: 10px;
                }

                .footer-note {
                    font-size: 12px;
                    opacity: 0.8;
                    margin-bottom: 5px;
                }

                .cashier-info {
                    background: #667eea;
                    color: white;
                    padding: 15px 20px;
                    margin: 0 20px 20px 20px;
                    border-radius: 8px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }

                @media print {
                    body {
                        margin: 0;
                        padding: 0;
                        -webkit-print-color-adjust: exact;
                        print-color-adjust: exact;
                    }
                    .no-print { display: none; }
                    .invoice-container {
                        box-shadow: none;
                        border-radius: 0;
                    }
                }
            </style>
        </head>
        <body>
            <div class="invoice-container">
                <!-- رأس الفاتورة -->
                <div class="invoice-header">
                    <div class="store-name">${storeSettings.storeName || 'برومت هايبر ماركت'}</div>
                    <div class="store-info">📍 العنوان: ${storeSettings.address || 'بغداد، جمهورية العراق'}</div>
                    <div class="store-info">📞 الهاتف: ${storeSettings.phone || '+964 XXX XXX XXXX'}</div>
                    <div class="store-info">🏢 الرقم الضريبي: ${storeSettings.taxNumber || 'XXXXXXXXX'}</div>
                </div>

                <!-- نوع البيع -->
                <div class="sale-type">
                    ${sale.saleType === 'wholesale' ? '🏪 فاتورة بيع بالجملة' : '🛒 فاتورة بيع بالمفرد'}
                </div>

                <!-- معلومات الفاتورة والعميل -->
                <div class="invoice-details">
                    <div class="invoice-info">
                        <div class="info-title">
                            <i class="fas fa-file-invoice"></i>
                            معلومات الفاتورة
                        </div>
                        <div class="info-row">
                            <span class="info-label">رقم الفاتورة:</span>
                            <span class="info-value">${sale.invoiceNumber}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">التاريخ:</span>
                            <span class="info-value">${sale.date}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">الوقت:</span>
                            <span class="info-value">${sale.time}</span>
                        </div>
                    </div>

                    <div class="customer-info">
                        <div class="info-title">
                            <i class="fas fa-user"></i>
                            معلومات العميل والكاشير
                        </div>
                        <div class="info-row">
                            <span class="info-label">العميل:</span>
                            <span class="info-value">${sale.customerName || 'عميل نقدي'}</span>
                        </div>
                        ${sale.customerPhone ? `
                        <div class="info-row">
                            <span class="info-label">الهاتف:</span>
                            <span class="info-value">${sale.customerPhone}</span>
                        </div>
                        ` : ''}
                        <div class="info-row">
                            <span class="info-label">${cashierInfo.roleIcon} ${cashierInfo.roleText}:</span>
                            <span class="info-value">${cashierInfo.name}</span>
                        </div>
                    </div>
                </div>

                <!-- جدول المنتجات -->
                <div class="items-section">
                    <div class="items-title">
                        <i class="fas fa-shopping-cart"></i>
                        تفاصيل المنتجات
                    </div>
                    <table class="items-table">
                        <thead>
                            <tr>
                                <th style="width: 40%;">المنتج</th>
                                <th style="width: 15%;">الكمية</th>
                                <th style="width: 20%;">السعر الوحدة</th>
                                <th style="width: 25%;">المجموع</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${sale.items.map(item => `
                                <tr>
                                    <td>
                                        <strong>${item.name}</strong>
                                        ${item.description ? `<br><small style="color: #6c757d;">${item.description}</small>` : ''}
                                    </td>
                                    <td style="text-align: center;">${item.quantity}</td>
                                    <td>${item.price.toFixed(0)} د.ع</td>
                                    <td><strong>${item.total.toFixed(0)} د.ع</strong></td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>

                <!-- ملخص المبالغ -->
                <div class="totals-section">
                    <div class="totals-title">
                        <i class="fas fa-calculator"></i>
                        ملخص الفاتورة
                    </div>

                    <div class="total-row">
                        <span class="total-label">المجموع الفرعي:</span>
                        <span class="total-value">${sale.subtotal.toFixed(0)} د.ع</span>
                    </div>

                    ${sale.discount > 0 ? `
                    <div class="total-row">
                        <span class="total-label">الخصم:</span>
                        <span class="total-value" style="color: #dc3545;">-${sale.discount.toFixed(0)} د.ع</span>
                    </div>
                    ` : ''}

                    <div class="total-row">
                        <span class="total-label">ضريبة القيمة المضافة (15%):</span>
                        <span class="total-value">${sale.tax.toFixed(0)} د.ع</span>
                    </div>

                    <div class="total-final">
                        <span>💰 المجموع الكلي:</span>
                        <span>${sale.total.toFixed(0)} د.ع</span>
                    </div>
                </div>

                <!-- معلومات الدفع -->
                <div class="payment-info">
                    <div class="info-title">
                        <i class="fas fa-credit-card"></i>
                        تفاصيل الدفع
                    </div>
                    <div class="info-row">
                        <span class="info-label">طريقة الدفع:</span>
                        <span class="info-value">${getPaymentMethodName(sale.paymentMethod)}</span>
                    </div>
                    ${sale.paidAmount ? `
                    <div class="info-row">
                        <span class="info-label">المبلغ المدفوع:</span>
                        <span class="info-value">${sale.paidAmount.toFixed(0)} د.ع</span>
                    </div>
                    ` : ''}
                    ${sale.changeAmount && sale.changeAmount > 0 ? `
                    <div class="info-row">
                        <span class="info-label">الباقي:</span>
                        <span class="info-value" style="color: #28a745;">${sale.changeAmount.toFixed(0)} د.ع</span>
                    </div>
                    ` : ''}
                </div>

                <!-- معلومات الكاشير -->
                <div class="cashier-info">
                    <div>
                        <strong>${cashierInfo.roleIcon} ${cashierInfo.roleText}:</strong> ${cashierInfo.name}
                    </div>
                    <div style="font-size: 12px; opacity: 0.9;">
                        تم إنشاء الفاتورة: ${new Date().toLocaleString('ar-SA')}
                    </div>
                </div>

                <!-- تذييل الفاتورة -->
                <div class="footer">
                    <div class="footer-message">🙏 شكراً لتسوقكم معنا</div>
                    <div class="footer-note">تم إنشاء هذه الفاتورة إلكترونياً بواسطة نظام برومت هايبر ماركت</div>
                    <div class="footer-note">للاستفسارات: ${storeSettings.phone || '+964 XXX XXX XXXX'}</div>
                    <div class="footer-note">نتطلع لخدمتكم مرة أخرى</div>
                </div>
            </div>

            <script>
                window.onload = function() {
                    // إعطاء وقت لتحميل الأنماط
                    setTimeout(function() {
                        window.print();
                        // إغلاق النافذة بعد الطباعة
                        setTimeout(function() {
                            window.close();
                        }, 2000);
                    }, 500);
                }

                // التعامل مع أحداث الطباعة
                window.addEventListener('beforeprint', function() {
                    console.log('بدء الطباعة...');
                });

                window.addEventListener('afterprint', function() {
                    console.log('انتهاء الطباعة');
                });
            </script>
        </body>
        </html>
    `);

    printWindow.document.close();
}

// WhatsApp Invoice Function
function sendWhatsAppInvoice(saleId) {
    const sale = sales.find(s => s.id === saleId);
    if (!sale) {
        showNotification('لم يتم العثور على الفاتورة', 'error');
        return;
    }

    if (!sale.customerPhone) {
        showNotification('لا يوجد رقم هاتف للعميل', 'warning');
        return;
    }

    const storeSettings = JSON.parse(localStorage.getItem('storeSettings')) || {};

    let message = `🧾 *فاتورة ${sale.saleType === 'wholesale' ? 'بيع بالجملة' : 'بيع بالمفرد'}*\n\n`;
    message += `📋 رقم الفاتورة: ${sale.invoiceNumber}\n`;
    message += `📅 التاريخ: ${sale.date}\n`;
    message += `🕐 الوقت: ${sale.time}\n`;
    message += `👤 العميل: ${sale.customerName}\n\n`;

    message += `🛍️ *المنتجات:*\n`;
    sale.items.forEach(item => {
        message += `• ${item.name}\n`;
        message += `  الكمية: ${item.quantity} × ${item.price.toFixed(0)} = ${item.total.toFixed(0)} دينار\n\n`;
    });

    message += `💰 *ملخص الفاتورة:*\n`;
    message += `المجموع الفرعي: ${sale.subtotal.toFixed(0)} دينار\n`;
    if (sale.discount > 0) {
        message += `الخصم: -${sale.discount.toFixed(0)} دينار\n`;
    }
    message += `الضريبة (15%): ${sale.tax.toFixed(0)} دينار\n`;
    message += `*المجموع الكلي: ${sale.total.toFixed(0)} دينار*\n\n`;

    message += `💳 طريقة الدفع: ${getPaymentMethodName(sale.paymentMethod)}\n\n`;

    message += `🏪 ${storeSettings.storeName || 'متجرنا'}\n`;
    message += `📞 ${storeSettings.phone || ''}\n`;
    message += `شكراً لتسوقكم معنا! 🙏`;

    const phoneNumber = sale.customerPhone.replace(/[^0-9]/g, '');
    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;

    window.open(whatsappUrl, '_blank');
    showNotification('تم فتح واتساب لإرسال الفاتورة', 'success');
}

function closeModal() {
    const modal = document.querySelector('.modal-overlay');
    if (modal) {
        modal.classList.remove('show');
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
}

// Reports Functions
function showReportsModal() {
    const today = new Date().toLocaleDateString('ar-SA');
    const todaySales = sales.filter(sale => sale.date === today);
    const todayTotal = todaySales.reduce((sum, sale) => sum + sale.total, 0);
    const todayCount = todaySales.length;

    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal reports-modal">
            <div class="modal-header">
                <h3><i class="fas fa-chart-bar"></i> تقارير المبيعات</h3>
            </div>
            <div class="modal-body">
                <div class="reports-summary">
                    <div class="report-card">
                        <div class="report-icon">
                            <i class="fas fa-calendar-day"></i>
                        </div>
                        <div class="report-info">
                            <h4>مبيعات اليوم</h4>
                            <div class="report-value">${todayTotal.toFixed(0)} دينار</div>
                            <div class="report-count">${todayCount} فاتورة</div>
                        </div>
                    </div>

                    <div class="report-card">
                        <div class="report-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="report-info">
                            <h4>إجمالي المبيعات</h4>
                            <div class="report-value">${sales.reduce((sum, sale) => sum + sale.total, 0).toFixed(0)} دينار</div>
                            <div class="report-count">${sales.length} فاتورة</div>
                        </div>
                    </div>

                    <div class="report-card">
                        <div class="report-icon">
                            <i class="fas fa-pause-circle"></i>
                        </div>
                        <div class="report-info">
                            <h4>المبيعات المعلقة</h4>
                            <div class="report-value">${heldSales.length}</div>
                            <div class="report-count">عملية معلقة</div>
                        </div>
                    </div>
                </div>

                <div class="reports-actions">
                    <button class="btn btn-info" onclick="showDailySalesReport()">
                        <i class="fas fa-file-alt"></i>
                        تقرير يومي مفصل
                    </button>
                    <button class="btn btn-success" onclick="exportSalesData()">
                        <i class="fas fa-download"></i>
                        تصدير البيانات
                    </button>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                    إغلاق
                </button>
            </div>
        </div>
    `;

    document.getElementById('modalContainer').appendChild(modal);
    setTimeout(() => modal.classList.add('show'), 100);
}

function showHeldSales() {
    if (heldSales.length === 0) {
        showNotification('لا توجد مبيعات معلقة', 'info');
        return;
    }

    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal held-sales-modal">
            <div class="modal-header">
                <h3><i class="fas fa-pause-circle"></i> المبيعات المعلقة</h3>
            </div>
            <div class="modal-body">
                <div class="held-sales-list">
                    ${heldSales.map(sale => `
                        <div class="held-sale-item">
                            <div class="held-sale-info">
                                <div class="held-sale-id">#${sale.id.slice(-6)}</div>
                                <div class="held-sale-time">${new Date(sale.timestamp).toLocaleString('ar-SA')}</div>
                                <div class="held-sale-items">${sale.cart.length} منتج</div>
                                <div class="held-sale-total">${sale.cart.reduce((sum, item) => sum + item.total, 0).toFixed(0)} دينار</div>
                            </div>
                            <div class="held-sale-actions">
                                <button class="btn btn-sm btn-success" onclick="resumeHeldSale('${sale.id}')">
                                    <i class="fas fa-play"></i>
                                    استكمال
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="deleteHeldSale('${sale.id}')">
                                    <i class="fas fa-trash"></i>
                                    حذف
                                </button>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                    إغلاق
                </button>
            </div>
        </div>
    `;

    document.getElementById('modalContainer').appendChild(modal);
    setTimeout(() => modal.classList.add('show'), 100);
}

function resumeHeldSale(saleId) {
    const heldSale = heldSales.find(sale => sale.id === saleId);
    if (!heldSale) {
        showNotification('لم يتم العثور على البيع المعلق', 'error');
        return;
    }

    // Clear current cart
    cart = [];

    // Load held sale data
    cart = [...heldSale.cart];
    currentDiscount = heldSale.discount;
    currentSaleType = heldSale.saleType;

    // Update UI
    document.querySelector(`input[name="saleType"][value="${currentSaleType}"]`).checked = true;
    document.getElementById('discountAmount').value = currentDiscount.toFixed(2);

    updateCartDisplay();
    updateTotals();

    // Remove from held sales
    heldSales = heldSales.filter(sale => sale.id !== saleId);
    localStorage.setItem('heldSales', JSON.stringify(heldSales));

    closeModal();
    showNotification('تم استكمال البيع المعلق', 'success');
}

function deleteHeldSale(saleId) {
    if (confirm('هل أنت متأكد من حذف هذا البيع المعلق؟')) {
        heldSales = heldSales.filter(sale => sale.id !== saleId);
        localStorage.setItem('heldSales', JSON.stringify(heldSales));

        // Refresh modal
        closeModal();
        setTimeout(() => showHeldSales(), 300);

        showNotification('تم حذف البيع المعلق', 'info');
    }
}

function showLastSales() {
    const recentSales = sales.slice(-10).reverse(); // Last 10 sales

    if (recentSales.length === 0) {
        showNotification('لا توجد مبيعات سابقة', 'info');
        return;
    }

    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal last-sales-modal">
            <div class="modal-header">
                <h3><i class="fas fa-history"></i> آخر المبيعات</h3>
            </div>
            <div class="modal-body">
                <div class="last-sales-list">
                    ${recentSales.map(sale => `
                        <div class="last-sale-item">
                            <div class="last-sale-info">
                                <div class="last-sale-invoice">${sale.invoiceNumber}</div>
                                <div class="last-sale-customer">${sale.customerName}</div>
                                <div class="last-sale-time">${sale.date} ${sale.time}</div>
                                <div class="last-sale-total">${sale.total.toFixed(0)} دينار</div>
                            </div>
                            <div class="last-sale-actions">
                                <button class="btn btn-sm btn-info" onclick="printInvoice('${sale.id}')">
                                    <i class="fas fa-print"></i>
                                    طباعة
                                </button>
                                <button class="btn btn-sm btn-success" onclick="sendWhatsAppInvoice('${sale.id}')">
                                    <i class="fab fa-whatsapp"></i>
                                    واتساب
                                </button>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                    إغلاق
                </button>
            </div>
        </div>
    `;

    document.getElementById('modalContainer').appendChild(modal);
    setTimeout(() => modal.classList.add('show'), 100);
}

function showCouponsModal() {
    const coupons = JSON.parse(localStorage.getItem('adminCoupons')) || [];
    const validCoupons = coupons.filter(coupon => {
        const now = new Date();
        const expiryDate = new Date(coupon.expiryDate);
        return expiryDate > now && coupon.usageCount < coupon.usageLimit;
    });

    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal coupons-modal">
            <div class="modal-header">
                <h3><i class="fas fa-ticket-alt"></i> الكوبونات المتاحة</h3>
            </div>
            <div class="modal-body">
                ${validCoupons.length === 0 ? `
                    <div class="no-coupons">
                        <i class="fas fa-ticket-alt"></i>
                        <p>لا توجد كوبونات متاحة حالياً</p>
                    </div>
                ` : `
                    <div class="coupons-list">
                        ${validCoupons.map(coupon => `
                            <div class="coupon-item">
                                <div class="coupon-info">
                                    <div class="coupon-code">${coupon.code}</div>
                                    <div class="coupon-discount">${coupon.discountType === 'percentage' ? coupon.discountValue + '%' : coupon.discountValue + ' دينار'}</div>
                                    <div class="coupon-desc">${coupon.description}</div>
                                </div>
                                <button class="btn btn-sm btn-success" onclick="applyCoupon('${coupon.code}')">
                                    <i class="fas fa-check"></i>
                                    تطبيق
                                </button>
                            </div>
                        `).join('')}
                    </div>
                `}
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                    إغلاق
                </button>
            </div>
        </div>
    `;

    document.getElementById('modalContainer').appendChild(modal);
    setTimeout(() => modal.classList.add('show'), 100);
}

function applyCoupon(couponCode) {
    const coupons = JSON.parse(localStorage.getItem('adminCoupons')) || [];
    const coupon = coupons.find(c => c.code === couponCode);

    if (!coupon) {
        showNotification('كوبون غير صحيح', 'error');
        return;
    }

    const subtotal = cart.reduce((sum, item) => sum + item.total, 0);

    if (coupon.discountType === 'percentage') {
        currentDiscount = (subtotal * coupon.discountValue) / 100;
    } else {
        currentDiscount = coupon.discountValue;
    }

    // Update coupon usage
    coupon.usageCount = (coupon.usageCount || 0) + 1;
    localStorage.setItem('adminCoupons', JSON.stringify(coupons));

    document.getElementById('discountAmount').value = currentDiscount.toFixed(2);
    updateTotals();
    closeModal();

    showNotification(`تم تطبيق الكوبون: ${couponCode}`, 'success');
}

function showSettingsModal() {
    showNotification('إعدادات الكاشير قيد التطوير', 'info');
}

function exportSalesData() {
    const exportData = {
        sales: sales,
        heldSales: heldSales,
        exportDate: new Date().toISOString(),
        exportedBy: currentEmployee ? currentEmployee.name : 'غير محدد',
        exporterId: currentEmployee ? currentEmployee.id : null
    };

    const jsonString = JSON.stringify(exportData, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = `sales-export-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showNotification('تم تصدير بيانات المبيعات', 'success');
}
