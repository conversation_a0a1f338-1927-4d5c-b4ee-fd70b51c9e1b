// ===== PROFESSIONAL CASHIER SYSTEM =====

// ===== GLOBAL VARIABLES =====
let allProducts = [];
let cart = [];
let currentDiscount = 0;
let currentTax = 0;
let currentPaymentMethod = 'cash';
let lastInvoiceNumber = parseInt(localStorage.getItem('lastInvoiceNumber')) || 1000;
let heldSales = JSON.parse(localStorage.getItem('heldSales')) || [];
let settings = {
    storeName: 'متجر الأمانة',
    cashierName: 'أحمد علي',
    currency: 'د.ع',
    taxRate: 0,
    soundEnabled: true
};

// ===== SYSTEM INITIALIZATION =====
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تهيئة نظام الكاشير المتقدم...');

    initializeSystem();
    loadProducts();
    updateDateTime();

    // Update time every second
    setInterval(updateDateTime, 1000);

    console.log('✅ تم تحميل نظام الكاشير بنجاح');
});

function initializeSystem() {
    loadSettings();
    updateCartDisplay();
    updateSummary();
    console.log('⚙️ تم تهيئة النظام');
}

// ===== DATE AND TIME =====
function updateDateTime() {
    const now = new Date();
    const dateElement = document.getElementById('currentDate');
    const timeElement = document.getElementById('currentTime');

    if (dateElement) {
        dateElement.textContent = now.toLocaleDateString('ar-EG', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }

    if (timeElement) {
        timeElement.textContent = now.toLocaleTimeString('ar-EG');
    }
}

// ===== PRODUCTS MANAGEMENT =====
function loadProducts() {
    try {
        const adminProducts = JSON.parse(localStorage.getItem('adminProducts')) || [];
        const mainProducts = JSON.parse(localStorage.getItem('products')) || [];

        // Merge and remove duplicates
        allProducts = [...adminProducts, ...mainProducts].filter((product, index, self) =>
            index === self.findIndex(p => p.id === product.id)
        );

        // Normalize product data
        allProducts = allProducts.map(product => normalizeProductData(product));

        console.log(`📦 تم تحميل ${allProducts.length} منتج`);
        displayProducts(allProducts);

    } catch (error) {
        console.error('❌ خطأ في تحميل المنتجات:', error);
        showNotification('خطأ في تحميل المنتجات', 'error');
    }
}

function normalizeProductData(product) {
    return {
        id: product.id || generateUniqueId(),
        nameAr: product.nameAr || product.name || 'منتج غير محدد',
        nameEn: product.nameEn || product.name || 'Unknown Product',
        price: parseFloat(product.price) || 0,
        wholesalePrice: parseFloat(product.wholesalePrice) || parseFloat(product.price) || 0,
        stock: parseInt(product.stock) || parseInt(product.quantity) || 0,
        category: product.category || 'عام',
        barcode: product.barcode || generateBarcode(),
        image: product.image || null,
        description: product.description || '',
        unit: product.unit || 'قطعة'
    };
}

function generateUniqueId() {
    return 'prod_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

function generateBarcode() {
    return Date.now().toString().slice(-10);
}

function displayProducts(products) {
    const productsGrid = document.getElementById('productsGrid');

    if (!products || products.length === 0) {
        productsGrid.innerHTML = `
            <div class="no-products">
                <i class="fas fa-box-open"></i>
                <h3>لا توجد منتجات</h3>
                <p>لم يتم العثور على منتجات لعرضها</p>
            </div>
        `;
        return;
    }

    productsGrid.innerHTML = products.map(product => createProductCard(product)).join('');
}

function createProductCard(product) {
    const stockStatus = getStockStatus(product.stock);

    return `
        <div class="product-card ${stockStatus.class}" data-product-id="${product.id}">
            <div class="product-image">
                ${product.image ?
                    `<img src="${product.image}" alt="${product.nameAr}" onerror="this.style.display='none'">` :
                    `<div class="no-image"><i class="fas fa-box"></i></div>`
                }
            </div>

            <div class="product-info">
                <h4 class="product-name">${product.nameAr}</h4>
                <p class="product-price">${formatCurrency(product.price)}</p>
                <div class="product-details">
                    <span class="product-stock ${stockStatus.class}">
                        <i class="fas fa-boxes"></i>
                        ${product.stock} ${product.unit}
                    </span>
                    <span class="product-barcode">
                        <i class="fas fa-barcode"></i>
                        ${product.barcode}
                    </span>
                </div>
            </div>

            <div class="product-actions">
                <button class="add-btn" onclick="addToCart('${product.id}')" ${product.stock <= 0 ? 'disabled' : ''}>
                    <i class="fas fa-cart-plus"></i>
                    إضافة
                </button>
            </div>
        </div>
    `;
}

function getStockStatus(stock) {
    if (stock <= 0) return { class: 'out-of-stock', text: 'نفذ' };
    if (stock <= 5) return { class: 'low-stock', text: 'قليل' };
    return { class: 'in-stock', text: 'متوفر' };
}

// ===== CART MANAGEMENT =====
function addToCart(productId) {
    const product = allProducts.find(p => p.id === productId);

    if (!product) {
        showNotification('المنتج غير موجود', 'error');
        return;
    }

    if (product.stock <= 0) {
        showNotification('المنتج غير متوفر في المخزون', 'error');
        return;
    }

    const existingItem = cart.find(item => item.id === productId);

    if (existingItem) {
        if (existingItem.quantity >= product.stock) {
            showNotification('لا يمكن إضافة كمية أكثر من المتوفر', 'error');
            return;
        }
        existingItem.quantity++;
    } else {
        cart.push({
            ...product,
            quantity: 1
        });
    }

    updateCartDisplay();
    updateSummary();
    showNotification(`تم إضافة ${product.nameAr} للسلة`, 'success');

    console.log(`➕ تم إضافة ${product.nameAr} للسلة`);
}

function removeFromCart(productId) {
    cart = cart.filter(item => item.id !== productId);
    updateCartDisplay();
    updateSummary();
    showNotification('تم حذف المنتج من السلة', 'info');
}

function increaseQuantity(productId) {
    const item = cart.find(item => item.id === productId);
    const product = allProducts.find(p => p.id === productId);

    if (item && product && item.quantity < product.stock) {
        item.quantity++;
        updateCartDisplay();
        updateSummary();
    } else {
        showNotification('لا يمكن زيادة الكمية أكثر من المتوفر', 'error');
    }
}

function decreaseQuantity(productId) {
    const item = cart.find(item => item.id === productId);

    if (item) {
        if (item.quantity > 1) {
            item.quantity--;
            updateCartDisplay();
            updateSummary();
        } else {
            removeFromCart(productId);
        }
    }
}

function clearCart() {
    if (cart.length === 0) {
        showNotification('السلة فارغة بالفعل', 'info');
        return;
    }

    if (confirm('هل تريد مسح جميع المنتجات من السلة؟')) {
        cart = [];
        currentDiscount = 0;
        updateCartDisplay();
        updateSummary();
        showNotification('تم مسح السلة', 'success');
    }
}

function updateCartDisplay() {
    const cartItems = document.getElementById('cartItems');

    if (cart.length === 0) {
        cartItems.innerHTML = `
            <div class="empty-cart">
                <i class="fas fa-shopping-cart"></i>
                <p>السلة فارغة</p>
            </div>
        `;
        return;
    }

    cartItems.innerHTML = cart.map(item => createCartItem(item)).join('');
}

function createCartItem(item) {
    return `
        <div class="cart-item" data-product-id="${item.id}">
            <div class="item-info">
                <h4>${item.nameAr}</h4>
                <p class="item-price">${formatCurrency(item.price)}</p>
            </div>
            <div class="item-controls">
                <button class="qty-btn" onclick="decreaseQuantity('${item.id}')">
                    <i class="fas fa-minus"></i>
                </button>
                <span class="quantity">${item.quantity}</span>
                <button class="qty-btn" onclick="increaseQuantity('${item.id}')">
                    <i class="fas fa-plus"></i>
                </button>
            </div>
            <div class="item-total">
                ${formatCurrency(item.price * item.quantity)}
            </div>
            <button class="remove-btn" onclick="removeFromCart('${item.id}')">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;
}

// ===== SUMMARY AND CALCULATIONS =====
function updateSummary() {
    const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const discountAmount = (subtotal * currentDiscount) / 100;
    const taxAmount = ((subtotal - discountAmount) * settings.taxRate) / 100;
    const total = subtotal - discountAmount + taxAmount;

    // Update display
    document.getElementById('subtotal').textContent = formatCurrency(subtotal);
    document.getElementById('discount').textContent = formatCurrency(discountAmount);
    document.getElementById('tax').textContent = formatCurrency(taxAmount);
    document.getElementById('total').textContent = formatCurrency(total);

    // Enable/disable complete sale button
    const completeSaleBtn = document.getElementById('completeSaleBtn');
    if (completeSaleBtn) {
        completeSaleBtn.disabled = cart.length === 0;
    }
}

function applyDiscount() {
    const discountAmount = parseFloat(document.getElementById('discountAmount').value) || 0;
    const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);

    if (discountAmount > subtotal) {
        showNotification('مبلغ الخصم لا يمكن أن يكون أكبر من المجموع الفرعي', 'error');
        return;
    }

    currentDiscount = (discountAmount / subtotal) * 100;
    updateSummary();
}

function applyDiscountPercent() {
    const discountPercent = parseFloat(document.getElementById('discountPercent').value) || 0;

    if (discountPercent > 100) {
        showNotification('نسبة الخصم لا يمكن أن تكون أكبر من 100%', 'error');
        return;
    }

    currentDiscount = discountPercent;
    updateSummary();
}

function clearDiscount() {
    currentDiscount = 0;
    document.getElementById('discountAmount').value = '';
    document.getElementById('discountPercent').value = '';
    updateSummary();
}

// ===== PAYMENT MANAGEMENT =====
function selectPayment(method) {
    currentPaymentMethod = method;

    // Update button states
    document.querySelectorAll('.payment-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-method="${method}"]`).classList.add('active');

    console.log(`💳 تم اختيار طريقة الدفع: ${method}`);
}

function calculateChange() {
    const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const paidAmount = parseFloat(document.getElementById('paidAmount').value) || 0;
    const change = paidAmount - total;

    document.getElementById('changeAmount').textContent = formatCurrency(Math.max(0, change));
}

// ===== SEARCH AND FILTER =====
function searchProducts() {
    const searchTerm = document.getElementById('productSearch').value.toLowerCase();
    const categoryFilter = document.getElementById('categoryFilter').value;
    const stockFilter = document.getElementById('stockFilter').value;

    let filteredProducts = allProducts;

    // Apply search filter
    if (searchTerm) {
        filteredProducts = filteredProducts.filter(product =>
            product.nameAr.toLowerCase().includes(searchTerm) ||
            product.nameEn.toLowerCase().includes(searchTerm) ||
            product.barcode.includes(searchTerm)
        );
    }

    // Apply category filter
    if (categoryFilter) {
        filteredProducts = filteredProducts.filter(product =>
            product.category === categoryFilter
        );
    }

    // Apply stock filter
    if (stockFilter) {
        filteredProducts = filteredProducts.filter(product => {
            switch (stockFilter) {
                case 'available': return product.stock > 0;
                case 'low': return product.stock > 0 && product.stock <= 5;
                case 'out': return product.stock <= 0;
                default: return true;
            }
        });
    }

    displayProducts(filteredProducts);
}

function filterProducts() {
    searchProducts();
}

// ===== BARCODE HANDLING =====
function handleBarcode(event) {
    if (event.key === 'Enter') {
        const barcode = event.target.value.trim();

        if (!barcode) return;

        const product = allProducts.find(p => p.barcode === barcode);

        if (product) {
            addToCart(product.id);
            document.getElementById('barcodeStatus').textContent = 'تم العثور على المنتج';
            document.getElementById('barcodeStatus').className = 'barcode-status success';
        } else {
            document.getElementById('barcodeStatus').textContent = 'لم يتم العثور على المنتج';
            document.getElementById('barcodeStatus').className = 'barcode-status error';
        }

        // Clear input and reset status after 2 seconds
        setTimeout(() => {
            event.target.value = '';
            document.getElementById('barcodeStatus').textContent = 'جاهز';
            document.getElementById('barcodeStatus').className = 'barcode-status';
        }, 2000);
    }
}

function openBarcodeScanner() {
    showNotification('ميزة الكاميرا قيد التطوير', 'info');
}

// ===== SALES MANAGEMENT =====
function completeSale() {
    if (cart.length === 0) {
        showNotification('السلة فارغة', 'error');
        return;
    }

    const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const discountAmount = (subtotal * currentDiscount) / 100;
    const taxAmount = ((subtotal - discountAmount) * settings.taxRate) / 100;
    const total = subtotal - discountAmount + taxAmount;

    const sale = {
        id: ++lastInvoiceNumber,
        date: new Date().toISOString(),
        items: [...cart],
        subtotal: subtotal,
        discount: discountAmount,
        tax: taxAmount,
        total: total,
        paymentMethod: currentPaymentMethod,
        cashier: settings.cashierName
    };

    // Save sale
    const sales = JSON.parse(localStorage.getItem('sales')) || [];
    sales.push(sale);
    localStorage.setItem('sales', JSON.stringify(sales));
    localStorage.setItem('lastInvoiceNumber', lastInvoiceNumber.toString());

    // Clear cart
    cart = [];
    currentDiscount = 0;
    updateCartDisplay();
    updateSummary();

    showNotification(`تم إتمام البيع - رقم الفاتورة: ${sale.id}`, 'success');

    // Auto print if enabled
    if (confirm('هل تريد طباعة الفاتورة؟')) {
        printInvoice(sale);
    }
}

function holdSale() {
    if (cart.length === 0) {
        showNotification('السلة فارغة', 'error');
        return;
    }

    const heldSale = {
        id: Date.now(),
        date: new Date().toISOString(),
        items: [...cart],
        discount: currentDiscount
    };

    heldSales.push(heldSale);
    localStorage.setItem('heldSales', JSON.stringify(heldSales));

    // Clear current cart
    cart = [];
    currentDiscount = 0;
    updateCartDisplay();
    updateSummary();

    showNotification('تم تعليق البيع', 'success');
}

function printInvoice(sale = null) {
    if (!sale && cart.length === 0) {
        showNotification('لا توجد فاتورة للطباعة', 'error');
        return;
    }

    // Create print content
    const printContent = generateInvoiceHTML(sale);

    // Open print window
    const printWindow = window.open('', '_blank');
    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.print();
}

function generateInvoiceHTML(sale) {
    const invoiceData = sale || {
        id: 'معاينة',
        date: new Date().toISOString(),
        items: cart,
        subtotal: cart.reduce((sum, item) => sum + (item.price * item.quantity), 0),
        discount: (cart.reduce((sum, item) => sum + (item.price * item.quantity), 0) * currentDiscount) / 100,
        tax: 0,
        total: cart.reduce((sum, item) => sum + (item.price * item.quantity), 0) - ((cart.reduce((sum, item) => sum + (item.price * item.quantity), 0) * currentDiscount) / 100),
        paymentMethod: currentPaymentMethod,
        cashier: settings.cashierName
    };

    return `
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>فاتورة ${invoiceData.id}</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { text-align: center; margin-bottom: 20px; }
                .invoice-details { margin-bottom: 20px; }
                table { width: 100%; border-collapse: collapse; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                th { background-color: #f2f2f2; }
                .total { font-weight: bold; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>${settings.storeName}</h1>
                <h2>فاتورة مبيعات</h2>
            </div>

            <div class="invoice-details">
                <p><strong>رقم الفاتورة:</strong> ${invoiceData.id}</p>
                <p><strong>التاريخ:</strong> ${new Date(invoiceData.date).toLocaleString('ar-EG')}</p>
                <p><strong>الكاشير:</strong> ${invoiceData.cashier}</p>
                <p><strong>طريقة الدفع:</strong> ${invoiceData.paymentMethod}</p>
            </div>

            <table>
                <thead>
                    <tr>
                        <th>المنتج</th>
                        <th>الكمية</th>
                        <th>السعر</th>
                        <th>الإجمالي</th>
                    </tr>
                </thead>
                <tbody>
                    ${invoiceData.items.map(item => `
                        <tr>
                            <td>${item.nameAr}</td>
                            <td>${item.quantity}</td>
                            <td>${formatCurrency(item.price)}</td>
                            <td>${formatCurrency(item.price * item.quantity)}</td>
                        </tr>
                    `).join('')}
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="3"><strong>المجموع الفرعي:</strong></td>
                        <td class="total">${formatCurrency(invoiceData.subtotal)}</td>
                    </tr>
                    ${invoiceData.discount > 0 ? `
                    <tr>
                        <td colspan="3"><strong>الخصم:</strong></td>
                        <td class="total">-${formatCurrency(invoiceData.discount)}</td>
                    </tr>
                    ` : ''}
                    ${invoiceData.tax > 0 ? `
                    <tr>
                        <td colspan="3"><strong>الضريبة:</strong></td>
                        <td class="total">${formatCurrency(invoiceData.tax)}</td>
                    </tr>
                    ` : ''}
                    <tr>
                        <td colspan="3"><strong>الإجمالي النهائي:</strong></td>
                        <td class="total">${formatCurrency(invoiceData.total)}</td>
                    </tr>
                </tfoot>
            </table>

            <div style="text-align: center; margin-top: 30px;">
                <p>شكراً لزيارتكم - نتطلع لخدمتكم مرة أخرى</p>
            </div>
        </body>
        </html>
    `;
}

// ===== UTILITY FUNCTIONS =====
function formatCurrency(amount) {
    return `${amount.toLocaleString('ar-EG')} ${settings.currency}`;
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <i class="fas fa-${getNotificationIcon(type)}"></i>
        <span>${message}</span>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Show notification
    setTimeout(() => notification.classList.add('show'), 100);

    // Hide and remove notification
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);

    console.log(`${type.toUpperCase()}: ${message}`);
}

function getNotificationIcon(type) {
    switch (type) {
        case 'success': return 'check-circle';
        case 'error': return 'exclamation-circle';
        case 'warning': return 'exclamation-triangle';
        default: return 'info-circle';
    }
}

function loadSettings() {
    const savedSettings = localStorage.getItem('cashierSettings');
    if (savedSettings) {
        settings = { ...settings, ...JSON.parse(savedSettings) };
    }
}

function refreshProducts() {
    loadProducts();
    showNotification('تم تحديث المنتجات', 'success');
}

// ===== MODAL FUNCTIONS =====
function showSettings() {
    document.getElementById('settingsModal').style.display = 'flex';
}

function hideSettings() {
    document.getElementById('settingsModal').style.display = 'none';
}

function showCalculator() {
    document.getElementById('calculatorModal').style.display = 'flex';
}

function hideCalculator() {
    document.getElementById('calculatorModal').style.display = 'none';
}

function showReports() {
    showNotification('ميزة التقارير قيد التطوير', 'info');
}

function goToMainSite() {
    if (confirm('هل تريد الانتقال للموقع الرئيسي؟')) {
        window.location.href = 'index.html';
    }
}

// ===== CALCULATOR FUNCTIONS =====
let calcDisplay = '0';
let calcOperator = null;
let calcPrevValue = null;
let calcWaitingForOperand = false;

function inputCalc(value) {
    const display = document.getElementById('calcDisplay');

    if (calcWaitingForOperand) {
        calcDisplay = value;
        calcWaitingForOperand = false;
    } else {
        calcDisplay = calcDisplay === '0' ? value : calcDisplay + value;
    }

    display.textContent = calcDisplay;
}

function clearCalculator() {
    calcDisplay = '0';
    calcOperator = null;
    calcPrevValue = null;
    calcWaitingForOperand = false;
    document.getElementById('calcDisplay').textContent = calcDisplay;
}

function backspace() {
    calcDisplay = calcDisplay.slice(0, -1) || '0';
    document.getElementById('calcDisplay').textContent = calcDisplay;
}

function calculateResult() {
    const display = document.getElementById('calcDisplay');
    const currentValue = parseFloat(calcDisplay);

    if (calcPrevValue !== null && calcOperator) {
        let result;

        switch (calcOperator) {
            case '+':
                result = calcPrevValue + currentValue;
                break;
            case '-':
                result = calcPrevValue - currentValue;
                break;
            case '*':
                result = calcPrevValue * currentValue;
                break;
            case '/':
                result = calcPrevValue / currentValue;
                break;
            default:
                return;
        }

        calcDisplay = result.toString();
        display.textContent = calcDisplay;
        calcOperator = null;
        calcPrevValue = null;
        calcWaitingForOperand = true;
    }
}

// ===== QUICK ACTIONS =====
function showHeldSales() {
    if (heldSales.length === 0) {
        showNotification('لا توجد مبيعات معلقة', 'info');
        return;
    }

    showNotification(`يوجد ${heldSales.length} مبيعات معلقة`, 'info');
}

function showLastSales() {
    const sales = JSON.parse(localStorage.getItem('sales')) || [];
    if (sales.length === 0) {
        showNotification('لا توجد مبيعات سابقة', 'info');
        return;
    }

    showNotification(`آخر عملية بيع: فاتورة رقم ${sales[sales.length - 1].id}`, 'info');
}

function openCashDrawer() {
    showNotification('تم فتح درج النقد', 'success');
}

// ===== TAB FUNCTIONS =====
function showTab(tabName) {
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(content => {
        content.style.display = 'none';
    });

    // Remove active class from all tabs
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // Show selected tab content
    const tabContent = document.getElementById(tabName + 'Tab');
    if (tabContent) {
        tabContent.style.display = 'block';
    }

    // Add active class to clicked tab
    event.target.classList.add('active');
}

function saveSettings() {
    showNotification('تم حفظ الإعدادات', 'success');
    hideSettings();
}
