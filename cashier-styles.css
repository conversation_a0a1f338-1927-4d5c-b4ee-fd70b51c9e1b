/* CSS Variables for Theming */
:root {
    --primary-color: #2c5530;
    --secondary-color: #34495e;
    --text-color: #2c3e50;
    --bg-color: #f5f7fa;
    --base-font-size: 16px;
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* Cashier System Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, var(--bg-color), #c3cfe2);
    color: var(--text-color);
    overflow-x: hidden;
    font-size: var(--base-font-size);
    transition: all 0.3s ease;
}

/* Theme Classes */
body.theme-dark {
    --primary-color: #34495e;
    --secondary-color: #2c3e50;
    --text-color: #ecf0f1;
    --bg-color: #2c3e50;
    background: linear-gradient(135deg, #2c3e50, #34495e);
}

body.theme-light {
    --primary-color: #3498db;
    --secondary-color: #ecf0f1;
    --text-color: #2c3e50;
    --bg-color: #ffffff;
    background: linear-gradient(135deg, #ecf0f1, #bdc3c7);
}

body.theme-blue {
    --primary-color: #2980b9;
    --secondary-color: #3498db;
    --text-color: #ecf0f1;
    --bg-color: #1e3a5f;
    background: linear-gradient(135deg, #3498db, #2980b9);
}

body.theme-green {
    --primary-color: #27ae60;
    --secondary-color: #2ecc71;
    --text-color: #ecf0f1;
    --bg-color: #1e5f3a;
    background: linear-gradient(135deg, #27ae60, #2ecc71);
}

/* Header */
.cashier-header {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
    padding: 1rem 2rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo-section i {
    font-size: 2rem;
    color: #3498db;
}

.logo-section h1 {
    font-size: 1.5rem;
    font-weight: 700;
}

.header-info {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.header-info > div {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

/* Main Container */
.cashier-container {
    display: grid;
    grid-template-columns: 1fr 400px 350px;
    gap: 1rem;
    padding: 1rem;
    max-width: 1400px;
    margin: 0 auto;
    min-height: calc(100vh - 140px);
}

/* Panels */
.products-panel,
.cart-panel,
.payment-panel {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(248, 249, 250, 0.95));
    border-radius: 20px;
    box-shadow:
        0 10px 30px rgba(102, 126, 234, 0.15),
        0 4px 15px rgba(102, 126, 234, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    border: 1px solid rgba(102, 126, 234, 0.12);
    position: relative;
}

.products-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
    background-size: 200% 100%;
    animation: shimmer 3s ease-in-out infinite;
    z-index: 1;
}

.panel-header {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.panel-header h3 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.1rem;
    font-weight: 600;
}

/* Advanced Search Section */
.advanced-search-section {
    padding: 1.2rem;
    background: linear-gradient(145deg, rgba(102, 126, 234, 0.03), rgba(118, 75, 162, 0.03));
    border-radius: 18px;
    margin: 12px;
    border: 1px solid rgba(102, 126, 234, 0.1);
    position: relative;
    backdrop-filter: blur(10px);
    font-size: 0.9rem;
}

.advanced-search-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
    background-size: 200% 100%;
    animation: shimmer 3s ease-in-out infinite;
    border-radius: 20px 20px 0 0;
}

/* Search Header */
.search-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.2rem;
    padding-bottom: 0.8rem;
    border-bottom: 1px solid rgba(102, 126, 234, 0.15);
}

.search-title {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #2c3e50;
}

.search-title i {
    font-size: 1.1rem;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.search-title h4 {
    margin: 0;
    font-size: 1rem;
    font-weight: 700;
}

.search-stats {
    display: flex;
    gap: 10px;
    font-size: 0.8rem;
}

.total-products,
.filtered-products {
    padding: 4px 10px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-radius: 12px;
    font-weight: 600;
    box-shadow: 0 2px 6px rgba(102, 126, 234, 0.25);
    font-size: 0.75rem;
}

/* Multi Search Container */
.multi-search-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 0.8rem;
    margin-bottom: 1.2rem;
}

.search-field {
    position: relative;
}

.search-field label {
    position: absolute;
    top: -6px;
    left: 12px;
    background: white;
    padding: 0 6px;
    font-size: 0.7rem;
    font-weight: 600;
    color: #667eea;
    z-index: 2;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input-wrapper i {
    position: absolute;
    left: 12px;
    color: #667eea;
    font-size: 0.9rem;
    z-index: 2;
}

.search-input-wrapper input {
    width: 100%;
    padding: 10px 40px 10px 35px;
    border: 1px solid rgba(102, 126, 234, 0.2);
    border-radius: 12px;
    font-size: 0.85rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    box-shadow: inset 0 1px 4px rgba(102, 126, 234, 0.05);
}

.search-input-wrapper input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow:
        inset 0 2px 8px rgba(102, 126, 234, 0.1),
        0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

.clear-search {
    position: absolute;
    right: 15px;
    background: none;
    border: none;
    color: #95a5a6;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;
    z-index: 2;
}

.clear-search:hover {
    background: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
}

/* Barcode Field Special Styling */
.barcode-field {
    position: relative;
}

.barcode-actions {
    position: absolute;
    right: 50px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    gap: 5px;
    z-index: 2;
}

.btn-scan-mini,
.btn-history-mini {
    width: 30px;
    height: 30px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.btn-scan-mini {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
    box-shadow: 0 2px 8px rgba(39, 174, 96, 0.3);
}

.btn-scan-mini:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(39, 174, 96, 0.4);
}

.btn-history-mini {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

.btn-history-mini:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.4);
}

/* Quick Filters */
.quick-filters {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 0.8rem;
    margin-bottom: 1rem;
    padding: 0.8rem;
    background: rgba(102, 126, 234, 0.03);
    border-radius: 12px;
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.filter-group label {
    font-size: 0.75rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 3px;
}

.filter-group select {
    padding: 6px 10px;
    border: 1px solid rgba(102, 126, 234, 0.2);
    border-radius: 8px;
    background: white;
    font-size: 0.8rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.filter-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    justify-content: center;
}

.btn-clear-all,
.btn-refresh {
    padding: 8px 15px;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    font-size: 0.85rem;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

.btn-clear-all {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
}

.btn-clear-all:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.4);
}

.btn-refresh {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

.btn-refresh:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.4);
}

/* Barcode Status Bar */
.barcode-status-bar {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    border-radius: 12px;
    padding: 12px;
    margin-top: 0.8rem;
    border: 1px solid #667eea;
    box-shadow: 0 3px 12px rgba(102, 126, 234, 0.2);
}

.status-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.status-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    box-shadow: 0 3px 12px rgba(102, 126, 234, 0.3);
}

.status-info {
    flex: 1;
}

.status-text {
    display: block;
    color: #ecf0f1;
    font-weight: 700;
    font-size: 0.85rem;
    margin-bottom: 2px;
}

.status-details {
    display: block;
    color: #bdc3c7;
    font-size: 0.75rem;
    font-weight: 400;
}

.status-indicator {
    width: 15px;
    height: 15px;
    border-radius: 50%;
    background: #27ae60;
    box-shadow: 0 0 10px rgba(39, 174, 96, 0.5);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(39, 174, 96, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(39, 174, 96, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(39, 174, 96, 0);
    }
}

.search-box {
    position: relative;
    margin-bottom: 1rem;
}

.search-box i {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #7f8c8d;
}

.search-box input {
    width: 100%;
    padding: 1rem 3.5rem 1rem 1.2rem;
    border: 2px solid rgba(102, 126, 234, 0.2);
    border-radius: 25px;
    font-size: 1rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    box-shadow: inset 0 2px 8px rgba(102, 126, 234, 0.05);
    font-weight: 500;
}

.search-box input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow:
        inset 0 2px 8px rgba(102, 126, 234, 0.1),
        0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

.search-box input::placeholder {
    color: #95a5a6;
    font-weight: 400;
}

.filter-buttons {
    display: flex;
    gap: 0.5rem;
}

.filter-btn {
    padding: 0.7rem 1.2rem;
    border: 2px solid rgba(102, 126, 234, 0.2);
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 0.85rem;
    font-weight: 600;
    position: relative;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.05);
}

.filter-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.filter-btn:hover::before {
    left: 100%;
}

.filter-btn.active,
.filter-btn:hover {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}

/* Categories Tabs */
.categories-tabs {
    display: flex;
    gap: 0.5rem;
    padding: 1rem;
    border-bottom: 1px solid #ecf0f1;
    overflow-x: auto;
}

.category-tab {
    padding: 0.5rem 1rem;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    font-size: 0.85rem;
    font-weight: 600;
}

.category-tab.active {
    background: #3498db;
    color: white;
    border-color: #3498db;
}

/* Products Grid */
.products-grid {
    flex: 1;
    padding: 1.2rem;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 1rem;
    overflow-y: auto;
    max-height: 500px;
    background: rgba(102, 126, 234, 0.02);
    border-radius: 15px;
    margin: 8px;
}

.products-grid::-webkit-scrollbar {
    width: 8px;
}

.products-grid::-webkit-scrollbar-track {
    background: rgba(102, 126, 234, 0.1);
    border-radius: 4px;
}

.products-grid::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 4px;
}

.products-grid::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #764ba2, #667eea);
}

.product-card {
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    border: 2px solid transparent;
    border-radius: 15px;
    padding: 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    box-shadow:
        0 3px 12px rgba(102, 126, 234, 0.08),
        0 1px 6px rgba(102, 126, 234, 0.05);
    overflow: visible;
    z-index: 1;
}

.product-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #667eea, #764ba2);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
    margin: -2px;
    border-radius: inherit;
}

.product-card:hover {
    transform: translateY(-8px) scale(1.08);
    box-shadow:
        0 20px 40px rgba(102, 126, 234, 0.25),
        0 10px 25px rgba(102, 126, 234, 0.2);
    z-index: 10;
    border-color: rgba(102, 126, 234, 0.3);
}

.product-card:hover::before {
    opacity: 1;
}

.product-card:hover .product-details {
    opacity: 1;
    transform: translateY(0);
    visibility: visible;
}

.product-card:active {
    transform: translateY(-5px) scale(1.05);
    transition: all 0.1s ease;
}

.product-card.out-of-stock {
    opacity: 0.5;
    cursor: not-allowed;
}

.product-image {
    width: 80px;
    height: 80px;
    background: linear-gradient(145deg, #ecf0f1, #e0e6ed);
    border-radius: 12px;
    margin: 0 auto 0.6rem;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    border: 2px solid rgba(102, 126, 234, 0.1);
    transition: all 0.4s ease;
    position: relative;
}

.product-card:hover .product-image {
    border-color: rgba(255, 255, 255, 0.8);
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 10px;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-image i {
    font-size: 1.5rem;
    color: #7f8c8d;
}

.product-name {
    font-size: 0.8rem;
    font-weight: 700;
    margin-bottom: 0.4rem;
    line-height: 1.2;
    color: #2c3e50;
    transition: color 0.3s ease;
    height: 2.4rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.product-card:hover .product-name {
    color: #ffffff;
}

.product-price {
    font-size: 0.9rem;
    font-weight: 800;
    color: #27ae60;
    margin-bottom: 0.3rem;
    transition: color 0.3s ease;
}

.product-card:hover .product-price {
    color: #ffffff;
}

.product-barcode {
    font-family: 'Courier New', monospace;
    font-size: 0.65rem;
    color: #7f8c8d;
    margin-top: 0.4rem;
    padding: 3px 6px;
    background: rgba(102, 126, 234, 0.08);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 3px;
    transition: all 0.3s ease;
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.product-card:hover .product-barcode {
    background: rgba(255, 255, 255, 0.2);
    color: #ffffff;
    border-color: rgba(255, 255, 255, 0.3);
}

.product-barcode i {
    font-size: 0.7rem;
    color: #667eea;
    transition: color 0.3s ease;
}

.product-card:hover .product-barcode i {
    color: #ffffff;
}

/* Product Details Overlay */
.product-details {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: linear-gradient(145deg, #2c3e50, #34495e);
    border-radius: 0 0 15px 15px;
    padding: 12px;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.4s ease;
    visibility: hidden;
    z-index: 5;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    border-top: 2px solid rgba(102, 126, 234, 0.3);
}

.product-details-content {
    color: #ecf0f1;
    font-size: 0.75rem;
    line-height: 1.4;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
    padding: 3px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.detail-row:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.detail-label {
    font-weight: 600;
    color: #bdc3c7;
    font-size: 0.7rem;
}

.detail-value {
    font-weight: 700;
    color: #ecf0f1;
    font-size: 0.75rem;
}

.stock-status {
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 0.65rem;
    font-weight: 600;
}

.stock-status.available {
    background: rgba(39, 174, 96, 0.2);
    color: #2ecc71;
    border: 1px solid rgba(39, 174, 96, 0.3);
}

.stock-status.low {
    background: rgba(243, 156, 18, 0.2);
    color: #f39c12;
    border: 1px solid rgba(243, 156, 18, 0.3);
}

.stock-status.out {
    background: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
    border: 1px solid rgba(231, 76, 60, 0.3);
}

.wholesale-price {
    color: #3498db;
    font-weight: 700;
}

.category-tag {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 0.65rem;
    font-weight: 600;
}

/* Product Actions */
.product-actions {
    display: flex;
    gap: 6px;
    margin-top: 8px;
    opacity: 0;
    transform: translateY(8px);
    transition: all 0.3s ease;
}

.product-card:hover .product-actions {
    opacity: 1;
    transform: translateY(0);
}

.btn-add-to-cart {
    flex: 1;
    padding: 6px 10px;
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 0.7rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    box-shadow: 0 2px 6px rgba(39, 174, 96, 0.3);
}

.btn-add-to-cart:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(39, 174, 96, 0.4);
}

.btn-add-to-cart:active {
    transform: translateY(0);
}

.btn-quick-add {
    width: 30px;
    height: 30px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    box-shadow: 0 2px 6px rgba(52, 152, 219, 0.3);
}

.btn-quick-add:hover {
    transform: translateY(-2px) scale(1.1);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.4);
}

/* Product Selection State */
.product-card.selected {
    border-color: #27ae60;
    box-shadow:
        0 8px 25px rgba(39, 174, 96, 0.3),
        0 4px 15px rgba(39, 174, 96, 0.2);
    transform: translateY(-3px) scale(1.02);
}

.product-card.selected::before {
    opacity: 0.1;
    background: linear-gradient(135deg, #27ae60, #2ecc71);
}

/* Product Loading State */
.product-card.adding {
    pointer-events: none;
    opacity: 0.7;
}

.product-card.adding::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #27ae60;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 10;
}

/* Direct Add Animation */
.product-card.adding-direct {
    position: relative;
    z-index: 5;
}

.product-card.adding-direct::before {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    font-weight: bold;
    z-index: 10;
    box-shadow: 0 4px 15px rgba(39, 174, 96, 0.4);
    animation: checkmarkPop 0.6s ease-out;
}

@keyframes checkmarkPop {
    0% {
        transform: translate(-50%, -50%) scale(0);
        opacity: 0;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
}

/* Enhanced Direct Click Effect */
.product-card {
    cursor: pointer;
    user-select: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.product-card:active {
    transform: scale(0.95);
    transition: all 0.1s ease;
}

/* Click Ripple Effect */
.product-card::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(39, 174, 96, 0.3);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
    pointer-events: none;
    z-index: 1;
}

.product-card.ripple::after {
    width: 200px;
    height: 200px;
}

/* Quick Shake Animation */
@keyframes quickShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-2px); }
    75% { transform: translateX(2px); }
}

/* Enhanced Product Card Interaction */
.product-card {
    position: relative;
    overflow: visible;
}

.product-card:hover {
    cursor: pointer;
}

.product-card:hover::before {
    content: 'انقر للإضافة';
    position: absolute;
    top: -35px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.7rem;
    font-weight: 600;
    white-space: nowrap;
    z-index: 20;
    opacity: 0;
    animation: tooltipFadeIn 0.3s ease-out forwards;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.product-card:hover::after {
    content: '';
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #667eea;
    z-index: 20;
    opacity: 0;
    animation: tooltipFadeIn 0.3s ease-out 0.1s forwards;
}

@keyframes tooltipFadeIn {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(-5px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

/* Direct Add Notification */
.direct-add-notification .notification-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.direct-add-notification .notification-icon {
    width: 35px;
    height: 35px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    animation: iconPulse 0.6s ease-out;
}

.direct-add-notification .notification-text {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.direct-add-notification .notification-text strong {
    font-size: 0.9rem;
    font-weight: 700;
}

.direct-add-notification .notification-text span {
    font-size: 0.8rem;
    opacity: 0.9;
    font-weight: 500;
}

.direct-add-notification .notification-cart {
    position: relative;
    width: 30px;
    height: 30px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.direct-add-notification .cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #e74c3c;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: 700;
    animation: countBounce 0.5s ease-out;
}

@keyframes iconPulse {
    0% { transform: scale(0.8); opacity: 0.5; }
    50% { transform: scale(1.2); opacity: 1; }
    100% { transform: scale(1); opacity: 1; }
}

@keyframes countBounce {
    0% { transform: scale(0); }
    50% { transform: scale(1.3); }
    100% { transform: scale(1); }
}

/* Direct Add Indicator */
.direct-add-indicator {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 25px;
    height: 25px;
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.8rem;
    font-weight: bold;
    opacity: 0;
    transform: scale(0);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 5;
    box-shadow: 0 2px 8px rgba(39, 174, 96, 0.3);
}

.product-card:hover .direct-add-indicator {
    opacity: 1;
    transform: scale(1);
    animation: pulseGlow 2s infinite;
}

.product-card.adding-direct .direct-add-indicator {
    opacity: 1;
    transform: scale(1.2);
    background: linear-gradient(135deg, #2ecc71, #27ae60);
    animation: successPulse 0.6s ease-out;
}

.product-card.adding-direct .direct-add-indicator i::before {
    content: '✓';
}

@keyframes pulseGlow {
    0%, 100% {
        box-shadow: 0 2px 8px rgba(39, 174, 96, 0.3);
        transform: scale(1);
    }
    50% {
        box-shadow: 0 4px 15px rgba(39, 174, 96, 0.6);
        transform: scale(1.05);
    }
}

@keyframes successPulse {
    0% {
        transform: scale(1);
        box-shadow: 0 2px 8px rgba(39, 174, 96, 0.3);
    }
    50% {
        transform: scale(1.3);
        box-shadow: 0 6px 20px rgba(39, 174, 96, 0.6);
    }
    100% {
        transform: scale(1.2);
        box-shadow: 0 4px 15px rgba(39, 174, 96, 0.4);
    }
}

/* Enhanced Product Card Click State */
.product-card:active .direct-add-indicator {
    transform: scale(0.9);
    background: linear-gradient(135deg, #229954, #27ae60);
}

/* ===== NEW SIMPLIFIED CASHIER STYLES ===== */

/* Search Section */
.search-section {
    padding: 15px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.search-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.search-box {
    position: relative;
    display: flex;
    align-items: center;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 0 15px;
    transition: all 0.3s ease;
}

.search-box:focus-within {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-box i {
    color: #6c757d;
    margin-right: 10px;
}

.search-box input {
    flex: 1;
    border: none;
    outline: none;
    padding: 12px 0;
    font-size: 14px;
    background: transparent;
}

.clear-btn {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.clear-btn:hover {
    background: #f8f9fa;
    color: #dc3545;
}

.barcode-section {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.barcode-input {
    position: relative;
    display: flex;
    align-items: center;
    background: white;
    border: 2px solid #28a745;
    border-radius: 10px;
    padding: 0 15px;
    transition: all 0.3s ease;
}

.barcode-input:focus-within {
    border-color: #20c997;
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
}

.barcode-input i {
    color: #28a745;
    margin-right: 10px;
}

.barcode-input input {
    flex: 1;
    border: none;
    outline: none;
    padding: 12px 0;
    font-size: 14px;
    background: transparent;
    font-family: 'Courier New', monospace;
}

.scan-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-left: 10px;
}

.scan-btn:hover {
    background: #218838;
    transform: translateY(-1px);
}

.barcode-status {
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    transition: all 0.3s ease;
}

.barcode-status.ready {
    background: #e3f2fd;
    color: #1976d2;
}

.barcode-status.searching {
    background: #fff3e0;
    color: #f57c00;
}

.barcode-status.success {
    background: #e8f5e8;
    color: #2e7d32;
}

.barcode-status.error {
    background: #ffebee;
    color: #c62828;
}

/* Enhanced Product Cards with Zoom Effects */
.product-card {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 15px;
    padding: 0;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
}

.product-card:hover {
    border-color: #667eea;
    box-shadow: 0 15px 35px rgba(102, 126, 234, 0.2);
}

.product-card.adding {
    border-color: #28a745;
    background: #f8fff9;
    transform: scale(1.02);
    box-shadow: 0 20px 40px rgba(40, 167, 69, 0.3);
}

.product-card.hovered {
    transform: scale(1.05) !important;
    z-index: 10 !important;
    box-shadow: 0 20px 40px rgba(0,0,0,0.2) !important;
}

/* Product Image Container with Zoom */
.product-image-container {
    position: relative;
    height: 200px;
    overflow: hidden;
    border-radius: 15px 15px 0 0;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.product-card .product-image {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.4s ease;
}

.product-card:hover .product-image {
    transform: scale(1.1);
}

.product-card .product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.product-card .product-image i {
    font-size: 3rem;
    color: #adb5bd;
}

/* Zoom Overlay */
.zoom-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(102, 126, 234, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}

.product-card:hover .zoom-overlay {
    opacity: 1;
}

.zoom-content {
    text-align: center;
    color: white;
    transform: translateY(20px);
    transition: transform 0.3s ease;
}

.product-card:hover .zoom-content {
    transform: translateY(0);
}

.zoom-content i {
    font-size: 2rem;
    margin-bottom: 8px;
    display: block;
}

.zoom-content span {
    font-size: 14px;
    font-weight: 600;
}

/* Quick Add Button */
.quick-add-btn {
    position: absolute;
    top: 15px;
    right: 15px;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #667eea;
    font-size: 1.2rem;
    opacity: 0;
    transform: scale(0);
    transition: all 0.3s ease;
    cursor: pointer;
    backdrop-filter: blur(10px);
}

.product-card:hover .quick-add-btn {
    opacity: 1;
    transform: scale(1);
}

.quick-add-btn:hover {
    background: #667eea;
    color: white;
    transform: scale(1.1);
}

/* Badges */
.discount-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 700;
    z-index: 5;
}

.featured-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    z-index: 5;
}

.product-card .add-indicator {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 20px;
    height: 20px;
    background: #28a745;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    opacity: 0;
    transform: scale(0);
    transition: all 0.3s ease;
}

.product-card:hover .add-indicator {
    opacity: 1;
    transform: scale(1);
}

/* Product Info Section */
.product-card .product-info {
    padding: 20px;
    text-align: center;
}

.product-card .product-name {
    font-size: 16px;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 12px 0;
    line-height: 1.4;
    height: 44px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-prices {
    margin-bottom: 15px;
}

.current-price {
    font-size: 18px;
    font-weight: 800;
    color: #667eea;
    margin-bottom: 4px;
}

.old-price {
    font-size: 14px;
    color: #95a5a6;
    text-decoration: line-through;
    font-weight: 500;
}

.product-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 15px;
}

.product-details > div {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    font-size: 12px;
    color: #7f8c8d;
}

.product-details i {
    width: 14px;
    text-align: center;
    color: #bdc3c7;
}

.product-stock {
    font-weight: 600;
}

.product-barcode {
    font-family: 'Courier New', monospace;
    font-size: 10px;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 6px;
    margin: 0 auto;
    max-width: fit-content;
}

/* Product Actions */
.product-card .product-actions {
    padding: 0 20px 20px;
    display: flex;
    gap: 8px;
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s ease;
}

.product-card:hover .product-actions {
    opacity: 1;
    transform: translateY(0);
}

.product-actions .btn {
    flex: 1;
    padding: 10px 16px;
    border: none;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.product-actions .btn.primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.product-actions .btn.primary:hover {
    background: linear-gradient(135deg, #5a6fd8, #6a4190);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.product-actions .btn.secondary {
    background: #f8f9fa;
    color: #6c757d;
    border: 1px solid #e9ecef;
}

.product-actions .btn.secondary:hover {
    background: #e9ecef;
    color: #495057;
    transform: translateY(-1px);
}

/* Legacy support for old button classes */
.product-card .add-btn {
    width: 100%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
}

.product-card .add-btn:hover {
    background: linear-gradient(135deg, #5a6fd8, #6a4190);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

/* Stock Status */
.product-card.out-of-stock {
    opacity: 0.6;
    border-color: #dc3545;
}

.product-card.out-of-stock::after {
    content: 'نفذ المخزون';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #dc3545;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: 600;
}

.product-card.low-stock {
    border-color: #ffc107;
}

.product-card.low-stock .product-stock {
    color: #ffc107;
    font-weight: 600;
}

/* No Products State */
.no-products {
    grid-column: 1 / -1;
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.no-products i {
    font-size: 4rem;
    margin-bottom: 20px;
    color: #e9ecef;
}

.no-products h4 {
    margin: 0 0 10px 0;
    color: #495057;
}

.no-products p {
    margin: 0 0 20px 0;
    font-size: 14px;
}

.no-products .btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
}

.no-products .btn:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

/* Product Details Modal */
.product-details-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.product-details-modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.9);
    background: white;
    border-radius: 20px;
    width: 90%;
    max-width: 600px;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease;
}

.product-details-modal.show .modal-content {
    transform: translate(-50%, -50%) scale(1);
}

.modal-header {
    padding: 25px 30px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 700;
}

.close-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.modal-body {
    padding: 30px;
    max-height: 60vh;
    overflow-y: auto;
    display: flex;
    gap: 30px;
}

.product-image-large {
    flex: 0 0 200px;
    height: 200px;
    border-radius: 15px;
    overflow: hidden;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.product-image-large img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-image-large i {
    font-size: 3rem;
    color: #adb5bd;
}

.product-details-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.detail-row {
    display: flex;
    align-items: flex-start;
    gap: 15px;
}

.detail-row .label {
    font-weight: 600;
    color: #2c3e50;
    min-width: 100px;
    flex-shrink: 0;
}

.detail-row .value {
    color: #34495e;
    flex: 1;
}

.detail-row .value.barcode {
    font-family: 'Courier New', monospace;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 14px;
}

.detail-row.description .value {
    line-height: 1.6;
}

.modal-footer {
    padding: 20px 30px;
    background: #f8f9fa;
    display: flex;
    gap: 15px;
    justify-content: flex-end;
}

.modal-footer .btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.modal-footer .btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.modal-footer .btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8, #6a4190);
    transform: translateY(-1px);
}

.modal-footer .btn-secondary {
    background: #6c757d;
    color: white;
}

.modal-footer .btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

/* Responsive Modal */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        max-height: 90vh;
    }

    .modal-body {
        flex-direction: column;
        gap: 20px;
    }

    .product-image-large {
        flex: none;
        width: 100%;
        height: 150px;
    }

    .modal-footer {
        flex-direction: column;
    }

    .modal-footer .btn {
        width: 100%;
        justify-content: center;
    }
}

/* ===== SIMPLE ENHANCED DISCOUNT SECTION ===== */

/* Discount Section */
.discount-section {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 12px;
    padding: 20px;
    margin: 15px 0;
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
}

.discount-section h4 {
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.discount-section h4 i {
    background: rgba(255, 255, 255, 0.2);
    padding: 6px;
    border-radius: 50%;
    font-size: 14px;
}

/* Discount Controls */
.discount-controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.discount-controls .form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-bottom: 18px;
    position: relative;
}

.discount-controls .form-group::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.discount-controls .form-group:hover::before {
    opacity: 1;
}

/* Input Container */
.input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.input-container input {
    flex: 1;
    padding-right: 70px !important;
}

.input-unit {
    position: absolute;
    right: 20px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 16px;
    font-weight: 700;
    pointer-events: none;
    background: rgba(255, 255, 255, 0.1);
    padding: 4px 8px;
    border-radius: 6px;
    backdrop-filter: blur(5px);
}

.discount-controls .form-group label {
    font-size: 16px;
    font-weight: 700;
    color: rgba(255, 255, 255, 0.95);
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.discount-controls .form-group input,
.input-container input {
    background: rgba(255, 255, 255, 0.15);
    border: 3px solid rgba(255, 255, 255, 0.4);
    border-radius: 12px;
    padding: 20px 25px;
    color: white;
    font-size: 22px;
    font-weight: 700;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    min-height: 65px;
    text-align: center;
    width: 100%;
    box-sizing: border-box;
}

.discount-controls .form-group input::placeholder,
.input-container input::placeholder {
    color: rgba(255, 255, 255, 0.7);
    font-size: 18px;
    font-weight: 600;
}

.discount-controls .form-group input:focus,
.input-container input:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.9);
    background: rgba(255, 255, 255, 0.3);
    box-shadow: 0 0 0 5px rgba(255, 255, 255, 0.2);
    transform: scale(1.03);
}

/* Discount Buttons */
.discount-buttons {
    display: flex;
    gap: 10px;
    margin-top: 5px;
}

.discount-buttons .btn {
    flex: 1;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

.discount-buttons .btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
}

.discount-buttons .btn-info:hover {
    background: linear-gradient(135deg, #17a2b8, #138496);
    border-color: #17a2b8;
}

.discount-buttons .btn-warning:hover {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    border-color: #ffc107;
}

/* Responsive Design */
@media (max-width: 768px) {
    .discount-section {
        padding: 18px;
        margin: 12px 0;
    }

    .discount-controls .form-group input {
        padding: 18px 20px;
        font-size: 20px;
        min-height: 60px;
    }

    .discount-controls .form-group label {
        font-size: 15px;
        margin-bottom: 8px;
    }

    .discount-buttons {
        flex-direction: column;
        gap: 10px;
        margin-top: 15px;
    }

    .discount-buttons .btn {
        width: 100%;
        padding: 12px 16px;
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .discount-controls .form-group input {
        padding: 16px 18px;
        font-size: 18px;
        min-height: 55px;
    }

    .discount-controls .form-group input::placeholder {
        font-size: 16px;
    }
}

/* Enhanced Product Info */
.product-info {
    position: relative;
    z-index: 1;
}

.product-availability {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #27ae60;
    box-shadow: 0 0 8px rgba(39, 174, 96, 0.5);
}

.product-availability.low-stock {
    background: #f39c12;
    box-shadow: 0 0 8px rgba(243, 156, 18, 0.5);
}

.product-availability.out-of-stock {
    background: #e74c3c;
    box-shadow: 0 0 8px rgba(231, 76, 60, 0.5);
}

.product-stock {
    position: absolute;
    top: 5px;
    left: 5px;
    background: #e74c3c;
    color: white;
    padding: 0.2rem 0.4rem;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: 600;
}

.product-stock.in-stock {
    background: #27ae60;
}

/* Cart Items */
.cart-items {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    max-height: 400px;
}

.empty-cart {
    text-align: center;
    color: #7f8c8d;
    padding: 2rem;
}

.empty-cart i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.cart-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid #ecf0f1;
    border-radius: 10px;
    margin-bottom: 0.5rem;
    background: #f8f9fa;
}

.cart-item-image {
    width: 50px;
    height: 50px;
    background: #ecf0f1;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.cart-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.cart-item-info {
    flex: 1;
}

.cart-item-name {
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 0.2rem;
}

.cart-item-price {
    color: #27ae60;
    font-weight: 600;
    font-size: 0.8rem;
}

.cart-item-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.qty-btn {
    width: 30px;
    height: 30px;
    border: 1px solid #dee2e6;
    background: white;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.qty-btn:hover {
    background: #3498db;
    color: white;
    border-color: #3498db;
}

.qty-input {
    width: 50px;
    text-align: center;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 0.3rem;
    font-weight: 600;
}

.remove-btn {
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 0.3rem 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.remove-btn:hover {
    background: #c0392b;
}

/* Cart Summary */
.cart-summary {
    padding: 1rem;
    border-top: 2px solid #ecf0f1;
    background: #f8f9fa;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.summary-row.total {
    font-size: 1.1rem;
    font-weight: 700;
    color: #2c3e50;
    border-top: 2px solid #dee2e6;
    padding-top: 0.5rem;
    margin-top: 0.5rem;
}

/* Payment Panel */
.payment-panel {
    padding: 0;
}

.sale-type {
    display: flex;
    gap: 1rem;
}

.sale-type label {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 600;
}

.customer-section,
.discount-section,
.payment-methods {
    padding: 1rem;
    border-bottom: 1px solid #ecf0f1;
}

.customer-section h4,
.discount-section h4,
.payment-methods h4 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    font-size: 1rem;
    color: #2c3e50;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group input {
    width: 100%;
    padding: 0.8rem;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.discount-controls {
    display: grid;
    grid-template-columns: 1fr 1fr auto;
    gap: 0.5rem;
    align-items: end;
}

.payment-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
}

.payment-btn {
    padding: 1rem;
    border: 2px solid #ecf0f1;
    background: white;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.3rem;
    font-size: 0.8rem;
    font-weight: 600;
}

.payment-btn.active,
.payment-btn:hover {
    background: #3498db;
    color: white;
    border-color: #3498db;
}

.payment-btn i {
    font-size: 1.2rem;
}

.payment-amount {
    padding: 1rem;
}

.change-amount {
    display: flex;
    justify-content: space-between;
    margin-top: 1rem;
    padding: 0.8rem;
    background: #e8f5e8;
    border-radius: 8px;
    font-weight: 600;
    color: #27ae60;
}

.action-buttons {
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

/* Buttons */
.btn {
    padding: 0.6rem 1.2rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    font-family: 'Cairo', sans-serif;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    font-size: 0.9rem;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.btn-success {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, #f39c12, #f1c40f);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.btn-secondary {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
    color: white;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
}

.btn-sm {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
}

.btn-large {
    padding: 1rem 1.5rem;
    font-size: 1rem;
    font-weight: 700;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

/* Quick Actions */
.quick-actions {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 1rem;
    background: white;
    padding: 1rem;
    border-radius: 50px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    z-index: 1000;
}

.quick-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.3rem;
    padding: 1rem;
    background: #f8f9fa;
    border: 2px solid #ecf0f1;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 80px;
}

.quick-btn:hover {
    background: #3498db;
    color: white;
    border-color: #3498db;
    transform: translateY(-2px);
}

.quick-btn i {
    font-size: 1.2rem;
}

.quick-btn span {
    font-size: 0.7rem;
    font-weight: 600;
    text-align: center;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .cashier-container {
        grid-template-columns: 1fr 350px 300px;
    }
}

@media (max-width: 768px) {
    .cashier-container {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }
    
    .header-info {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .quick-actions {
        position: relative;
        transform: none;
        left: auto;
        bottom: auto;
        margin: 1rem;
        justify-content: center;
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modal-overlay.show {
    opacity: 1;
}

.modal {
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    transform: scale(0.8);
    transition: transform 0.3s ease;
}

.modal-overlay.show .modal {
    transform: scale(1);
}

.modal-header {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
    padding: 1.5rem;
    border-radius: 15px 15px 0 0;
}

.modal-header h3 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    padding: 1rem 2rem 2rem;
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Sale Complete Modal */
.sale-complete-modal .modal-header {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
}

.sale-summary {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.8rem 0;
    border-bottom: 1px solid #dee2e6;
    font-size: 1rem;
}

.summary-item:last-child {
    border-bottom: none;
}

.invoice-number {
    font-weight: 700;
    color: #3498db;
    font-size: 1.1rem;
}

.total-amount {
    font-weight: 700;
    color: #27ae60;
    font-size: 1.2rem;
}

.change-amount {
    font-weight: 700;
    color: #e74c3c;
    font-size: 1.1rem;
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.8rem;
    z-index: 10001;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    min-width: 300px;
    border-left: 4px solid #3498db;
}

.notification.show {
    transform: translateX(0);
}

.notification-success {
    border-left-color: #27ae60;
    background: linear-gradient(135deg, #d5f4e6, #e8f5e8);
}

.notification-error {
    border-left-color: #e74c3c;
    background: linear-gradient(135deg, #fdf2f2, #fef5f5);
}

.notification-warning {
    border-left-color: #f39c12;
    background: linear-gradient(135deg, #fff8e1, #fffbf0);
}

.notification-info {
    border-left-color: #3498db;
    background: linear-gradient(135deg, #e3f2fd, #f0f8ff);
}

.notification i {
    font-size: 1.2rem;
}

.notification-success i {
    color: #27ae60;
}

.notification-error i {
    color: #e74c3c;
}

.notification-warning i {
    color: #f39c12;
}

.notification-info i {
    color: #3498db;
}

.notification span {
    font-weight: 600;
    color: #2c3e50;
}

/* No Products Message */
.no-products {
    grid-column: 1 / -1;
    text-align: center;
    padding: 4rem 2rem;
    color: #7f8c8d;
    background: linear-gradient(145deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
    border-radius: 20px;
    margin: 20px;
    border: 2px dashed rgba(102, 126, 234, 0.2);
}

.no-products i {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    opacity: 0.6;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.no-products p {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #2c3e50;
}

.no-products small {
    font-size: 1rem;
    color: #667eea;
    font-weight: 500;
}

/* Loading Animation for Products */
.products-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 3rem;
    grid-column: 1 / -1;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(102, 126, 234, 0.2);
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Product Card Loading State */
.product-card.loading {
    pointer-events: none;
    opacity: 0.7;
    position: relative;
}

.product-card.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
    animation: loading 1.5s infinite;
}

/* Enhanced Product Grid Animation */
.products-grid .product-card {
    animation: fadeInUp 0.5s ease-out;
    animation-fill-mode: both;
}

.products-grid .product-card:nth-child(1) { animation-delay: 0.1s; }
.products-grid .product-card:nth-child(2) { animation-delay: 0.2s; }
.products-grid .product-card:nth-child(3) { animation-delay: 0.3s; }
.products-grid .product-card:nth-child(4) { animation-delay: 0.4s; }
.products-grid .product-card:nth-child(5) { animation-delay: 0.5s; }
.products-grid .product-card:nth-child(6) { animation-delay: 0.6s; }

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Print Styles */
@media print {
    body * {
        visibility: hidden;
    }

    .print-area,
    .print-area * {
        visibility: visible;
    }

    .print-area {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
    }

    .no-print {
        display: none !important;
    }
}

/* Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Animations */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes bounceIn {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }
    50% {
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.cart-item {
    animation: slideInRight 0.3s ease;
}

.product-card:hover {
    animation: bounceIn 0.3s ease;
}

/* Accessibility Improvements */
.btn:focus,
.form-group input:focus,
.qty-input:focus {
    outline: 3px solid rgba(52, 152, 219, 0.3);
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .product-card {
        border-width: 3px;
    }

    .btn {
        border: 2px solid currentColor;
    }

    .notification {
        border-width: 3px;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Reports Modal Styles */
.reports-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.report-card {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 10px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border-left: 4px solid #3498db;
    transition: all 0.3s ease;
}

.report-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.report-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.report-info h4 {
    margin: 0 0 0.5rem 0;
    color: #2c3e50;
    font-size: 1rem;
    font-weight: 600;
}

.report-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #27ae60;
    margin-bottom: 0.2rem;
}

.report-count {
    font-size: 0.9rem;
    color: #7f8c8d;
}

.reports-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Held Sales Modal */
.held-sales-list,
.last-sales-list {
    max-height: 400px;
    overflow-y: auto;
}

.held-sale-item,
.last-sale-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.held-sale-item:hover,
.last-sale-item:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.held-sale-info,
.last-sale-info {
    flex: 1;
}

.held-sale-id,
.last-sale-invoice {
    font-weight: 700;
    color: #3498db;
    font-size: 1rem;
    margin-bottom: 0.3rem;
}

.held-sale-time,
.last-sale-time {
    font-size: 0.8rem;
    color: #7f8c8d;
    margin-bottom: 0.3rem;
}

.held-sale-items,
.last-sale-customer {
    font-size: 0.9rem;
    color: #2c3e50;
    margin-bottom: 0.3rem;
}

.held-sale-total,
.last-sale-total {
    font-weight: 600;
    color: #27ae60;
    font-size: 1rem;
}

.held-sale-actions,
.last-sale-actions {
    display: flex;
    gap: 0.5rem;
}

/* Coupons Modal */
.coupons-list {
    max-height: 400px;
    overflow-y: auto;
}

.coupon-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border: 2px solid #dee2e6;
    border-radius: 10px;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, #fff8e1, #fffbf0);
    transition: all 0.3s ease;
}

.coupon-item:hover {
    border-color: #f39c12;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(243, 156, 18, 0.2);
}

.coupon-info {
    flex: 1;
}

.coupon-code {
    font-weight: 700;
    color: #f39c12;
    font-size: 1.1rem;
    margin-bottom: 0.3rem;
    font-family: 'Courier New', monospace;
    background: white;
    padding: 0.2rem 0.5rem;
    border-radius: 5px;
    display: inline-block;
}

.coupon-discount {
    font-weight: 600;
    color: #27ae60;
    font-size: 1rem;
    margin-bottom: 0.3rem;
}

.coupon-desc {
    font-size: 0.9rem;
    color: #7f8c8d;
}

.no-coupons {
    text-align: center;
    padding: 3rem;
    color: #7f8c8d;
}

.no-coupons i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Enhanced Modal Animations */
.modal {
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        transform: scale(0.8) translateY(-50px);
        opacity: 0;
    }
    to {
        transform: scale(1) translateY(0);
        opacity: 1;
    }
}

/* Responsive Modal Design */
@media (max-width: 768px) {
    .modal {
        width: 95%;
        margin: 1rem;
    }

    .reports-summary {
        grid-template-columns: 1fr;
    }

    .report-card {
        padding: 1rem;
    }

    .report-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .report-value {
        font-size: 1.2rem;
    }

    .held-sale-item,
    .last-sale-item,
    .coupon-item {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .held-sale-actions,
    .last-sale-actions {
        justify-content: center;
    }

    .modal-footer {
        flex-direction: column;
    }

    .modal-footer .btn {
        width: 100%;
    }
}

/* Print Receipt Styles */
.receipt-preview {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
    max-width: 300px;
    margin: 0 auto;
    background: white;
    padding: 1rem;
    border: 1px solid #dee2e6;
    border-radius: 5px;
}

.receipt-header {
    text-align: center;
    border-bottom: 1px dashed #333;
    padding-bottom: 0.5rem;
    margin-bottom: 0.5rem;
}

.receipt-items {
    margin: 0.5rem 0;
}

.receipt-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.2rem;
}

.receipt-totals {
    border-top: 1px dashed #333;
    padding-top: 0.5rem;
    margin-top: 0.5rem;
}

.receipt-footer {
    text-align: center;
    margin-top: 0.5rem;
    font-size: 10px;
    color: #666;
}

/* Status Indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;
    padding: 0.3rem 0.6rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-paid {
    background: #d5f4e6;
    color: #27ae60;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-cancelled {
    background: #f8d7da;
    color: #721c24;
}

/* Enhanced Scrollbars */
.held-sales-list::-webkit-scrollbar,
.last-sales-list::-webkit-scrollbar,
.coupons-list::-webkit-scrollbar {
    width: 8px;
}

.held-sales-list::-webkit-scrollbar-track,
.last-sales-list::-webkit-scrollbar-track,
.coupons-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.held-sales-list::-webkit-scrollbar-thumb,
.last-sales-list::-webkit-scrollbar-thumb,
.coupons-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 10px;
}

.held-sales-list::-webkit-scrollbar-thumb:hover,
.last-sales-list::-webkit-scrollbar-thumb:hover,
.coupons-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Calculator Styles */
.calculator-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    animation: fadeIn 0.3s ease;
}

.calculator-container {
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    border-radius: 20px;
    padding: 18px;
    box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
    max-width: 350px;
    width: 85%;
    max-height: 80vh;
    overflow-y: auto;
    border: 2px solid #667eea;
}

.calculator-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 12px;
    border-bottom: 2px solid #667eea;
}

.calculator-header h3 {
    color: #2c3e50;
    font-size: 1.2rem;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 700;
}

.close-calculator {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    border: none;
    border-radius: 12px;
    width: 30px;
    height: 30px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 0.85rem;
}

.close-calculator:hover {
    background: linear-gradient(135deg, #ee5a52, #ff6b6b);
    transform: scale(1.1);
}

.calculator-display {
    background: linear-gradient(145deg, #2c3e50, #34495e);
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 15px;
    border: 2px solid #667eea;
    box-shadow: inset 0 3px 10px rgba(0, 0, 0, 0.2);
}

.display-history {
    color: #95a5a6;
    font-size: 0.9rem;
    min-height: 20px;
    text-align: right;
    margin-bottom: 5px;
    font-family: 'Courier New', monospace;
}

.display-current {
    color: #ecf0f1;
    font-size: 1.8rem;
    font-weight: bold;
    text-align: right;
    font-family: 'Courier New', monospace;
    word-break: break-all;
    min-height: 50px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.memory-functions {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 6px;
    margin-bottom: 12px;
}

.memory-btn {
    background: linear-gradient(145deg, #8e44ad, #9b59b6);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 6px;
    font-size: 0.7rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 6px rgba(142, 68, 173, 0.3);
}

.memory-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 12px rgba(142, 68, 173, 0.4);
}

.memory-btn:active {
    transform: translateY(0);
}

.calculator-buttons {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 8px;
    margin-bottom: 15px;
}

.calc-btn {
    border: none;
    border-radius: 10px;
    padding: 12px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
}

.calc-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

.calc-btn:active::before {
    width: 100px;
    height: 100px;
}

.number-btn {
    background: linear-gradient(145deg, #34495e, #2c3e50);
    color: #ecf0f1;
}

.number-btn:hover {
    background: linear-gradient(145deg, #3c5a78, #34495e);
    transform: translateY(-2px);
}

.operator-btn {
    background: linear-gradient(145deg, #e67e22, #d35400);
    color: white;
}

.operator-btn:hover {
    background: linear-gradient(145deg, #f39c12, #e67e22);
    transform: translateY(-2px);
}

.function-btn {
    background: linear-gradient(145deg, #95a5a6, #7f8c8d);
    color: white;
}

.function-btn:hover {
    background: linear-gradient(145deg, #bdc3c7, #95a5a6);
    transform: translateY(-2px);
}

.scientific-btn {
    background: linear-gradient(145deg, #3498db, #2980b9);
    color: white;
}

.scientific-btn:hover {
    background: linear-gradient(145deg, #5dade2, #3498db);
    transform: translateY(-2px);
}

.equals-btn {
    background: linear-gradient(145deg, #27ae60, #229954);
    color: white;
    grid-column: span 2;
}

.equals-btn:hover {
    background: linear-gradient(145deg, #2ecc71, #27ae60);
    transform: translateY(-2px);
}

.zero-btn {
    grid-column: span 2;
}



.calculation-history {
    background: rgba(52, 73, 94, 0.3);
    border-radius: 15px;
    padding: 15px;
    border: 1px solid #3498db;
    max-height: 150px;
}

.calculation-history h4 {
    color: #ecf0f1;
    margin-bottom: 10px;
    text-align: center;
    font-size: 1rem;
}

.history-list {
    max-height: 120px;
    overflow-y: auto;
    margin-bottom: 10px;
}

.history-item {
    background: rgba(26, 37, 47, 0.5);
    padding: 8px;
    margin-bottom: 5px;
    border-radius: 8px;
    color: #bdc3c7;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.history-item:hover {
    background: rgba(52, 152, 219, 0.3);
    color: #ecf0f1;
}

.clear-history-btn {
    background: linear-gradient(145deg, #e74c3c, #c0392b);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 8px 15px;
    font-size: 0.9rem;
    cursor: pointer;
    width: 100%;
    transition: all 0.2s ease;
}

.clear-history-btn:hover {
    background: linear-gradient(145deg, #ec7063, #e74c3c);
}

/* Calculator Responsive Design */
@media (max-width: 768px) {
    .calculator-container {
        width: 95%;
        padding: 12px;
        max-height: 90vh;
        max-width: 320px;
    }

    .calculator-buttons {
        gap: 6px;
    }

    .calc-btn {
        padding: 10px;
        font-size: 0.9rem;
    }

    .display-current {
        font-size: 1.5rem;
    }

    .calculator-header {
        padding: 12px;
    }

    .calculator-header h3 {
        font-size: 1.1rem;
    }

    .close-calculator {
        width: 26px;
        height: 26px;
        font-size: 0.8rem;
    }

    .calculator-display {
        padding: 12px;
        margin-bottom: 12px;
    }

    .memory-functions {
        gap: 4px;
        margin-bottom: 10px;
    }

    .memory-btn {
        padding: 5px;
        font-size: 0.65rem;
    }

    .currency-buttons {
        grid-template-columns: 1fr;
    }

    .memory-functions {
        grid-template-columns: repeat(3, 1fr);
    }

    .calculator-header h3 {
        font-size: 1.2rem;
    }
}

/* Calculator Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Calculator Button Hover Effects */
.calc-btn:hover {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

.calc-btn:active {
    transform: translateY(1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Memory Button Active State */
.memory-btn.active {
    background: linear-gradient(145deg, #9b59b6, #8e44ad);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Calculator Display Glow Effect */
.calculator-display {
    position: relative;
}

.calculator-display::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #3498db, #2980b9, #3498db);
    border-radius: 17px;
    z-index: -1;
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from {
        box-shadow: 0 0 5px rgba(52, 152, 219, 0.5);
    }
    to {
        box-shadow: 0 0 20px rgba(52, 152, 219, 0.8);
    }
}

/* Enhanced Scrollbar for History */
.history-list::-webkit-scrollbar {
    width: 6px;
}

.history-list::-webkit-scrollbar-track {
    background: rgba(52, 73, 94, 0.3);
    border-radius: 3px;
}

.history-list::-webkit-scrollbar-thumb {
    background: rgba(52, 152, 219, 0.6);
    border-radius: 3px;
}

.history-list::-webkit-scrollbar-thumb:hover {
    background: rgba(52, 152, 219, 0.8);
}

/* Calculator Modal Backdrop */
.calculator-modal::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(52, 152, 219, 0.1) 0%, rgba(0, 0, 0, 0.8) 100%);
    z-index: -1;
}

/* Enhanced Button Ripple Effect */
@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

.calc-btn {
    position: relative;
    overflow: hidden;
}

.calc-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%);
    transform-origin: 50% 50%;
}

.calc-btn:focus:not(:active)::after {
    animation: ripple 1s ease-out;
}

/* Calculator Theme Variations */
.calculator-container.dark-theme {
    background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
    border-color: #444;
}

.calculator-container.light-theme {
    background: linear-gradient(145deg, #f8f9fa, #e9ecef);
    color: #333;
}

.calculator-container.neon-theme {
    background: linear-gradient(145deg, #0a0a0a, #1a1a1a);
    border: 2px solid #00ff88;
    box-shadow: 0 0 30px rgba(0, 255, 136, 0.3);
}

/* Settings Modal Styles */
.settings-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    animation: fadeIn 0.3s ease;
}

.settings-container {
    background: linear-gradient(145deg, #2c3e50, #34495e);
    border-radius: 20px;
    padding: 0;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    max-width: 900px;
    width: 95%;
    max-height: 90vh;
    overflow: hidden;
    border: 2px solid #3498db;
    display: flex;
    flex-direction: column;
}

.settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 2px solid #3498db;
    background: rgba(52, 152, 219, 0.1);
}

.settings-header h3 {
    color: #ecf0f1;
    font-size: 1.4rem;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.close-settings {
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.close-settings:hover {
    background: #c0392b;
    transform: scale(1.1);
}

.settings-body {
    flex: 1;
    overflow-y: auto;
    padding: 0;
}

/* Settings Tabs */
.settings-tabs {
    display: flex;
    background: rgba(52, 73, 94, 0.3);
    border-bottom: 1px solid #34495e;
    overflow-x: auto;
}

.settings-tab {
    background: transparent;
    color: #bdc3c7;
    border: none;
    padding: 15px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    font-size: 0.9rem;
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 120px;
    justify-content: center;
}

.settings-tab:hover {
    background: rgba(52, 152, 219, 0.2);
    color: #ecf0f1;
}

.settings-tab.active {
    background: #3498db;
    color: white;
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

/* Settings Panels */
.settings-panel {
    display: none;
    padding: 25px;
    animation: fadeIn 0.3s ease;
}

.settings-panel.active {
    display: block;
}

.settings-panel h4 {
    color: #ecf0f1;
    font-size: 1.2rem;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #34495e;
}

/* Setting Groups */
.setting-group {
    margin-bottom: 20px;
    background: rgba(52, 73, 94, 0.2);
    padding: 15px;
    border-radius: 10px;
    border: 1px solid #34495e;
}

.setting-group label {
    display: block;
    color: #ecf0f1;
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 0.95rem;
}

.setting-group input,
.setting-group select,
.setting-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #34495e;
    border-radius: 8px;
    background: #2c3e50;
    color: #ecf0f1;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.setting-group input:focus,
.setting-group select:focus,
.setting-group textarea:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.setting-group textarea {
    resize: vertical;
    min-height: 60px;
}

.setting-group small {
    display: block;
    color: #95a5a6;
    font-size: 0.8rem;
    margin-top: 5px;
    line-height: 1.4;
}

/* Custom Checkbox */
.checkbox-label {
    display: flex !important;
    align-items: center;
    cursor: pointer;
    margin-bottom: 0 !important;
    font-weight: 600 !important;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    background: #2c3e50;
    border: 2px solid #34495e;
    border-radius: 4px;
    margin-left: 10px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: #3498db;
    border-color: #3498db;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '';
    position: absolute;
    left: 6px;
    top: 2px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* Backup Actions */
.backup-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    margin: 20px 0;
}

.backup-actions .btn {
    padding: 12px 16px;
    font-size: 0.9rem;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

/* Backup Info */
.backup-info {
    background: rgba(26, 37, 47, 0.5);
    border-radius: 10px;
    padding: 15px;
    margin-top: 20px;
}

.backup-info h5 {
    color: #ecf0f1;
    margin-bottom: 15px;
    font-size: 1rem;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #34495e;
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    color: #bdc3c7;
    font-size: 0.9rem;
}

.info-value {
    color: #3498db;
    font-weight: 600;
    font-size: 0.9rem;
}

/* Settings Actions */
.settings-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 10px;
    padding: 20px 25px;
    background: rgba(52, 73, 94, 0.3);
    border-top: 1px solid #34495e;
}

.settings-actions .btn {
    padding: 12px 16px;
    font-size: 0.9rem;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.settings-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Settings Responsive Design */
@media (max-width: 768px) {
    .settings-container {
        width: 98%;
        max-height: 95vh;
    }

    .settings-tabs {
        flex-wrap: wrap;
    }

    .settings-tab {
        min-width: 100px;
        padding: 12px 15px;
        font-size: 0.8rem;
    }

    .settings-panel {
        padding: 15px;
    }

    .settings-actions {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .backup-actions {
        grid-template-columns: 1fr;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }
}

/* Settings Animation */
@keyframes settingsSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.settings-panel.active {
    animation: settingsSlideIn 0.3s ease;
}

/* Settings Theme Support */
.settings-container.dark-theme {
    background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
}

.settings-container.light-theme {
    background: linear-gradient(145deg, #f8f9fa, #e9ecef);
    color: #333;
}

.settings-container.light-theme .settings-header h3,
.settings-container.light-theme .settings-panel h4,
.settings-container.light-theme .setting-group label {
    color: #333;
}

.settings-container.light-theme .setting-group input,
.settings-container.light-theme .setting-group select,
.settings-container.light-theme .setting-group textarea {
    background: white;
    color: #333;
    border-color: #ddd;
}

/* Settings Success/Error States */
.setting-group.success {
    border-color: #27ae60;
    background: rgba(39, 174, 96, 0.1);
}

.setting-group.error {
    border-color: #e74c3c;
    background: rgba(231, 76, 60, 0.1);
}

.setting-group.success::before {
    content: '✓';
    position: absolute;
    top: 10px;
    right: 10px;
    color: #27ae60;
    font-weight: bold;
}

.setting-group.error::before {
    content: '✗';
    position: absolute;
    top: 10px;
    right: 10px;
    color: #e74c3c;
    font-weight: bold;
}

/* Barcode Scanner Styles */
.barcode-section {
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    border-radius: 20px;
    padding: 25px;
    margin: 20px 0;
    box-shadow:
        0 10px 30px rgba(102, 126, 234, 0.15),
        0 1px 8px rgba(102, 126, 234, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(102, 126, 234, 0.2);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.barcode-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
    background-size: 200% 100%;
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { background-position: 200% 0; }
    50% { background-position: -200% 0; }
}

.barcode-input-container {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    border-radius: 25px;
    padding: 12px 25px;
    box-shadow:
        inset 0 2px 10px rgba(102, 126, 234, 0.08),
        0 4px 15px rgba(102, 126, 234, 0.1);
    border: 2px solid transparent;
    background-clip: padding-box;
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.barcode-input-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: inherit;
    z-index: -1;
    margin: -2px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.barcode-input-container:focus-within::before {
    opacity: 1;
}

.barcode-input-container:focus-within {
    transform: translateY(-2px);
    box-shadow:
        inset 0 2px 10px rgba(102, 126, 234, 0.15),
        0 8px 25px rgba(102, 126, 234, 0.2);
}

.barcode-input-container i {
    color: #667eea;
    font-size: 1.4rem;
    min-width: 28px;
    text-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
    transition: all 0.3s ease;
}

.barcode-input-container:focus-within i {
    color: #764ba2;
    transform: scale(1.1);
}

.barcode-input-container input {
    flex: 1;
    padding: 15px 0;
    border: none;
    background: transparent;
    font-size: 1.1rem;
    color: #2c3e50;
    font-weight: 500;
    transition: all 0.3s ease;
    font-family: 'Cairo', sans-serif;
}

.barcode-input-container input:focus {
    outline: none;
    color: #667eea;
    font-weight: 600;
}

.barcode-input-container input::placeholder {
    color: #95a5a6;
    font-weight: 400;
    transition: color 0.3s ease;
}

.barcode-input-container:focus-within input::placeholder {
    color: #bdc3c7;
}

.btn-scan, .btn-manual {
    background: linear-gradient(145deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 18px;
    width: 55px;
    height: 55px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3rem;
    box-shadow:
        0 6px 20px rgba(102, 126, 234, 0.3),
        0 2px 8px rgba(102, 126, 234, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.btn-scan::before, .btn-manual::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.btn-scan:hover::before, .btn-manual:hover::before {
    left: 100%;
}

.btn-scan:hover, .btn-manual:hover {
    background: linear-gradient(145deg, #764ba2, #667eea);
    transform: translateY(-4px) scale(1.08);
    box-shadow:
        0 12px 35px rgba(102, 126, 234, 0.4),
        0 4px 15px rgba(102, 126, 234, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.btn-scan:active, .btn-manual:active {
    transform: translateY(-2px) scale(1.02);
    transition: all 0.1s ease;
}

/* Button specific styles */
.btn-scan {
    background: linear-gradient(145deg, #667eea, #4facfe);
}

.btn-scan:hover {
    background: linear-gradient(145deg, #4facfe, #667eea);
}

.btn-manual {
    background: linear-gradient(145deg, #764ba2, #667eea);
}

.btn-manual:hover {
    background: linear-gradient(145deg, #667eea, #764ba2);
}

.barcode-status {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 25px;
    background: linear-gradient(145deg, rgba(102, 126, 234, 0.08), rgba(118, 75, 162, 0.08));
    border-radius: 20px;
    font-size: 1rem;
    border: 1px solid rgba(102, 126, 234, 0.15);
    backdrop-filter: blur(15px);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.barcode-status::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.barcode-status.active::before {
    transform: translateX(100%);
}

.status-text {
    color: #667eea;
    font-weight: 600;
    text-shadow: 0 1px 3px rgba(102, 126, 234, 0.2);
    font-family: 'Cairo', sans-serif;
    display: flex;
    align-items: center;
    gap: 10px;
}

.status-text::before {
    content: '';
    width: 8px;
    height: 8px;
    background: currentColor;
    border-radius: 50%;
    opacity: 0.6;
}

.status-indicator {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #27ae60;
    animation: pulse 2s infinite;
    box-shadow:
        0 0 15px rgba(39, 174, 96, 0.6),
        0 0 30px rgba(39, 174, 96, 0.3);
    position: relative;
    border: 2px solid rgba(255, 255, 255, 0.8);
}

.status-indicator::after {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border-radius: 50%;
    background: inherit;
    opacity: 0.2;
    animation: inherit;
    z-index: -1;
}

.status-indicator.scanning {
    background: #f39c12;
    animation: scanningPulse 1.2s infinite;
    box-shadow:
        0 0 15px rgba(243, 156, 18, 0.6),
        0 0 30px rgba(243, 156, 18, 0.3);
}

.status-indicator.error {
    background: #e74c3c;
    animation: errorShake 0.5s ease-in-out;
    box-shadow:
        0 0 15px rgba(231, 76, 60, 0.6),
        0 0 30px rgba(231, 76, 60, 0.3);
}

.status-indicator.success {
    background: #27ae60;
    animation: successBurst 0.8s ease-out;
    box-shadow:
        0 0 20px rgba(39, 174, 96, 0.8),
        0 0 40px rgba(39, 174, 96, 0.4);
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

@keyframes scanningPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.7;
    }
}

@keyframes errorShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-3px); }
    75% { transform: translateX(3px); }
}

@keyframes successBurst {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.4);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Barcode Scanner Modal */
.barcode-scanner-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    animation: fadeIn 0.3s ease;
}

.scanner-container {
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    border-radius: 25px;
    padding: 0;
    max-width: 550px;
    width: 90%;
    max-height: 85vh;
    overflow: hidden;
    border: 3px solid transparent;
    background-clip: padding-box;
    box-shadow:
        0 25px 50px rgba(102, 126, 234, 0.25),
        0 8px 25px rgba(102, 126, 234, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    position: relative;
    backdrop-filter: blur(15px);
}

.scanner-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #667eea, #764ba2, #667eea);
    background-size: 200% 200%;
    z-index: -1;
    margin: -3px;
    border-radius: inherit;
    animation: gradientShift 4s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.scanner-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    border-bottom: 1px solid rgba(102, 126, 234, 0.2);
    backdrop-filter: blur(15px);
    position: relative;
}

.scanner-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 10%;
    right: 10%;
    height: 2px;
    background: linear-gradient(90deg, transparent, #667eea, #764ba2, #667eea, transparent);
    border-radius: 2px;
}

.scanner-header h3 {
    color: #2c3e50;
    font-size: 1.2rem;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(102, 126, 234, 0.2);
}

.scanner-header h3 i {
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 1.3rem;
}

.close-scanner {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    border: none;
    border-radius: 12px;
    width: 32px;
    height: 32px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 3px 12px rgba(255, 107, 107, 0.3);
    position: relative;
    overflow: hidden;
    font-size: 0.9rem;
}

.close-scanner::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.close-scanner:hover::before {
    left: 100%;
}

.close-scanner:hover {
    background: linear-gradient(135deg, #ee5a52, #ff6b6b);
    transform: scale(1.1) rotate(90deg);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

.scanner-body {
    padding: 20px;
}

/* Camera Container */
.camera-container {
    position: relative;
    background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
    border-radius: 15px;
    overflow: hidden;
    margin-bottom: 20px;
    aspect-ratio: 4/3;
    max-height: 250px;
    box-shadow:
        inset 0 2px 8px rgba(0, 0, 0, 0.5),
        0 6px 20px rgba(102, 126, 234, 0.2);
    border: 2px solid rgba(102, 126, 234, 0.3);
}

#barcodeVideo {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.scanner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.scanner-frame {
    position: relative;
    width: 220px;
    height: 140px;
    border: 2px solid transparent;
    border-radius: 12px;
    background: linear-gradient(45deg, #667eea, #764ba2, #667eea, #764ba2);
    background-size: 400% 400%;
    animation: borderGlow 3s ease-in-out infinite;
    padding: 2px;
}

.scanner-frame::before {
    content: '';
    position: absolute;
    top: 3px;
    left: 3px;
    right: 3px;
    bottom: 3px;
    background: transparent;
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

@keyframes borderGlow {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.corner {
    position: absolute;
    width: 20px;
    height: 20px;
    border: 3px solid #667eea;
    z-index: 2;
}

.corner.top-left {
    top: -4px;
    left: -4px;
    border-right: none;
    border-bottom: none;
    border-radius: 8px 0 0 0;
}

.corner.top-right {
    top: -4px;
    right: -4px;
    border-left: none;
    border-bottom: none;
    border-radius: 0 8px 0 0;
}

.corner.bottom-left {
    bottom: -4px;
    left: -4px;
    border-right: none;
    border-top: none;
    border-radius: 0 0 0 8px;
}

.corner.bottom-right {
    bottom: -4px;
    right: -4px;
    border-left: none;
    border-top: none;
    border-radius: 0 0 8px 0;
}

.scanner-line {
    position: absolute;
    top: 50%;
    left: 10px;
    right: 10px;
    height: 3px;
    background: linear-gradient(90deg, transparent, #667eea, #764ba2, #667eea, transparent);
    border-radius: 2px;
    animation: scanLine 2.5s ease-in-out infinite;
    box-shadow: 0 0 10px rgba(102, 126, 234, 0.6);
}

@keyframes scanLine {
    0% {
        transform: translateY(-90px);
        opacity: 0;
    }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% {
        transform: translateY(90px);
        opacity: 0;
    }
}

.scanner-instructions {
    margin-top: 15px;
    text-align: center;
    color: rgba(255, 255, 255, 0.9);
    background: rgba(102, 126, 234, 0.1);
    padding: 12px 15px;
    border-radius: 12px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.scanner-instructions p {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 5px;
    color: #2c3e50;
    text-shadow: 0 1px 3px rgba(102, 126, 234, 0.3);
}

.scanner-instructions small {
    font-size: 0.85rem;
    opacity: 0.8;
    color: #667eea;
    font-weight: 500;
}

/* Scanner Controls */
.scanner-controls {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(248, 249, 250, 0.8);
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

.scanner-controls .btn {
    padding: 10px 18px;
    border-radius: 15px;
    font-weight: 600;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 6px;
    border: none;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    font-size: 0.9rem;
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1);
}

.scanner-controls .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.scanner-controls .btn:hover::before {
    left: 100%;
}

.scanner-controls .btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.scanner-controls .btn-primary:hover {
    background: linear-gradient(135deg, #764ba2, #667eea);
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.scanner-controls .btn-secondary {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
}

.scanner-controls .btn-secondary:hover {
    background: linear-gradient(135deg, #e67e22, #f39c12);
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 25px rgba(243, 156, 18, 0.3);
}

.scanner-controls .btn-info {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.scanner-controls .btn-info:hover {
    background: linear-gradient(135deg, #2980b9, #3498db);
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
}

/* Manual Input Section */
.manual-input-section {
    background: linear-gradient(145deg, rgba(102, 126, 234, 0.08), rgba(118, 75, 162, 0.08));
    border-radius: 15px;
    padding: 18px;
    margin-bottom: 20px;
    border: 1px solid rgba(102, 126, 234, 0.2);
    backdrop-filter: blur(10px);
}

.manual-input-section h4 {
    color: #2c3e50;
    margin-bottom: 12px;
    font-size: 1rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 8px;
}

.manual-input-section h4::before {
    content: '⌨️';
    font-size: 1.3rem;
}

.manual-barcode-input {
    display: flex;
    gap: 12px;
    align-items: stretch;
}

.manual-barcode-input input {
    flex: 1;
    padding: 12px 15px;
    border: 2px solid rgba(102, 126, 234, 0.2);
    border-radius: 12px;
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    color: #2c3e50;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: inset 0 2px 6px rgba(102, 126, 234, 0.05);
}

.manual-barcode-input input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow:
        inset 0 2px 8px rgba(102, 126, 234, 0.1),
        0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

.manual-barcode-input .btn-success {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 12px 20px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.9rem;
    box-shadow: 0 3px 12px rgba(39, 174, 96, 0.3);
}

.manual-barcode-input .btn-success:hover {
    background: linear-gradient(135deg, #2ecc71, #27ae60);
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 25px rgba(39, 174, 96, 0.4);
}

/* Recent Scans */
.recent-scans {
    background: linear-gradient(145deg, rgba(102, 126, 234, 0.08), rgba(118, 75, 162, 0.08));
    border-radius: 15px;
    padding: 18px;
    border: 1px solid rgba(102, 126, 234, 0.2);
    backdrop-filter: blur(10px);
}

.recent-scans h4 {
    color: #2c3e50;
    margin-bottom: 12px;
    font-size: 1rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 8px;
}

.recent-scans h4::before {
    content: '📋';
    font-size: 1.3rem;
}

.scans-list {
    max-height: 120px;
    overflow-y: auto;
    padding-right: 5px;
}

.scans-list::-webkit-scrollbar {
    width: 6px;
}

.scans-list::-webkit-scrollbar-track {
    background: rgba(102, 126, 234, 0.1);
    border-radius: 3px;
}

.scans-list::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 3px;
}

.scan-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    border-radius: 10px;
    margin-bottom: 6px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(102, 126, 234, 0.1);
    position: relative;
    overflow: hidden;
}

.scan-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
    transition: left 0.5s ease;
}

.scan-item:hover::before {
    left: 100%;
}

.scan-item:hover {
    background: linear-gradient(145deg, #f8f9fa, #ffffff);
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.scan-item.success {
    border-left: 4px solid #27ae60;
    box-shadow: 0 2px 8px rgba(39, 174, 96, 0.2);
}

.scan-item.failed {
    border-left: 4px solid #e74c3c;
    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.2);
}

.scan-barcode {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #667eea;
    font-size: 0.95rem;
}

.scan-time {
    font-size: 0.85rem;
    color: #7f8c8d;
    font-weight: 500;
}

/* Barcode History Modal */
.barcode-history-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    animation: fadeIn 0.3s ease;
}

.history-container {
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    border-radius: 20px;
    padding: 0;
    max-width: 600px;
    width: 90%;
    max-height: 85vh;
    overflow: hidden;
    border: 2px solid #667eea;
    box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 18px 22px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    border-bottom: 1px solid rgba(102, 126, 234, 0.2);
}

.history-header h3 {
    color: #2c3e50;
    font-size: 1.2rem;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 700;
}

.close-history {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    border: none;
    border-radius: 12px;
    width: 32px;
    height: 32px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.close-history:hover {
    background: linear-gradient(135deg, #ee5a52, #ff6b6b);
    transform: scale(1.1);
}

.history-body {
    padding: 18px;
    overflow-y: auto;
    max-height: calc(85vh - 90px);
}

/* History Stats */
.history-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.stat-card {
    background: rgba(52, 73, 94, 0.3);
    padding: 15px;
    border-radius: 10px;
    text-align: center;
    border: 1px solid #34495e;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: bold;
    color: #3498db;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    color: #bdc3c7;
}

/* History Filters */
.history-filters {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    gap: 15px;
}

.history-filters select {
    padding: 8px 12px;
    border: 1px solid #34495e;
    border-radius: 8px;
    background: #2c3e50;
    color: #ecf0f1;
    font-size: 0.9rem;
}

/* History List */
.history-list {
    max-height: 400px;
    overflow-y: auto;
}

.history-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background: rgba(52, 73, 94, 0.3);
    border-radius: 10px;
    margin-bottom: 8px;
    border: 1px solid #34495e;
    transition: all 0.2s ease;
}

.history-item:hover {
    background: rgba(52, 152, 219, 0.2);
    border-color: #3498db;
}

.history-item.success {
    border-left: 4px solid #27ae60;
}

.history-item.failed {
    border-left: 4px solid #e74c3c;
}

.history-item-info {
    flex: 1;
}

.history-barcode {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #3498db;
    font-size: 1rem;
    margin-bottom: 3px;
}

.history-product {
    color: #ecf0f1;
    font-size: 0.9rem;
    margin-bottom: 3px;
}

.history-time {
    color: #95a5a6;
    font-size: 0.8rem;
}

.history-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.history-status.success {
    background: rgba(39, 174, 96, 0.2);
    color: #27ae60;
}

.history-status.failed {
    background: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
}

/* Responsive Design for Barcode */
@media (max-width: 768px) {
    .barcode-input-container {
        flex-wrap: wrap;
        padding: 10px 20px;
    }

    .btn-scan, .btn-manual {
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }

    .scanner-container,
    .history-container {
        width: 95%;
        max-height: 90vh;
        max-width: 450px;
    }

    .scanner-header {
        padding: 15px 20px;
    }

    .scanner-header h3 {
        font-size: 1.1rem;
    }

    .close-scanner {
        width: 28px;
        height: 28px;
        font-size: 0.8rem;
    }

    .camera-container {
        max-height: 200px;
        margin-bottom: 15px;
    }

    .scanner-frame {
        width: 180px;
        height: 110px;
    }

    .corner {
        width: 15px;
        height: 15px;
        border-width: 2px;
    }

    .scanner-controls {
        flex-wrap: wrap;
        gap: 8px;
        padding: 12px;
    }

    .scanner-controls .btn {
        padding: 8px 12px;
        font-size: 0.8rem;
        gap: 4px;
    }

    .manual-barcode-input {
        flex-direction: column;
        gap: 10px;
    }

    .manual-input-section,
    .recent-scans {
        padding: 15px;
    }

    .scans-list {
        max-height: 100px;
    }

    .scan-item {
        padding: 6px 10px;
        font-size: 0.85rem;
    }

    .history-stats {
        grid-template-columns: 1fr;
    }

    .history-filters {
        flex-direction: column;
        align-items: stretch;
    }
}

/* Enhanced Barcode Effects */

/* Particle Effects */
@keyframes particlesuccess {
    0% {
        transform: translate(-50%, -50%) scale(0) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translate(calc(-50% + 50px), calc(-50% - 50px)) scale(1) rotate(180deg);
        opacity: 0;
    }
}

@keyframes particleerror {
    0% {
        transform: translate(-50%, -50%) scale(0) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translate(calc(-50% + 30px), calc(-50% - 30px)) scale(1) rotate(-180deg);
        opacity: 0;
    }
}

/* Shake Animation */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-3px); }
    20%, 40%, 60%, 80% { transform: translateX(3px); }
}

/* Enhanced Status Text Animation */
.status-text {
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.barcode-status.active .status-text {
    transform: scale(1.05);
}

/* Enhanced Input Focus Effects */
.barcode-input-container input {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.barcode-input-container input:focus {
    transform: translateY(-2px);
    font-size: 1.15rem;
}

/* Button Ripple Effect */
.btn-scan, .btn-manual {
    position: relative;
    overflow: hidden;
}

.btn-scan:active::after, .btn-manual:active::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    transform: translate(-50%, -50%);
    animation: ripple 0.6s ease-out;
}

@keyframes ripple {
    to {
        width: 100px;
        height: 100px;
        opacity: 0;
    }
}

/* Enhanced Hover Effects */
.barcode-section:hover {
    transform: translateY(-2px);
    box-shadow:
        0 15px 35px rgba(102, 126, 234, 0.2),
        0 5px 15px rgba(102, 126, 234, 0.15);
}

/* Smooth Transitions */
.barcode-section,
.barcode-input-container,
.barcode-status,
.btn-scan,
.btn-manual {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Loading State */
.barcode-section.loading {
    position: relative;
}

.barcode-section.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
    animation: loading 1.5s infinite;
    pointer-events: none;
}

@keyframes loading {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Success Glow Effect */
.barcode-section.success {
    box-shadow:
        0 0 20px rgba(39, 174, 96, 0.3),
        0 0 40px rgba(39, 174, 96, 0.2),
        0 0 60px rgba(39, 174, 96, 0.1);
    animation: successGlow 2s ease-out;
}

@keyframes successGlow {
    0% { box-shadow: 0 0 0 rgba(39, 174, 96, 0); }
    50% { box-shadow: 0 0 30px rgba(39, 174, 96, 0.5); }
    100% { box-shadow: 0 0 0 rgba(39, 174, 96, 0); }
}

/* Error Pulse Effect */
.barcode-section.error {
    animation: errorPulse 0.5s ease-in-out;
}

@keyframes errorPulse {
    0%, 100% {
        border-color: rgba(102, 126, 234, 0.2);
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.15);
    }
    50% {
        border-color: rgba(231, 76, 60, 0.5);
        box-shadow: 0 10px 30px rgba(231, 76, 60, 0.3);
    }
}

/* Advanced Search Responsive Design */
@media (max-width: 768px) {
    .advanced-search-section {
        padding: 1rem;
        margin: 8px;
        font-size: 0.85rem;
    }

    .search-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
        margin-bottom: 1rem;
    }

    .search-stats {
        align-self: flex-end;
    }

    .multi-search-container {
        grid-template-columns: 1fr;
        gap: 0.6rem;
    }

    .quick-filters {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.6rem;
        padding: 0.6rem;
    }

    .filter-actions {
        grid-column: 1 / -1;
        flex-direction: row;
        gap: 10px;
    }

    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: 0.8rem;
        padding: 1rem;
    }

    .product-card {
        padding: 0.8rem;
    }

    .product-card:hover {
        transform: translateY(-4px) scale(1.05);
    }

    .product-image {
        width: 60px;
        height: 60px;
    }

    .product-name {
        font-size: 0.75rem;
        height: 2rem;
    }

    .product-price {
        font-size: 0.8rem;
    }

    .product-barcode {
        font-size: 0.6rem;
        padding: 2px 4px;
    }

    .product-details {
        position: fixed;
        top: 50%;
        left: 50%;
        right: auto;
        bottom: auto;
        transform: translate(-50%, -50%);
        width: 90%;
        max-width: 300px;
        border-radius: 15px;
        z-index: 1000;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    }

    .barcode-status-bar {
        padding: 10px;
        margin-top: 0.6rem;
    }

    .status-icon {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }

    .status-text {
        font-size: 0.8rem;
    }

    .status-details {
        font-size: 0.7rem;
    }

    .btn-add-to-cart {
        font-size: 0.65rem;
        padding: 5px 8px;
    }

    .btn-quick-add {
        width: 25px;
        height: 25px;
        font-size: 0.7rem;
    }
}
