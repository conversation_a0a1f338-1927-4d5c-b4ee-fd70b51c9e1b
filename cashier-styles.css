/* CSS Variables for Theming */
:root {
    --primary-color: #2c5530;
    --secondary-color: #34495e;
    --text-color: #2c3e50;
    --bg-color: #f5f7fa;
    --base-font-size: 16px;
    --border-radius: 8px;
    --box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* Cashier System Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, var(--bg-color), #c3cfe2);
    color: var(--text-color);
    overflow-x: hidden;
    font-size: var(--base-font-size);
    transition: all 0.3s ease;
}

/* Theme Classes */
body.theme-dark {
    --primary-color: #34495e;
    --secondary-color: #2c3e50;
    --text-color: #ecf0f1;
    --bg-color: #2c3e50;
    background: linear-gradient(135deg, #2c3e50, #34495e);
}

body.theme-light {
    --primary-color: #3498db;
    --secondary-color: #ecf0f1;
    --text-color: #2c3e50;
    --bg-color: #ffffff;
    background: linear-gradient(135deg, #ecf0f1, #bdc3c7);
}

body.theme-blue {
    --primary-color: #2980b9;
    --secondary-color: #3498db;
    --text-color: #ecf0f1;
    --bg-color: #1e3a5f;
    background: linear-gradient(135deg, #3498db, #2980b9);
}

body.theme-green {
    --primary-color: #27ae60;
    --secondary-color: #2ecc71;
    --text-color: #ecf0f1;
    --bg-color: #1e5f3a;
    background: linear-gradient(135deg, #27ae60, #2ecc71);
}

/* Header */
.cashier-header {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
    padding: 1rem 2rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo-section i {
    font-size: 2rem;
    color: #3498db;
}

.logo-section h1 {
    font-size: 1.5rem;
    font-weight: 700;
}

.header-info {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.header-info > div {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

/* Main Container */
.cashier-container {
    display: grid;
    grid-template-columns: 1fr 400px 350px;
    gap: 1rem;
    padding: 1rem;
    max-width: 1400px;
    margin: 0 auto;
    min-height: calc(100vh - 140px);
}

/* Panels */
.products-panel,
.cart-panel,
.payment-panel {
    background: white;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.panel-header {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.panel-header h3 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.1rem;
    font-weight: 600;
}

/* Search Section */
.search-section {
    margin-top: 1rem;
}

.search-box {
    position: relative;
    margin-bottom: 1rem;
}

.search-box i {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #7f8c8d;
}

.search-box input {
    width: 100%;
    padding: 0.8rem 3rem 0.8rem 1rem;
    border: 2px solid #ecf0f1;
    border-radius: 25px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.filter-buttons {
    display: flex;
    gap: 0.5rem;
}

.filter-btn {
    padding: 0.5rem 1rem;
    border: 2px solid #ecf0f1;
    background: white;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.8rem;
    font-weight: 600;
}

.filter-btn.active,
.filter-btn:hover {
    background: #3498db;
    color: white;
    border-color: #3498db;
}

/* Categories Tabs */
.categories-tabs {
    display: flex;
    gap: 0.5rem;
    padding: 1rem;
    border-bottom: 1px solid #ecf0f1;
    overflow-x: auto;
}

.category-tab {
    padding: 0.5rem 1rem;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    font-size: 0.85rem;
    font-weight: 600;
}

.category-tab.active {
    background: #3498db;
    color: white;
    border-color: #3498db;
}

/* Products Grid */
.products-grid {
    flex: 1;
    padding: 1rem;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1rem;
    overflow-y: auto;
    max-height: 500px;
}

.product-card {
    background: #f8f9fa;
    border: 2px solid #ecf0f1;
    border-radius: 10px;
    padding: 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.product-card:hover {
    border-color: #3498db;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.2);
}

.product-card.out-of-stock {
    opacity: 0.5;
    cursor: not-allowed;
}

.product-image {
    width: 60px;
    height: 60px;
    background: #ecf0f1;
    border-radius: 8px;
    margin: 0 auto 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-image i {
    font-size: 1.5rem;
    color: #7f8c8d;
}

.product-name {
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 0.3rem;
    line-height: 1.2;
}

.product-price {
    font-size: 0.9rem;
    font-weight: 700;
    color: #27ae60;
}

.product-stock {
    position: absolute;
    top: 5px;
    left: 5px;
    background: #e74c3c;
    color: white;
    padding: 0.2rem 0.4rem;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: 600;
}

.product-stock.in-stock {
    background: #27ae60;
}

/* Cart Items */
.cart-items {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    max-height: 400px;
}

.empty-cart {
    text-align: center;
    color: #7f8c8d;
    padding: 2rem;
}

.empty-cart i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.cart-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid #ecf0f1;
    border-radius: 10px;
    margin-bottom: 0.5rem;
    background: #f8f9fa;
}

.cart-item-image {
    width: 50px;
    height: 50px;
    background: #ecf0f1;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.cart-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.cart-item-info {
    flex: 1;
}

.cart-item-name {
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 0.2rem;
}

.cart-item-price {
    color: #27ae60;
    font-weight: 600;
    font-size: 0.8rem;
}

.cart-item-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.qty-btn {
    width: 30px;
    height: 30px;
    border: 1px solid #dee2e6;
    background: white;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.qty-btn:hover {
    background: #3498db;
    color: white;
    border-color: #3498db;
}

.qty-input {
    width: 50px;
    text-align: center;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 0.3rem;
    font-weight: 600;
}

.remove-btn {
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 0.3rem 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.remove-btn:hover {
    background: #c0392b;
}

/* Cart Summary */
.cart-summary {
    padding: 1rem;
    border-top: 2px solid #ecf0f1;
    background: #f8f9fa;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.summary-row.total {
    font-size: 1.1rem;
    font-weight: 700;
    color: #2c3e50;
    border-top: 2px solid #dee2e6;
    padding-top: 0.5rem;
    margin-top: 0.5rem;
}

/* Payment Panel */
.payment-panel {
    padding: 0;
}

.sale-type {
    display: flex;
    gap: 1rem;
}

.sale-type label {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 600;
}

.customer-section,
.discount-section,
.payment-methods {
    padding: 1rem;
    border-bottom: 1px solid #ecf0f1;
}

.customer-section h4,
.discount-section h4,
.payment-methods h4 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    font-size: 1rem;
    color: #2c3e50;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group input {
    width: 100%;
    padding: 0.8rem;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.discount-controls {
    display: grid;
    grid-template-columns: 1fr 1fr auto;
    gap: 0.5rem;
    align-items: end;
}

.payment-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
}

.payment-btn {
    padding: 1rem;
    border: 2px solid #ecf0f1;
    background: white;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.3rem;
    font-size: 0.8rem;
    font-weight: 600;
}

.payment-btn.active,
.payment-btn:hover {
    background: #3498db;
    color: white;
    border-color: #3498db;
}

.payment-btn i {
    font-size: 1.2rem;
}

.payment-amount {
    padding: 1rem;
}

.change-amount {
    display: flex;
    justify-content: space-between;
    margin-top: 1rem;
    padding: 0.8rem;
    background: #e8f5e8;
    border-radius: 8px;
    font-weight: 600;
    color: #27ae60;
}

.action-buttons {
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

/* Buttons */
.btn {
    padding: 0.6rem 1.2rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    font-family: 'Cairo', sans-serif;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    font-size: 0.9rem;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.btn-success {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, #f39c12, #f1c40f);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.btn-secondary {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
    color: white;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
}

.btn-sm {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
}

.btn-large {
    padding: 1rem 1.5rem;
    font-size: 1rem;
    font-weight: 700;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

/* Quick Actions */
.quick-actions {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 1rem;
    background: white;
    padding: 1rem;
    border-radius: 50px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    z-index: 1000;
}

.quick-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.3rem;
    padding: 1rem;
    background: #f8f9fa;
    border: 2px solid #ecf0f1;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 80px;
}

.quick-btn:hover {
    background: #3498db;
    color: white;
    border-color: #3498db;
    transform: translateY(-2px);
}

.quick-btn i {
    font-size: 1.2rem;
}

.quick-btn span {
    font-size: 0.7rem;
    font-weight: 600;
    text-align: center;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .cashier-container {
        grid-template-columns: 1fr 350px 300px;
    }
}

@media (max-width: 768px) {
    .cashier-container {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }
    
    .header-info {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .quick-actions {
        position: relative;
        transform: none;
        left: auto;
        bottom: auto;
        margin: 1rem;
        justify-content: center;
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modal-overlay.show {
    opacity: 1;
}

.modal {
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    transform: scale(0.8);
    transition: transform 0.3s ease;
}

.modal-overlay.show .modal {
    transform: scale(1);
}

.modal-header {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
    padding: 1.5rem;
    border-radius: 15px 15px 0 0;
}

.modal-header h3 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    padding: 1rem 2rem 2rem;
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Sale Complete Modal */
.sale-complete-modal .modal-header {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
}

.sale-summary {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.8rem 0;
    border-bottom: 1px solid #dee2e6;
    font-size: 1rem;
}

.summary-item:last-child {
    border-bottom: none;
}

.invoice-number {
    font-weight: 700;
    color: #3498db;
    font-size: 1.1rem;
}

.total-amount {
    font-weight: 700;
    color: #27ae60;
    font-size: 1.2rem;
}

.change-amount {
    font-weight: 700;
    color: #e74c3c;
    font-size: 1.1rem;
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.8rem;
    z-index: 10001;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    min-width: 300px;
    border-left: 4px solid #3498db;
}

.notification.show {
    transform: translateX(0);
}

.notification-success {
    border-left-color: #27ae60;
    background: linear-gradient(135deg, #d5f4e6, #e8f5e8);
}

.notification-error {
    border-left-color: #e74c3c;
    background: linear-gradient(135deg, #fdf2f2, #fef5f5);
}

.notification-warning {
    border-left-color: #f39c12;
    background: linear-gradient(135deg, #fff8e1, #fffbf0);
}

.notification-info {
    border-left-color: #3498db;
    background: linear-gradient(135deg, #e3f2fd, #f0f8ff);
}

.notification i {
    font-size: 1.2rem;
}

.notification-success i {
    color: #27ae60;
}

.notification-error i {
    color: #e74c3c;
}

.notification-warning i {
    color: #f39c12;
}

.notification-info i {
    color: #3498db;
}

.notification span {
    font-weight: 600;
    color: #2c3e50;
}

/* No Products Message */
.no-products {
    grid-column: 1 / -1;
    text-align: center;
    padding: 3rem;
    color: #7f8c8d;
}

.no-products i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Print Styles */
@media print {
    body * {
        visibility: hidden;
    }

    .print-area,
    .print-area * {
        visibility: visible;
    }

    .print-area {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
    }

    .no-print {
        display: none !important;
    }
}

/* Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Animations */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes bounceIn {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }
    50% {
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.cart-item {
    animation: slideInRight 0.3s ease;
}

.product-card:hover {
    animation: bounceIn 0.3s ease;
}

/* Accessibility Improvements */
.btn:focus,
.form-group input:focus,
.qty-input:focus {
    outline: 3px solid rgba(52, 152, 219, 0.3);
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .product-card {
        border-width: 3px;
    }

    .btn {
        border: 2px solid currentColor;
    }

    .notification {
        border-width: 3px;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Reports Modal Styles */
.reports-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.report-card {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 10px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border-left: 4px solid #3498db;
    transition: all 0.3s ease;
}

.report-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.report-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.report-info h4 {
    margin: 0 0 0.5rem 0;
    color: #2c3e50;
    font-size: 1rem;
    font-weight: 600;
}

.report-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #27ae60;
    margin-bottom: 0.2rem;
}

.report-count {
    font-size: 0.9rem;
    color: #7f8c8d;
}

.reports-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Held Sales Modal */
.held-sales-list,
.last-sales-list {
    max-height: 400px;
    overflow-y: auto;
}

.held-sale-item,
.last-sale-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.held-sale-item:hover,
.last-sale-item:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.held-sale-info,
.last-sale-info {
    flex: 1;
}

.held-sale-id,
.last-sale-invoice {
    font-weight: 700;
    color: #3498db;
    font-size: 1rem;
    margin-bottom: 0.3rem;
}

.held-sale-time,
.last-sale-time {
    font-size: 0.8rem;
    color: #7f8c8d;
    margin-bottom: 0.3rem;
}

.held-sale-items,
.last-sale-customer {
    font-size: 0.9rem;
    color: #2c3e50;
    margin-bottom: 0.3rem;
}

.held-sale-total,
.last-sale-total {
    font-weight: 600;
    color: #27ae60;
    font-size: 1rem;
}

.held-sale-actions,
.last-sale-actions {
    display: flex;
    gap: 0.5rem;
}

/* Coupons Modal */
.coupons-list {
    max-height: 400px;
    overflow-y: auto;
}

.coupon-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border: 2px solid #dee2e6;
    border-radius: 10px;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, #fff8e1, #fffbf0);
    transition: all 0.3s ease;
}

.coupon-item:hover {
    border-color: #f39c12;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(243, 156, 18, 0.2);
}

.coupon-info {
    flex: 1;
}

.coupon-code {
    font-weight: 700;
    color: #f39c12;
    font-size: 1.1rem;
    margin-bottom: 0.3rem;
    font-family: 'Courier New', monospace;
    background: white;
    padding: 0.2rem 0.5rem;
    border-radius: 5px;
    display: inline-block;
}

.coupon-discount {
    font-weight: 600;
    color: #27ae60;
    font-size: 1rem;
    margin-bottom: 0.3rem;
}

.coupon-desc {
    font-size: 0.9rem;
    color: #7f8c8d;
}

.no-coupons {
    text-align: center;
    padding: 3rem;
    color: #7f8c8d;
}

.no-coupons i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Enhanced Modal Animations */
.modal {
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        transform: scale(0.8) translateY(-50px);
        opacity: 0;
    }
    to {
        transform: scale(1) translateY(0);
        opacity: 1;
    }
}

/* Responsive Modal Design */
@media (max-width: 768px) {
    .modal {
        width: 95%;
        margin: 1rem;
    }

    .reports-summary {
        grid-template-columns: 1fr;
    }

    .report-card {
        padding: 1rem;
    }

    .report-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .report-value {
        font-size: 1.2rem;
    }

    .held-sale-item,
    .last-sale-item,
    .coupon-item {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .held-sale-actions,
    .last-sale-actions {
        justify-content: center;
    }

    .modal-footer {
        flex-direction: column;
    }

    .modal-footer .btn {
        width: 100%;
    }
}

/* Print Receipt Styles */
.receipt-preview {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
    max-width: 300px;
    margin: 0 auto;
    background: white;
    padding: 1rem;
    border: 1px solid #dee2e6;
    border-radius: 5px;
}

.receipt-header {
    text-align: center;
    border-bottom: 1px dashed #333;
    padding-bottom: 0.5rem;
    margin-bottom: 0.5rem;
}

.receipt-items {
    margin: 0.5rem 0;
}

.receipt-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.2rem;
}

.receipt-totals {
    border-top: 1px dashed #333;
    padding-top: 0.5rem;
    margin-top: 0.5rem;
}

.receipt-footer {
    text-align: center;
    margin-top: 0.5rem;
    font-size: 10px;
    color: #666;
}

/* Status Indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;
    padding: 0.3rem 0.6rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-paid {
    background: #d5f4e6;
    color: #27ae60;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-cancelled {
    background: #f8d7da;
    color: #721c24;
}

/* Enhanced Scrollbars */
.held-sales-list::-webkit-scrollbar,
.last-sales-list::-webkit-scrollbar,
.coupons-list::-webkit-scrollbar {
    width: 8px;
}

.held-sales-list::-webkit-scrollbar-track,
.last-sales-list::-webkit-scrollbar-track,
.coupons-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.held-sales-list::-webkit-scrollbar-thumb,
.last-sales-list::-webkit-scrollbar-thumb,
.coupons-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 10px;
}

.held-sales-list::-webkit-scrollbar-thumb:hover,
.last-sales-list::-webkit-scrollbar-thumb:hover,
.coupons-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Calculator Styles */
.calculator-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    animation: fadeIn 0.3s ease;
}

.calculator-container {
    background: linear-gradient(145deg, #2c3e50, #34495e);
    border-radius: 20px;
    padding: 25px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    max-width: 450px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    border: 2px solid #3498db;
}

.calculator-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #3498db;
}

.calculator-header h3 {
    color: #ecf0f1;
    font-size: 1.4rem;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.close-calculator {
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.close-calculator:hover {
    background: #c0392b;
    transform: scale(1.1);
}

.calculator-display {
    background: #1a252f;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    border: 2px solid #3498db;
    box-shadow: inset 0 5px 15px rgba(0, 0, 0, 0.3);
}

.display-history {
    color: #95a5a6;
    font-size: 0.9rem;
    min-height: 20px;
    text-align: right;
    margin-bottom: 5px;
    font-family: 'Courier New', monospace;
}

.display-current {
    color: #ecf0f1;
    font-size: 2.2rem;
    font-weight: bold;
    text-align: right;
    font-family: 'Courier New', monospace;
    word-break: break-all;
    min-height: 50px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}

.memory-functions {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 8px;
    margin-bottom: 15px;
}

.memory-btn {
    background: linear-gradient(145deg, #8e44ad, #9b59b6);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 8px;
    font-size: 0.8rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 3px 8px rgba(142, 68, 173, 0.3);
}

.memory-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 12px rgba(142, 68, 173, 0.4);
}

.memory-btn:active {
    transform: translateY(0);
}

.calculator-buttons {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    margin-bottom: 20px;
}

.calc-btn {
    border: none;
    border-radius: 12px;
    padding: 15px;
    font-size: 1.2rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
}

.calc-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

.calc-btn:active::before {
    width: 100px;
    height: 100px;
}

.number-btn {
    background: linear-gradient(145deg, #34495e, #2c3e50);
    color: #ecf0f1;
}

.number-btn:hover {
    background: linear-gradient(145deg, #3c5a78, #34495e);
    transform: translateY(-2px);
}

.operator-btn {
    background: linear-gradient(145deg, #e67e22, #d35400);
    color: white;
}

.operator-btn:hover {
    background: linear-gradient(145deg, #f39c12, #e67e22);
    transform: translateY(-2px);
}

.function-btn {
    background: linear-gradient(145deg, #95a5a6, #7f8c8d);
    color: white;
}

.function-btn:hover {
    background: linear-gradient(145deg, #bdc3c7, #95a5a6);
    transform: translateY(-2px);
}

.scientific-btn {
    background: linear-gradient(145deg, #3498db, #2980b9);
    color: white;
}

.scientific-btn:hover {
    background: linear-gradient(145deg, #5dade2, #3498db);
    transform: translateY(-2px);
}

.equals-btn {
    background: linear-gradient(145deg, #27ae60, #229954);
    color: white;
    grid-column: span 2;
}

.equals-btn:hover {
    background: linear-gradient(145deg, #2ecc71, #27ae60);
    transform: translateY(-2px);
}

.zero-btn {
    grid-column: span 2;
}

.currency-functions {
    background: rgba(52, 73, 94, 0.3);
    border-radius: 15px;
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid #3498db;
}

.currency-functions h4 {
    color: #ecf0f1;
    margin-bottom: 10px;
    text-align: center;
    font-size: 1rem;
}

.currency-buttons {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
}

.currency-btn {
    background: linear-gradient(145deg, #16a085, #1abc9c);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 10px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 3px 8px rgba(22, 160, 133, 0.3);
}

.currency-btn:hover {
    background: linear-gradient(145deg, #1abc9c, #48c9b0);
    transform: translateY(-2px);
}

.calculation-history {
    background: rgba(52, 73, 94, 0.3);
    border-radius: 15px;
    padding: 15px;
    border: 1px solid #3498db;
    max-height: 200px;
}

.calculation-history h4 {
    color: #ecf0f1;
    margin-bottom: 10px;
    text-align: center;
    font-size: 1rem;
}

.history-list {
    max-height: 120px;
    overflow-y: auto;
    margin-bottom: 10px;
}

.history-item {
    background: rgba(26, 37, 47, 0.5);
    padding: 8px;
    margin-bottom: 5px;
    border-radius: 8px;
    color: #bdc3c7;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.history-item:hover {
    background: rgba(52, 152, 219, 0.3);
    color: #ecf0f1;
}

.clear-history-btn {
    background: linear-gradient(145deg, #e74c3c, #c0392b);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 8px 15px;
    font-size: 0.9rem;
    cursor: pointer;
    width: 100%;
    transition: all 0.2s ease;
}

.clear-history-btn:hover {
    background: linear-gradient(145deg, #ec7063, #e74c3c);
}

/* Calculator Responsive Design */
@media (max-width: 768px) {
    .calculator-container {
        width: 95%;
        padding: 15px;
        max-height: 95vh;
    }

    .calculator-buttons {
        gap: 8px;
    }

    .calc-btn {
        padding: 12px;
        font-size: 1rem;
    }

    .display-current {
        font-size: 1.8rem;
    }

    .currency-buttons {
        grid-template-columns: 1fr;
    }

    .memory-functions {
        grid-template-columns: repeat(3, 1fr);
    }

    .calculator-header h3 {
        font-size: 1.2rem;
    }
}

/* Calculator Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Calculator Button Hover Effects */
.calc-btn:hover {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

.calc-btn:active {
    transform: translateY(1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Memory Button Active State */
.memory-btn.active {
    background: linear-gradient(145deg, #9b59b6, #8e44ad);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Calculator Display Glow Effect */
.calculator-display {
    position: relative;
}

.calculator-display::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #3498db, #2980b9, #3498db);
    border-radius: 17px;
    z-index: -1;
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from {
        box-shadow: 0 0 5px rgba(52, 152, 219, 0.5);
    }
    to {
        box-shadow: 0 0 20px rgba(52, 152, 219, 0.8);
    }
}

/* Enhanced Scrollbar for History */
.history-list::-webkit-scrollbar {
    width: 6px;
}

.history-list::-webkit-scrollbar-track {
    background: rgba(52, 73, 94, 0.3);
    border-radius: 3px;
}

.history-list::-webkit-scrollbar-thumb {
    background: rgba(52, 152, 219, 0.6);
    border-radius: 3px;
}

.history-list::-webkit-scrollbar-thumb:hover {
    background: rgba(52, 152, 219, 0.8);
}

/* Calculator Modal Backdrop */
.calculator-modal::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at center, rgba(52, 152, 219, 0.1) 0%, rgba(0, 0, 0, 0.8) 100%);
    z-index: -1;
}

/* Enhanced Button Ripple Effect */
@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

.calc-btn {
    position: relative;
    overflow: hidden;
}

.calc-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%);
    transform-origin: 50% 50%;
}

.calc-btn:focus:not(:active)::after {
    animation: ripple 1s ease-out;
}

/* Calculator Theme Variations */
.calculator-container.dark-theme {
    background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
    border-color: #444;
}

.calculator-container.light-theme {
    background: linear-gradient(145deg, #f8f9fa, #e9ecef);
    color: #333;
}

.calculator-container.neon-theme {
    background: linear-gradient(145deg, #0a0a0a, #1a1a1a);
    border: 2px solid #00ff88;
    box-shadow: 0 0 30px rgba(0, 255, 136, 0.3);
}

/* Settings Modal Styles */
.settings-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    animation: fadeIn 0.3s ease;
}

.settings-container {
    background: linear-gradient(145deg, #2c3e50, #34495e);
    border-radius: 20px;
    padding: 0;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    max-width: 900px;
    width: 95%;
    max-height: 90vh;
    overflow: hidden;
    border: 2px solid #3498db;
    display: flex;
    flex-direction: column;
}

.settings-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 2px solid #3498db;
    background: rgba(52, 152, 219, 0.1);
}

.settings-header h3 {
    color: #ecf0f1;
    font-size: 1.4rem;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.close-settings {
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.close-settings:hover {
    background: #c0392b;
    transform: scale(1.1);
}

.settings-body {
    flex: 1;
    overflow-y: auto;
    padding: 0;
}

/* Settings Tabs */
.settings-tabs {
    display: flex;
    background: rgba(52, 73, 94, 0.3);
    border-bottom: 1px solid #34495e;
    overflow-x: auto;
}

.settings-tab {
    background: transparent;
    color: #bdc3c7;
    border: none;
    padding: 15px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    font-size: 0.9rem;
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 120px;
    justify-content: center;
}

.settings-tab:hover {
    background: rgba(52, 152, 219, 0.2);
    color: #ecf0f1;
}

.settings-tab.active {
    background: #3498db;
    color: white;
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

/* Settings Panels */
.settings-panel {
    display: none;
    padding: 25px;
    animation: fadeIn 0.3s ease;
}

.settings-panel.active {
    display: block;
}

.settings-panel h4 {
    color: #ecf0f1;
    font-size: 1.2rem;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #34495e;
}

/* Setting Groups */
.setting-group {
    margin-bottom: 20px;
    background: rgba(52, 73, 94, 0.2);
    padding: 15px;
    border-radius: 10px;
    border: 1px solid #34495e;
}

.setting-group label {
    display: block;
    color: #ecf0f1;
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 0.95rem;
}

.setting-group input,
.setting-group select,
.setting-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #34495e;
    border-radius: 8px;
    background: #2c3e50;
    color: #ecf0f1;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.setting-group input:focus,
.setting-group select:focus,
.setting-group textarea:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.setting-group textarea {
    resize: vertical;
    min-height: 60px;
}

.setting-group small {
    display: block;
    color: #95a5a6;
    font-size: 0.8rem;
    margin-top: 5px;
    line-height: 1.4;
}

/* Custom Checkbox */
.checkbox-label {
    display: flex !important;
    align-items: center;
    cursor: pointer;
    margin-bottom: 0 !important;
    font-weight: 600 !important;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    background: #2c3e50;
    border: 2px solid #34495e;
    border-radius: 4px;
    margin-left: 10px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: #3498db;
    border-color: #3498db;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '';
    position: absolute;
    left: 6px;
    top: 2px;
    width: 6px;
    height: 10px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* Backup Actions */
.backup-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    margin: 20px 0;
}

.backup-actions .btn {
    padding: 12px 16px;
    font-size: 0.9rem;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

/* Backup Info */
.backup-info {
    background: rgba(26, 37, 47, 0.5);
    border-radius: 10px;
    padding: 15px;
    margin-top: 20px;
}

.backup-info h5 {
    color: #ecf0f1;
    margin-bottom: 15px;
    font-size: 1rem;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #34495e;
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    color: #bdc3c7;
    font-size: 0.9rem;
}

.info-value {
    color: #3498db;
    font-weight: 600;
    font-size: 0.9rem;
}

/* Settings Actions */
.settings-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 10px;
    padding: 20px 25px;
    background: rgba(52, 73, 94, 0.3);
    border-top: 1px solid #34495e;
}

.settings-actions .btn {
    padding: 12px 16px;
    font-size: 0.9rem;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.settings-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Settings Responsive Design */
@media (max-width: 768px) {
    .settings-container {
        width: 98%;
        max-height: 95vh;
    }

    .settings-tabs {
        flex-wrap: wrap;
    }

    .settings-tab {
        min-width: 100px;
        padding: 12px 15px;
        font-size: 0.8rem;
    }

    .settings-panel {
        padding: 15px;
    }

    .settings-actions {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .backup-actions {
        grid-template-columns: 1fr;
    }

    .info-grid {
        grid-template-columns: 1fr;
    }
}

/* Settings Animation */
@keyframes settingsSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.settings-panel.active {
    animation: settingsSlideIn 0.3s ease;
}

/* Settings Theme Support */
.settings-container.dark-theme {
    background: linear-gradient(145deg, #1a1a1a, #2d2d2d);
}

.settings-container.light-theme {
    background: linear-gradient(145deg, #f8f9fa, #e9ecef);
    color: #333;
}

.settings-container.light-theme .settings-header h3,
.settings-container.light-theme .settings-panel h4,
.settings-container.light-theme .setting-group label {
    color: #333;
}

.settings-container.light-theme .setting-group input,
.settings-container.light-theme .setting-group select,
.settings-container.light-theme .setting-group textarea {
    background: white;
    color: #333;
    border-color: #ddd;
}

/* Settings Success/Error States */
.setting-group.success {
    border-color: #27ae60;
    background: rgba(39, 174, 96, 0.1);
}

.setting-group.error {
    border-color: #e74c3c;
    background: rgba(231, 76, 60, 0.1);
}

.setting-group.success::before {
    content: '✓';
    position: absolute;
    top: 10px;
    right: 10px;
    color: #27ae60;
    font-weight: bold;
}

.setting-group.error::before {
    content: '✗';
    position: absolute;
    top: 10px;
    right: 10px;
    color: #e74c3c;
    font-weight: bold;
}

/* Barcode Scanner Styles */
.barcode-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 15px;
    margin: 10px 0;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 2px solid #3498db;
}

.barcode-input-container {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.barcode-input-container i {
    color: #3498db;
    font-size: 1.2rem;
    min-width: 20px;
}

.barcode-input-container input {
    flex: 1;
    padding: 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 25px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.barcode-input-container input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.btn-scan, .btn-manual {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border: none;
    border-radius: 50%;
    width: 45px;
    height: 45px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
}

.btn-scan:hover, .btn-manual:hover {
    background: linear-gradient(135deg, #2980b9, #1f5f8b);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.barcode-status {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 15px;
    background: rgba(52, 152, 219, 0.1);
    border-radius: 20px;
    font-size: 0.9rem;
}

.status-text {
    color: #2980b9;
    font-weight: 600;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #27ae60;
    animation: pulse 2s infinite;
}

.status-indicator.scanning {
    background: #f39c12;
    animation: blink 1s infinite;
}

.status-indicator.error {
    background: #e74c3c;
    animation: none;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* Barcode Scanner Modal */
.barcode-scanner-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    animation: fadeIn 0.3s ease;
}

.scanner-container {
    background: #2c3e50;
    border-radius: 20px;
    padding: 0;
    max-width: 600px;
    width: 95%;
    max-height: 90vh;
    overflow: hidden;
    border: 2px solid #3498db;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

.scanner-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    background: rgba(52, 152, 219, 0.1);
    border-bottom: 2px solid #3498db;
}

.scanner-header h3 {
    color: #ecf0f1;
    font-size: 1.4rem;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.close-scanner {
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.close-scanner:hover {
    background: #c0392b;
    transform: scale(1.1);
}

.scanner-body {
    padding: 20px;
}

/* Camera Container */
.camera-container {
    position: relative;
    background: #000;
    border-radius: 15px;
    overflow: hidden;
    margin-bottom: 20px;
    aspect-ratio: 4/3;
    max-height: 300px;
}

#barcodeVideo {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.scanner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.scanner-frame {
    position: relative;
    width: 250px;
    height: 150px;
    border: 2px solid rgba(52, 152, 219, 0.8);
    border-radius: 10px;
}

.corner {
    position: absolute;
    width: 20px;
    height: 20px;
    border: 3px solid #3498db;
}

.corner.top-left {
    top: -3px;
    left: -3px;
    border-right: none;
    border-bottom: none;
}

.corner.top-right {
    top: -3px;
    right: -3px;
    border-left: none;
    border-bottom: none;
}

.corner.bottom-left {
    bottom: -3px;
    left: -3px;
    border-right: none;
    border-top: none;
}

.corner.bottom-right {
    bottom: -3px;
    right: -3px;
    border-left: none;
    border-top: none;
}

.scanner-line {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, #3498db, transparent);
    animation: scanLine 2s linear infinite;
}

@keyframes scanLine {
    0% { transform: translateY(-75px); }
    100% { transform: translateY(75px); }
}

.scanner-instructions {
    margin-top: 20px;
    text-align: center;
    color: white;
}

.scanner-instructions p {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.scanner-instructions small {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Scanner Controls */
.scanner-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 20px;
}

.scanner-controls .btn {
    padding: 12px 20px;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Manual Input Section */
.manual-input-section {
    background: rgba(52, 73, 94, 0.3);
    border-radius: 15px;
    padding: 15px;
    margin-bottom: 20px;
}

.manual-input-section h4 {
    color: #ecf0f1;
    margin-bottom: 10px;
    font-size: 1rem;
}

.manual-barcode-input {
    display: flex;
    gap: 10px;
}

.manual-barcode-input input {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid #34495e;
    border-radius: 8px;
    background: #2c3e50;
    color: #ecf0f1;
    font-size: 0.9rem;
}

.manual-barcode-input input:focus {
    outline: none;
    border-color: #3498db;
}

/* Recent Scans */
.recent-scans {
    background: rgba(52, 73, 94, 0.3);
    border-radius: 15px;
    padding: 15px;
}

.recent-scans h4 {
    color: #ecf0f1;
    margin-bottom: 10px;
    font-size: 1rem;
}

.scans-list {
    max-height: 150px;
    overflow-y: auto;
}

.scan-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: rgba(26, 37, 47, 0.5);
    border-radius: 8px;
    margin-bottom: 5px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.scan-item:hover {
    background: rgba(52, 152, 219, 0.3);
}

.scan-item.success {
    border-left: 4px solid #27ae60;
}

.scan-item.failed {
    border-left: 4px solid #e74c3c;
}

.scan-barcode {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #3498db;
}

.scan-time {
    font-size: 0.8rem;
    color: #95a5a6;
}

/* Barcode History Modal */
.barcode-history-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    animation: fadeIn 0.3s ease;
}

.history-container {
    background: linear-gradient(145deg, #2c3e50, #34495e);
    border-radius: 20px;
    padding: 0;
    max-width: 800px;
    width: 95%;
    max-height: 90vh;
    overflow: hidden;
    border: 2px solid #3498db;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    background: rgba(52, 152, 219, 0.1);
    border-bottom: 2px solid #3498db;
}

.history-header h3 {
    color: #ecf0f1;
    font-size: 1.4rem;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.close-history {
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.close-history:hover {
    background: #c0392b;
    transform: scale(1.1);
}

.history-body {
    padding: 20px;
    overflow-y: auto;
    max-height: calc(90vh - 100px);
}

/* History Stats */
.history-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.stat-card {
    background: rgba(52, 73, 94, 0.3);
    padding: 15px;
    border-radius: 10px;
    text-align: center;
    border: 1px solid #34495e;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: bold;
    color: #3498db;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    color: #bdc3c7;
}

/* History Filters */
.history-filters {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    gap: 15px;
}

.history-filters select {
    padding: 8px 12px;
    border: 1px solid #34495e;
    border-radius: 8px;
    background: #2c3e50;
    color: #ecf0f1;
    font-size: 0.9rem;
}

/* History List */
.history-list {
    max-height: 400px;
    overflow-y: auto;
}

.history-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background: rgba(52, 73, 94, 0.3);
    border-radius: 10px;
    margin-bottom: 8px;
    border: 1px solid #34495e;
    transition: all 0.2s ease;
}

.history-item:hover {
    background: rgba(52, 152, 219, 0.2);
    border-color: #3498db;
}

.history-item.success {
    border-left: 4px solid #27ae60;
}

.history-item.failed {
    border-left: 4px solid #e74c3c;
}

.history-item-info {
    flex: 1;
}

.history-barcode {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #3498db;
    font-size: 1rem;
    margin-bottom: 3px;
}

.history-product {
    color: #ecf0f1;
    font-size: 0.9rem;
    margin-bottom: 3px;
}

.history-time {
    color: #95a5a6;
    font-size: 0.8rem;
}

.history-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 600;
}

.history-status.success {
    background: rgba(39, 174, 96, 0.2);
    color: #27ae60;
}

.history-status.failed {
    background: rgba(231, 76, 60, 0.2);
    color: #e74c3c;
}

/* Responsive Design for Barcode */
@media (max-width: 768px) {
    .barcode-input-container {
        flex-wrap: wrap;
    }

    .btn-scan, .btn-manual {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .scanner-container,
    .history-container {
        width: 98%;
        max-height: 95vh;
    }

    .scanner-frame {
        width: 200px;
        height: 120px;
    }

    .scanner-controls {
        flex-wrap: wrap;
        gap: 10px;
    }

    .scanner-controls .btn {
        padding: 10px 15px;
        font-size: 0.9rem;
    }

    .manual-barcode-input {
        flex-direction: column;
    }

    .history-stats {
        grid-template-columns: 1fr;
    }

    .history-filters {
        flex-direction: column;
        align-items: stretch;
    }
}
