/* Cashier System Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #f5f7fa, #c3cfe2);
    color: #2c3e50;
    overflow-x: hidden;
}

/* Header */
.cashier-header {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
    padding: 1rem 2rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo-section i {
    font-size: 2rem;
    color: #3498db;
}

.logo-section h1 {
    font-size: 1.5rem;
    font-weight: 700;
}

.header-info {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.header-info > div {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

/* Main Container */
.cashier-container {
    display: grid;
    grid-template-columns: 1fr 400px 350px;
    gap: 1rem;
    padding: 1rem;
    max-width: 1400px;
    margin: 0 auto;
    min-height: calc(100vh - 140px);
}

/* Panels */
.products-panel,
.cart-panel,
.payment-panel {
    background: white;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.panel-header {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.panel-header h3 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.1rem;
    font-weight: 600;
}

/* Search Section */
.search-section {
    margin-top: 1rem;
}

.search-box {
    position: relative;
    margin-bottom: 1rem;
}

.search-box i {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #7f8c8d;
}

.search-box input {
    width: 100%;
    padding: 0.8rem 3rem 0.8rem 1rem;
    border: 2px solid #ecf0f1;
    border-radius: 25px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.filter-buttons {
    display: flex;
    gap: 0.5rem;
}

.filter-btn {
    padding: 0.5rem 1rem;
    border: 2px solid #ecf0f1;
    background: white;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.8rem;
    font-weight: 600;
}

.filter-btn.active,
.filter-btn:hover {
    background: #3498db;
    color: white;
    border-color: #3498db;
}

/* Categories Tabs */
.categories-tabs {
    display: flex;
    gap: 0.5rem;
    padding: 1rem;
    border-bottom: 1px solid #ecf0f1;
    overflow-x: auto;
}

.category-tab {
    padding: 0.5rem 1rem;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    font-size: 0.85rem;
    font-weight: 600;
}

.category-tab.active {
    background: #3498db;
    color: white;
    border-color: #3498db;
}

/* Products Grid */
.products-grid {
    flex: 1;
    padding: 1rem;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 1rem;
    overflow-y: auto;
    max-height: 500px;
}

.product-card {
    background: #f8f9fa;
    border: 2px solid #ecf0f1;
    border-radius: 10px;
    padding: 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.product-card:hover {
    border-color: #3498db;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.2);
}

.product-card.out-of-stock {
    opacity: 0.5;
    cursor: not-allowed;
}

.product-image {
    width: 60px;
    height: 60px;
    background: #ecf0f1;
    border-radius: 8px;
    margin: 0 auto 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-image i {
    font-size: 1.5rem;
    color: #7f8c8d;
}

.product-name {
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 0.3rem;
    line-height: 1.2;
}

.product-price {
    font-size: 0.9rem;
    font-weight: 700;
    color: #27ae60;
}

.product-stock {
    position: absolute;
    top: 5px;
    left: 5px;
    background: #e74c3c;
    color: white;
    padding: 0.2rem 0.4rem;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: 600;
}

.product-stock.in-stock {
    background: #27ae60;
}

/* Cart Items */
.cart-items {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    max-height: 400px;
}

.empty-cart {
    text-align: center;
    color: #7f8c8d;
    padding: 2rem;
}

.empty-cart i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.cart-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid #ecf0f1;
    border-radius: 10px;
    margin-bottom: 0.5rem;
    background: #f8f9fa;
}

.cart-item-image {
    width: 50px;
    height: 50px;
    background: #ecf0f1;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.cart-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.cart-item-info {
    flex: 1;
}

.cart-item-name {
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 0.2rem;
}

.cart-item-price {
    color: #27ae60;
    font-weight: 600;
    font-size: 0.8rem;
}

.cart-item-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.qty-btn {
    width: 30px;
    height: 30px;
    border: 1px solid #dee2e6;
    background: white;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.qty-btn:hover {
    background: #3498db;
    color: white;
    border-color: #3498db;
}

.qty-input {
    width: 50px;
    text-align: center;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 0.3rem;
    font-weight: 600;
}

.remove-btn {
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 0.3rem 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.remove-btn:hover {
    background: #c0392b;
}

/* Cart Summary */
.cart-summary {
    padding: 1rem;
    border-top: 2px solid #ecf0f1;
    background: #f8f9fa;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.summary-row.total {
    font-size: 1.1rem;
    font-weight: 700;
    color: #2c3e50;
    border-top: 2px solid #dee2e6;
    padding-top: 0.5rem;
    margin-top: 0.5rem;
}

/* Payment Panel */
.payment-panel {
    padding: 0;
}

.sale-type {
    display: flex;
    gap: 1rem;
}

.sale-type label {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 600;
}

.customer-section,
.discount-section,
.payment-methods {
    padding: 1rem;
    border-bottom: 1px solid #ecf0f1;
}

.customer-section h4,
.discount-section h4,
.payment-methods h4 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    font-size: 1rem;
    color: #2c3e50;
}

.form-group {
    margin-bottom: 1rem;
}

.form-group input {
    width: 100%;
    padding: 0.8rem;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.discount-controls {
    display: grid;
    grid-template-columns: 1fr 1fr auto;
    gap: 0.5rem;
    align-items: end;
}

.payment-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
}

.payment-btn {
    padding: 1rem;
    border: 2px solid #ecf0f1;
    background: white;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.3rem;
    font-size: 0.8rem;
    font-weight: 600;
}

.payment-btn.active,
.payment-btn:hover {
    background: #3498db;
    color: white;
    border-color: #3498db;
}

.payment-btn i {
    font-size: 1.2rem;
}

.payment-amount {
    padding: 1rem;
}

.change-amount {
    display: flex;
    justify-content: space-between;
    margin-top: 1rem;
    padding: 0.8rem;
    background: #e8f5e8;
    border-radius: 8px;
    font-weight: 600;
    color: #27ae60;
}

.action-buttons {
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

/* Buttons */
.btn {
    padding: 0.6rem 1.2rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    font-family: 'Cairo', sans-serif;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    font-size: 0.9rem;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.btn-primary {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.btn-success {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, #f39c12, #f1c40f);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.btn-secondary {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
    color: white;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
}

.btn-sm {
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
}

.btn-large {
    padding: 1rem 1.5rem;
    font-size: 1rem;
    font-weight: 700;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

/* Quick Actions */
.quick-actions {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 1rem;
    background: white;
    padding: 1rem;
    border-radius: 50px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    z-index: 1000;
}

.quick-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.3rem;
    padding: 1rem;
    background: #f8f9fa;
    border: 2px solid #ecf0f1;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 80px;
}

.quick-btn:hover {
    background: #3498db;
    color: white;
    border-color: #3498db;
    transform: translateY(-2px);
}

.quick-btn i {
    font-size: 1.2rem;
}

.quick-btn span {
    font-size: 0.7rem;
    font-weight: 600;
    text-align: center;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .cashier-container {
        grid-template-columns: 1fr 350px 300px;
    }
}

@media (max-width: 768px) {
    .cashier-container {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
    
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }
    
    .header-info {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .quick-actions {
        position: relative;
        transform: none;
        left: auto;
        bottom: auto;
        margin: 1rem;
        justify-content: center;
    }
    
    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.modal-overlay.show {
    opacity: 1;
}

.modal {
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    transform: scale(0.8);
    transition: transform 0.3s ease;
}

.modal-overlay.show .modal {
    transform: scale(1);
}

.modal-header {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
    padding: 1.5rem;
    border-radius: 15px 15px 0 0;
}

.modal-header h3 {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    padding: 1rem 2rem 2rem;
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Sale Complete Modal */
.sale-complete-modal .modal-header {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
}

.sale-summary {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.8rem 0;
    border-bottom: 1px solid #dee2e6;
    font-size: 1rem;
}

.summary-item:last-child {
    border-bottom: none;
}

.invoice-number {
    font-weight: 700;
    color: #3498db;
    font-size: 1.1rem;
}

.total-amount {
    font-weight: 700;
    color: #27ae60;
    font-size: 1.2rem;
}

.change-amount {
    font-weight: 700;
    color: #e74c3c;
    font-size: 1.1rem;
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.8rem;
    z-index: 10001;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    min-width: 300px;
    border-left: 4px solid #3498db;
}

.notification.show {
    transform: translateX(0);
}

.notification-success {
    border-left-color: #27ae60;
    background: linear-gradient(135deg, #d5f4e6, #e8f5e8);
}

.notification-error {
    border-left-color: #e74c3c;
    background: linear-gradient(135deg, #fdf2f2, #fef5f5);
}

.notification-warning {
    border-left-color: #f39c12;
    background: linear-gradient(135deg, #fff8e1, #fffbf0);
}

.notification-info {
    border-left-color: #3498db;
    background: linear-gradient(135deg, #e3f2fd, #f0f8ff);
}

.notification i {
    font-size: 1.2rem;
}

.notification-success i {
    color: #27ae60;
}

.notification-error i {
    color: #e74c3c;
}

.notification-warning i {
    color: #f39c12;
}

.notification-info i {
    color: #3498db;
}

.notification span {
    font-weight: 600;
    color: #2c3e50;
}

/* No Products Message */
.no-products {
    grid-column: 1 / -1;
    text-align: center;
    padding: 3rem;
    color: #7f8c8d;
}

.no-products i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Print Styles */
@media print {
    body * {
        visibility: hidden;
    }

    .print-area,
    .print-area * {
        visibility: visible;
    }

    .print-area {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
    }

    .no-print {
        display: none !important;
    }
}

/* Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Animations */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes bounceIn {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }
    50% {
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.cart-item {
    animation: slideInRight 0.3s ease;
}

.product-card:hover {
    animation: bounceIn 0.3s ease;
}

/* Accessibility Improvements */
.btn:focus,
.form-group input:focus,
.qty-input:focus {
    outline: 3px solid rgba(52, 152, 219, 0.3);
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .product-card {
        border-width: 3px;
    }

    .btn {
        border: 2px solid currentColor;
    }

    .notification {
        border-width: 3px;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Reports Modal Styles */
.reports-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.report-card {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 10px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border-left: 4px solid #3498db;
    transition: all 0.3s ease;
}

.report-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.report-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #3498db, #2980b9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.report-info h4 {
    margin: 0 0 0.5rem 0;
    color: #2c3e50;
    font-size: 1rem;
    font-weight: 600;
}

.report-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #27ae60;
    margin-bottom: 0.2rem;
}

.report-count {
    font-size: 0.9rem;
    color: #7f8c8d;
}

.reports-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Held Sales Modal */
.held-sales-list,
.last-sales-list {
    max-height: 400px;
    overflow-y: auto;
}

.held-sale-item,
.last-sale-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.held-sale-item:hover,
.last-sale-item:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.held-sale-info,
.last-sale-info {
    flex: 1;
}

.held-sale-id,
.last-sale-invoice {
    font-weight: 700;
    color: #3498db;
    font-size: 1rem;
    margin-bottom: 0.3rem;
}

.held-sale-time,
.last-sale-time {
    font-size: 0.8rem;
    color: #7f8c8d;
    margin-bottom: 0.3rem;
}

.held-sale-items,
.last-sale-customer {
    font-size: 0.9rem;
    color: #2c3e50;
    margin-bottom: 0.3rem;
}

.held-sale-total,
.last-sale-total {
    font-weight: 600;
    color: #27ae60;
    font-size: 1rem;
}

.held-sale-actions,
.last-sale-actions {
    display: flex;
    gap: 0.5rem;
}

/* Coupons Modal */
.coupons-list {
    max-height: 400px;
    overflow-y: auto;
}

.coupon-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border: 2px solid #dee2e6;
    border-radius: 10px;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, #fff8e1, #fffbf0);
    transition: all 0.3s ease;
}

.coupon-item:hover {
    border-color: #f39c12;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(243, 156, 18, 0.2);
}

.coupon-info {
    flex: 1;
}

.coupon-code {
    font-weight: 700;
    color: #f39c12;
    font-size: 1.1rem;
    margin-bottom: 0.3rem;
    font-family: 'Courier New', monospace;
    background: white;
    padding: 0.2rem 0.5rem;
    border-radius: 5px;
    display: inline-block;
}

.coupon-discount {
    font-weight: 600;
    color: #27ae60;
    font-size: 1rem;
    margin-bottom: 0.3rem;
}

.coupon-desc {
    font-size: 0.9rem;
    color: #7f8c8d;
}

.no-coupons {
    text-align: center;
    padding: 3rem;
    color: #7f8c8d;
}

.no-coupons i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Enhanced Modal Animations */
.modal {
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        transform: scale(0.8) translateY(-50px);
        opacity: 0;
    }
    to {
        transform: scale(1) translateY(0);
        opacity: 1;
    }
}

/* Responsive Modal Design */
@media (max-width: 768px) {
    .modal {
        width: 95%;
        margin: 1rem;
    }

    .reports-summary {
        grid-template-columns: 1fr;
    }

    .report-card {
        padding: 1rem;
    }

    .report-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .report-value {
        font-size: 1.2rem;
    }

    .held-sale-item,
    .last-sale-item,
    .coupon-item {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .held-sale-actions,
    .last-sale-actions {
        justify-content: center;
    }

    .modal-footer {
        flex-direction: column;
    }

    .modal-footer .btn {
        width: 100%;
    }
}

/* Print Receipt Styles */
.receipt-preview {
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.4;
    max-width: 300px;
    margin: 0 auto;
    background: white;
    padding: 1rem;
    border: 1px solid #dee2e6;
    border-radius: 5px;
}

.receipt-header {
    text-align: center;
    border-bottom: 1px dashed #333;
    padding-bottom: 0.5rem;
    margin-bottom: 0.5rem;
}

.receipt-items {
    margin: 0.5rem 0;
}

.receipt-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.2rem;
}

.receipt-totals {
    border-top: 1px dashed #333;
    padding-top: 0.5rem;
    margin-top: 0.5rem;
}

.receipt-footer {
    text-align: center;
    margin-top: 0.5rem;
    font-size: 10px;
    color: #666;
}

/* Status Indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;
    padding: 0.3rem 0.6rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-paid {
    background: #d5f4e6;
    color: #27ae60;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-cancelled {
    background: #f8d7da;
    color: #721c24;
}

/* Enhanced Scrollbars */
.held-sales-list::-webkit-scrollbar,
.last-sales-list::-webkit-scrollbar,
.coupons-list::-webkit-scrollbar {
    width: 8px;
}

.held-sales-list::-webkit-scrollbar-track,
.last-sales-list::-webkit-scrollbar-track,
.coupons-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.held-sales-list::-webkit-scrollbar-thumb,
.last-sales-list::-webkit-scrollbar-thumb,
.coupons-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 10px;
}

.held-sales-list::-webkit-scrollbar-thumb:hover,
.last-sales-list::-webkit-scrollbar-thumb:hover,
.coupons-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
