/* ===== PROFESSIONAL CASHIER SYSTEM STYLES ===== */

/* CSS Variables */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #3498db;
    --light-bg: #ecf0f1;
    --dark-bg: #34495e;
    --text-color: #2c3e50;
    --border-color: #bdc3c7;
    --shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    --border-radius: 10px;
    --transition: all 0.3s ease;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: var(--text-color);
    direction: rtl;
    overflow-x: hidden;
    min-height: 100vh;
}

/* ===== HEADER STYLES ===== */
.cashier-header {
    background: linear-gradient(135deg, var(--primary-color), var(--dark-bg));
    color: white;
    padding: 15px 0;
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
}

.header-left .store-info h1 {
    font-size: 1.8rem;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.header-left .store-info .cashier-name {
    font-size: 1rem;
    opacity: 0.8;
}

.header-center .date-time {
    text-align: center;
}

.header-center .current-date {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.header-center .current-time {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--secondary-color);
}

.header-right {
    display: flex;
    gap: 10px;
}

.header-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 10px 15px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    text-decoration: none;
}

.header-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-2px);
}

.header-btn.primary {
    background: linear-gradient(135deg, var(--secondary-color), #2980b9);
    border-color: var(--secondary-color);
}

.header-btn.primary:hover {
    background: linear-gradient(135deg, #2980b9, #1f5f8b);
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

/* ===== MAIN LAYOUT ===== */
.cashier-main {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    min-height: calc(100vh - 100px);
}

/* ===== PRODUCTS PANEL ===== */
.products-panel {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.panel-header {
    background: linear-gradient(135deg, var(--light-bg), #d5dbdb);
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.panel-header h2 {
    font-size: 1.5rem;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 10px;
}

.panel-controls {
    display: flex;
    gap: 10px;
}

.control-btn {
    background: var(--secondary-color);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.9rem;
}

.control-btn:hover {
    background: #2980b9;
    transform: translateY(-2px);
}

/* ===== SEARCH SECTION ===== */
.search-section {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    background: #f8f9fa;
}

.search-bar {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.search-bar input {
    flex: 1;
    padding: 12px 15px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
}

.search-bar input:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.search-btn {
    background: var(--secondary-color);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.search-btn:hover {
    background: #2980b9;
}

.filters {
    display: flex;
    gap: 15px;
}

.filters select {
    padding: 10px 15px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    background: white;
    font-size: 0.9rem;
    cursor: pointer;
    transition: var(--transition);
}

.filters select:focus {
    outline: none;
    border-color: var(--secondary-color);
}

/* ===== BARCODE SECTION ===== */
.barcode-section {
    padding: 15px 20px;
    background: #e8f4fd;
    border-bottom: 1px solid var(--border-color);
}

.barcode-input {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

.barcode-input input {
    flex: 1;
    padding: 10px 15px;
    border: 2px solid var(--secondary-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    background: white;
}

.barcode-btn {
    background: var(--secondary-color);
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.barcode-btn:hover {
    background: #2980b9;
}

.barcode-status {
    font-size: 0.9rem;
    color: var(--text-color);
    text-align: center;
    padding: 5px;
    border-radius: 5px;
    background: rgba(255, 255, 255, 0.7);
}

.barcode-status.success {
    background: rgba(39, 174, 96, 0.1);
    color: var(--success-color);
}

.barcode-status.error {
    background: rgba(231, 76, 60, 0.1);
    color: var(--danger-color);
}

/* ===== PRODUCTS GRID ===== */
.products-grid {
    flex: 1;
    padding: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
    overflow-y: auto;
    max-height: 600px;
}

.product-card {
    background: white;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    transition: var(--transition);
    cursor: pointer;
}

.product-card:hover {
    border-color: var(--secondary-color);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.product-image {
    height: 150px;
    background: var(--light-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-image .no-image {
    color: var(--border-color);
    font-size: 3rem;
}

.product-info {
    padding: 15px;
}

.product-name {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--primary-color);
}

.product-price {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--success-color);
    margin-bottom: 10px;
}

.product-details {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    margin-bottom: 15px;
}

.product-stock {
    display: flex;
    align-items: center;
    gap: 5px;
}

.product-stock.in-stock {
    color: var(--success-color);
}

.product-stock.low-stock {
    color: var(--warning-color);
}

.product-stock.out-of-stock {
    color: var(--danger-color);
}

.product-barcode {
    display: flex;
    align-items: center;
    gap: 5px;
    color: var(--text-color);
    opacity: 0.7;
}

.product-actions {
    padding: 0 15px 15px;
}

.add-btn {
    width: 100%;
    background: linear-gradient(135deg, var(--success-color), #2ecc71);
    color: white;
    border: none;
    padding: 10px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-weight: 600;
}

.add-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #2ecc71, #27ae60);
    transform: translateY(-2px);
}

.add-btn:disabled {
    background: var(--border-color);
    cursor: not-allowed;
    opacity: 0.6;
}

.no-products {
    grid-column: 1 / -1;
    text-align: center;
    padding: 60px 20px;
    color: var(--border-color);
}

.no-products i {
    font-size: 4rem;
    margin-bottom: 20px;
}

.no-products h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
}

.no-products p {
    font-size: 1rem;
    opacity: 0.7;
}

/* ===== CART PANEL ===== */
.cart-panel {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.cart-actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    padding: 8px 12px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.8rem;
    font-weight: 600;
}

.action-btn.warning {
    background: var(--warning-color);
    color: white;
}

.action-btn.warning:hover {
    background: #e67e22;
    transform: translateY(-2px);
}

.action-btn.danger {
    background: var(--danger-color);
    color: white;
}

.action-btn.danger:hover {
    background: #c0392b;
    transform: translateY(-2px);
}

.action-btn.success {
    background: var(--success-color);
    color: white;
}

.action-btn.success:hover {
    background: #2ecc71;
    transform: translateY(-2px);
}

.action-btn.info {
    background: var(--info-color);
    color: white;
}

.action-btn.info:hover {
    background: #2980b9;
    transform: translateY(-2px);
}

.action-btn.large {
    padding: 15px 25px;
    font-size: 1.1rem;
    width: 100%;
    justify-content: center;
}

.action-btn:disabled {
    background: var(--border-color);
    cursor: not-allowed;
    opacity: 0.6;
}

/* ===== CART ITEMS ===== */
.cart-items {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    max-height: 400px;
}

.empty-cart {
    text-align: center;
    padding: 40px 20px;
    color: var(--border-color);
}

.empty-cart i {
    font-size: 3rem;
    margin-bottom: 15px;
}

.empty-cart p {
    font-size: 1.1rem;
}

.cart-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin-bottom: 10px;
    background: #f8f9fa;
    transition: var(--transition);
}

.cart-item:hover {
    background: #e9ecef;
    border-color: var(--secondary-color);
}

.item-info {
    flex: 1;
}

.item-info h4 {
    font-size: 1rem;
    margin-bottom: 5px;
    color: var(--primary-color);
}

.item-price {
    font-size: 0.9rem;
    color: var(--success-color);
    font-weight: 600;
}

.item-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.qty-btn {
    background: var(--secondary-color);
    color: white;
    border: none;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.qty-btn:hover {
    background: #2980b9;
    transform: scale(1.1);
}

.quantity {
    font-weight: 600;
    font-size: 1.1rem;
    min-width: 30px;
    text-align: center;
}

.item-total {
    font-weight: 700;
    color: var(--primary-color);
    min-width: 80px;
    text-align: left;
}

.remove-btn {
    background: var(--danger-color);
    color: white;
    border: none;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.remove-btn:hover {
    background: #c0392b;
    transform: scale(1.1);
}

/* ===== CART SUMMARY ===== */
.cart-summary {
    padding: 20px;
    border-top: 1px solid var(--border-color);
    background: #f8f9fa;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    font-size: 1rem;
}

.summary-row.total {
    border-top: 2px solid var(--primary-color);
    margin-top: 10px;
    padding-top: 15px;
    font-size: 1.2rem;
    font-weight: 700;
    color: var(--primary-color);
}

/* ===== DISCOUNT SECTION ===== */
.discount-section {
    padding: 20px;
    border-top: 1px solid var(--border-color);
    background: #fff3cd;
}

.discount-section h3 {
    margin-bottom: 15px;
    color: var(--warning-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.discount-inputs {
    display: flex;
    gap: 10px;
    align-items: center;
}

.discount-inputs input {
    flex: 1;
    padding: 10px;
    border: 2px solid var(--warning-color);
    border-radius: var(--border-radius);
    font-size: 0.9rem;
}

.discount-inputs input:focus {
    outline: none;
    border-color: #e67e22;
}

.discount-btn {
    background: var(--warning-color);
    color: white;
    border: none;
    padding: 10px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.discount-btn:hover {
    background: #e67e22;
}

/* ===== PAYMENT SECTION ===== */
.payment-section {
    padding: 20px;
    border-top: 1px solid var(--border-color);
    background: #d4edda;
}

.payment-section h3 {
    margin-bottom: 15px;
    color: var(--success-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.payment-methods {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.payment-btn {
    flex: 1;
    padding: 12px;
    border: 2px solid var(--success-color);
    background: white;
    color: var(--success-color);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    font-size: 0.8rem;
    font-weight: 600;
}

.payment-btn:hover {
    background: rgba(39, 174, 96, 0.1);
}

.payment-btn.active {
    background: var(--success-color);
    color: white;
}

.payment-btn i {
    font-size: 1.2rem;
}

.payment-amount {
    margin-top: 15px;
}

.payment-amount input {
    width: 100%;
    padding: 12px;
    border: 2px solid var(--success-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    margin-bottom: 10px;
}

.payment-amount input:focus {
    outline: none;
    border-color: #2ecc71;
}

.change-amount {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: rgba(39, 174, 96, 0.1);
    border-radius: var(--border-radius);
    font-weight: 600;
}

/* ===== ACTION BUTTONS ===== */
.action-buttons {
    padding: 20px;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 10px;
}

/* ===== QUICK ACTIONS ===== */
.quick-actions {
    position: fixed;
    bottom: 20px;
    left: 20px;
    display: flex;
    gap: 15px;
    z-index: 100;
}

.quick-btn {
    background: white;
    border: 2px solid var(--secondary-color);
    color: var(--secondary-color);
    padding: 15px;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: var(--shadow);
    width: 60px;
    height: 60px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
}

.quick-btn:hover {
    background: var(--secondary-color);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(52, 152, 219, 0.3);
}

.quick-btn i {
    font-size: 1.2rem;
    margin-bottom: 2px;
}

.quick-btn span {
    font-size: 0.7rem;
    font-weight: 600;
    position: absolute;
    bottom: -25px;
    white-space: nowrap;
    background: var(--primary-color);
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    opacity: 0;
    transition: var(--transition);
}

.quick-btn:hover span {
    opacity: 1;
    bottom: -30px;
}

/* ===== MODAL STYLES ===== */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    backdrop-filter: blur(5px);
}

.modal-content {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 90%;
    max-height: 90%;
    overflow: hidden;
    transform: scale(0.9);
    opacity: 0;
    transition: var(--transition);
}

.modal:not([style*="display: none"]) .modal-content {
    transform: scale(1);
    opacity: 1;
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-color), var(--dark-bg));
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.close-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.modal-body {
    padding: 20px;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    background: #f8f9fa;
}

/* ===== SETTINGS TABS ===== */
.settings-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 20px;
}

.tab-btn {
    flex: 1;
    padding: 15px;
    border: none;
    background: transparent;
    cursor: pointer;
    transition: var(--transition);
    border-bottom: 3px solid transparent;
    font-weight: 600;
}

.tab-btn:hover {
    background: rgba(52, 152, 219, 0.1);
}

.tab-btn.active {
    background: rgba(52, 152, 219, 0.1);
    border-bottom-color: var(--secondary-color);
    color: var(--secondary-color);
}

/* ===== CALCULATOR STYLES ===== */
.modal-content.calculator {
    width: 350px;
}

.calculator-body {
    padding: 20px;
}

.calculator-display {
    background: #2c3e50;
    color: white;
    padding: 20px;
    font-size: 2rem;
    text-align: left;
    border-radius: var(--border-radius);
    margin-bottom: 20px;
    min-height: 60px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    font-family: 'Courier New', monospace;
}

.calculator-buttons {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
}

.calc-btn {
    padding: 20px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-size: 1.2rem;
    font-weight: 600;
    background: var(--light-bg);
    color: var(--text-color);
}

.calc-btn:hover {
    background: var(--border-color);
    transform: scale(1.05);
}

.calc-btn.clear {
    background: var(--danger-color);
    color: white;
}

.calc-btn.clear:hover {
    background: #c0392b;
}

.calc-btn.equals {
    background: var(--success-color);
    color: white;
    grid-row: span 2;
}

.calc-btn.equals:hover {
    background: #2ecc71;
}

.calc-btn.zero {
    grid-column: span 2;
}

/* ===== BUTTONS ===== */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn.success {
    background: var(--success-color);
    color: white;
}

.btn.success:hover {
    background: #2ecc71;
    transform: translateY(-2px);
}

.btn.secondary {
    background: var(--border-color);
    color: var(--text-color);
}

.btn.secondary:hover {
    background: #95a5a6;
    transform: translateY(-2px);
}

/* ===== NOTIFICATIONS ===== */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    padding: 15px 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    transform: translateX(400px);
    opacity: 0;
    transition: var(--transition);
    z-index: 3000;
    border-left: 4px solid var(--info-color);
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification.success {
    border-left-color: var(--success-color);
    color: var(--success-color);
}

.notification.error {
    border-left-color: var(--danger-color);
    color: var(--danger-color);
}

.notification.warning {
    border-left-color: var(--warning-color);
    color: var(--warning-color);
}

.notification i {
    font-size: 1.2rem;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1200px) {
    .cashier-main {
        grid-template-columns: 1.5fr 1fr;
        gap: 15px;
        padding: 15px;
    }

    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 10px;
    }
}

@media (max-width: 768px) {
    .cashier-main {
        grid-template-columns: 1fr;
        gap: 15px;
        padding: 10px;
    }

    .header-content {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .header-right {
        justify-content: center;
    }

    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        max-height: 400px;
    }

    .quick-actions {
        position: relative;
        bottom: auto;
        left: auto;
        justify-content: center;
        margin: 20px 0;
    }

    .quick-btn {
        position: relative;
    }

    .quick-btn span {
        position: static;
        opacity: 1;
        background: transparent;
        color: var(--text-color);
        font-size: 0.6rem;
        margin-top: 5px;
    }
}

@media (max-width: 480px) {
    .header-btn {
        padding: 8px 10px;
        font-size: 0.8rem;
    }

    .header-btn span {
        display: none;
    }

    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }

    .modal-content {
        width: 95%;
        margin: 10px;
    }

    .calculator-buttons {
        gap: 5px;
    }

    .calc-btn {
        padding: 15px;
        font-size: 1rem;
    }
}

/* ===== SETTINGS STYLES ===== */
.tab-content {
    padding: 20px 0;
}

.setting-group {
    margin-bottom: 20px;
}

.setting-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--primary-color);
}

.setting-group input,
.setting-group select {
    width: 100%;
    padding: 10px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
}

.setting-group input:focus,
.setting-group select:focus {
    outline: none;
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* ===== REPORTS STYLES ===== */
.reports-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    padding: 20px 0;
}

.report-card {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 30px 20px;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
}

.report-card:hover {
    border-color: var(--secondary-color);
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.report-card i {
    font-size: 3rem;
    color: var(--secondary-color);
    margin-bottom: 15px;
}

.report-card h4 {
    font-size: 1.3rem;
    margin-bottom: 10px;
    color: var(--primary-color);
}

.report-card p {
    color: var(--text-color);
    opacity: 0.8;
}

/* ===== HELD SALES STYLES ===== */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--border-color);
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 20px;
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
}

.empty-state p {
    font-size: 1rem;
    opacity: 0.7;
}

.held-sale-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin-bottom: 15px;
    background: #f8f9fa;
    transition: var(--transition);
}

.held-sale-item:hover {
    background: #e9ecef;
    border-color: var(--secondary-color);
}

.sale-info h4 {
    margin-bottom: 5px;
    color: var(--primary-color);
}

.sale-info p {
    margin: 2px 0;
    color: var(--text-color);
    opacity: 0.8;
    font-size: 0.9rem;
}

.sale-actions {
    display: flex;
    gap: 10px;
}

.sale-actions .btn {
    padding: 8px 15px;
    font-size: 0.9rem;
}

/* ===== ENHANCED ANIMATIONS ===== */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.product-card {
    animation: fadeInUp 0.3s ease;
}

.cart-item {
    animation: slideInRight 0.3s ease;
}

.notification {
    animation: slideInLeft 0.3s ease;
}

.add-btn:active {
    animation: pulse 0.2s ease;
}

/* ===== LOADING STATES ===== */
.loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--border-color);
    border-top-color: var(--secondary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* ===== PRINT STYLES ===== */
@media print {
    body * {
        visibility: hidden;
    }

    .print-area,
    .print-area * {
        visibility: visible;
    }

    .print-area {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
    }

    .no-print {
        display: none !important;
    }
}

/* ===== ACCESSIBILITY ===== */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

button:focus,
input:focus,
select:focus {
    outline: 2px solid var(--secondary-color);
    outline-offset: 2px;
}

/* ===== DARK MODE SUPPORT ===== */
@media (prefers-color-scheme: dark) {
    :root {
        --primary-color: #ecf0f1;
        --text-color: #ecf0f1;
        --light-bg: #34495e;
        --border-color: #7f8c8d;
    }

    body {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    }

    .products-panel,
    .cart-panel,
    .modal-content {
        background: #2c3e50;
        color: #ecf0f1;
    }

    .product-card {
        background: #34495e;
        border-color: #7f8c8d;
    }

    .cart-item {
        background: #34495e;
        border-color: #7f8c8d;
    }
}
