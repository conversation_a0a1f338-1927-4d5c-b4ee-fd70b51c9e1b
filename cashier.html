<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام الكاشير - البيع المباشر</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="cashier-styles.css">
</head>
<body>
    <!-- Header -->
    <header class="cashier-header">
        <div class="header-content">
            <div class="logo-section">
                <i class="fas fa-cash-register"></i>
                <h1>نظام الكاشير</h1>
            </div>
            <div class="header-info">
                <div class="cashier-info">
                    <i class="fas fa-user"></i>
                    <span id="cashierName">💰 الكاشير: نظام المبيعات</span>
                </div>
                <div class="date-time">
                    <i class="fas fa-clock"></i>
                    <span id="currentDateTime"></span>
                </div>
                <div class="shift-info">
                    <i class="fas fa-calendar-day"></i>
                    <span>الوردية: صباحية</span>
                </div>
            </div>
            <div class="header-actions">
                <button class="btn btn-secondary" onclick="showReportsModal()">
                    <i class="fas fa-chart-bar"></i>
                    التقارير
                </button>
                <button class="btn btn-warning" onclick="showSettingsModal()">
                    <i class="fas fa-cog"></i>
                    الإعدادات
                </button>
                <button class="btn btn-primary" onclick="goToMainSite()">
                    <i class="fas fa-home"></i>
                    الموقع الرئيسي
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="cashier-container">
        <!-- Left Panel - Products -->
        <div class="products-panel">
            <div class="panel-header">
                <h3><i class="fas fa-boxes"></i> المنتجات</h3>
                <div class="search-section">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="productSearch" placeholder="البحث عن منتج..." onkeyup="searchProducts()">
                    </div>
                    <div class="filter-buttons">
                        <button class="filter-btn active" onclick="filterProducts('all')">الكل</button>
                        <button class="filter-btn" onclick="filterProducts('category')">حسب الفئة</button>
                        <button class="filter-btn" onclick="filterProducts('barcode')">باركود</button>
                    </div>
                </div>
            </div>
            
            <div class="categories-tabs" id="categoriesTabs">
                <!-- Categories will be loaded here -->
            </div>
            
            <div class="products-grid" id="productsGrid">
                <!-- Products will be loaded here -->
            </div>
        </div>

        <!-- Center Panel - Cart -->
        <div class="cart-panel">
            <div class="panel-header">
                <h3><i class="fas fa-shopping-cart"></i> سلة المشتريات</h3>
                <div class="cart-actions">
                    <button class="btn btn-sm btn-warning" onclick="holdSale()">
                        <i class="fas fa-pause"></i>
                        تعليق
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="clearCart()">
                        <i class="fas fa-trash"></i>
                        مسح
                    </button>
                </div>
            </div>
            
            <div class="cart-items" id="cartItems">
                <div class="empty-cart">
                    <i class="fas fa-shopping-cart"></i>
                    <p>السلة فارغة</p>
                    <small>أضف منتجات للبدء في البيع</small>
                </div>
            </div>
            
            <div class="cart-summary">
                <div class="summary-row">
                    <span>المجموع الفرعي:</span>
                    <span id="subtotal">0 دينار</span>
                </div>
                <div class="summary-row">
                    <span>الخصم:</span>
                    <span id="discount">0 دينار</span>
                </div>
                <div class="summary-row">
                    <span>الضريبة (15%):</span>
                    <span id="tax">0 دينار</span>
                </div>
                <div class="summary-row total">
                    <span>المجموع الكلي:</span>
                    <span id="total">0 دينار</span>
                </div>
            </div>
        </div>

        <!-- Right Panel - Payment -->
        <div class="payment-panel">
            <div class="panel-header">
                <h3><i class="fas fa-credit-card"></i> الدفع</h3>
                <div class="sale-type">
                    <label>
                        <input type="radio" name="saleType" value="retail" checked onchange="updateSaleType()">
                        <span>مفرد</span>
                    </label>
                    <label>
                        <input type="radio" name="saleType" value="wholesale" onchange="updateSaleType()">
                        <span>جملة</span>
                    </label>
                </div>
            </div>
            
            <div class="customer-section">
                <h4><i class="fas fa-user"></i> معلومات العميل</h4>
                <div class="form-group">
                    <input type="text" id="customerName" placeholder="اسم العميل (اختياري)">
                </div>
                <div class="form-group">
                    <input type="tel" id="customerPhone" placeholder="رقم الهاتف (اختياري)">
                </div>
            </div>
            
            <div class="discount-section">
                <h4><i class="fas fa-percentage"></i> الخصم</h4>
                <div class="discount-controls">
                    <div class="form-group">
                        <input type="number" id="discountAmount" placeholder="مبلغ الخصم" onchange="applyDiscount()">
                    </div>
                    <div class="form-group">
                        <input type="number" id="discountPercent" placeholder="نسبة الخصم %" onchange="applyDiscountPercent()">
                    </div>
                    <button class="btn btn-sm btn-info" onclick="showCouponsModal()">
                        <i class="fas fa-ticket-alt"></i>
                        كوبون
                    </button>
                </div>
            </div>
            
            <div class="payment-methods">
                <h4><i class="fas fa-money-bill-wave"></i> طريقة الدفع</h4>
                <div class="payment-buttons">
                    <button class="payment-btn active" data-method="cash" onclick="selectPaymentMethod('cash')">
                        <i class="fas fa-money-bill"></i>
                        نقدي
                    </button>
                    <button class="payment-btn" data-method="card" onclick="selectPaymentMethod('card')">
                        <i class="fas fa-credit-card"></i>
                        بطاقة
                    </button>
                    <button class="payment-btn" data-method="transfer" onclick="selectPaymentMethod('transfer')">
                        <i class="fas fa-exchange-alt"></i>
                        تحويل
                    </button>
                    <button class="payment-btn" data-method="mixed" onclick="selectPaymentMethod('mixed')">
                        <i class="fas fa-coins"></i>
                        مختلط
                    </button>
                </div>
            </div>
            
            <div class="payment-amount" id="paymentAmount">
                <div class="form-group">
                    <label>المبلغ المدفوع:</label>
                    <input type="number" id="paidAmount" placeholder="0" onchange="calculateChange()">
                </div>
                <div class="change-amount">
                    <span>الباقي:</span>
                    <span id="changeAmount">0 دينار</span>
                </div>
            </div>
            
            <div class="action-buttons">
                <button class="btn btn-success btn-large" onclick="completeSale()" id="completeSaleBtn" disabled>
                    <i class="fas fa-check"></i>
                    إتمام البيع
                </button>
                <button class="btn btn-info btn-large" onclick="printInvoice()">
                    <i class="fas fa-print"></i>
                    طباعة الفاتورة
                </button>
            </div>
        </div>
    </div>

    <!-- Quick Actions Bar -->
    <div class="quick-actions">
        <button class="quick-btn" onclick="showHeldSales()">
            <i class="fas fa-pause-circle"></i>
            <span>المبيعات المعلقة</span>
        </button>
        <button class="quick-btn" onclick="showLastSales()">
            <i class="fas fa-history"></i>
            <span>آخر المبيعات</span>
        </button>
        <button class="quick-btn" onclick="openCashDrawer()">
            <i class="fas fa-cash-register"></i>
            <span>فتح الدرج</span>
        </button>
        <button class="quick-btn" onclick="showCalculator()">
            <i class="fas fa-calculator"></i>
            <span>الآلة الحاسبة</span>
        </button>
        <button class="quick-btn" onclick="showSettingsModal()">
            <i class="fas fa-cog"></i>
            <span>الإعدادات</span>
        </button>
    </div>

    <!-- Modals will be added here -->
    <div id="modalContainer"></div>

    <!-- Scripts -->
    <script src="cashier-script.js"></script>
    <script>
        // متغيرات النظام العامة
        let cart = [];
        let currentSaleType = 'retail';
        let currentPaymentMethod = 'cash';
        let currentDiscount = 0;
        let currentTax = 0.15; // 15% ضريبة
        let heldSales = [];
        let lastInvoiceNumber = 1;

        // تحميل نظام الكاشير مباشرة
        document.addEventListener('DOMContentLoaded', function() {
            initializeCashierSystem();
            updateDateTime();
            setInterval(updateDateTime, 1000);

            // التأكد من تحميل الوظائف من cashier-script.js
            setTimeout(() => {
                console.log('🔍 فحص الوظائف المتاحة...');
                console.log('showCalculator:', typeof showCalculator);
                console.log('showSettingsModal:', typeof showSettingsModal);

                // تحميل الوظائف الجديدة
                console.log('🔧 تحميل الوظائف الجديدة...');
                window.showSettingsModal = openSettings;
                if (typeof openCashDrawer !== 'function') {
                    window.openCashDrawer = function() {
                        alert('تم فتح درج النقد');
                        console.log('💰 تم فتح درج النقد');
                    };
                }
                if (typeof showHeldSales !== 'function') {
                    window.showHeldSales = function() {
                        alert('المبيعات المعلقة غير متاحة حالياً');
                    };
                }
                if (typeof showLastSales !== 'function') {
                    window.showLastSales = function() {
                        alert('آخر المبيعات غير متاحة حالياً');
                    };
                }

                console.log('✅ تم التحقق من جميع الوظائف');
            }, 100);
        });

        // تم حذف وظيفة التحقق من تسجيل الدخول

        // تم حذف وظيفة redirectToLogin

        // تم حذف وظيفة updateUserInterface

        // تم حذف جميع الوظائف المرتبطة بواجهة المستخدم

        function initializeCashierSystem() {
            loadProducts();
            loadCategories();
            updateCartDisplay();
            updateSummary();

            // تحميل آخر رقم فاتورة
            const lastInvoice = localStorage.getItem('lastInvoiceNumber');
            if (lastInvoice) {
                lastInvoiceNumber = parseInt(lastInvoice) + 1;
            }
        }

        function updateDateTime() {
            const now = new Date();
            const dateTimeElement = document.getElementById('currentDateTime');
            if (dateTimeElement) {
                const options = {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                };
                dateTimeElement.textContent = now.toLocaleDateString('en-US', options);
            }
        }

        function loadProducts() {
            updateProductsDisplay();
        }

        function updateProductsDisplay() {
            const products = JSON.parse(localStorage.getItem('products')) || [];
            const productsGrid = document.getElementById('productsGrid');

            if (products.length === 0) {
                productsGrid.innerHTML = `
                    <div class="no-products">
                        <i class="fas fa-box-open"></i>
                        <p>لا توجد منتجات متاحة</p>
                        <small>يرجى إضافة منتجات من لوحة الإدارة</small>
                    </div>
                `;
                return;
            }

            productsGrid.innerHTML = products.map(product => {
                const displayPrice = currentSaleType === 'wholesale' && product.wholesalePrice ?
                    product.wholesalePrice : product.price;
                const priceLabel = currentSaleType === 'wholesale' ? 'جملة' : 'مفرد';

                return `
                    <div class="product-card" onclick="addToCart('${product.id}')">
                        <div class="product-image">
                            ${product.image ? `<img src="${product.image}" alt="${product.name}">` : '<i class="fas fa-image"></i>'}
                        </div>
                        <div class="product-info">
                            <h4>${product.name}</h4>
                            <div class="product-prices">
                                <p class="product-price current-price">${formatCurrency(displayPrice)} <span class="price-type">(${priceLabel})</span></p>
                                ${currentSaleType === 'wholesale' && product.price !== product.wholesalePrice ?
                                    `<p class="product-price retail-price">مفرد: ${formatCurrency(product.price)}</p>` : ''}
                                ${currentSaleType === 'retail' && product.wholesalePrice ?
                                    `<p class="product-price wholesale-price">جملة: ${formatCurrency(product.wholesalePrice)}</p>` : ''}
                            </div>
                            <p class="product-stock">المخزون: ${product.quantity || 0}</p>
                        </div>
                    </div>
                `;
            }).join('');
        }

        function loadCategories() {
            const categories = JSON.parse(localStorage.getItem('categories')) || [];
            const categoriesTabs = document.getElementById('categoriesTabs');

            categoriesTabs.innerHTML = `
                <button class="category-tab active" onclick="filterByCategory('all')">الكل</button>
                ${categories.map(category => `
                    <button class="category-tab" onclick="filterByCategory('${category.id}')">${category.name}</button>
                `).join('')}
            `;
        }

        function filterByCategory(categoryId) {
            const products = JSON.parse(localStorage.getItem('products')) || [];
            const productsGrid = document.getElementById('productsGrid');

            // تحديث الأزرار النشطة
            document.querySelectorAll('.category-tab').forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');

            let filteredProducts = products;
            if (categoryId !== 'all') {
                const categories = JSON.parse(localStorage.getItem('categories')) || [];
                const category = categories.find(c => c.id === categoryId);
                if (category) {
                    filteredProducts = products.filter(p => p.category === category.name);
                }
            }

            productsGrid.innerHTML = filteredProducts.map(product => {
                const displayPrice = currentSaleType === 'wholesale' && product.wholesalePrice ?
                    product.wholesalePrice : product.price;
                const priceLabel = currentSaleType === 'wholesale' ? 'جملة' : 'مفرد';

                return `
                    <div class="product-card" onclick="addToCart('${product.id}')">
                        <div class="product-image">
                            ${product.image ? `<img src="${product.image}" alt="${product.name}">` : '<i class="fas fa-image"></i>'}
                        </div>
                        <div class="product-info">
                            <h4>${product.name}</h4>
                            <div class="product-prices">
                                <p class="product-price current-price">${formatCurrency(displayPrice)} <span class="price-type">(${priceLabel})</span></p>
                                ${currentSaleType === 'wholesale' && product.price !== product.wholesalePrice ?
                                    `<p class="product-price retail-price">مفرد: ${formatCurrency(product.price)}</p>` : ''}
                                ${currentSaleType === 'retail' && product.wholesalePrice ?
                                    `<p class="product-price wholesale-price">جملة: ${formatCurrency(product.wholesalePrice)}</p>` : ''}
                            </div>
                            <p class="product-stock">المخزون: ${product.quantity || 0}</p>
                        </div>
                    </div>
                `;
            }).join('');
        }

        function goToMainSite() {
            if (confirm('هل تريد الانتقال إلى الموقع الرئيسي؟')) {
                window.location.href = 'index.html';
            }
        }

        function addToCart(productId) {
            const products = JSON.parse(localStorage.getItem('products')) || [];
            const product = products.find(p => p.id === productId);

            if (!product) {
                alert('المنتج غير موجود');
                return;
            }

            if (product.quantity <= 0) {
                alert('المنتج غير متوفر في المخزون');
                return;
            }

            // تحديد السعر حسب نوع البيع
            const itemPrice = currentSaleType === 'wholesale' && product.wholesalePrice ?
                parseFloat(product.wholesalePrice) : parseFloat(product.price);

            const existingItem = cart.find(item => item.id === productId);

            if (existingItem) {
                if (existingItem.quantity >= product.quantity) {
                    alert('لا يمكن إضافة كمية أكثر من المتوفر في المخزون');
                    return;
                }
                existingItem.quantity++;
                // تحديث السعر في حالة تغيير نوع البيع
                existingItem.price = itemPrice;
                existingItem.priceType = currentSaleType;
            } else {
                cart.push({
                    id: product.id,
                    name: product.name,
                    price: itemPrice,
                    retailPrice: parseFloat(product.price),
                    wholesalePrice: product.wholesalePrice ? parseFloat(product.wholesalePrice) : null,
                    quantity: 1,
                    stock: product.quantity,
                    image: product.image,
                    priceType: currentSaleType
                });
            }

            updateCartDisplay();
            updateSummary();
        }

        function updateCartDisplay() {
            const cartItems = document.getElementById('cartItems');

            if (cart.length === 0) {
                cartItems.innerHTML = `
                    <div class="empty-cart">
                        <i class="fas fa-shopping-cart"></i>
                        <p>السلة فارغة</p>
                        <small>أضف منتجات للبدء في البيع</small>
                    </div>
                `;
                return;
            }

            cartItems.innerHTML = cart.map((item, index) => {
                const priceTypeLabel = item.priceType === 'wholesale' ? 'جملة' : 'مفرد';
                const priceTypeClass = item.priceType === 'wholesale' ? 'wholesale' : 'retail';

                return `
                    <div class="cart-item">
                        <div class="item-image">
                            ${item.image ? `<img src="${item.image}" alt="${item.name}">` : '<i class="fas fa-box"></i>'}
                        </div>
                        <div class="item-details">
                            <h5>${item.name}</h5>
                            <p class="item-price ${priceTypeClass}">
                                ${formatCurrency(item.price)}
                                <span class="price-type-badge">${priceTypeLabel}</span>
                            </p>
                            ${item.priceType === 'wholesale' && item.retailPrice ?
                                `<small class="retail-price-small">مفرد: ${formatCurrency(item.retailPrice)}</small>` : ''}
                        </div>
                        <div class="item-controls">
                            <button class="qty-btn" onclick="decreaseQuantity(${index})">-</button>
                            <span class="quantity">${item.quantity}</span>
                            <button class="qty-btn" onclick="increaseQuantity(${index})">+</button>
                        </div>
                        <div class="item-total">
                            ${formatCurrency(item.price * item.quantity)}
                        </div>
                        <button class="remove-btn" onclick="removeFromCart(${index})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                `;
            }).join('');
        }

        function increaseQuantity(index) {
            const item = cart[index];
            if (item.quantity >= item.stock) {
                alert('لا يمكن إضافة كمية أكثر من المتوفر في المخزون');
                return;
            }
            item.quantity++;
            updateCartDisplay();
            updateSummary();
        }

        function decreaseQuantity(index) {
            const item = cart[index];
            if (item.quantity > 1) {
                item.quantity--;
                updateCartDisplay();
                updateSummary();
            }
        }

        function removeFromCart(index) {
            cart.splice(index, 1);
            updateCartDisplay();
            updateSummary();
        }

        function clearCart() {
            if (cart.length === 0) return;

            if (confirm('هل أنت متأكد من مسح جميع المنتجات من السلة؟')) {
                cart = [];
                updateCartDisplay();
                updateSummary();
            }
        }

        function updateSummary() {
            const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            const discountAmount = currentDiscount;
            const taxableAmount = subtotal - discountAmount;
            const taxAmount = taxableAmount * currentTax;
            const total = taxableAmount + taxAmount;

            document.getElementById('subtotal').textContent = formatCurrency(subtotal);
            document.getElementById('discount').textContent = formatCurrency(discountAmount);
            document.getElementById('tax').textContent = formatCurrency(taxAmount);
            document.getElementById('total').textContent = formatCurrency(total);

            // تفعيل/تعطيل زر إتمام البيع
            const completeSaleBtn = document.getElementById('completeSaleBtn');
            completeSaleBtn.disabled = cart.length === 0;
        }

        function updateSaleType() {
            const saleType = document.querySelector('input[name="saleType"]:checked').value;
            currentSaleType = saleType;

            // تطبيق أسعار مختلفة للجملة والمفرد
            cart.forEach(item => {
                const products = JSON.parse(localStorage.getItem('products')) || [];
                const product = products.find(p => p.id === item.id);

                if (product) {
                    if (saleType === 'wholesale' && product.wholesalePrice) {
                        item.price = parseFloat(product.wholesalePrice);
                        item.priceType = 'wholesale';
                    } else {
                        item.price = parseFloat(product.price);
                        item.priceType = 'retail';
                    }
                }
            });

            // تحديث عرض السلة والملخص
            updateCartDisplay();
            updateSummary();
            updateProductsDisplay();

            console.log(`تم تغيير نوع البيع إلى: ${saleType === 'wholesale' ? 'جملة' : 'مفرد'}`);
        }

        function selectPaymentMethod(method) {
            currentPaymentMethod = method;

            document.querySelectorAll('.payment-btn').forEach(btn => btn.classList.remove('active'));
            document.querySelector(`[data-method="${method}"]`).classList.add('active');

            const paymentAmount = document.getElementById('paymentAmount');
            if (method === 'cash') {
                paymentAmount.style.display = 'block';
            } else if (method === 'mixed') {
                paymentAmount.style.display = 'block';
                // يمكن إضافة واجهة للدفع المختلط
            } else {
                paymentAmount.style.display = 'none';
                calculateChange();
            }
        }

        function calculateChange() {
            const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0) - currentDiscount;
            const totalWithTax = total + (total * currentTax);
            const paidAmount = parseFloat(document.getElementById('paidAmount').value) || 0;
            const change = paidAmount - totalWithTax;

            document.getElementById('changeAmount').textContent = formatCurrency(Math.max(0, change));
        }

        function applyDiscount() {
            const discountAmount = parseFloat(document.getElementById('discountAmount').value) || 0;
            currentDiscount = discountAmount;
            document.getElementById('discountPercent').value = '';
            updateSummary();
        }

        function applyDiscountPercent() {
            const discountPercent = parseFloat(document.getElementById('discountPercent').value) || 0;
            const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            currentDiscount = (subtotal * discountPercent) / 100;
            document.getElementById('discountAmount').value = currentDiscount.toFixed(2);
            updateSummary();
        }

        function completeSale() {
            if (cart.length === 0) {
                alert('السلة فارغة');
                return;
            }

            const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0) - currentDiscount;
            const totalWithTax = total + (total * currentTax);

            if (currentPaymentMethod === 'cash') {
                const paidAmount = parseFloat(document.getElementById('paidAmount').value) || 0;
                if (paidAmount < totalWithTax) {
                    alert('المبلغ المدفوع أقل من المطلوب');
                    return;
                }
            }

            // إنشاء الفاتورة
            const invoice = createInvoice();

            // حفظ البيع
            saveSale(invoice);

            // تحديث المخزون
            updateStock();

            // طباعة الفاتورة
            printInvoice(invoice);

            // مسح السلة
            cart = [];
            currentDiscount = 0;
            document.getElementById('discountAmount').value = '';
            document.getElementById('discountPercent').value = '';
            document.getElementById('paidAmount').value = '';
            document.getElementById('customerName').value = '';
            document.getElementById('customerPhone').value = '';

            updateCartDisplay();
            updateSummary();

            alert('تم إتمام البيع بنجاح!');
        }

        function createInvoice() {
            const now = new Date();
            const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            const discountAmount = currentDiscount;
            const taxableAmount = subtotal - discountAmount;
            const taxAmount = taxableAmount * currentTax;
            const total = taxableAmount + taxAmount;

            return {
                id: `INV-${lastInvoiceNumber}`,
                number: lastInvoiceNumber++,
                date: now.toISOString(),
                cashier: { name: 'الكاشير', role: 'cashier', roleText: 'الكاشير', roleIcon: '💰' },
                customer: {
                    name: document.getElementById('customerName').value || 'عميل نقدي',
                    phone: document.getElementById('customerPhone').value || ''
                },
                items: [...cart],
                saleType: currentSaleType,
                paymentMethod: currentPaymentMethod,
                subtotal: subtotal,
                discount: discountAmount,
                tax: taxAmount,
                total: total,
                paidAmount: currentPaymentMethod === 'cash' ? parseFloat(document.getElementById('paidAmount').value) || total : total,
                change: currentPaymentMethod === 'cash' ? Math.max(0, (parseFloat(document.getElementById('paidAmount').value) || 0) - total) : 0
            };
        }

        function saveSale(invoice) {
            const sales = JSON.parse(localStorage.getItem('cashierSales')) || [];
            sales.push(invoice);
            localStorage.setItem('cashierSales', JSON.stringify(sales));
            localStorage.setItem('lastInvoiceNumber', lastInvoiceNumber.toString());
        }

        function updateStock() {
            const products = JSON.parse(localStorage.getItem('products')) || [];

            cart.forEach(cartItem => {
                const productIndex = products.findIndex(p => p.id === cartItem.id);
                if (productIndex !== -1) {
                    products[productIndex].quantity = Math.max(0, products[productIndex].quantity - cartItem.quantity);
                }
            });

            localStorage.setItem('products', JSON.stringify(products));
            loadProducts(); // إعادة تحميل المنتجات لتحديث العرض
        }

        function formatCurrency(amount) {
            return new Intl.NumberFormat('ar-IQ', {
                style: 'currency',
                currency: 'IQD',
                minimumFractionDigits: 0
            }).format(amount);
        }

        // وظائف إضافية
        function searchProducts() {
            const searchTerm = document.getElementById('productSearch').value.toLowerCase();
            const products = JSON.parse(localStorage.getItem('products')) || [];
            const filteredProducts = products.filter(product =>
                product.name.toLowerCase().includes(searchTerm) ||
                product.nameEn?.toLowerCase().includes(searchTerm) ||
                product.barcode?.includes(searchTerm)
            );

            const productsGrid = document.getElementById('productsGrid');
            productsGrid.innerHTML = filteredProducts.map(product => {
                const displayPrice = currentSaleType === 'wholesale' && product.wholesalePrice ?
                    product.wholesalePrice : product.price;
                const priceLabel = currentSaleType === 'wholesale' ? 'جملة' : 'مفرد';

                return `
                    <div class="product-card" onclick="addToCart('${product.id}')">
                        <div class="product-image">
                            ${product.image ? `<img src="${product.image}" alt="${product.name}">` : '<i class="fas fa-image"></i>'}
                        </div>
                        <div class="product-info">
                            <h4>${product.name}</h4>
                            <div class="product-prices">
                                <p class="product-price current-price">${formatCurrency(displayPrice)} <span class="price-type">(${priceLabel})</span></p>
                                ${currentSaleType === 'wholesale' && product.price !== product.wholesalePrice ?
                                    `<p class="product-price retail-price">مفرد: ${formatCurrency(product.price)}</p>` : ''}
                                ${currentSaleType === 'retail' && product.wholesalePrice ?
                                    `<p class="product-price wholesale-price">جملة: ${formatCurrency(product.wholesalePrice)}</p>` : ''}
                            </div>
                            <p class="product-stock">المخزون: ${product.quantity || 0}</p>
                        </div>
                    </div>
                `;
            }).join('');
        }

        function filterProducts(type) {
            document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            if (type === 'all') {
                loadProducts();
            }
        }

        function holdSale() {
            if (cart.length === 0) {
                alert('السلة فارغة');
                return;
            }

            const heldSale = {
                id: Date.now(),
                items: [...cart],
                discount: currentDiscount,
                saleType: currentSaleType,
                timestamp: new Date().toISOString()
            };

            heldSales.push(heldSale);
            localStorage.setItem('heldSales', JSON.stringify(heldSales));

            cart = [];
            currentDiscount = 0;
            updateCartDisplay();
            updateSummary();

            alert('تم تعليق البيع بنجاح');
        }

        function showHeldSales() {
            // يمكن إضافة نافذة منبثقة لعرض المبيعات المعلقة
            alert('ميزة المبيعات المعلقة قيد التطوير');
        }





        function printInvoice(invoice) {
            if (!invoice) {
                alert('لا توجد فاتورة للطباعة');
                return;
            }

            const printWindow = window.open('', '_blank');
            const invoiceHTML = generateInvoiceHTML(invoice);

            printWindow.document.write(invoiceHTML);
            printWindow.document.close();
            printWindow.print();
        }

        function generateInvoiceHTML(invoice) {
            return `
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>فاتورة رقم ${invoice.number}</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        .invoice-header { text-align: center; margin-bottom: 20px; }
                        .invoice-details { margin-bottom: 20px; }
                        .items-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                        .items-table th, .items-table td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                        .items-table th { background-color: #f2f2f2; }
                        .totals { text-align: right; }
                        .total-row { font-weight: bold; font-size: 1.2em; }
                    </style>
                </head>
                <body>
                    <div class="invoice-header">
                        <h1>فاتورة مبيعات</h1>
                        <h2>رقم الفاتورة: ${invoice.number}</h2>
                    </div>

                    <div class="invoice-details">
                        <p><strong>التاريخ:</strong> ${new Date(invoice.date).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric'
                        })}</p>
                        <p><strong>الوقت:</strong> ${new Date(invoice.date).toLocaleTimeString('en-US', {
                            hour: '2-digit',
                            minute: '2-digit',
                            hour12: true
                        })}</p>
                        <p><strong>الكاشير:</strong> ${invoice.cashier.name}</p>
                        <p><strong>العميل:</strong> ${invoice.customer.name}</p>
                        ${invoice.customer.phone ? `<p><strong>الهاتف:</strong> ${invoice.customer.phone}</p>` : ''}
                        <p><strong>نوع البيع:</strong> ${invoice.saleType === 'retail' ? 'مفرد' : 'جملة'}</p>
                        <p><strong>طريقة الدفع:</strong> ${getPaymentMethodText(invoice.paymentMethod)}</p>
                    </div>

                    <table class="items-table">
                        <thead>
                            <tr>
                                <th>المنتج</th>
                                <th>الكمية</th>
                                <th>السعر</th>
                                <th>المجموع</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${invoice.items.map(item => `
                                <tr>
                                    <td>
                                        ${item.name}
                                        <br><small style="color: #666; font-size: 0.8em;">(${item.priceType === 'wholesale' ? 'جملة' : 'مفرد'})</small>
                                    </td>
                                    <td>${item.quantity}</td>
                                    <td>${formatCurrency(item.price)}</td>
                                    <td>${formatCurrency(item.price * item.quantity)}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>

                    <div class="totals">
                        <p>المجموع الفرعي: ${formatCurrency(invoice.subtotal)}</p>
                        ${invoice.discount > 0 ? `<p>الخصم: ${formatCurrency(invoice.discount)}</p>` : ''}
                        <p>الضريبة (15%): ${formatCurrency(invoice.tax)}</p>
                        <p class="total-row">المجموع الكلي: ${formatCurrency(invoice.total)}</p>
                        ${invoice.paymentMethod === 'cash' ? `
                            <p>المبلغ المدفوع: ${formatCurrency(invoice.paidAmount)}</p>
                            <p>الباقي: ${formatCurrency(invoice.change)}</p>
                        ` : ''}
                    </div>

                    <div style="text-align: center; margin-top: 30px;">
                        <p>شكراً لتسوقكم معنا</p>
                    </div>
                </body>
                </html>
            `;
        }

        function getPaymentMethodText(method) {
            const methods = {
                'cash': 'نقدي',
                'card': 'بطاقة',
                'transfer': 'تحويل',
                'mixed': 'مختلط'
            };
            return methods[method] || method;
        }





        // === 🔔 وظيفة الإشعارات ===
        function showNotification(message, type = 'info') {
            // إزالة الإشعارات السابقة
            const existingNotifications = document.querySelectorAll('.notification');
            existingNotifications.forEach(notification => {
                notification.remove();
            });

            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <div class="notification-content">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'}"></i>
                    <span>${message}</span>
                </div>
                <button class="notification-close" onclick="this.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            `;

            document.body.appendChild(notification);

            // إزالة تلقائية بعد 4 ثوان
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.style.animation = 'slideInRight 0.3s ease reverse';
                    setTimeout(() => {
                        notification.remove();
                    }, 300);
                }
            }, 4000);
        }

        // === 💰 وظيفة تنسيق العملة ===
        function formatCurrency(amount) {
            return `${amount.toLocaleString()} دينار`;
        }

        // === ⚙️ الإعدادات الاحترافية الجديدة ===
        function openSettings() {
            console.log('⚙️ فتح الإعدادات الاحترافية');

            // إنشاء النافذة المنبثقة
            const settingsModal = document.createElement('div');
            settingsModal.id = 'settingsModal';
            settingsModal.innerHTML = `
                <div class="settings-backdrop" onclick="closeSettings()"></div>
                <div class="settings-window">
                    <!-- رأس النافذة -->
                    <div class="settings-header">
                        <div class="settings-title">
                            <i class="fas fa-cog rotating"></i>
                            <span>إعدادات الكاشير المتقدمة</span>
                        </div>
                        <button class="settings-close" onclick="closeSettings()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <!-- شريط التبويبات -->
                    <div class="settings-tabs">
                        <div class="tab-item active" onclick="switchTab('general')" data-tab="general">
                            <i class="fas fa-cogs"></i>
                            <span>الإعدادات العامة</span>
                        </div>
                        <div class="tab-item" onclick="switchTab('invoice')" data-tab="invoice">
                            <i class="fas fa-file-invoice-dollar"></i>
                            <span>الفواتير والطباعة</span>
                        </div>
                        <div class="tab-item" onclick="switchTab('display')" data-tab="display">
                            <i class="fas fa-palette"></i>
                            <span>المظهر والعرض</span>
                        </div>
                        <div class="tab-item" onclick="switchTab('advanced')" data-tab="advanced">
                            <i class="fas fa-sliders-h"></i>
                            <span>إعدادات متقدمة</span>
                        </div>
                    </div>

                    <!-- محتوى التبويبات -->
                    <div class="settings-content">
                        <!-- الإعدادات العامة -->
                        <div class="tab-panel active" id="general-panel">
                            <div class="settings-group">
                                <h3><i class="fas fa-cogs"></i> الإعدادات الأساسية</h3>

                                <div class="setting-row">
                                    <div class="setting-info">
                                        <label>حفظ تلقائي للمبيعات</label>
                                        <small>حفظ المبيعات تلقائياً عند إتمام العملية</small>
                                    </div>
                                    <div class="setting-control">
                                        <label class="toggle-switch">
                                            <input type="checkbox" id="autoSave" checked>
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                </div>

                                <div class="setting-row">
                                    <div class="setting-info">
                                        <label>إظهار معلومات العميل</label>
                                        <small>عرض حقول بيانات العميل في الفاتورة</small>
                                    </div>
                                    <div class="setting-control">
                                        <label class="toggle-switch">
                                            <input type="checkbox" id="showCustomerInfo">
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                </div>

                                <div class="setting-row">
                                    <div class="setting-info">
                                        <label>تفعيل الأصوات</label>
                                        <small>تشغيل أصوات التنبيه والإشعارات</small>
                                    </div>
                                    <div class="setting-control">
                                        <label class="toggle-switch">
                                            <input type="checkbox" id="enableSounds" checked>
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                </div>

                                <div class="setting-row">
                                    <div class="setting-info">
                                        <label>معدل الضريبة</label>
                                        <small>نسبة الضريبة المطبقة على المبيعات (%)</small>
                                    </div>
                                    <div class="setting-control">
                                        <div class="input-group">
                                            <input type="number" id="taxRate" value="15" min="0" max="100" class="form-input">
                                            <span class="input-suffix">%</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="setting-row">
                                    <div class="setting-info">
                                        <label>نوع البيع الافتراضي</label>
                                        <small>النوع المحدد افتراضياً عند بدء عملية بيع جديدة</small>
                                    </div>
                                    <div class="setting-control">
                                        <select id="defaultSaleType" class="form-select">
                                            <option value="retail">بيع مفرد</option>
                                            <option value="wholesale">بيع جملة</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- إعدادات الفواتير -->
                        <div class="tab-panel" id="invoice-panel">
                            <div class="settings-group">
                                <h3><i class="fas fa-file-invoice-dollar"></i> إعدادات الفواتير</h3>

                                <div class="setting-row">
                                    <div class="setting-info">
                                        <label>طباعة تلقائية</label>
                                        <small>طباعة الفاتورة تلقائياً بعد إتمام البيع</small>
                                    </div>
                                    <div class="setting-control">
                                        <label class="toggle-switch">
                                            <input type="checkbox" id="printAfterSale" checked>
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                </div>

                                <div class="setting-row">
                                    <div class="setting-info">
                                        <label>قالب الفاتورة</label>
                                        <small>تصميم وتخطيط الفاتورة المطبوعة</small>
                                    </div>
                                    <div class="setting-control">
                                        <select id="invoiceTemplate" class="form-select">
                                            <option value="standard">قياسي - تصميم بسيط</option>
                                            <option value="detailed">مفصل - معلومات شاملة</option>
                                            <option value="compact">مضغوط - توفير الورق</option>
                                            <option value="professional">احترافي - تصميم متقدم</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="setting-row">
                                    <div class="setting-info">
                                        <label>العملة الأساسية</label>
                                        <small>العملة المستخدمة في عرض الأسعار والفواتير</small>
                                    </div>
                                    <div class="setting-control">
                                        <select id="currency" class="form-select">
                                            <option value="IQD">دينار عراقي (IQD)</option>
                                            <option value="USD">دولار أمريكي (USD)</option>
                                            <option value="EUR">يورو (EUR)</option>
                                            <option value="SAR">ريال سعودي (SAR)</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="setting-row">
                                    <div class="setting-info">
                                        <label>حجم الخط في الفاتورة</label>
                                        <small>حجم النص المطبوع في الفواتير</small>
                                    </div>
                                    <div class="setting-control">
                                        <select id="fontSize" class="form-select">
                                            <option value="small">صغير</option>
                                            <option value="medium" selected>متوسط</option>
                                            <option value="large">كبير</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- إعدادات العرض -->
                        <div class="tab-panel" id="display-panel">
                            <div class="settings-group">
                                <h3><i class="fas fa-palette"></i> المظهر والعرض</h3>

                                <div class="setting-row">
                                    <div class="setting-info">
                                        <label>نمط المظهر</label>
                                        <small>اختيار المظهر العام للواجهة</small>
                                    </div>
                                    <div class="setting-control">
                                        <select id="theme" class="form-select">
                                            <option value="light">فاتح - مظهر نهاري</option>
                                            <option value="dark">داكن - مظهر ليلي</option>
                                            <option value="auto">تلقائي - حسب النظام</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="setting-row">
                                    <div class="setting-info">
                                        <label>لغة الواجهة</label>
                                        <small>لغة عرض النصوص والقوائم</small>
                                    </div>
                                    <div class="setting-control">
                                        <select id="language" class="form-select">
                                            <option value="ar">العربية</option>
                                            <option value="en">English</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="setting-row">
                                    <div class="setting-info">
                                        <label>حجم الخط في الواجهة</label>
                                        <small>حجم النصوص المعروضة في البرنامج</small>
                                    </div>
                                    <div class="setting-control">
                                        <select id="uiFontSize" class="form-select">
                                            <option value="small">صغير</option>
                                            <option value="medium" selected>متوسط</option>
                                            <option value="large">كبير</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- الإعدادات المتقدمة -->
                        <div class="tab-panel" id="advanced-panel">
                            <div class="settings-group">
                                <h3><i class="fas fa-sliders-h"></i> الإعدادات المتقدمة</h3>

                                <div class="setting-row">
                                    <div class="setting-info">
                                        <label>نسخ احتياطي تلقائي</label>
                                        <small>إنشاء نسخة احتياطية من البيانات يومياً</small>
                                    </div>
                                    <div class="setting-control">
                                        <label class="toggle-switch">
                                            <input type="checkbox" id="autoBackup">
                                            <span class="toggle-slider"></span>
                                        </label>
                                    </div>
                                </div>

                                <div class="setting-row">
                                    <div class="setting-info">
                                        <label>مهلة انتظار الطباعة</label>
                                        <small>الوقت بالثواني قبل إلغاء عملية الطباعة</small>
                                    </div>
                                    <div class="setting-control">
                                        <div class="input-group">
                                            <input type="number" id="printTimeout" value="30" min="5" max="120" class="form-input">
                                            <span class="input-suffix">ثانية</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="setting-row">
                                    <div class="setting-info">
                                        <label>عدد المنتجات في الصفحة</label>
                                        <small>عدد المنتجات المعروضة في كل صفحة</small>
                                    </div>
                                    <div class="setting-control">
                                        <select id="itemsPerPage" class="form-select">
                                            <option value="10">10 منتجات</option>
                                            <option value="20" selected>20 منتج</option>
                                            <option value="50">50 منتج</option>
                                            <option value="100">100 منتج</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="settings-footer">
                        <button class="btn btn-primary" onclick="saveSettings()">
                            <i class="fas fa-save"></i>
                            <span>حفظ الإعدادات</span>
                        </button>
                        <button class="btn btn-secondary" onclick="resetSettings()">
                            <i class="fas fa-undo"></i>
                            <span>إعادة تعيين</span>
                        </button>
                        <button class="btn btn-outline" onclick="exportSettings()">
                            <i class="fas fa-download"></i>
                            <span>تصدير الإعدادات</span>
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(settingsModal);
            loadSettings();
        }

        // === وظائف الإعدادات ===
        function switchTab(tabName) {
            // إزالة التفعيل من جميع التبويبات
            document.querySelectorAll('.tab-item').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab-panel').forEach(panel => {
                panel.classList.remove('active');
            });

            // تفعيل التبويب المحدد
            document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
            document.getElementById(`${tabName}-panel`).classList.add('active');
        }

        function loadSettings() {
            const settings = JSON.parse(localStorage.getItem('cashierSettings')) || {};

            // تحميل الإعدادات العامة
            document.getElementById('autoSave').checked = settings.autoSave !== false;
            document.getElementById('showCustomerInfo').checked = settings.showCustomerInfo || false;
            document.getElementById('enableSounds').checked = settings.enableSounds !== false;
            document.getElementById('taxRate').value = settings.taxRate || 15;
            document.getElementById('defaultSaleType').value = settings.defaultSaleType || 'retail';

            // تحميل إعدادات الفواتير
            document.getElementById('printAfterSale').checked = settings.printAfterSale !== false;
            document.getElementById('invoiceTemplate').value = settings.invoiceTemplate || 'standard';
            document.getElementById('currency').value = settings.currency || 'IQD';
            document.getElementById('fontSize').value = settings.fontSize || 'medium';

            // تحميل إعدادات العرض
            document.getElementById('theme').value = settings.theme || 'light';
            document.getElementById('language').value = settings.language || 'ar';
            document.getElementById('uiFontSize').value = settings.uiFontSize || 'medium';

            // تحميل الإعدادات المتقدمة
            document.getElementById('autoBackup').checked = settings.autoBackup || false;
            document.getElementById('printTimeout').value = settings.printTimeout || 30;
            document.getElementById('itemsPerPage').value = settings.itemsPerPage || 20;
        }

        function saveSettings() {
            const settings = {
                // الإعدادات العامة
                autoSave: document.getElementById('autoSave').checked,
                showCustomerInfo: document.getElementById('showCustomerInfo').checked,
                enableSounds: document.getElementById('enableSounds').checked,
                taxRate: parseFloat(document.getElementById('taxRate').value) || 15,
                defaultSaleType: document.getElementById('defaultSaleType').value,

                // إعدادات الفواتير
                printAfterSale: document.getElementById('printAfterSale').checked,
                invoiceTemplate: document.getElementById('invoiceTemplate').value,
                currency: document.getElementById('currency').value,
                fontSize: document.getElementById('fontSize').value,

                // إعدادات العرض
                theme: document.getElementById('theme').value,
                language: document.getElementById('language').value,
                uiFontSize: document.getElementById('uiFontSize').value,

                // الإعدادات المتقدمة
                autoBackup: document.getElementById('autoBackup').checked,
                printTimeout: parseInt(document.getElementById('printTimeout').value) || 30,
                itemsPerPage: parseInt(document.getElementById('itemsPerPage').value) || 20,

                // تاريخ الحفظ
                lastSaved: new Date().toISOString()
            };

            localStorage.setItem('cashierSettings', JSON.stringify(settings));
            showNotification('تم حفظ الإعدادات بنجاح', 'success');
            console.log('💾 تم حفظ الإعدادات:', settings);
        }

        function resetSettings() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟\nسيتم فقدان جميع الإعدادات المخصصة.')) {
                localStorage.removeItem('cashierSettings');
                loadSettings();
                showNotification('تم إعادة تعيين الإعدادات إلى القيم الافتراضية', 'info');
            }
        }

        function exportSettings() {
            const settings = JSON.parse(localStorage.getItem('cashierSettings')) || {};
            const dataStr = JSON.stringify(settings, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `cashier-settings-${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            showNotification('تم تصدير الإعدادات بنجاح', 'success');
        }

        function closeSettings() {
            const modal = document.getElementById('settingsModal');
            if (modal) {
                modal.remove();
            }
        }

        // وظيفة إظهار الإشعارات
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <div class="notification-content">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'}"></i>
                    <span>${message}</span>
                </div>
                <button class="notification-close" onclick="this.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            `;

            document.body.appendChild(notification);

            // إزالة تلقائية بعد 4 ثوان
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 4000);
        }

    </script>

    <!-- CSS الاحترافي الجديد للآلة الحاسبة والإعدادات -->
    <style>





        /* === ⚙️ إعدادات الكاشير الاحترافية === */
        #settingsModal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 10000;
            display: flex;
            justify-content: center;
            align-items: center;
            animation: fadeIn 0.4s ease;
        }

        .settings-backdrop {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(0,0,0,0.8), rgba(0,0,0,0.6));
            backdrop-filter: blur(15px);
        }

        .settings-window {
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border-radius: 35px;
            box-shadow:
                0 35px 70px rgba(0, 0, 0, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            max-width: 900px;
            width: 90%;
            max-height: 90vh;
            position: relative;
            z-index: 10001;
            animation: slideUp 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .settings-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 2.5rem 3rem 2rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            color: white;
            position: relative;
            overflow: hidden;
        }

        .settings-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .settings-title {
            display: flex;
            align-items: center;
            gap: 1.5rem;
            font-size: 2rem;
            font-weight: 800;
            position: relative;
            z-index: 1;
        }

        .settings-title i {
            font-size: 2.5rem;
            animation: spin 3s linear infinite;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
        }

        .settings-title span {
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .settings-close {
            background: rgba(255, 255, 255, 0.15);
            color: white;
            border: none;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.4rem;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.2);
            position: relative;
            z-index: 1;
        }

        .settings-close:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: scale(1.1) rotate(90deg);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .settings-tabs {
            display: flex;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-bottom: 1px solid #dee2e6;
            position: relative;
        }

        .settings-tabs::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 25%;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            transition: all 0.3s ease;
            border-radius: 2px 2px 0 0;
        }

        .tab-item {
            flex: 1;
            padding: 2rem 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.8rem;
            font-weight: 700;
            color: #6c757d;
            position: relative;
            background: transparent;
        }

        .tab-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            opacity: 0;
            transition: all 0.3s ease;
        }

        .tab-item:hover::before {
            opacity: 1;
        }

        .tab-item:hover {
            color: #495057;
            transform: translateY(-2px);
        }

        .tab-item.active {
            color: #667eea;
            background: white;
            box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
        }

        .tab-item.active::before {
            opacity: 0;
        }

        .tab-item i {
            font-size: 1.3rem;
        }

        .settings-content {
            max-height: 60vh;
            overflow-y: auto;
            padding: 0;
            background: linear-gradient(135deg, #fafbfc, #f8f9fa);
        }

        .settings-content::-webkit-scrollbar {
            width: 8px;
        }

        .settings-content::-webkit-scrollbar-track {
            background: #f1f3f4;
        }

        .settings-content::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 4px;
        }

        .tab-panel {
            display: none;
            padding: 3rem;
        }

        .tab-panel.active {
            display: block;
            animation: fadeIn 0.4s ease;
        }

        .settings-group {
            margin-bottom: 3rem;
        }

        .settings-group h3 {
            color: #2c3e50;
            font-size: 1.5rem;
            font-weight: 800;
            margin-bottom: 2rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 20px;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .settings-group h3 i {
            font-size: 1.8rem;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
        }

        .setting-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 2rem;
            margin-bottom: 1.5rem;
            background: white;
            border-radius: 20px;
            box-shadow:
                0 4px 12px rgba(0, 0, 0, 0.08),
                0 2px 4px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
            border: 1px solid rgba(102, 126, 234, 0.1);
            position: relative;
            overflow: hidden;
        }

        .setting-row::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            opacity: 0;
            transition: all 0.3s ease;
        }

        .setting-row:hover {
            transform: translateY(-4px);
            box-shadow:
                0 8px 25px rgba(0, 0, 0, 0.12),
                0 4px 8px rgba(0, 0, 0, 0.08);
        }

        .setting-row:hover::before {
            opacity: 1;
        }

        .setting-info {
            flex: 1;
        }

        .setting-info label {
            font-weight: 700;
            color: #2c3e50;
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
            display: block;
        }

        .setting-info small {
            color: #6c757d;
            font-size: 1rem;
            line-height: 1.4;
        }

        .setting-control {
            margin-left: 2.5rem;
        }

        /* Toggle Switch المحسن */
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 70px;
            height: 40px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #ddd, #ccc);
            transition: 0.4s;
            border-radius: 40px;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 32px;
            width: 32px;
            left: 4px;
            bottom: 4px;
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
            transition: 0.4s;
            border-radius: 50%;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        .toggle-switch input:checked + .toggle-slider {
            background: linear-gradient(135deg, #667eea, #764ba2);
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
        }

        .toggle-switch input:checked + .toggle-slider:before {
            transform: translateX(30px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
        }

        /* Form Controls المحسنة */
        .form-input, .form-select {
            width: 220px;
            padding: 1rem 1.5rem;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow:
                0 0 0 4px rgba(102, 126, 234, 0.1),
                0 4px 12px rgba(102, 126, 234, 0.2);
            transform: translateY(-2px);
        }

        .input-group {
            display: flex;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            border-radius: 15px;
            overflow: hidden;
        }

        .input-suffix {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: 2px solid #e9ecef;
            border-left: none;
            padding: 1rem 1.5rem;
            color: #6c757d;
            font-weight: 700;
            font-size: 1.1rem;
        }

        .input-group .form-input {
            border-radius: 15px 0 0 15px;
            box-shadow: none;
        }

        .settings-footer {
            padding: 3rem;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            display: flex;
            gap: 1.5rem;
            justify-content: center;
            border-top: 1px solid #dee2e6;
        }

        .btn {
            padding: 1.2rem 2.5rem;
            border: none;
            border-radius: 18px;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: 700;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.8rem;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.6s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(145deg, #28a745, #20c997);
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(145deg, #34ce57, #2dd4aa);
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(40, 167, 69, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(145deg, #ffc107, #fd7e14);
            color: white;
        }

        .btn-secondary:hover {
            background: linear-gradient(145deg, #ffcd39, #ff8c42);
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(255, 193, 7, 0.4);
        }

        .btn-outline {
            background: white;
            color: #667eea;
            border: 2px solid #667eea;
        }

        .btn-outline:hover {
            background: linear-gradient(145deg, #667eea, #764ba2);
            color: white;
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }

        /* الإشعارات */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 1.5rem;
            border-radius: 12px;
            color: white;
            font-weight: 600;
            z-index: 20000;
            display: flex;
            align-items: center;
            gap: 1rem;
            animation: slideInRight 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            max-width: 400px;
        }

        .notification.success {
            background: linear-gradient(145deg, #28a745, #20c997);
        }

        .notification.error {
            background: linear-gradient(145deg, #dc3545, #c82333);
        }

        .notification.info {
            background: linear-gradient(145deg, #17a2b8, #138496);
        }

        .notification-content {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            flex: 1;
        }

        .notification-close {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            transition: all 0.3s ease;
        }

        .notification-close:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        /* === 🔔 الإشعارات الاحترافية === */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1.2rem 1.8rem;
            border-radius: 15px;
            color: white;
            font-weight: 600;
            z-index: 20000;
            display: flex;
            align-items: center;
            gap: 1rem;
            animation: slideInRight 0.4s ease;
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
            max-width: 400px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .notification.success {
            background: linear-gradient(145deg, #28a745, #20c997);
        }

        .notification.error {
            background: linear-gradient(145deg, #dc3545, #c82333);
        }

        .notification.info {
            background: linear-gradient(145deg, #17a2b8, #138496);
        }

        .notification-content {
            display: flex;
            align-items: center;
            gap: 0.8rem;
            flex: 1;
        }

        .notification-close {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 28px;
            height: 28px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .notification-close:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        /* === 🎬 الحركات والانتقالات === */
        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        @keyframes fadeOut {
            from {
                opacity: 1;
            }
            to {
                opacity: 0;
            }
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
        }

        @keyframes spin {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }

        /* === 📱 الاستجابة للشاشات الصغيرة === */
        @media (max-width: 768px) {
            /* الإعدادات */
            .settings-window {
                width: 95%;
                margin: 1rem;
            }

            .settings-header {
                padding: 2rem;
            }

            .settings-title {
                font-size: 1.6rem;
            }

            .settings-title i {
                font-size: 2rem;
            }

            .settings-tabs {
                flex-direction: column;
            }

            .settings-tabs::before {
                display: none;
            }

            .tab-item {
                padding: 1.5rem;
                border-bottom: 1px solid #dee2e6;
            }

            .tab-item.active {
                background: linear-gradient(135deg, #667eea, #764ba2);
                color: white;
            }

            .tab-panel {
                padding: 2rem;
            }

            .settings-group h3 {
                font-size: 1.3rem;
                padding: 1rem;
            }

            .setting-row {
                flex-direction: column;
                align-items: flex-start;
                gap: 1.5rem;
                padding: 1.5rem;
            }

            .setting-control {
                margin-left: 0;
                width: 100%;
            }

            .form-input, .form-select {
                width: 100%;
            }

            .settings-footer {
                flex-direction: column;
                padding: 2rem;
            }

            .btn {
                padding: 1rem 2rem;
                font-size: 1rem;
            }

            .notification {
                top: 10px;
                right: 10px;
                left: 10px;
                max-width: none;
                padding: 1rem 1.5rem;
            }
        }

        @media (max-width: 480px) {
            /* الإعدادات */
            .settings-window {
                width: 98%;
                margin: 0.5rem;
            }

            .settings-header {
                padding: 1.5rem;
            }

            .settings-title {
                font-size: 1.4rem;
                gap: 1rem;
            }

            .settings-title i {
                font-size: 1.8rem;
            }

            .tab-item {
                padding: 1rem;
                font-size: 0.9rem;
            }

            .tab-item i {
                font-size: 1.1rem;
            }

            .tab-panel {
                padding: 1.5rem;
            }

            .settings-group h3 {
                font-size: 1.1rem;
                padding: 0.8rem;
            }

            .setting-row {
                padding: 1rem;
            }

            .setting-info label {
                font-size: 1rem;
            }

            .setting-info small {
                font-size: 0.9rem;
            }

            .toggle-switch {
                width: 60px;
                height: 34px;
            }

            .toggle-slider:before {
                height: 26px;
                width: 26px;
            }

            .toggle-switch input:checked + .toggle-slider:before {
                transform: translateX(26px);
            }

            .settings-footer {
                padding: 1.5rem;
            }

            .btn {
                padding: 0.8rem 1.5rem;
                font-size: 0.9rem;
            }
        }
    </style>
</body>
</html>

                if (window.calcOperator && !window.calcWaitingForOperand) {
                    calcEquals();
                }
                window.calcPreviousInput = window.calcCurrentInput;
                window.calcOperator = value;
                window.calcWaitingForOperand = true;
            } else {
                if (window.calcWaitingForOperand) {
                    window.calcCurrentInput = value;
                    window.calcWaitingForOperand = false;
                } else {
                    if (value === '.' && window.calcCurrentInput.includes('.')) {
                        return;
                    }
                    window.calcCurrentInput = window.calcCurrentInput === '0' ? value : window.calcCurrentInput + value;
                }
            }
            updateCalcDisplay();
        }

        function calcEquals() {
            if (window.calcOperator && window.calcPreviousInput !== null) {
                const prev = parseFloat(window.calcPreviousInput);
                const current = parseFloat(window.calcCurrentInput);
                let result = 0;

                switch (window.calcOperator) {
                    case '+': result = prev + current; break;
                    case '-': result = prev - current; break;
                    case '*': result = prev * current; break;
                    case '/': result = current !== 0 ? prev / current : 0; break;
                }

                window.calcCurrentInput = result.toString();
                window.calcOperator = null;
                window.calcPreviousInput = null;
                window.calcWaitingForOperand = true;
                updateCalcDisplay();
            }
        }

        function clearCalculator() {
            window.calcCurrentInput = '0';
            window.calcOperator = null;
            window.calcPreviousInput = null;
            window.calcWaitingForOperand = false;
            updateCalcDisplay();
        }

        function calcBackspace() {
            if (window.calcCurrentInput.length > 1) {
                window.calcCurrentInput = window.calcCurrentInput.slice(0, -1);
            } else {
                window.calcCurrentInput = '0';
            }
            updateCalcDisplay();
        }

        function updateCalcDisplay() {
            const display = document.getElementById('calcDisplay');
            if (display) {
                display.textContent = window.calcCurrentInput;
            }
        }

        function closeCalculator() {
            const modal = document.querySelector('.calculator-modal');
            if (modal) {
                modal.remove();
            }
        }

        function memoryOperation(operation) {
            const currentValue = parseFloat(window.calcCurrentInput) || 0;

            switch (operation) {
                case 'clear':
                    window.calcMemory = 0;
                    showNotificationMsg('تم مسح الذاكرة', 'info');
                    break;
                case 'recall':
                    window.calcCurrentInput = window.calcMemory.toString();
                    updateCalcDisplay();
                    break;
                case 'add':
                    window.calcMemory += currentValue;
                    showNotificationMsg(`تم إضافة ${currentValue} إلى الذاكرة`, 'success');
                    break;
                case 'subtract':
                    window.calcMemory -= currentValue;
                    showNotificationMsg(`تم طرح ${currentValue} من الذاكرة`, 'success');
                    break;
            }
        }

        function copyToTotal() {
            const value = parseFloat(window.calcCurrentInput);
            if (!isNaN(value) && value > 0) {
                const totalElement = document.getElementById('total');
                if (totalElement) {
                    totalElement.textContent = `${value.toFixed(0)} دينار`;
                    showNotificationMsg(`تم نسخ ${value.toFixed(0)} دينار إلى المجموع`, 'success');
                } else {
                    window.calculatedTotal = value;
                    showNotificationMsg(`تم حفظ القيمة: ${value.toFixed(0)} دينار`, 'success');
                }
                closeCalculator();
            } else {
                showNotificationMsg('يرجى إدخال قيمة صحيحة أكبر من صفر', 'error');
            }
        }

        function copyToDiscount() {
            const value = parseFloat(window.calcCurrentInput);
            if (!isNaN(value) && value >= 0) {
                const discountAmountInput = document.getElementById('discountAmount');
                const discountPercentInput = document.getElementById('discountPercent');

                if (value <= 100 && discountPercentInput) {
                    discountPercentInput.value = value;
                    applyDiscountPercent();
                    showNotificationMsg(`تم تطبيق خصم: ${value}%`, 'success');
                } else if (discountAmountInput) {
                    discountAmountInput.value = value;
                    applyDiscount();
                    showNotificationMsg(`تم تطبيق خصم: ${value} دينار`, 'success');
                } else {
                    window.calculatedDiscount = value;
                    showNotificationMsg(`تم حفظ خصم: ${value}`, 'success');
                }
                closeCalculator();
            } else {
                showNotificationMsg('قيمة الخصم يجب أن تكون أكبر من أو تساوي صفر', 'error');
            }
        }

        // وظائف الإعدادات
        function switchSettingsTab(tabName) {
            // إخفاء جميع التبويبات
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            // إزالة التفعيل من جميع الأزرار
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // إظهار التبويب المحدد وتفعيل الزر
            document.getElementById(`${tabName}-content`).classList.add('active');
            document.getElementById(`tab-${tabName}`).classList.add('active');
        }

        function loadSavedSettingsData() {
            const settings = JSON.parse(localStorage.getItem('cashierSettings')) || {};

            // تحميل الإعدادات العامة
            if (settings.autoSave !== undefined) {
                document.getElementById('autoSave').checked = settings.autoSave;
            }
            if (settings.showCustomerInfo !== undefined) {
                document.getElementById('showCustomerInfo').checked = settings.showCustomerInfo;
            }
            if (settings.enableSounds !== undefined) {
                document.getElementById('enableSounds').checked = settings.enableSounds;
            }
            if (settings.taxRate !== undefined) {
                document.getElementById('taxRate').value = settings.taxRate;
            }
            if (settings.defaultSaleType !== undefined) {
                document.getElementById('defaultSaleType').value = settings.defaultSaleType;
            }

            // تحميل إعدادات الفواتير
            if (settings.printAfterSale !== undefined) {
                document.getElementById('printAfterSale').checked = settings.printAfterSale;
            }
            if (settings.invoiceTemplate !== undefined) {
                document.getElementById('invoiceTemplate').value = settings.invoiceTemplate;
            }
            if (settings.currency !== undefined) {
                document.getElementById('currency').value = settings.currency;
            }

            // تحميل إعدادات العرض
            if (settings.theme !== undefined) {
                document.getElementById('theme').value = settings.theme;
            }
            if (settings.language !== undefined) {
                document.getElementById('language').value = settings.language;
            }
        }

        function saveSettingsData() {
            const settings = {
                autoSave: document.getElementById('autoSave').checked,
                showCustomerInfo: document.getElementById('showCustomerInfo').checked,
                enableSounds: document.getElementById('enableSounds').checked,
                taxRate: parseFloat(document.getElementById('taxRate').value) || 15,
                defaultSaleType: document.getElementById('defaultSaleType').value,
                printAfterSale: document.getElementById('printAfterSale').checked,
                invoiceTemplate: document.getElementById('invoiceTemplate').value,
                currency: document.getElementById('currency').value,
                theme: document.getElementById('theme').value,
                language: document.getElementById('language').value
            };

            localStorage.setItem('cashierSettings', JSON.stringify(settings));
            showNotificationMsg('تم حفظ الإعدادات بنجاح', 'success');
            console.log('💾 تم حفظ الإعدادات:', settings);
        }

        function resetSettingsData() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟')) {
                // إعادة تعيين القيم الافتراضية
                document.getElementById('autoSave').checked = true;
                document.getElementById('showCustomerInfo').checked = false;
                document.getElementById('enableSounds').checked = true;
                document.getElementById('taxRate').value = 15;
                document.getElementById('defaultSaleType').value = 'retail';
                document.getElementById('printAfterSale').checked = true;
                document.getElementById('invoiceTemplate').value = 'standard';
                document.getElementById('currency').value = 'IQD';
                document.getElementById('theme').value = 'light';
                document.getElementById('language').value = 'ar';

                showNotificationMsg('تم إعادة تعيين الإعدادات بنجاح', 'success');
            }
        }

        function closeSettingsModal() {
            const modal = document.querySelector('.settings-modal');
            if (modal) {
                modal.remove();
            }
        }

        // وظيفة إظهار الإشعارات
        function showNotificationMsg(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                <span>${message}</span>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.classList.add('fade-out');
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }
    </script>

    <!-- CSS للآلة الحاسبة والإعدادات -->
    <style>
        /* الآلة الحاسبة */
        .calculator-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1000;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .calculator-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
        }

        .calculator-container {
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            max-width: 400px;
            width: 90%;
            position: relative;
            z-index: 1001;
            animation: slideUp 0.3s ease;
        }

        .calculator-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e9ecef;
        }

        .calculator-header h3 {
            margin: 0;
            color: #2c3e50;
            font-size: 1.5rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .calculator-header h3 i {
            color: #667eea;
        }

        .close-btn {
            background: #e74c3c;
            color: white;
            border: none;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            transform: scale(1.1);
        }

        .calculator-display {
            background: linear-gradient(145deg, #2c3e50, #34495e);
            color: white;
            padding: 1.5rem;
            border-radius: 15px;
            margin-bottom: 1.5rem;
            text-align: right;
            font-size: 2rem;
            font-weight: 600;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.3);
            font-family: 'Courier New', monospace;
            letter-spacing: 2px;
        }

        .calculator-memory {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .memory-btn {
            background: linear-gradient(145deg, #95a5a6, #7f8c8d);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 600;
            transition: all 0.3s ease;
            flex: 1;
        }

        .memory-btn:hover {
            transform: translateY(-2px);
        }

        .calculator-buttons {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 0.75rem;
            margin-bottom: 1.5rem;
        }

        .calc-btn {
            border: none;
            padding: 1rem;
            border-radius: 12px;
            cursor: pointer;
            font-size: 1.2rem;
            font-weight: 700;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .number-btn {
            background: linear-gradient(145deg, #34495e, #2c3e50);
            color: white;
            box-shadow: 0 4px 8px rgba(52, 73, 94, 0.3);
        }

        .operator-btn {
            background: linear-gradient(145deg, #f39c12, #e67e22);
            color: white;
            box-shadow: 0 4px 8px rgba(243, 156, 18, 0.3);
        }

        .clear-btn {
            background: linear-gradient(145deg, #e74c3c, #c0392b);
            color: white;
            box-shadow: 0 4px 8px rgba(231, 76, 60, 0.3);
            grid-column: span 2;
        }

        .equals-btn {
            background: linear-gradient(145deg, #27ae60, #229954);
            color: white;
            box-shadow: 0 4px 8px rgba(39, 174, 96, 0.3);
            grid-row: span 2;
        }

        .zero-btn {
            grid-column: span 2;
        }

        .calc-btn:hover {
            transform: translateY(-3px);
        }

        .calculator-actions {
            display: flex;
            gap: 0.75rem;
        }

        .action-btn {
            border: none;
            padding: 0.75rem 1rem;
            border-radius: 10px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 600;
            transition: all 0.3s ease;
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .total-btn {
            background: linear-gradient(145deg, #3498db, #2980b9);
            color: white;
        }

        .discount-btn {
            background: linear-gradient(145deg, #e67e22, #d35400);
            color: white;
        }

        .action-btn:hover {
            transform: translateY(-2px);
        }

        /* الإعدادات */
        .settings-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1000;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .settings-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
        }

        .settings-container {
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            max-width: 600px;
            width: 90%;
            max-height: 85vh;
            overflow-y: auto;
            position: relative;
            z-index: 1001;
            animation: slideUp 0.3s ease;
        }

        .settings-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e9ecef;
        }

        .settings-header h3 {
            margin: 0;
            color: #2c3e50;
            font-size: 1.8rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .settings-header h3 i {
            color: #667eea;
            animation: spin 2s linear infinite;
        }

        .settings-tabs {
            display: flex;
            margin-bottom: 2rem;
            background: #f8f9fa;
            border-radius: 12px;
            padding: 0.5rem;
            gap: 0.5rem;
        }

        .tab-btn {
            background: transparent;
            color: #6c757d;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .tab-btn.active {
            background: linear-gradient(145deg, #667eea, #764ba2);
            color: white;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .settings-section {
            background: linear-gradient(145deg, #f8f9fa, #ffffff);
            padding: 1.5rem;
            border-radius: 15px;
            border: 1px solid #e9ecef;
        }

        .settings-section h4 {
            color: #495057;
            margin-bottom: 1.5rem;
            font-size: 1.2rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .setting-item {
            margin-bottom: 1rem;
        }

        .setting-label {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            cursor: pointer;
            padding: 0.75rem;
            background: white;
            border-radius: 10px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
            position: relative;
        }

        .setting-label:hover {
            border-color: #667eea;
        }

        .setting-label input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: #667eea;
        }

        .setting-text {
            font-weight: 500;
        }

        .input-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #495057;
        }

        .setting-input, .setting-select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ced4da;
            border-radius: 8px;
            font-size: 1rem;
            background: white;
            transition: all 0.3s ease;
        }

        .setting-input:focus, .setting-select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .settings-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid #e9ecef;
        }

        .save-btn {
            background: linear-gradient(145deg, #28a745, #20c997);
            box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
        }

        .reset-btn {
            background: linear-gradient(145deg, #ffc107, #fd7e14);
            box-shadow: 0 4px 8px rgba(255, 193, 7, 0.3);
        }

        .save-btn:hover, .reset-btn:hover {
            transform: translateY(-2px);
        }

        /* الإشعارات */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            z-index: 10000;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            animation: slideInRight 0.3s ease;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .notification.success {
            background: #28a745;
        }

        .notification.error {
            background: #dc3545;
        }

        .notification.info {
            background: #17a2b8;
        }

        .notification.fade-out {
            animation: slideOutRight 0.3s ease;
        }

        /* الحركات */
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideOutRight {
            from {
                opacity: 1;
                transform: translateX(0);
            }
            to {
                opacity: 0;
                transform: translateX(100%);
            }
        }

        @keyframes spin {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }

        /* استجابة للشاشات الصغيرة */
        @media (max-width: 768px) {
            .calculator-container, .settings-container {
                width: 95%;
                padding: 1.5rem;
            }

            .calculator-buttons {
                gap: 0.5rem;
            }

            .calc-btn {
                padding: 0.75rem;
                font-size: 1rem;
            }

            .settings-tabs {
                flex-direction: column;
                gap: 0.25rem;
            }

            .tab-btn {
                padding: 0.5rem 1rem;
            }
        }
    </style>
</body>
</html>
                        margin-bottom: 1.5rem;
                    ">
                        <!-- الصف الأول -->
                        <button onclick="clearCalc()" style="
                            grid-column: span 2;
                            background: linear-gradient(145deg, #e74c3c, #c0392b);
                            color: white;
                            border: none;
                            padding: 1rem;
                            border-radius: 12px;
                            cursor: pointer;
                            font-size: 1.1rem;
                            font-weight: 700;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 8px rgba(231, 76, 60, 0.3);
                        " onmouseover="this.style.transform='translateY(-3px)'; this.style.boxShadow='0 6px 12px rgba(231, 76, 60, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 8px rgba(231, 76, 60, 0.3)'">مسح</button>

                        <button onclick="calcInput('/')" style="
                            background: linear-gradient(145deg, #f39c12, #e67e22);
                            color: white;
                            border: none;
                            padding: 1rem;
                            border-radius: 12px;
                            cursor: pointer;
                            font-size: 1.3rem;
                            font-weight: 700;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 8px rgba(243, 156, 18, 0.3);
                        " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">÷</button>

                        <button onclick="calcBackspace()" style="
                            background: linear-gradient(145deg, #9b59b6, #8e44ad);
                            color: white;
                            border: none;
                            padding: 1rem;
                            border-radius: 12px;
                            cursor: pointer;
                            font-size: 1.1rem;
                            font-weight: 700;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 8px rgba(155, 89, 182, 0.3);
                        " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">⌫</button>

                        <!-- الصف الثاني -->
                        <button onclick="calcInput('7')" style="
                            background: linear-gradient(145deg, #34495e, #2c3e50);
                            color: white;
                            border: none;
                            padding: 1rem;
                            border-radius: 12px;
                            cursor: pointer;
                            font-size: 1.3rem;
                            font-weight: 700;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 8px rgba(52, 73, 94, 0.3);
                        " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">7</button>

                        <button onclick="calcInput('8')" style="
                            background: linear-gradient(145deg, #34495e, #2c3e50);
                            color: white;
                            border: none;
                            padding: 1rem;
                            border-radius: 12px;
                            cursor: pointer;
                            font-size: 1.3rem;
                            font-weight: 700;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 8px rgba(52, 73, 94, 0.3);
                        " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">8</button>

                        <button onclick="calcInput('9')" style="
                            background: linear-gradient(145deg, #34495e, #2c3e50);
                            color: white;
                            border: none;
                            padding: 1rem;
                            border-radius: 12px;
                            cursor: pointer;
                            font-size: 1.3rem;
                            font-weight: 700;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 8px rgba(52, 73, 94, 0.3);
                        " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">9</button>

                        <button onclick="calcInput('*')" style="
                            background: linear-gradient(145deg, #f39c12, #e67e22);
                            color: white;
                            border: none;
                            padding: 1rem;
                            border-radius: 12px;
                            cursor: pointer;
                            font-size: 1.3rem;
                            font-weight: 700;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 8px rgba(243, 156, 18, 0.3);
                        " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">×</button>

                        <!-- الصف الثالث -->
                        <button onclick="calcInput('4')" style="
                            background: linear-gradient(145deg, #34495e, #2c3e50);
                            color: white;
                            border: none;
                            padding: 1rem;
                            border-radius: 12px;
                            cursor: pointer;
                            font-size: 1.3rem;
                            font-weight: 700;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 8px rgba(52, 73, 94, 0.3);
                        " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">4</button>

                        <button onclick="calcInput('5')" style="
                            background: linear-gradient(145deg, #34495e, #2c3e50);
                            color: white;
                            border: none;
                            padding: 1rem;
                            border-radius: 12px;
                            cursor: pointer;
                            font-size: 1.3rem;
                            font-weight: 700;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 8px rgba(52, 73, 94, 0.3);
                        " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">5</button>

                        <button onclick="calcInput('6')" style="
                            background: linear-gradient(145deg, #34495e, #2c3e50);
                            color: white;
                            border: none;
                            padding: 1rem;
                            border-radius: 12px;
                            cursor: pointer;
                            font-size: 1.3rem;
                            font-weight: 700;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 8px rgba(52, 73, 94, 0.3);
                        " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">6</button>

                        <button onclick="calcInput('-')" style="
                            background: linear-gradient(145deg, #f39c12, #e67e22);
                            color: white;
                            border: none;
                            padding: 1rem;
                            border-radius: 12px;
                            cursor: pointer;
                            font-size: 1.3rem;
                            font-weight: 700;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 8px rgba(243, 156, 18, 0.3);
                        " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">-</button>

                        <!-- الصف الرابع -->
                        <button onclick="calcInput('1')" style="
                            background: linear-gradient(145deg, #34495e, #2c3e50);
                            color: white;
                            border: none;
                            padding: 1rem;
                            border-radius: 12px;
                            cursor: pointer;
                            font-size: 1.3rem;
                            font-weight: 700;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 8px rgba(52, 73, 94, 0.3);
                        " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">1</button>

                        <button onclick="calcInput('2')" style="
                            background: linear-gradient(145deg, #34495e, #2c3e50);
                            color: white;
                            border: none;
                            padding: 1rem;
                            border-radius: 12px;
                            cursor: pointer;
                            font-size: 1.3rem;
                            font-weight: 700;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 8px rgba(52, 73, 94, 0.3);
                        " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">2</button>

                        <button onclick="calcInput('3')" style="
                            background: linear-gradient(145deg, #34495e, #2c3e50);
                            color: white;
                            border: none;
                            padding: 1rem;
                            border-radius: 12px;
                            cursor: pointer;
                            font-size: 1.3rem;
                            font-weight: 700;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 8px rgba(52, 73, 94, 0.3);
                        " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">3</button>

                        <button onclick="calcInput('+')" style="
                            background: linear-gradient(145deg, #f39c12, #e67e22);
                            color: white;
                            border: none;
                            padding: 1rem;
                            border-radius: 12px;
                            cursor: pointer;
                            font-size: 1.3rem;
                            font-weight: 700;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 8px rgba(243, 156, 18, 0.3);
                        " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">+</button>

                        <!-- الصف الخامس -->
                        <button onclick="calcInput('0')" style="
                            grid-column: span 2;
                            background: linear-gradient(145deg, #34495e, #2c3e50);
                            color: white;
                            border: none;
                            padding: 1rem;
                            border-radius: 12px;
                            cursor: pointer;
                            font-size: 1.3rem;
                            font-weight: 700;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 8px rgba(52, 73, 94, 0.3);
                        " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">0</button>

                        <button onclick="calcInput('.')" style="
                            background: linear-gradient(145deg, #34495e, #2c3e50);
                            color: white;
                            border: none;
                            padding: 1rem;
                            border-radius: 12px;
                            cursor: pointer;
                            font-size: 1.3rem;
                            font-weight: 700;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 8px rgba(52, 73, 94, 0.3);
                        " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">.</button>

                        <button onclick="calcEquals()" style="
                            background: linear-gradient(145deg, #27ae60, #229954);
                            color: white;
                            border: none;
                            padding: 1rem;
                            border-radius: 12px;
                            cursor: pointer;
                            font-size: 1.3rem;
                            font-weight: 700;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 8px rgba(39, 174, 96, 0.3);
                        " onmouseover="this.style.transform='translateY(-3px)'; this.style.boxShadow='0 6px 12px rgba(39, 174, 96, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 8px rgba(39, 174, 96, 0.3)'">＝</button>
                    </div>

                    <!-- أزرار الكاشير -->
                    <div style="
                        display: flex;
                        gap: 0.75rem;
                        margin-bottom: 1rem;
                    ">
                        <button onclick="copyToTotal()" style="
                            background: linear-gradient(145deg, #3498db, #2980b9);
                            color: white;
                            border: none;
                            padding: 0.75rem 1rem;
                            border-radius: 10px;
                            cursor: pointer;
                            font-size: 0.9rem;
                            font-weight: 600;
                            transition: all 0.3s ease;
                            flex: 1;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            gap: 0.5rem;
                        " onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                            <i class="fas fa-arrow-right"></i>
                            نسخ للمجموع
                        </button>

                        <button onclick="copyToDiscount()" style="
                            background: linear-gradient(145deg, #e67e22, #d35400);
                            color: white;
                            border: none;
                            padding: 0.75rem 1rem;
                            border-radius: 10px;
                            cursor: pointer;
                            font-size: 0.9rem;
                            font-weight: 600;
                            transition: all 0.3s ease;
                            flex: 1;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            gap: 0.5rem;
                        " onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                            <i class="fas fa-percent"></i>
                            نسخ للخصم
                        </button>
                    </div>
                </div>

                <style>
                    @keyframes fadeIn {
                        from { opacity: 0; }
                        to { opacity: 1; }
                    }

                    @keyframes slideUp {
                        from {
                            opacity: 0;
                            transform: translateY(30px);
                        }
                        to {
                            opacity: 1;
                            transform: translateY(0);
                        }
                    }
                </style>
            `;

            document.body.appendChild(modal);

            // متغيرات الآلة الحاسبة
            window.calcCurrentInput = '0';
            window.calcOperator = null;
            window.calcPreviousInput = null;
            window.calcWaitingForOperand = false;
            window.calcMemory = 0;

            updateCalcDisplay();
        }

        function createEnhancedSettings() {
            console.log('⚙️ فتح الإعدادات المحسنة');

            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: linear-gradient(135deg, rgba(0,0,0,0.7), rgba(0,0,0,0.5));
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
                backdrop-filter: blur(5px);
                animation: fadeIn 0.3s ease;
            `;

            modal.innerHTML = `
                <div style="
                    background: linear-gradient(145deg, #ffffff, #f8f9fa);
                    padding: 2.5rem;
                    border-radius: 20px;
                    box-shadow: 0 25px 50px rgba(0,0,0,0.3);
                    max-width: 600px;
                    width: 90%;
                    max-height: 85vh;
                    overflow-y: auto;
                    border: 1px solid rgba(255,255,255,0.2);
                    animation: slideUp 0.3s ease;
                ">
                    <!-- رأس النافذة -->
                    <div style="
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 2rem;
                        padding-bottom: 1rem;
                        border-bottom: 2px solid #e9ecef;
                    ">
                        <h3 style="
                            margin: 0;
                            color: #2c3e50;
                            font-size: 1.8rem;
                            font-weight: 700;
                            display: flex;
                            align-items: center;
                            gap: 0.75rem;
                        ">
                            <i class="fas fa-cog" style="color: #667eea; animation: spin 2s linear infinite;"></i>
                            إعدادات الكاشير
                        </h3>
                        <button onclick="closeSettings()" style="
                            background: #e74c3c;
                            color: white;
                            border: none;
                            width: 40px;
                            height: 40px;
                            border-radius: 50%;
                            cursor: pointer;
                            font-size: 1.1rem;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            transition: all 0.3s ease;
                        " onmouseover="this.style.transform='scale(1.1)'" onmouseout="this.style.transform='scale(1)'">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <!-- التبويبات -->
                    <div style="
                        display: flex;
                        margin-bottom: 2rem;
                        background: #f8f9fa;
                        border-radius: 12px;
                        padding: 0.5rem;
                        gap: 0.5rem;
                    ">
                        <button onclick="switchTab('general')" id="tab-general" style="
                            background: linear-gradient(145deg, #667eea, #764ba2);
                            color: white;
                            border: none;
                            padding: 0.75rem 1.5rem;
                            border-radius: 8px;
                            cursor: pointer;
                            font-weight: 600;
                            transition: all 0.3s ease;
                            flex: 1;
                        ">عام</button>
                        <button onclick="switchTab('invoice')" id="tab-invoice" style="
                            background: transparent;
                            color: #6c757d;
                            border: none;
                            padding: 0.75rem 1.5rem;
                            border-radius: 8px;
                            cursor: pointer;
                            font-weight: 600;
                            transition: all 0.3s ease;
                            flex: 1;
                        ">الفواتير</button>
                        <button onclick="switchTab('display')" id="tab-display" style="
                            background: transparent;
                            color: #6c757d;
                            border: none;
                            padding: 0.75rem 1.5rem;
                            border-radius: 8px;
                            cursor: pointer;
                            font-weight: 600;
                            transition: all 0.3s ease;
                            flex: 1;
                        ">العرض</button>
                    </div>

                    <!-- محتوى التبويبات -->
                    <div id="settings-content">
                        <!-- تبويب الإعدادات العامة -->
                        <div id="general-tab" style="display: block;">
                            <div style="
                                background: linear-gradient(145deg, #f8f9fa, #ffffff);
                                padding: 1.5rem;
                                border-radius: 15px;
                                margin-bottom: 1.5rem;
                                border: 1px solid #e9ecef;
                            ">
                                <h4 style="
                                    color: #495057;
                                    margin-bottom: 1.5rem;
                                    font-size: 1.2rem;
                                    font-weight: 600;
                                    display: flex;
                                    align-items: center;
                                    gap: 0.5rem;
                                ">
                                    <i class="fas fa-cogs" style="color: #667eea;"></i>
                                    الإعدادات العامة
                                </h4>

                                <div style="display: grid; gap: 1rem;">
                                    <label style="
                                        display: flex;
                                        align-items: center;
                                        gap: 0.75rem;
                                        cursor: pointer;
                                        padding: 0.75rem;
                                        background: white;
                                        border-radius: 10px;
                                        border: 1px solid #e9ecef;
                                        transition: all 0.3s ease;
                                    " onmouseover="this.style.borderColor='#667eea'" onmouseout="this.style.borderColor='#e9ecef'">
                                        <input type="checkbox" id="autoSave" checked style="
                                            width: 18px;
                                            height: 18px;
                                            accent-color: #667eea;
                                        ">
                                        <span style="font-weight: 500;">حفظ تلقائي للمبيعات</span>
                                    </label>

                                    <label style="
                                        display: flex;
                                        align-items: center;
                                        gap: 0.75rem;
                                        cursor: pointer;
                                        padding: 0.75rem;
                                        background: white;
                                        border-radius: 10px;
                                        border: 1px solid #e9ecef;
                                        transition: all 0.3s ease;
                                    " onmouseover="this.style.borderColor='#667eea'" onmouseout="this.style.borderColor='#e9ecef'">
                                        <input type="checkbox" id="showCustomerInfo" style="
                                            width: 18px;
                                            height: 18px;
                                            accent-color: #667eea;
                                        ">
                                        <span style="font-weight: 500;">إظهار معلومات العميل</span>
                                    </label>

                                    <label style="
                                        display: flex;
                                        align-items: center;
                                        gap: 0.75rem;
                                        cursor: pointer;
                                        padding: 0.75rem;
                                        background: white;
                                        border-radius: 10px;
                                        border: 1px solid #e9ecef;
                                        transition: all 0.3s ease;
                                    " onmouseover="this.style.borderColor='#667eea'" onmouseout="this.style.borderColor='#e9ecef'">
                                        <input type="checkbox" id="enableSounds" checked style="
                                            width: 18px;
                                            height: 18px;
                                            accent-color: #667eea;
                                        ">
                                        <span style="font-weight: 500;">تفعيل الأصوات</span>
                                    </label>

                                    <div style="
                                        padding: 0.75rem;
                                        background: white;
                                        border-radius: 10px;
                                        border: 1px solid #e9ecef;
                                    ">
                                        <label style="
                                            display: block;
                                            margin-bottom: 0.5rem;
                                            font-weight: 600;
                                            color: #495057;
                                        ">معدل الضريبة (%)</label>
                                        <input type="number" id="taxRate" value="15" min="0" max="100" style="
                                            width: 100%;
                                            padding: 0.75rem;
                                            border: 1px solid #ced4da;
                                            border-radius: 8px;
                                            font-size: 1rem;
                                            transition: all 0.3s ease;
                                        " onfocus="this.style.borderColor='#667eea'" onblur="this.style.borderColor='#ced4da'">
                                    </div>

                                    <div style="
                                        padding: 0.75rem;
                                        background: white;
                                        border-radius: 10px;
                                        border: 1px solid #e9ecef;
                                    ">
                                        <label style="
                                            display: block;
                                            margin-bottom: 0.5rem;
                                            font-weight: 600;
                                            color: #495057;
                                        ">نوع البيع الافتراضي</label>
                                        <select id="defaultSaleType" style="
                                            width: 100%;
                                            padding: 0.75rem;
                                            border: 1px solid #ced4da;
                                            border-radius: 8px;
                                            font-size: 1rem;
                                            background: white;
                                            transition: all 0.3s ease;
                                        " onfocus="this.style.borderColor='#667eea'" onblur="this.style.borderColor='#ced4da'">
                                            <option value="retail">مفرد</option>
                                            <option value="wholesale">جملة</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تبويب إعدادات الفواتير -->
                        <div id="invoice-tab" style="display: none;">
                            <div style="
                                background: linear-gradient(145deg, #f8f9fa, #ffffff);
                                padding: 1.5rem;
                                border-radius: 15px;
                                margin-bottom: 1.5rem;
                                border: 1px solid #e9ecef;
                            ">
                                <h4 style="
                                    color: #495057;
                                    margin-bottom: 1.5rem;
                                    font-size: 1.2rem;
                                    font-weight: 600;
                                    display: flex;
                                    align-items: center;
                                    gap: 0.5rem;
                                ">
                                    <i class="fas fa-file-invoice" style="color: #28a745;"></i>
                                    إعدادات الفواتير
                                </h4>

                                <div style="display: grid; gap: 1rem;">
                                    <label style="
                                        display: flex;
                                        align-items: center;
                                        gap: 0.75rem;
                                        cursor: pointer;
                                        padding: 0.75rem;
                                        background: white;
                                        border-radius: 10px;
                                        border: 1px solid #e9ecef;
                                        transition: all 0.3s ease;
                                    " onmouseover="this.style.borderColor='#28a745'" onmouseout="this.style.borderColor='#e9ecef'">
                                        <input type="checkbox" id="printAfterSale" checked style="
                                            width: 18px;
                                            height: 18px;
                                            accent-color: #28a745;
                                        ">
                                        <span style="font-weight: 500;">طباعة تلقائية بعد البيع</span>
                                    </label>

                                    <div style="
                                        padding: 0.75rem;
                                        background: white;
                                        border-radius: 10px;
                                        border: 1px solid #e9ecef;
                                    ">
                                        <label style="
                                            display: block;
                                            margin-bottom: 0.5rem;
                                            font-weight: 600;
                                            color: #495057;
                                        ">قالب الفاتورة</label>
                                        <select id="invoiceTemplate" style="
                                            width: 100%;
                                            padding: 0.75rem;
                                            border: 1px solid #ced4da;
                                            border-radius: 8px;
                                            font-size: 1rem;
                                            background: white;
                                            transition: all 0.3s ease;
                                        " onfocus="this.style.borderColor='#28a745'" onblur="this.style.borderColor='#ced4da'">
                                            <option value="standard">قياسي</option>
                                            <option value="detailed">مفصل</option>
                                            <option value="compact">مضغوط</option>
                                        </select>
                                    </div>

                                    <div style="
                                        padding: 0.75rem;
                                        background: white;
                                        border-radius: 10px;
                                        border: 1px solid #e9ecef;
                                    ">
                                        <label style="
                                            display: block;
                                            margin-bottom: 0.5rem;
                                            font-weight: 600;
                                            color: #495057;
                                        ">العملة</label>
                                        <select id="currency" style="
                                            width: 100%;
                                            padding: 0.75rem;
                                            border: 1px solid #ced4da;
                                            border-radius: 8px;
                                            font-size: 1rem;
                                            background: white;
                                            transition: all 0.3s ease;
                                        " onfocus="this.style.borderColor='#28a745'" onblur="this.style.borderColor='#ced4da'">
                                            <option value="IQD">دينار عراقي (IQD)</option>
                                            <option value="USD">دولار أمريكي (USD)</option>
                                            <option value="EUR">يورو (EUR)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تبويب إعدادات العرض -->
                        <div id="display-tab" style="display: none;">
                            <div style="
                                background: linear-gradient(145deg, #f8f9fa, #ffffff);
                                padding: 1.5rem;
                                border-radius: 15px;
                                margin-bottom: 1.5rem;
                                border: 1px solid #e9ecef;
                            ">
                                <h4 style="
                                    color: #495057;
                                    margin-bottom: 1.5rem;
                                    font-size: 1.2rem;
                                    font-weight: 600;
                                    display: flex;
                                    align-items: center;
                                    gap: 0.5rem;
                                ">
                                    <i class="fas fa-desktop" style="color: #6f42c1;"></i>
                                    إعدادات العرض
                                </h4>

                                <div style="display: grid; gap: 1rem;">
                                    <div style="
                                        padding: 0.75rem;
                                        background: white;
                                        border-radius: 10px;
                                        border: 1px solid #e9ecef;
                                    ">
                                        <label style="
                                            display: block;
                                            margin-bottom: 0.5rem;
                                            font-weight: 600;
                                            color: #495057;
                                        ">المظهر</label>
                                        <select id="theme" style="
                                            width: 100%;
                                            padding: 0.75rem;
                                            border: 1px solid #ced4da;
                                            border-radius: 8px;
                                            font-size: 1rem;
                                            background: white;
                                            transition: all 0.3s ease;
                                        " onfocus="this.style.borderColor='#6f42c1'" onblur="this.style.borderColor='#ced4da'">
                                            <option value="light">فاتح</option>
                                            <option value="dark">داكن</option>
                                            <option value="auto">تلقائي</option>
                                        </select>
                                    </div>

                                    <div style="
                                        padding: 0.75rem;
                                        background: white;
                                        border-radius: 10px;
                                        border: 1px solid #e9ecef;
                                    ">
                                        <label style="
                                            display: block;
                                            margin-bottom: 0.5rem;
                                            font-weight: 600;
                                            color: #495057;
                                        ">اللغة</label>
                                        <select id="language" style="
                                            width: 100%;
                                            padding: 0.75rem;
                                            border: 1px solid #ced4da;
                                            border-radius: 8px;
                                            font-size: 1rem;
                                            background: white;
                                            transition: all 0.3s ease;
                                        " onfocus="this.style.borderColor='#6f42c1'" onblur="this.style.borderColor='#ced4da'">
                                            <option value="ar">العربية</option>
                                            <option value="en">English</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الحفظ والإغلاق -->
                    <div style="
                        display: flex;
                        gap: 1rem;
                        justify-content: center;
                        margin-top: 2rem;
                        padding-top: 1.5rem;
                        border-top: 1px solid #e9ecef;
                    ">
                        <button onclick="saveSettings()" style="
                            background: linear-gradient(145deg, #28a745, #20c997);
                            color: white;
                            border: none;
                            padding: 1rem 2rem;
                            border-radius: 12px;
                            cursor: pointer;
                            font-size: 1rem;
                            font-weight: 600;
                            transition: all 0.3s ease;
                            display: flex;
                            align-items: center;
                            gap: 0.5rem;
                            box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
                        " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 12px rgba(40, 167, 69, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 8px rgba(40, 167, 69, 0.3)'">
                            <i class="fas fa-save"></i>
                            حفظ الإعدادات
                        </button>

                        <button onclick="resetSettings()" style="
                            background: linear-gradient(145deg, #ffc107, #fd7e14);
                            color: white;
                            border: none;
                            padding: 1rem 2rem;
                            border-radius: 12px;
                            cursor: pointer;
                            font-size: 1rem;
                            font-weight: 600;
                            transition: all 0.3s ease;
                            display: flex;
                            align-items: center;
                            gap: 0.5rem;
                            box-shadow: 0 4px 8px rgba(255, 193, 7, 0.3);
                        " onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                            <i class="fas fa-undo"></i>
                            إعادة تعيين
                        </button>
                    </div>
                </div>

                <style>
                    @keyframes fadeIn {
                        from { opacity: 0; }
                        to { opacity: 1; }
                    }

                    @keyframes slideUp {
                        from {
                            opacity: 0;
                            transform: translateY(30px);
                        }
                        to {
                            opacity: 1;
                            transform: translateY(0);
                        }
                    }

                    @keyframes spin {
                        from { transform: rotate(0deg); }
                        to { transform: rotate(360deg); }
                    }
                </style>
            `;

            document.body.appendChild(modal);

            // تحميل الإعدادات المحفوظة
            loadSavedSettings();
        }

        // وظائف الآلة الحاسبة الاحتياطية
        function calcInput(value) {
            // التحقق من نوع الإدخال
            if (['+', '-', '*', '/'].includes(value)) {
                // عملية حسابية
                if (window.calcOperator && !window.calcWaitingForOperand) {
                    calcEquals();
                }
                window.calcPreviousInput = window.calcCurrentInput;
                window.calcOperator = value;
                window.calcWaitingForOperand = true;
            } else {
                // رقم أو نقطة عشرية
                if (window.calcWaitingForOperand) {
                    window.calcCurrentInput = value;
                    window.calcWaitingForOperand = false;
                } else {
                    if (value === '.' && window.calcCurrentInput.includes('.')) {
                        return; // منع إدخال أكثر من نقطة عشرية
                    }
                    window.calcCurrentInput = window.calcCurrentInput === '0' ? value : window.calcCurrentInput + value;
                }
            }
            updateCalcDisplay();
        }

        function calcEquals() {
            if (window.calcOperator && window.calcPreviousInput !== null) {
                const prev = parseFloat(window.calcPreviousInput);
                const current = parseFloat(window.calcCurrentInput);
                let result = 0;

                switch (window.calcOperator) {
                    case '+': result = prev + current; break;
                    case '-': result = prev - current; break;
                    case '*': result = prev * current; break;
                    case '/': result = current !== 0 ? prev / current : 0; break;
                }

                window.calcCurrentInput = result.toString();
                window.calcOperator = null;
                window.calcPreviousInput = null;
                window.calcWaitingForOperand = true;
                updateCalcDisplay();
            }
        }

        function clearCalc() {
            window.calcCurrentInput = '0';
            window.calcOperator = null;
            window.calcPreviousInput = null;
            window.calcWaitingForOperand = false;
            updateCalcDisplay();
        }

        function calcBackspace() {
            if (window.calcCurrentInput.length > 1) {
                window.calcCurrentInput = window.calcCurrentInput.slice(0, -1);
            } else {
                window.calcCurrentInput = '0';
            }
            updateCalcDisplay();
        }

        function updateCalcDisplay() {
            const display = document.getElementById('calcDisplay');
            if (display) {
                display.textContent = window.calcCurrentInput;
            }
        }

        function closeCalc() {
            const modal = document.querySelector('.modal-overlay');
            if (modal) {
                modal.remove();
            }
        }

        function saveSettings() {
            const settings = {
                autoSave: document.getElementById('autoSave').checked,
                printAfterSale: document.getElementById('printAfterSale').checked,
                taxRate: parseFloat(document.getElementById('taxRate').value) || 15,
                currency: document.getElementById('currency').value
            };

            localStorage.setItem('cashierSettings', JSON.stringify(settings));
            alert('تم حفظ الإعدادات بنجاح');
            console.log('💾 تم حفظ الإعدادات:', settings);
            closeSettings();
        }

        function closeSettings() {
            const modal = document.querySelector('.modal-overlay');
            if (modal) {
                modal.remove();
            }
        }

        // وظائف مساعدة للإعدادات
        function switchTab(tabName) {
            // إخفاء جميع التبويبات
            document.getElementById('general-tab').style.display = 'none';
            document.getElementById('invoice-tab').style.display = 'none';
            document.getElementById('display-tab').style.display = 'none';

            // إزالة التفعيل من جميع الأزرار
            document.getElementById('tab-general').style.background = 'transparent';
            document.getElementById('tab-general').style.color = '#6c757d';
            document.getElementById('tab-invoice').style.background = 'transparent';
            document.getElementById('tab-invoice').style.color = '#6c757d';
            document.getElementById('tab-display').style.background = 'transparent';
            document.getElementById('tab-display').style.color = '#6c757d';

            // إظهار التبويب المحدد وتفعيل الزر
            if (tabName === 'general') {
                document.getElementById('general-tab').style.display = 'block';
                document.getElementById('tab-general').style.background = 'linear-gradient(145deg, #667eea, #764ba2)';
                document.getElementById('tab-general').style.color = 'white';
            } else if (tabName === 'invoice') {
                document.getElementById('invoice-tab').style.display = 'block';
                document.getElementById('tab-invoice').style.background = 'linear-gradient(145deg, #28a745, #20c997)';
                document.getElementById('tab-invoice').style.color = 'white';
            } else if (tabName === 'display') {
                document.getElementById('display-tab').style.display = 'block';
                document.getElementById('tab-display').style.background = 'linear-gradient(145deg, #6f42c1, #e83e8c)';
                document.getElementById('tab-display').style.color = 'white';
            }
        }

        function loadSavedSettings() {
            const settings = JSON.parse(localStorage.getItem('cashierSettings')) || {};

            // تحميل الإعدادات العامة
            if (settings.autoSave !== undefined) {
                document.getElementById('autoSave').checked = settings.autoSave;
            }
            if (settings.showCustomerInfo !== undefined) {
                document.getElementById('showCustomerInfo').checked = settings.showCustomerInfo;
            }
            if (settings.enableSounds !== undefined) {
                document.getElementById('enableSounds').checked = settings.enableSounds;
            }
            if (settings.taxRate !== undefined) {
                document.getElementById('taxRate').value = settings.taxRate;
            }
            if (settings.defaultSaleType !== undefined) {
                document.getElementById('defaultSaleType').value = settings.defaultSaleType;
            }

            // تحميل إعدادات الفواتير
            if (settings.printAfterSale !== undefined) {
                document.getElementById('printAfterSale').checked = settings.printAfterSale;
            }
            if (settings.invoiceTemplate !== undefined) {
                document.getElementById('invoiceTemplate').value = settings.invoiceTemplate;
            }
            if (settings.currency !== undefined) {
                document.getElementById('currency').value = settings.currency;
            }

            // تحميل إعدادات العرض
            if (settings.theme !== undefined) {
                document.getElementById('theme').value = settings.theme;
            }
            if (settings.language !== undefined) {
                document.getElementById('language').value = settings.language;
            }
        }

        function resetSettings() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟')) {
                // إعادة تعيين القيم الافتراضية
                document.getElementById('autoSave').checked = true;
                document.getElementById('showCustomerInfo').checked = false;
                document.getElementById('enableSounds').checked = true;
                document.getElementById('taxRate').value = 15;
                document.getElementById('defaultSaleType').value = 'retail';
                document.getElementById('printAfterSale').checked = true;
                document.getElementById('invoiceTemplate').value = 'standard';
                document.getElementById('currency').value = 'IQD';
                document.getElementById('theme').value = 'light';
                document.getElementById('language').value = 'ar';

                alert('تم إعادة تعيين الإعدادات بنجاح');
            }
        }

        // وظائف مساعدة للآلة الحاسبة
        function memoryOperation(operation) {
            const currentValue = parseFloat(window.calcCurrentInput) || 0;

            switch (operation) {
                case 'clear':
                    window.calcMemory = 0;
                    showNotification('تم مسح الذاكرة', 'info');
                    break;
                case 'recall':
                    window.calcCurrentInput = window.calcMemory.toString();
                    updateCalcDisplay();
                    break;
                case 'add':
                    window.calcMemory += currentValue;
                    showNotification(`تم إضافة ${currentValue} إلى الذاكرة`, 'success');
                    break;
                case 'subtract':
                    window.calcMemory -= currentValue;
                    showNotification(`تم طرح ${currentValue} من الذاكرة`, 'success');
                    break;
            }
        }

        function copyToTotal() {
            const value = parseFloat(window.calcCurrentInput);
            if (!isNaN(value) && value > 0) {
                const totalElement = document.getElementById('total');
                if (totalElement) {
                    totalElement.textContent = `${value.toFixed(0)} دينار`;
                    showNotification(`تم نسخ ${value.toFixed(0)} دينار إلى المجموع`, 'success');
                } else {
                    window.calculatedTotal = value;
                    showNotification(`تم حفظ القيمة: ${value.toFixed(0)} دينار`, 'success');
                }
                closeCalc();
            } else {
                showNotification('يرجى إدخال قيمة صحيحة أكبر من صفر', 'error');
            }
        }

        function copyToDiscount() {
            const value = parseFloat(window.calcCurrentInput);
            if (!isNaN(value) && value >= 0) {
                const discountAmountInput = document.getElementById('discountAmount');
                const discountPercentInput = document.getElementById('discountPercent');

                if (value <= 100 && discountPercentInput) {
                    discountPercentInput.value = value;
                    applyDiscountPercent();
                    showNotification(`تم تطبيق خصم: ${value}%`, 'success');
                } else if (discountAmountInput) {
                    discountAmountInput.value = value;
                    applyDiscount();
                    showNotification(`تم تطبيق خصم: ${value} دينار`, 'success');
                } else {
                    window.calculatedDiscount = value;
                    showNotification(`تم حفظ خصم: ${value}`, 'success');
                }
                closeCalc();
            } else {
                showNotification('قيمة الخصم يجب أن تكون أكبر من أو تساوي صفر', 'error');
            }
        }

        // وظيفة إظهار الإشعارات
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
                z-index: 10000;
                font-weight: 600;
                animation: slideInRight 0.3s ease;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }
    </script>

    <style>
        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideOutRight {
            from {
                opacity: 1;
                transform: translateX(0);
            }
            to {
                opacity: 0;
                transform: translateX(100%);
            }
        }
    </style>
</body>
</html>
