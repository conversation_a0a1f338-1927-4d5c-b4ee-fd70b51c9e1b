<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام الكاشير - البيع المباشر</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="cashier-styles.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
</head>
<body>
    <!-- Header -->
    <header class="cashier-header">
        <div class="header-content">
            <div class="logo-section">
                <i class="fas fa-cash-register"></i>
                <h1>نظام الكاشير</h1>
            </div>
            <div class="header-info">
                <div class="cashier-info">
                    <i class="fas fa-user"></i>
                    <span id="cashierName">💰 الكاشير: نظام المبيعات</span>
                </div>
                <div class="date-time">
                    <i class="fas fa-clock"></i>
                    <span id="currentDateTime"></span>
                </div>
                <div class="shift-info">
                    <i class="fas fa-calendar-day"></i>
                    <span>الوردية: صباحية</span>
                </div>
            </div>
            <div class="header-actions">
                <button class="btn btn-secondary" onclick="showReportsModal()">
                    <i class="fas fa-chart-bar"></i>
                    التقارير
                </button>
                <button class="btn btn-warning" onclick="showSettingsModal()">
                    <i class="fas fa-cog"></i>
                    الإعدادات
                </button>
                <button class="btn btn-primary" onclick="goToMainSite()">
                    <i class="fas fa-home"></i>
                    الموقع الرئيسي
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="cashier-container">
        <!-- Left Panel - Products -->
        <div class="products-panel">
            <div class="panel-header">
                <h3><i class="fas fa-boxes"></i> المنتجات</h3>
                <div class="search-section">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="productSearch" placeholder="البحث عن منتج..." onkeyup="searchProducts()">
                    </div>
                    <div class="upload-section">
                        <input type="file" id="excelFile" accept=".xlsx,.xls,.csv" style="display: none;" onchange="handleExcelUpload(event)">
                        <button class="upload-btn" onclick="document.getElementById('excelFile').click()">
                            <i class="fas fa-file-excel"></i>
                            رفع ملف إكسل
                        </button>
                        <button class="help-btn" onclick="showExcelFormatHelp()">
                            <i class="fas fa-question-circle"></i>
                        </button>
                    </div>
                    <div class="filter-buttons">
                        <button class="filter-btn active" onclick="filterProducts('all')">الكل</button>
                        <button class="filter-btn" onclick="filterProducts('category')">حسب الفئة</button>
                        <button class="filter-btn" onclick="filterProducts('barcode')">باركود</button>
                    </div>
                </div>
            </div>
            
            <div class="categories-tabs" id="categoriesTabs">
                <!-- Categories will be loaded here -->
            </div>
            
            <div class="products-grid" id="productsGrid">
                <!-- Products will be loaded here -->
            </div>
        </div>

        <!-- Center Panel - Cart -->
        <div class="cart-panel">
            <div class="panel-header">
                <h3><i class="fas fa-shopping-cart"></i> سلة المشتريات</h3>
                <div class="cart-actions">
                    <button class="btn btn-sm btn-warning" onclick="holdSale()">
                        <i class="fas fa-pause"></i>
                        تعليق
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="clearCart()">
                        <i class="fas fa-trash"></i>
                        مسح
                    </button>
                </div>
            </div>
            
            <div class="cart-items" id="cartItems">
                <div class="empty-cart">
                    <i class="fas fa-shopping-cart"></i>
                    <p>السلة فارغة</p>
                    <small>أضف منتجات للبدء في البيع</small>
                </div>
            </div>
            
            <div class="cart-summary">
                <div class="summary-row">
                    <span>المجموع الفرعي:</span>
                    <span id="subtotal">0 دينار</span>
                </div>
                <div class="summary-row">
                    <span>الخصم:</span>
                    <span id="discount">0 دينار</span>
                </div>
                <div class="summary-row">
                    <span>الضريبة (15%):</span>
                    <span id="tax">0 دينار</span>
                </div>
                <div class="summary-row total">
                    <span>المجموع الكلي:</span>
                    <span id="total">0 دينار</span>
                </div>
            </div>
        </div>

        <!-- Right Panel - Payment -->
        <div class="payment-panel">
            <div class="panel-header">
                <h3><i class="fas fa-credit-card"></i> الدفع</h3>
                <div class="sale-type">
                    <label>
                        <input type="radio" name="saleType" value="retail" checked onchange="updateSaleType()">
                        <span>مفرد</span>
                    </label>
                    <label>
                        <input type="radio" name="saleType" value="wholesale" onchange="updateSaleType()">
                        <span>جملة</span>
                    </label>
                </div>
            </div>
            
            <div class="customer-section">
                <h4><i class="fas fa-user"></i> معلومات العميل</h4>
                <div class="form-group">
                    <input type="text" id="customerName" placeholder="اسم العميل (اختياري)">
                </div>
                <div class="form-group">
                    <input type="tel" id="customerPhone" placeholder="رقم الهاتف (اختياري)">
                </div>
            </div>
            
            <div class="discount-section">
                <h4><i class="fas fa-percentage"></i> الخصم</h4>
                <div class="discount-controls">
                    <div class="form-group">
                        <input type="number" id="discountAmount" placeholder="مبلغ الخصم" onchange="applyDiscount()">
                    </div>
                    <div class="form-group">
                        <input type="number" id="discountPercent" placeholder="نسبة الخصم %" onchange="applyDiscountPercent()">
                    </div>
                    <button class="btn btn-sm btn-info" onclick="showCouponsModal()">
                        <i class="fas fa-ticket-alt"></i>
                        كوبون
                    </button>
                </div>
            </div>
            
            <div class="payment-methods">
                <h4><i class="fas fa-money-bill-wave"></i> طريقة الدفع</h4>
                <div class="payment-buttons">
                    <button class="payment-btn active" data-method="cash" onclick="selectPaymentMethod('cash')">
                        <i class="fas fa-money-bill"></i>
                        نقدي
                    </button>
                    <button class="payment-btn" data-method="card" onclick="selectPaymentMethod('card')">
                        <i class="fas fa-credit-card"></i>
                        بطاقة
                    </button>
                    <button class="payment-btn" data-method="transfer" onclick="selectPaymentMethod('transfer')">
                        <i class="fas fa-exchange-alt"></i>
                        تحويل
                    </button>
                    <button class="payment-btn" data-method="mixed" onclick="selectPaymentMethod('mixed')">
                        <i class="fas fa-coins"></i>
                        مختلط
                    </button>
                </div>
            </div>
            
            <div class="payment-amount" id="paymentAmount">
                <div class="form-group">
                    <label>المبلغ المدفوع:</label>
                    <input type="number" id="paidAmount" placeholder="0" onchange="calculateChange()">
                </div>
                <div class="change-amount">
                    <span>الباقي:</span>
                    <span id="changeAmount">0 دينار</span>
                </div>
            </div>
            
            <div class="action-buttons">
                <button class="btn btn-success btn-large" onclick="completeSale()" id="completeSaleBtn" disabled>
                    <i class="fas fa-check"></i>
                    إتمام البيع
                </button>
                <button class="btn btn-info btn-large" onclick="printInvoice()">
                    <i class="fas fa-print"></i>
                    طباعة الفاتورة
                </button>
            </div>
        </div>
    </div>

    <!-- Quick Actions Bar -->
    <div class="quick-actions">
        <button class="quick-btn" onclick="showHeldSales()">
            <i class="fas fa-pause-circle"></i>
            <span>المبيعات المعلقة</span>
        </button>
        <button class="quick-btn" onclick="showLastSales()">
            <i class="fas fa-history"></i>
            <span>آخر المبيعات</span>
        </button>
        <button class="quick-btn" onclick="openCashDrawer()">
            <i class="fas fa-cash-register"></i>
            <span>فتح الدرج</span>
        </button>
        <button class="quick-btn" onclick="showCalculator()">
            <i class="fas fa-calculator"></i>
            <span>الآلة الحاسبة</span>
        </button>
    </div>

    <!-- Modals will be added here -->
    <div id="modalContainer"></div>

    <!-- Scripts -->
    <script>
        // متغيرات النظام العامة
        let cart = [];
        let currentSaleType = 'retail';
        let currentPaymentMethod = 'cash';
        let currentDiscount = 0;
        let currentTax = 0.15; // 15% ضريبة
        let heldSales = [];
        let lastInvoiceNumber = 1;

        // تحميل نظام الكاشير مباشرة
        document.addEventListener('DOMContentLoaded', function() {
            initializeCashierSystem();
            updateDateTime();
            setInterval(updateDateTime, 1000);
        });

        // تم حذف وظيفة التحقق من تسجيل الدخول

        // تم حذف وظيفة redirectToLogin

        // تم حذف وظيفة updateUserInterface

        // تم حذف جميع الوظائف المرتبطة بواجهة المستخدم

        function initializeCashierSystem() {
            loadProducts();
            loadCategories();
            updateCartDisplay();
            updateSummary();

            // تحميل آخر رقم فاتورة
            const lastInvoice = localStorage.getItem('lastInvoiceNumber');
            if (lastInvoice) {
                lastInvoiceNumber = parseInt(lastInvoice) + 1;
            }
        }

        function updateDateTime() {
            const now = new Date();
            const dateTimeElement = document.getElementById('currentDateTime');
            if (dateTimeElement) {
                const options = {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                };
                dateTimeElement.textContent = now.toLocaleDateString('en-US', options);
            }
        }

        function loadProducts() {
            const products = JSON.parse(localStorage.getItem('adminProducts')) || [];
            const productsGrid = document.getElementById('productsGrid');

            if (products.length === 0) {
                productsGrid.innerHTML = `
                    <div class="no-products">
                        <i class="fas fa-box-open"></i>
                        <p>لا توجد منتجات متاحة</p>
                        <small>يرجى إضافة منتجات من لوحة الإدارة</small>
                    </div>
                `;
                return;
            }

            productsGrid.innerHTML = products.map(product => `
                <div class="product-card" onclick="addToCart('${product.id}')">
                    <div class="product-image">
                        ${product.image ? `<img src="${product.image}" alt="${product.nameAr}">` : '<i class="fas fa-image"></i>'}
                    </div>
                    <div class="product-info">
                        <h4>${product.nameAr}</h4>
                        <p class="product-price">${formatCurrency(product.price)}</p>
                        <p class="product-stock">المخزون: ${product.stock || 0}</p>
                    </div>
                </div>
            `).join('');
        }

        function loadCategories() {
            const categories = JSON.parse(localStorage.getItem('adminCategories')) || [];
            const categoriesTabs = document.getElementById('categoriesTabs');

            categoriesTabs.innerHTML = `
                <button class="category-tab active" onclick="filterByCategory('all')">الكل</button>
                ${categories.map(category => `
                    <button class="category-tab" onclick="filterByCategory('${category.id}')">${category.nameAr}</button>
                `).join('')}
            `;
        }

        function filterByCategory(categoryId) {
            const products = JSON.parse(localStorage.getItem('adminProducts')) || [];
            const productsGrid = document.getElementById('productsGrid');

            // تحديث الأزرار النشطة
            document.querySelectorAll('.category-tab').forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');

            let filteredProducts = products;
            if (categoryId !== 'all') {
                const category = JSON.parse(localStorage.getItem('adminCategories')).find(c => c.id === categoryId);
                if (category) {
                    filteredProducts = products.filter(p => p.category === category.nameAr);
                }
            }

            productsGrid.innerHTML = filteredProducts.map(product => `
                <div class="product-card" onclick="addToCart('${product.id}')">
                    <div class="product-image">
                        ${product.image ? `<img src="${product.image}" alt="${product.nameAr}">` : '<i class="fas fa-image"></i>'}
                    </div>
                    <div class="product-info">
                        <h4>${product.nameAr}</h4>
                        <p class="product-price">${formatCurrency(product.price)}</p>
                        <p class="product-stock">المخزون: ${product.stock || 0}</p>
                    </div>
                </div>
            `).join('');
        }

        function goToMainSite() {
            if (confirm('هل تريد الانتقال إلى الموقع الرئيسي؟')) {
                window.location.href = 'index.html';
            }
        }

        function addToCart(productId) {
            const products = JSON.parse(localStorage.getItem('adminProducts')) || [];
            const product = products.find(p => p.id === productId);

            if (!product) {
                alert('المنتج غير موجود');
                return;
            }

            if (product.stock <= 0) {
                alert('المنتج غير متوفر في المخزون');
                return;
            }

            const existingItem = cart.find(item => item.id === productId);

            if (existingItem) {
                if (existingItem.quantity >= product.stock) {
                    alert('لا يمكن إضافة كمية أكثر من المتوفر في المخزون');
                    return;
                }
                existingItem.quantity++;
            } else {
                cart.push({
                    id: product.id,
                    name: product.nameAr,
                    price: parseFloat(product.price),
                    quantity: 1,
                    stock: product.stock,
                    image: product.image
                });
            }

            updateCartDisplay();
            updateSummary();
        }

        function updateCartDisplay() {
            const cartItems = document.getElementById('cartItems');

            if (cart.length === 0) {
                cartItems.innerHTML = `
                    <div class="empty-cart">
                        <i class="fas fa-shopping-cart"></i>
                        <p>السلة فارغة</p>
                        <small>أضف منتجات للبدء في البيع</small>
                    </div>
                `;
                return;
            }

            cartItems.innerHTML = cart.map((item, index) => `
                <div class="cart-item">
                    <div class="item-image">
                        ${item.image ? `<img src="${item.image}" alt="${item.name}">` : '<i class="fas fa-box"></i>'}
                    </div>
                    <div class="item-details">
                        <h5>${item.name}</h5>
                        <p class="item-price">${formatCurrency(item.price)}</p>
                    </div>
                    <div class="item-controls">
                        <button class="qty-btn" onclick="decreaseQuantity(${index})">-</button>
                        <span class="quantity">${item.quantity}</span>
                        <button class="qty-btn" onclick="increaseQuantity(${index})">+</button>
                    </div>
                    <div class="item-total">
                        ${formatCurrency(item.price * item.quantity)}
                    </div>
                    <button class="remove-btn" onclick="removeFromCart(${index})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `).join('');
        }

        function increaseQuantity(index) {
            const item = cart[index];
            if (item.quantity >= item.stock) {
                alert('لا يمكن إضافة كمية أكثر من المتوفر في المخزون');
                return;
            }
            item.quantity++;
            updateCartDisplay();
            updateSummary();
        }

        function decreaseQuantity(index) {
            const item = cart[index];
            if (item.quantity > 1) {
                item.quantity--;
                updateCartDisplay();
                updateSummary();
            }
        }

        function removeFromCart(index) {
            cart.splice(index, 1);
            updateCartDisplay();
            updateSummary();
        }

        function clearCart() {
            if (cart.length === 0) return;

            if (confirm('هل أنت متأكد من مسح جميع المنتجات من السلة؟')) {
                cart = [];
                updateCartDisplay();
                updateSummary();
            }
        }

        function updateSummary() {
            const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            const discountAmount = currentDiscount;
            const taxableAmount = subtotal - discountAmount;
            const taxAmount = taxableAmount * currentTax;
            const total = taxableAmount + taxAmount;

            document.getElementById('subtotal').textContent = formatCurrency(subtotal);
            document.getElementById('discount').textContent = formatCurrency(discountAmount);
            document.getElementById('tax').textContent = formatCurrency(taxAmount);
            document.getElementById('total').textContent = formatCurrency(total);

            // تفعيل/تعطيل زر إتمام البيع
            const completeSaleBtn = document.getElementById('completeSaleBtn');
            completeSaleBtn.disabled = cart.length === 0;
        }

        function updateSaleType() {
            const saleType = document.querySelector('input[name="saleType"]:checked').value;
            currentSaleType = saleType;

            // تطبيق أسعار مختلفة للجملة والمفرد
            cart.forEach(item => {
                const products = JSON.parse(localStorage.getItem('adminProducts')) || [];
                const product = products.find(p => p.id === item.id);
                if (product) {
                    if (saleType === 'wholesale' && product.wholesalePrice) {
                        item.price = parseFloat(product.wholesalePrice);
                    } else {
                        item.price = parseFloat(product.price);
                    }
                }
            });

            updateCartDisplay();
            updateSummary();
        }

        function selectPaymentMethod(method) {
            currentPaymentMethod = method;

            document.querySelectorAll('.payment-btn').forEach(btn => btn.classList.remove('active'));
            document.querySelector(`[data-method="${method}"]`).classList.add('active');

            const paymentAmount = document.getElementById('paymentAmount');
            if (method === 'cash') {
                paymentAmount.style.display = 'block';
            } else if (method === 'mixed') {
                paymentAmount.style.display = 'block';
                // يمكن إضافة واجهة للدفع المختلط
            } else {
                paymentAmount.style.display = 'none';
                calculateChange();
            }
        }

        function calculateChange() {
            const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0) - currentDiscount;
            const totalWithTax = total + (total * currentTax);
            const paidAmount = parseFloat(document.getElementById('paidAmount').value) || 0;
            const change = paidAmount - totalWithTax;

            document.getElementById('changeAmount').textContent = formatCurrency(Math.max(0, change));
        }

        function applyDiscount() {
            const discountAmount = parseFloat(document.getElementById('discountAmount').value) || 0;
            currentDiscount = discountAmount;
            document.getElementById('discountPercent').value = '';
            updateSummary();
        }

        function applyDiscountPercent() {
            const discountPercent = parseFloat(document.getElementById('discountPercent').value) || 0;
            const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            currentDiscount = (subtotal * discountPercent) / 100;
            document.getElementById('discountAmount').value = currentDiscount.toFixed(2);
            updateSummary();
        }

        function completeSale() {
            if (cart.length === 0) {
                alert('السلة فارغة');
                return;
            }

            const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0) - currentDiscount;
            const totalWithTax = total + (total * currentTax);

            if (currentPaymentMethod === 'cash') {
                const paidAmount = parseFloat(document.getElementById('paidAmount').value) || 0;
                if (paidAmount < totalWithTax) {
                    alert('المبلغ المدفوع أقل من المطلوب');
                    return;
                }
            }

            // إنشاء الفاتورة
            const invoice = createInvoice();

            // حفظ البيع
            saveSale(invoice);

            // تحديث المخزون
            updateStock();

            // طباعة الفاتورة
            printInvoice(invoice);

            // مسح السلة
            cart = [];
            currentDiscount = 0;
            document.getElementById('discountAmount').value = '';
            document.getElementById('discountPercent').value = '';
            document.getElementById('paidAmount').value = '';
            document.getElementById('customerName').value = '';
            document.getElementById('customerPhone').value = '';

            updateCartDisplay();
            updateSummary();

            alert('تم إتمام البيع بنجاح!');
        }

        function createInvoice() {
            const now = new Date();
            const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            const discountAmount = currentDiscount;
            const taxableAmount = subtotal - discountAmount;
            const taxAmount = taxableAmount * currentTax;
            const total = taxableAmount + taxAmount;

            return {
                id: `INV-${lastInvoiceNumber}`,
                number: lastInvoiceNumber++,
                date: now.toISOString(),
                cashier: { name: 'الكاشير', role: 'cashier', roleText: 'الكاشير', roleIcon: '💰' },
                customer: {
                    name: document.getElementById('customerName').value || 'عميل نقدي',
                    phone: document.getElementById('customerPhone').value || ''
                },
                items: [...cart],
                saleType: currentSaleType,
                paymentMethod: currentPaymentMethod,
                subtotal: subtotal,
                discount: discountAmount,
                tax: taxAmount,
                total: total,
                paidAmount: currentPaymentMethod === 'cash' ? parseFloat(document.getElementById('paidAmount').value) || total : total,
                change: currentPaymentMethod === 'cash' ? Math.max(0, (parseFloat(document.getElementById('paidAmount').value) || 0) - total) : 0
            };
        }

        function saveSale(invoice) {
            const sales = JSON.parse(localStorage.getItem('cashierSales')) || [];
            sales.push(invoice);
            localStorage.setItem('cashierSales', JSON.stringify(sales));
            localStorage.setItem('lastInvoiceNumber', lastInvoiceNumber.toString());
        }

        function updateStock() {
            const products = JSON.parse(localStorage.getItem('adminProducts')) || [];

            cart.forEach(cartItem => {
                const productIndex = products.findIndex(p => p.id === cartItem.id);
                if (productIndex !== -1) {
                    products[productIndex].stock = Math.max(0, products[productIndex].stock - cartItem.quantity);
                }
            });

            localStorage.setItem('adminProducts', JSON.stringify(products));
            loadProducts(); // إعادة تحميل المنتجات لتحديث العرض
        }

        function formatCurrency(amount) {
            return new Intl.NumberFormat('ar-IQ', {
                style: 'currency',
                currency: 'IQD',
                minimumFractionDigits: 0
            }).format(amount);
        }

        // وظائف إضافية
        function searchProducts() {
            const searchTerm = document.getElementById('productSearch').value.toLowerCase();
            const products = JSON.parse(localStorage.getItem('adminProducts')) || [];
            const filteredProducts = products.filter(product =>
                product.nameAr.toLowerCase().includes(searchTerm) ||
                product.nameEn.toLowerCase().includes(searchTerm) ||
                product.barcode?.includes(searchTerm)
            );

            const productsGrid = document.getElementById('productsGrid');
            productsGrid.innerHTML = filteredProducts.map(product => `
                <div class="product-card" onclick="addToCart('${product.id}')">
                    <div class="product-image">
                        ${product.image ? `<img src="${product.image}" alt="${product.nameAr}">` : '<i class="fas fa-image"></i>'}
                    </div>
                    <div class="product-info">
                        <h4>${product.nameAr}</h4>
                        <p class="product-price">${formatCurrency(product.price)}</p>
                        <p class="product-stock">المخزون: ${product.stock || 0}</p>
                    </div>
                </div>
            `).join('');
        }

        function filterProducts(type) {
            document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            if (type === 'all') {
                loadProducts();
            }
        }

        function holdSale() {
            if (cart.length === 0) {
                alert('السلة فارغة');
                return;
            }

            const heldSale = {
                id: Date.now(),
                items: [...cart],
                discount: currentDiscount,
                saleType: currentSaleType,
                timestamp: new Date().toISOString()
            };

            heldSales.push(heldSale);
            localStorage.setItem('heldSales', JSON.stringify(heldSales));

            cart = [];
            currentDiscount = 0;
            updateCartDisplay();
            updateSummary();

            alert('تم تعليق البيع بنجاح');
        }

        function showHeldSales() {
            // يمكن إضافة نافذة منبثقة لعرض المبيعات المعلقة
            alert('ميزة المبيعات المعلقة قيد التطوير');
        }

        function showLastSales() {
            // يمكن إضافة نافذة منبثقة لعرض آخر المبيعات
            alert('ميزة آخر المبيعات قيد التطوير');
        }

        function openCashDrawer() {
            alert('تم فتح درج النقد');
        }

        function showCalculator() {
            alert('ميزة الآلة الحاسبة قيد التطوير');
        }

        function showReportsModal() {
            alert('ميزة التقارير قيد التطوير');
        }

        function showSettingsModal() {
            alert('ميزة الإعدادات قيد التطوير');
        }

        function showCouponsModal() {
            alert('ميزة الكوبونات قيد التطوير');
        }

        // وظيفة رفع ملف الإكسل
        function handleExcelUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            // التحقق من نوع الملف
            const allowedTypes = [
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
                'application/vnd.ms-excel', // .xls
                'text/csv' // .csv
            ];

            if (!allowedTypes.includes(file.type)) {
                alert('يرجى اختيار ملف إكسل صحيح (.xlsx, .xls, .csv)');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    if (file.type === 'text/csv') {
                        parseCSV(e.target.result);
                    } else {
                        parseExcel(e.target.result);
                    }
                } catch (error) {
                    console.error('خطأ في قراءة الملف:', error);
                    alert('حدث خطأ في قراءة الملف. يرجى التأكد من صحة تنسيق الملف.');
                }
            };

            if (file.type === 'text/csv') {
                reader.readAsText(file);
            } else {
                reader.readAsArrayBuffer(file);
            }
        }

        // تحليل ملف CSV
        function parseCSV(csvText) {
            const lines = csvText.split('\n');
            const headers = lines[0].split(',').map(h => h.trim());

            // التحقق من وجود الأعمدة المطلوبة
            const requiredColumns = ['name', 'price', 'stock'];
            const missingColumns = requiredColumns.filter(col =>
                !headers.some(h => h.toLowerCase().includes(col))
            );

            if (missingColumns.length > 0) {
                alert(`الأعمدة المطلوبة مفقودة: ${missingColumns.join(', ')}\nيجب أن يحتوي الملف على: name, price, stock`);
                return;
            }

            const products = [];
            for (let i = 1; i < lines.length; i++) {
                const values = lines[i].split(',').map(v => v.trim());
                if (values.length >= 3 && values[0]) {
                    const product = {
                        id: Date.now() + i,
                        nameAr: values[0] || `منتج ${i}`,
                        nameEn: values[1] || values[0],
                        price: parseFloat(values[2]) || 0,
                        stock: parseInt(values[3]) || 0,
                        category: values[4] || 'عام',
                        description: values[5] || '',
                        barcode: values[6] || '',
                        image: values[7] || null,
                        wholesalePrice: parseFloat(values[8]) || parseFloat(values[2]) || 0
                    };
                    products.push(product);
                }
            }

            importProducts(products);
        }

        // تحليل ملف Excel باستخدام SheetJS
        function parseExcel(arrayBuffer) {
            try {
                const workbook = XLSX.read(arrayBuffer, { type: 'array' });
                const firstSheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[firstSheetName];

                // تحويل الورقة إلى JSON
                const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

                if (jsonData.length < 2) {
                    alert('الملف فارغ أو لا يحتوي على بيانات صالحة');
                    return;
                }

                const headers = jsonData[0].map(h => String(h).toLowerCase().trim());

                // البحث عن الأعمدة المطلوبة
                const nameIndex = headers.findIndex(h => h.includes('name') || h.includes('اسم'));
                const priceIndex = headers.findIndex(h => h.includes('price') || h.includes('سعر'));
                const stockIndex = headers.findIndex(h => h.includes('stock') || h.includes('مخزون') || h.includes('quantity') || h.includes('كمية'));

                if (nameIndex === -1 || priceIndex === -1) {
                    alert('يجب أن يحتوي الملف على أعمدة: الاسم والسعر على الأقل\n\nالأعمدة المتاحة:\n' + headers.join(', '));
                    return;
                }

                const products = [];
                for (let i = 1; i < jsonData.length; i++) {
                    const row = jsonData[i];
                    if (row && row[nameIndex]) {
                        const product = {
                            id: Date.now() + i,
                            nameAr: String(row[nameIndex] || `منتج ${i}`).trim(),
                            nameEn: String(row[nameIndex + 1] || row[nameIndex] || '').trim(),
                            price: parseFloat(row[priceIndex]) || 0,
                            stock: stockIndex !== -1 ? parseInt(row[stockIndex]) || 0 : 100,
                            category: String(row[headers.findIndex(h => h.includes('category') || h.includes('فئة'))] || 'عام').trim(),
                            description: String(row[headers.findIndex(h => h.includes('description') || h.includes('وصف'))] || '').trim(),
                            barcode: String(row[headers.findIndex(h => h.includes('barcode') || h.includes('باركود'))] || '').trim(),
                            image: String(row[headers.findIndex(h => h.includes('image') || h.includes('صورة'))] || '').trim() || null,
                            wholesalePrice: parseFloat(row[headers.findIndex(h => h.includes('wholesale') || h.includes('جملة'))]) || parseFloat(row[priceIndex]) || 0
                        };

                        // التأكد من صحة البيانات
                        if (product.nameAr && product.price > 0) {
                            products.push(product);
                        }
                    }
                }

                importProducts(products);

            } catch (error) {
                console.error('خطأ في تحليل ملف Excel:', error);
                alert('حدث خطأ في تحليل ملف Excel. يرجى التأكد من صحة تنسيق الملف.\n\nالتنسيق المطلوب:\n- العمود الأول: اسم المنتج\n- العمود الثاني: السعر\n- العمود الثالث: المخزون (اختياري)');
            }
        }

        // استيراد المنتجات
        function importProducts(newProducts) {
            if (newProducts.length === 0) {
                alert('لم يتم العثور على منتجات صالحة في الملف');
                return;
            }

            const existingProducts = JSON.parse(localStorage.getItem('adminProducts')) || [];

            // دمج المنتجات الجديدة مع الموجودة
            const allProducts = [...existingProducts];
            let addedCount = 0;
            let updatedCount = 0;

            newProducts.forEach(newProduct => {
                const existingIndex = allProducts.findIndex(p =>
                    p.nameAr === newProduct.nameAr || p.barcode === newProduct.barcode
                );

                if (existingIndex !== -1) {
                    // تحديث المنتج الموجود
                    allProducts[existingIndex] = { ...allProducts[existingIndex], ...newProduct };
                    updatedCount++;
                } else {
                    // إضافة منتج جديد
                    allProducts.push(newProduct);
                    addedCount++;
                }
            });

            // حفظ المنتجات
            localStorage.setItem('adminProducts', JSON.stringify(allProducts));

            // إعادة تحميل المنتجات
            loadProducts();

            // عرض رسالة النجاح
            alert(`تم استيراد المنتجات بنجاح!\n\nالمنتجات الجديدة: ${addedCount}\nالمنتجات المحدثة: ${updatedCount}\nالمجموع: ${newProducts.length}`);

            // مسح اختيار الملف
            document.getElementById('excelFile').value = '';
        }

        // عرض مساعدة تنسيق الملف
        function showExcelFormatHelp() {
            const helpMessage = `
📋 تنسيق ملف الإكسل المطلوب:

🔹 الأعمدة المطلوبة (بالترتيب):
1️⃣ اسم المنتج (name) - مطلوب
2️⃣ السعر (price) - مطلوب
3️⃣ المخزون (stock) - اختياري

🔹 الأعمدة الإضافية (اختيارية):
4️⃣ الفئة (category)
5️⃣ الوصف (description)
6️⃣ الباركود (barcode)
7️⃣ رابط الصورة (image)
8️⃣ سعر الجملة (wholesale)

📝 مثال على التنسيق:
name | price | stock | category
منتج 1 | 1000 | 50 | إلكترونيات
منتج 2 | 2000 | 30 | ملابس

⚠️ ملاحظات مهمة:
• الصف الأول يجب أن يحتوي على أسماء الأعمدة
• يمكن استخدام ملفات .xlsx أو .xls أو .csv
• تأكد من أن الأسعار أرقام صحيحة
• المخزون يجب أن يكون رقم صحيح

✅ الملف سيتم دمجه مع المنتجات الموجودة
            `;

            alert(helpMessage);
        }

        function printInvoice(invoice) {
            if (!invoice) {
                alert('لا توجد فاتورة للطباعة');
                return;
            }

            const printWindow = window.open('', '_blank');
            const invoiceHTML = generateInvoiceHTML(invoice);

            printWindow.document.write(invoiceHTML);
            printWindow.document.close();
            printWindow.print();
        }

        function generateInvoiceHTML(invoice) {
            return `
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>فاتورة رقم ${invoice.number}</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        .invoice-header { text-align: center; margin-bottom: 20px; }
                        .invoice-details { margin-bottom: 20px; }
                        .items-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                        .items-table th, .items-table td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                        .items-table th { background-color: #f2f2f2; }
                        .totals { text-align: right; }
                        .total-row { font-weight: bold; font-size: 1.2em; }
                    </style>
                </head>
                <body>
                    <div class="invoice-header">
                        <h1>فاتورة مبيعات</h1>
                        <h2>رقم الفاتورة: ${invoice.number}</h2>
                    </div>

                    <div class="invoice-details">
                        <p><strong>التاريخ:</strong> ${new Date(invoice.date).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric'
                        })}</p>
                        <p><strong>الوقت:</strong> ${new Date(invoice.date).toLocaleTimeString('en-US', {
                            hour: '2-digit',
                            minute: '2-digit',
                            hour12: true
                        })}</p>
                        <p><strong>الكاشير:</strong> ${invoice.cashier.name}</p>
                        <p><strong>العميل:</strong> ${invoice.customer.name}</p>
                        ${invoice.customer.phone ? `<p><strong>الهاتف:</strong> ${invoice.customer.phone}</p>` : ''}
                        <p><strong>نوع البيع:</strong> ${invoice.saleType === 'retail' ? 'مفرد' : 'جملة'}</p>
                        <p><strong>طريقة الدفع:</strong> ${getPaymentMethodText(invoice.paymentMethod)}</p>
                    </div>

                    <table class="items-table">
                        <thead>
                            <tr>
                                <th>المنتج</th>
                                <th>الكمية</th>
                                <th>السعر</th>
                                <th>المجموع</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${invoice.items.map(item => `
                                <tr>
                                    <td>${item.name}</td>
                                    <td>${item.quantity}</td>
                                    <td>${formatCurrency(item.price)}</td>
                                    <td>${formatCurrency(item.price * item.quantity)}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>

                    <div class="totals">
                        <p>المجموع الفرعي: ${formatCurrency(invoice.subtotal)}</p>
                        ${invoice.discount > 0 ? `<p>الخصم: ${formatCurrency(invoice.discount)}</p>` : ''}
                        <p>الضريبة (15%): ${formatCurrency(invoice.tax)}</p>
                        <p class="total-row">المجموع الكلي: ${formatCurrency(invoice.total)}</p>
                        ${invoice.paymentMethod === 'cash' ? `
                            <p>المبلغ المدفوع: ${formatCurrency(invoice.paidAmount)}</p>
                            <p>الباقي: ${formatCurrency(invoice.change)}</p>
                        ` : ''}
                    </div>

                    <div style="text-align: center; margin-top: 30px;">
                        <p>شكراً لتسوقكم معنا</p>
                    </div>
                </body>
                </html>
            `;
        }

        function getPaymentMethodText(method) {
            const methods = {
                'cash': 'نقدي',
                'card': 'بطاقة',
                'transfer': 'تحويل',
                'mixed': 'مختلط'
            };
            return methods[method] || method;
        }
    </script>
    <script src="cashier-script.js"></script>
</body>
</html>
