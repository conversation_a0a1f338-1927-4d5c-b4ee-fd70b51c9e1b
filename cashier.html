<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام الكاشير المتقدم</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="cashier-styles.css">
</head>
<body>
    <!-- Header -->
    <header class="cashier-header">
        <div class="header-content">
            <div class="header-left">
                <div class="store-info">
                    <h1><i class="fas fa-store"></i> متجر الأمانة</h1>
                    <span class="cashier-name">الكاشير: أحمد علي</span>
                </div>
            </div>
            <div class="header-center">
                <div class="date-time">
                    <div class="current-date" id="currentDate"></div>
                    <div class="current-time" id="currentTime"></div>
                </div>
            </div>
            <div class="header-right">
                <button class="header-btn" onclick="showSettings()">
                    <i class="fas fa-cog"></i>
                    <span>الإعدادات</span>
                </button>
                <button class="header-btn" onclick="showReports()">
                    <i class="fas fa-chart-bar"></i>
                    <span>التقارير</span>
                </button>
                <button class="header-btn primary" onclick="goToMainSite()">
                    <i class="fas fa-home"></i>
                    <span>الموقع الرئيسي</span>
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="cashier-main">
        <!-- Products Panel -->
        <section class="products-panel">
            <div class="panel-header">
                <h2><i class="fas fa-boxes"></i> المنتجات</h2>
                <div class="panel-controls">
                    <button class="control-btn" onclick="refreshProducts()">
                        <i class="fas fa-sync-alt"></i>
                        تحديث
                    </button>
                </div>
            </div>

            <!-- Search and Filters -->
            <div class="search-section">
                <div class="search-bar">
                    <input type="text" id="productSearch" placeholder="ابحث عن المنتجات..." onkeyup="searchProducts()">
                    <button class="search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>

                <div class="filters">
                    <select id="categoryFilter" onchange="filterProducts()">
                        <option value="">جميع الفئات</option>
                        <option value="فواكه">فواكه</option>
                        <option value="خضروات">خضروات</option>
                        <option value="لحوم">لحوم</option>
                        <option value="مخبوزات">مخبوزات</option>
                        <option value="مشروبات">مشروبات</option>
                    </select>

                    <select id="stockFilter" onchange="filterProducts()">
                        <option value="">جميع المنتجات</option>
                        <option value="available">متوفر</option>
                        <option value="low">قليل</option>
                        <option value="out">نفذ</option>
                    </select>
                </div>
            </div>

            <!-- Barcode Scanner -->
            <div class="barcode-section">
                <div class="barcode-input">
                    <input type="text" id="barcodeInput" placeholder="امسح الباركود..." onkeydown="handleBarcode(event)">
                    <button class="barcode-btn" onclick="openBarcodeScanner()">
                        <i class="fas fa-camera"></i>
                    </button>
                </div>
                <div class="barcode-status" id="barcodeStatus">جاهز</div>
            </div>

            <!-- Products Grid -->
            <div class="products-grid" id="productsGrid">
                <!-- Products will be loaded here -->
            </div>
        </section>

        <!-- Cart Panel -->
        <section class="cart-panel">
            <div class="panel-header">
                <h2><i class="fas fa-shopping-cart"></i> سلة المشتريات</h2>
                <div class="cart-actions">
                    <button class="action-btn warning" onclick="holdSale()">
                        <i class="fas fa-pause"></i>
                        تعليق
                    </button>
                    <button class="action-btn danger" onclick="clearCart()">
                        <i class="fas fa-trash"></i>
                        مسح
                    </button>
                </div>
            </div>

            <!-- Cart Items -->
            <div class="cart-items" id="cartItems">
                <div class="empty-cart">
                    <i class="fas fa-shopping-cart"></i>
                    <p>السلة فارغة</p>
                </div>
            </div>

            <!-- Cart Summary -->
            <div class="cart-summary">
                <div class="summary-row">
                    <span>المجموع الفرعي:</span>
                    <span id="subtotal">0 د.ع</span>
                </div>
                <div class="summary-row">
                    <span>الخصم:</span>
                    <span id="discount">0 د.ع</span>
                </div>
                <div class="summary-row">
                    <span>الضريبة:</span>
                    <span id="tax">0 د.ع</span>
                </div>
                <div class="summary-row total">
                    <span>الإجمالي:</span>
                    <span id="total">0 د.ع</span>
                </div>
            </div>

            <!-- Discount Section -->
            <div class="discount-section">
                <h3>الخصم</h3>
                <div class="discount-inputs">
                    <input type="number" id="discountAmount" placeholder="المبلغ" onchange="applyDiscount()">
                    <input type="number" id="discountPercent" placeholder="النسبة %" onchange="applyDiscountPercent()">
                    <button class="discount-btn" onclick="clearDiscount()">
                        <i class="fas fa-eraser"></i>
                    </button>
                </div>
            </div>

            <!-- Payment Section -->
            <div class="payment-section">
                <h3>طريقة الدفع</h3>
                <div class="payment-methods">
                    <button class="payment-btn active" data-method="cash" onclick="selectPayment('cash')">
                        <i class="fas fa-money-bill"></i>
                        نقدي
                    </button>
                    <button class="payment-btn" data-method="card" onclick="selectPayment('card')">
                        <i class="fas fa-credit-card"></i>
                        بطاقة
                    </button>
                    <button class="payment-btn" data-method="transfer" onclick="selectPayment('transfer')">
                        <i class="fas fa-exchange-alt"></i>
                        تحويل
                    </button>
                </div>

                <div class="payment-amount" id="paymentAmount">
                    <input type="number" id="paidAmount" placeholder="المبلغ المدفوع" onkeyup="calculateChange()">
                    <div class="change-amount">
                        <span>الباقي: </span>
                        <span id="changeAmount">0 د.ع</span>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <button class="action-btn success large" onclick="completeSale()" id="completeSaleBtn" disabled>
                    <i class="fas fa-check"></i>
                    إتمام البيع
                </button>
                <button class="action-btn info" onclick="printInvoice()">
                    <i class="fas fa-print"></i>
                    طباعة
                </button>
            </div>
        </section>
    </main>

    <!-- Quick Actions -->
    <div class="quick-actions">
        <button class="quick-btn" onclick="showHeldSales()">
            <i class="fas fa-pause-circle"></i>
            <span>المبيعات المعلقة</span>
        </button>
        <button class="quick-btn" onclick="showLastSales()">
            <i class="fas fa-history"></i>
            <span>آخر المبيعات</span>
        </button>
        <button class="quick-btn" onclick="openCashDrawer()">
            <i class="fas fa-cash-register"></i>
            <span>فتح الدرج</span>
        </button>
        <button class="quick-btn" onclick="showCalculator()">
            <i class="fas fa-calculator"></i>
            <span>الآلة الحاسبة</span>
        </button>
    </div>

    <!-- Settings Modal -->
    <div id="settingsModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-cog"></i> إعدادات النظام</h3>
                <button class="close-btn" onclick="hideSettings()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="settings-tabs">
                    <button class="tab-btn active" onclick="showTab('general')">عام</button>
                    <button class="tab-btn" onclick="showTab('display')">العرض</button>
                    <button class="tab-btn" onclick="showTab('printer')">الطباعة</button>
                </div>
                <div class="settings-content" id="settingsContent">
                    <div id="generalTab" class="tab-content">
                        <h4>الإعدادات العامة</h4>
                        <div class="setting-group">
                            <label>اسم المتجر:</label>
                            <input type="text" id="storeNameSetting" value="متجر الأمانة">
                        </div>
                        <div class="setting-group">
                            <label>اسم الكاشير:</label>
                            <input type="text" id="cashierNameSetting" value="أحمد علي">
                        </div>
                        <div class="setting-group">
                            <label>العملة:</label>
                            <select id="currencySetting">
                                <option value="د.ع">دينار عراقي</option>
                                <option value="$">دولار أمريكي</option>
                                <option value="€">يورو</option>
                            </select>
                        </div>
                    </div>
                    <div id="displayTab" class="tab-content" style="display: none;">
                        <h4>إعدادات العرض</h4>
                        <div class="setting-group">
                            <label>عدد المنتجات في الصفحة:</label>
                            <select id="productsPerPageSetting">
                                <option value="12">12 منتج</option>
                                <option value="24">24 منتج</option>
                                <option value="36">36 منتج</option>
                            </select>
                        </div>
                    </div>
                    <div id="printerTab" class="tab-content" style="display: none;">
                        <h4>إعدادات الطباعة</h4>
                        <div class="setting-group">
                            <label>حجم الورق:</label>
                            <select id="paperSizeSetting">
                                <option value="A4">A4</option>
                                <option value="80mm">80mm حراري</option>
                                <option value="58mm">58mm حراري</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn success" onclick="saveSettings()">حفظ</button>
                <button class="btn secondary" onclick="hideSettings()">إلغاء</button>
            </div>
        </div>
    </div>

    <!-- Reports Modal -->
    <div id="reportsModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-chart-bar"></i> التقارير</h3>
                <button class="close-btn" onclick="hideReports()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="reports-grid">
                    <div class="report-card" onclick="showSalesReport()">
                        <i class="fas fa-chart-line"></i>
                        <h4>تقرير المبيعات</h4>
                        <p>عرض تفصيلي للمبيعات اليومية</p>
                    </div>
                    <div class="report-card" onclick="showInventoryReport()">
                        <i class="fas fa-boxes"></i>
                        <h4>تقرير المخزون</h4>
                        <p>حالة المخزون والمنتجات</p>
                    </div>
                    <div class="report-card" onclick="showFinancialReport()">
                        <i class="fas fa-money-bill-wave"></i>
                        <h4>التقرير المالي</h4>
                        <p>الإيرادات والأرباح</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Held Sales Modal -->
    <div id="heldSalesModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-pause-circle"></i> المبيعات المعلقة</h3>
                <button class="close-btn" onclick="hideHeldSales()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="heldSalesList">
                    <!-- Held sales will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Calculator Modal -->
    <div id="calculatorModal" class="modal" style="display: none;">
        <div class="modal-content calculator">
            <div class="modal-header">
                <h3><i class="fas fa-calculator"></i> الآلة الحاسبة</h3>
                <button class="close-btn" onclick="hideCalculator()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="calculator-body">
                <div class="calculator-display" id="calcDisplay">0</div>
                <div class="calculator-buttons">
                    <button class="calc-btn clear" onclick="clearCalculator()">C</button>
                    <button class="calc-btn" onclick="inputCalc('/')">/</button>
                    <button class="calc-btn" onclick="inputCalc('*')">×</button>
                    <button class="calc-btn" onclick="backspace()">⌫</button>

                    <button class="calc-btn" onclick="inputCalc('7')">7</button>
                    <button class="calc-btn" onclick="inputCalc('8')">8</button>
                    <button class="calc-btn" onclick="inputCalc('9')">9</button>
                    <button class="calc-btn" onclick="inputCalc('-')">-</button>

                    <button class="calc-btn" onclick="inputCalc('4')">4</button>
                    <button class="calc-btn" onclick="inputCalc('5')">5</button>
                    <button class="calc-btn" onclick="inputCalc('6')">6</button>
                    <button class="calc-btn" onclick="inputCalc('+')">+</button>

                    <button class="calc-btn" onclick="inputCalc('1')">1</button>
                    <button class="calc-btn" onclick="inputCalc('2')">2</button>
                    <button class="calc-btn" onclick="inputCalc('3')">3</button>
                    <button class="calc-btn equals" onclick="calculateResult()" rowspan="2">=</button>

                    <button class="calc-btn zero" onclick="inputCalc('0')">0</button>
                    <button class="calc-btn" onclick="inputCalc('.')">.</button>
                </div>
            </div>
        </div>
    </div>

    <script src="cashier-script.js"></script>
</body>
</html>