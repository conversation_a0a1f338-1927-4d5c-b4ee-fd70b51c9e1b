<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام الكاشير - البيع المباشر</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="cashier-styles.css">
</head>
<body>
    <!-- Header -->
    <header class="cashier-header">
        <div class="header-content">
            <div class="logo-section">
                <i class="fas fa-cash-register"></i>
                <h1>نظام الكاشير</h1>
            </div>
            <div class="header-info">
                <div class="cashier-info">
                    <i class="fas fa-user"></i>
                    <span id="cashierName">💰 الكاشير: نظام المبيعات</span>
                </div>
                <div class="date-time">
                    <i class="fas fa-clock"></i>
                    <span id="currentDateTime"></span>
                </div>
                <div class="shift-info">
                    <i class="fas fa-calendar-day"></i>
                    <span>الوردية: صباحية</span>
                </div>
            </div>
            <div class="header-actions">
                <button class="btn btn-secondary" onclick="showReportsModal()">
                    <i class="fas fa-chart-bar"></i>
                    التقارير
                </button>
                <button class="btn btn-warning" onclick="showSettingsModal()">
                    <i class="fas fa-cog"></i>
                    الإعدادات
                </button>
                <button class="btn btn-primary" onclick="goToMainSite()">
                    <i class="fas fa-home"></i>
                    الموقع الرئيسي
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="cashier-container">
        <!-- Left Panel - Products -->
        <div class="products-panel">
            <div class="panel-header">
                <h3><i class="fas fa-boxes"></i> المنتجات</h3>
                <div class="search-section">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="productSearch" placeholder="البحث عن منتج..." onkeyup="searchProducts()">
                    </div>
                    <div class="filter-buttons">
                        <button class="filter-btn active" onclick="filterProducts('all')">الكل</button>
                        <button class="filter-btn" onclick="filterProducts('category')">حسب الفئة</button>
                        <button class="filter-btn" onclick="filterProducts('barcode')">باركود</button>
                    </div>
                </div>
            </div>
            
            <div class="categories-tabs" id="categoriesTabs">
                <!-- Categories will be loaded here -->
            </div>
            
            <div class="products-grid" id="productsGrid">
                <!-- Products will be loaded here -->
            </div>
        </div>

        <!-- Center Panel - Cart -->
        <div class="cart-panel">
            <div class="panel-header">
                <h3><i class="fas fa-shopping-cart"></i> سلة المشتريات</h3>
                <div class="cart-actions">
                    <button class="btn btn-sm btn-warning" onclick="holdSale()">
                        <i class="fas fa-pause"></i>
                        تعليق
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="clearCart()">
                        <i class="fas fa-trash"></i>
                        مسح
                    </button>
                </div>
            </div>
            
            <div class="cart-items" id="cartItems">
                <div class="empty-cart">
                    <i class="fas fa-shopping-cart"></i>
                    <p>السلة فارغة</p>
                    <small>أضف منتجات للبدء في البيع</small>
                </div>
            </div>
            
            <div class="cart-summary">
                <div class="summary-row">
                    <span>المجموع الفرعي:</span>
                    <span id="subtotal">0 دينار</span>
                </div>
                <div class="summary-row">
                    <span>الخصم:</span>
                    <span id="discount">0 دينار</span>
                </div>
                <div class="summary-row">
                    <span>الضريبة (15%):</span>
                    <span id="tax">0 دينار</span>
                </div>
                <div class="summary-row total">
                    <span>المجموع الكلي:</span>
                    <span id="total">0 دينار</span>
                </div>
            </div>
        </div>

        <!-- Right Panel - Payment -->
        <div class="payment-panel">
            <div class="panel-header">
                <h3><i class="fas fa-credit-card"></i> الدفع</h3>
                <div class="sale-type">
                    <label>
                        <input type="radio" name="saleType" value="retail" checked onchange="updateSaleType()">
                        <span>مفرد</span>
                    </label>
                    <label>
                        <input type="radio" name="saleType" value="wholesale" onchange="updateSaleType()">
                        <span>جملة</span>
                    </label>
                </div>
            </div>
            
            <div class="customer-section">
                <h4><i class="fas fa-user"></i> معلومات العميل</h4>
                <div class="form-group">
                    <input type="text" id="customerName" placeholder="اسم العميل (اختياري)">
                </div>
                <div class="form-group">
                    <input type="tel" id="customerPhone" placeholder="رقم الهاتف (اختياري)">
                </div>
            </div>
            
            <div class="discount-section">
                <h4><i class="fas fa-percentage"></i> الخصم</h4>
                <div class="discount-controls">
                    <div class="form-group">
                        <input type="number" id="discountAmount" placeholder="مبلغ الخصم" onchange="applyDiscount()">
                    </div>
                    <div class="form-group">
                        <input type="number" id="discountPercent" placeholder="نسبة الخصم %" onchange="applyDiscountPercent()">
                    </div>
                    <button class="btn btn-sm btn-info" onclick="showCouponsModal()">
                        <i class="fas fa-ticket-alt"></i>
                        كوبون
                    </button>
                </div>
            </div>
            
            <div class="payment-methods">
                <h4><i class="fas fa-money-bill-wave"></i> طريقة الدفع</h4>
                <div class="payment-buttons">
                    <button class="payment-btn active" data-method="cash" onclick="selectPaymentMethod('cash')">
                        <i class="fas fa-money-bill"></i>
                        نقدي
                    </button>
                    <button class="payment-btn" data-method="card" onclick="selectPaymentMethod('card')">
                        <i class="fas fa-credit-card"></i>
                        بطاقة
                    </button>
                    <button class="payment-btn" data-method="transfer" onclick="selectPaymentMethod('transfer')">
                        <i class="fas fa-exchange-alt"></i>
                        تحويل
                    </button>
                    <button class="payment-btn" data-method="mixed" onclick="selectPaymentMethod('mixed')">
                        <i class="fas fa-coins"></i>
                        مختلط
                    </button>
                </div>
            </div>
            
            <div class="payment-amount" id="paymentAmount">
                <div class="form-group">
                    <label>المبلغ المدفوع:</label>
                    <input type="number" id="paidAmount" placeholder="0" onchange="calculateChange()">
                </div>
                <div class="change-amount">
                    <span>الباقي:</span>
                    <span id="changeAmount">0 دينار</span>
                </div>
            </div>
            
            <div class="action-buttons">
                <button class="btn btn-success btn-large" onclick="completeSale()" id="completeSaleBtn" disabled>
                    <i class="fas fa-check"></i>
                    إتمام البيع
                </button>
                <button class="btn btn-info btn-large" onclick="printInvoice()">
                    <i class="fas fa-print"></i>
                    طباعة الفاتورة
                </button>
            </div>
        </div>
    </div>

    <!-- Quick Actions Bar -->
    <div class="quick-actions">
        <button class="quick-btn" onclick="showHeldSales()">
            <i class="fas fa-pause-circle"></i>
            <span>المبيعات المعلقة</span>
        </button>
        <button class="quick-btn" onclick="showLastSales()">
            <i class="fas fa-history"></i>
            <span>آخر المبيعات</span>
        </button>
        <button class="quick-btn" onclick="openCashDrawer()">
            <i class="fas fa-cash-register"></i>
            <span>فتح الدرج</span>
        </button>
        <button class="quick-btn" onclick="showCalculator()">
            <i class="fas fa-calculator"></i>
            <span>الآلة الحاسبة</span>
        </button>
        <button class="quick-btn" onclick="showSettingsModal()">
            <i class="fas fa-cog"></i>
            <span>الإعدادات</span>
        </button>
    </div>

    <!-- Modals will be added here -->
    <div id="modalContainer"></div>

    <!-- Scripts -->
    <script src="cashier-script.js"></script>
    <script>
        // متغيرات النظام العامة
        let cart = [];
        let currentSaleType = 'retail';
        let currentPaymentMethod = 'cash';
        let currentDiscount = 0;
        let currentTax = 0.15; // 15% ضريبة
        let heldSales = [];
        let lastInvoiceNumber = 1;

        // تحميل نظام الكاشير مباشرة
        document.addEventListener('DOMContentLoaded', function() {
            initializeCashierSystem();
            updateDateTime();
            setInterval(updateDateTime, 1000);

            // التأكد من تحميل الوظائف من cashier-script.js
            setTimeout(() => {
                console.log('🔍 فحص الوظائف المتاحة...');
                console.log('showCalculator:', typeof showCalculator);
                console.log('showSettingsModal:', typeof showSettingsModal);

                // تحميل الوظائف الجديدة
                console.log('🔧 تحميل الوظائف الجديدة...');
                window.showSettingsModal = openSettings;
                if (typeof openCashDrawer !== 'function') {
                    window.openCashDrawer = function() {
                        alert('تم فتح درج النقد');
                        console.log('💰 تم فتح درج النقد');
                    };
                }
                if (typeof showHeldSales !== 'function') {
                    window.showHeldSales = function() {
                        alert('المبيعات المعلقة غير متاحة حالياً');
                    };
                }
                if (typeof showLastSales !== 'function') {
                    window.showLastSales = function() {
                        alert('آخر المبيعات غير متاحة حالياً');
                    };
                }

                console.log('✅ تم التحقق من جميع الوظائف');
            }, 100);
        });

        // تم حذف وظيفة التحقق من تسجيل الدخول

        // تم حذف وظيفة redirectToLogin

        // تم حذف وظيفة updateUserInterface

        // تم حذف جميع الوظائف المرتبطة بواجهة المستخدم

        function initializeCashierSystem() {
            loadProducts();
            loadCategories();
            updateCartDisplay();
            updateSummary();

            // تحميل آخر رقم فاتورة
            const lastInvoice = localStorage.getItem('lastInvoiceNumber');
            if (lastInvoice) {
                lastInvoiceNumber = parseInt(lastInvoice) + 1;
            }
        }

        function updateDateTime() {
            const now = new Date();
            const dateTimeElement = document.getElementById('currentDateTime');
            if (dateTimeElement) {
                const options = {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                };
                dateTimeElement.textContent = now.toLocaleDateString('en-US', options);
            }
        }

        function loadProducts() {
            updateProductsDisplay();
        }

        function updateProductsDisplay() {
            const products = JSON.parse(localStorage.getItem('products')) || [];
            const productsGrid = document.getElementById('productsGrid');

            if (products.length === 0) {
                productsGrid.innerHTML = `
                    <div class="no-products">
                        <i class="fas fa-box-open"></i>
                        <p>لا توجد منتجات متاحة</p>
                        <small>يرجى إضافة منتجات من لوحة الإدارة</small>
                    </div>
                `;
                return;
            }

            productsGrid.innerHTML = products.map(product => {
                const displayPrice = currentSaleType === 'wholesale' && product.wholesalePrice ?
                    product.wholesalePrice : product.price;
                const priceLabel = currentSaleType === 'wholesale' ? 'جملة' : 'مفرد';

                return `
                    <div class="product-card" onclick="addToCart('${product.id}')">
                        <div class="product-image">
                            ${product.image ? `<img src="${product.image}" alt="${product.name}">` : '<i class="fas fa-image"></i>'}
                        </div>
                        <div class="product-info">
                            <h4>${product.name}</h4>
                            <div class="product-prices">
                                <p class="product-price current-price">${formatCurrency(displayPrice)} <span class="price-type">(${priceLabel})</span></p>
                                ${currentSaleType === 'wholesale' && product.price !== product.wholesalePrice ?
                                    `<p class="product-price retail-price">مفرد: ${formatCurrency(product.price)}</p>` : ''}
                                ${currentSaleType === 'retail' && product.wholesalePrice ?
                                    `<p class="product-price wholesale-price">جملة: ${formatCurrency(product.wholesalePrice)}</p>` : ''}
                            </div>
                            <p class="product-stock">المخزون: ${product.quantity || 0}</p>
                        </div>
                    </div>
                `;
            }).join('');
        }

        function loadCategories() {
            const categories = JSON.parse(localStorage.getItem('categories')) || [];
            const categoriesTabs = document.getElementById('categoriesTabs');

            categoriesTabs.innerHTML = `
                <button class="category-tab active" onclick="filterByCategory('all')">الكل</button>
                ${categories.map(category => `
                    <button class="category-tab" onclick="filterByCategory('${category.id}')">${category.name}</button>
                `).join('')}
            `;
        }

        function filterByCategory(categoryId) {
            const products = JSON.parse(localStorage.getItem('products')) || [];
            const productsGrid = document.getElementById('productsGrid');

            // تحديث الأزرار النشطة
            document.querySelectorAll('.category-tab').forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');

            let filteredProducts = products;
            if (categoryId !== 'all') {
                const categories = JSON.parse(localStorage.getItem('categories')) || [];
                const category = categories.find(c => c.id === categoryId);
                if (category) {
                    filteredProducts = products.filter(p => p.category === category.name);
                }
            }

            productsGrid.innerHTML = filteredProducts.map(product => {
                const displayPrice = currentSaleType === 'wholesale' && product.wholesalePrice ?
                    product.wholesalePrice : product.price;
                const priceLabel = currentSaleType === 'wholesale' ? 'جملة' : 'مفرد';

                return `
                    <div class="product-card" onclick="addToCart('${product.id}')">
                        <div class="product-image">
                            ${product.image ? `<img src="${product.image}" alt="${product.name}">` : '<i class="fas fa-image"></i>'}
                        </div>
                        <div class="product-info">
                            <h4>${product.name}</h4>
                            <div class="product-prices">
                                <p class="product-price current-price">${formatCurrency(displayPrice)} <span class="price-type">(${priceLabel})</span></p>
                                ${currentSaleType === 'wholesale' && product.price !== product.wholesalePrice ?
                                    `<p class="product-price retail-price">مفرد: ${formatCurrency(product.price)}</p>` : ''}
                                ${currentSaleType === 'retail' && product.wholesalePrice ?
                                    `<p class="product-price wholesale-price">جملة: ${formatCurrency(product.wholesalePrice)}</p>` : ''}
                            </div>
                            <p class="product-stock">المخزون: ${product.quantity || 0}</p>
                        </div>
                    </div>
                `;
            }).join('');
        }

        function goToMainSite() {
            if (confirm('هل تريد الانتقال إلى الموقع الرئيسي؟')) {
                window.location.href = 'index.html';
            }
        }

        function addToCart(productId) {
            const products = JSON.parse(localStorage.getItem('products')) || [];
            const product = products.find(p => p.id === productId);

            if (!product) {
                alert('المنتج غير موجود');
                return;
            }

            if (product.quantity <= 0) {
                alert('المنتج غير متوفر في المخزون');
                return;
            }

            // تحديد السعر حسب نوع البيع
            const itemPrice = currentSaleType === 'wholesale' && product.wholesalePrice ?
                parseFloat(product.wholesalePrice) : parseFloat(product.price);

            const existingItem = cart.find(item => item.id === productId);

            if (existingItem) {
                if (existingItem.quantity >= product.quantity) {
                    alert('لا يمكن إضافة كمية أكثر من المتوفر في المخزون');
                    return;
                }
                existingItem.quantity++;
                // تحديث السعر في حالة تغيير نوع البيع
                existingItem.price = itemPrice;
                existingItem.priceType = currentSaleType;
            } else {
                cart.push({
                    id: product.id,
                    name: product.name,
                    price: itemPrice,
                    retailPrice: parseFloat(product.price),
                    wholesalePrice: product.wholesalePrice ? parseFloat(product.wholesalePrice) : null,
                    quantity: 1,
                    stock: product.quantity,
                    image: product.image,
                    priceType: currentSaleType
                });
            }

            updateCartDisplay();
            updateSummary();
        }

        function updateCartDisplay() {
            const cartItems = document.getElementById('cartItems');

            if (cart.length === 0) {
                cartItems.innerHTML = `
                    <div class="empty-cart">
                        <i class="fas fa-shopping-cart"></i>
                        <p>السلة فارغة</p>
                        <small>أضف منتجات للبدء في البيع</small>
                    </div>
                `;
                return;
            }

            cartItems.innerHTML = cart.map((item, index) => {
                const priceTypeLabel = item.priceType === 'wholesale' ? 'جملة' : 'مفرد';
                const priceTypeClass = item.priceType === 'wholesale' ? 'wholesale' : 'retail';

                return `
                    <div class="cart-item">
                        <div class="item-image">
                            ${item.image ? `<img src="${item.image}" alt="${item.name}">` : '<i class="fas fa-box"></i>'}
                        </div>
                        <div class="item-details">
                            <h5>${item.name}</h5>
                            <p class="item-price ${priceTypeClass}">
                                ${formatCurrency(item.price)}
                                <span class="price-type-badge">${priceTypeLabel}</span>
                            </p>
                            ${item.priceType === 'wholesale' && item.retailPrice ?
                                `<small class="retail-price-small">مفرد: ${formatCurrency(item.retailPrice)}</small>` : ''}
                        </div>
                        <div class="item-controls">
                            <button class="qty-btn" onclick="decreaseQuantity(${index})">-</button>
                            <span class="quantity">${item.quantity}</span>
                            <button class="qty-btn" onclick="increaseQuantity(${index})">+</button>
                        </div>
                        <div class="item-total">
                            ${formatCurrency(item.price * item.quantity)}
                        </div>
                        <button class="remove-btn" onclick="removeFromCart(${index})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                `;
            }).join('');
        }

        function increaseQuantity(index) {
            const item = cart[index];
            if (item.quantity >= item.stock) {
                alert('لا يمكن إضافة كمية أكثر من المتوفر في المخزون');
                return;
            }
            item.quantity++;
            updateCartDisplay();
            updateSummary();
        }

        function decreaseQuantity(index) {
            const item = cart[index];
            if (item.quantity > 1) {
                item.quantity--;
                updateCartDisplay();
                updateSummary();
            }
        }

        function removeFromCart(index) {
            cart.splice(index, 1);
            updateCartDisplay();
            updateSummary();
        }

        function clearCart() {
            if (cart.length === 0) return;

            if (confirm('هل أنت متأكد من مسح جميع المنتجات من السلة؟')) {
                cart = [];
                updateCartDisplay();
                updateSummary();
            }
        }

        function updateSummary() {
            const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            const discountAmount = currentDiscount;
            const taxableAmount = subtotal - discountAmount;
            const taxAmount = taxableAmount * currentTax;
            const total = taxableAmount + taxAmount;

            document.getElementById('subtotal').textContent = formatCurrency(subtotal);
            document.getElementById('discount').textContent = formatCurrency(discountAmount);
            document.getElementById('tax').textContent = formatCurrency(taxAmount);
            document.getElementById('total').textContent = formatCurrency(total);

            // تفعيل/تعطيل زر إتمام البيع
            const completeSaleBtn = document.getElementById('completeSaleBtn');
            completeSaleBtn.disabled = cart.length === 0;
        }

        function updateSaleType() {
            const saleType = document.querySelector('input[name="saleType"]:checked').value;
            currentSaleType = saleType;

            // تطبيق أسعار مختلفة للجملة والمفرد
            cart.forEach(item => {
                const products = JSON.parse(localStorage.getItem('products')) || [];
                const product = products.find(p => p.id === item.id);

                if (product) {
                    if (saleType === 'wholesale' && product.wholesalePrice) {
                        item.price = parseFloat(product.wholesalePrice);
                        item.priceType = 'wholesale';
                    } else {
                        item.price = parseFloat(product.price);
                        item.priceType = 'retail';
                    }
                }
            });

            // تحديث عرض السلة والملخص
            updateCartDisplay();
            updateSummary();
            updateProductsDisplay();

            console.log(`تم تغيير نوع البيع إلى: ${saleType === 'wholesale' ? 'جملة' : 'مفرد'}`);
        }

        function selectPaymentMethod(method) {
            currentPaymentMethod = method;

            document.querySelectorAll('.payment-btn').forEach(btn => btn.classList.remove('active'));
            document.querySelector(`[data-method="${method}"]`).classList.add('active');

            const paymentAmount = document.getElementById('paymentAmount');
            if (method === 'cash') {
                paymentAmount.style.display = 'block';
            } else if (method === 'mixed') {
                paymentAmount.style.display = 'block';
                // يمكن إضافة واجهة للدفع المختلط
            } else {
                paymentAmount.style.display = 'none';
                calculateChange();
            }
        }

        function calculateChange() {
            const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0) - currentDiscount;
            const totalWithTax = total + (total * currentTax);
            const paidAmount = parseFloat(document.getElementById('paidAmount').value) || 0;
            const change = paidAmount - totalWithTax;

            document.getElementById('changeAmount').textContent = formatCurrency(Math.max(0, change));
        }

        function applyDiscount() {
            const discountAmount = parseFloat(document.getElementById('discountAmount').value) || 0;
            currentDiscount = discountAmount;
            document.getElementById('discountPercent').value = '';
            updateSummary();
        }

        function applyDiscountPercent() {
            const discountPercent = parseFloat(document.getElementById('discountPercent').value) || 0;
            const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            currentDiscount = (subtotal * discountPercent) / 100;
            document.getElementById('discountAmount').value = currentDiscount.toFixed(2);
            updateSummary();
        }

        function completeSale() {
            if (cart.length === 0) {
                alert('السلة فارغة');
                return;
            }

            const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0) - currentDiscount;
            const totalWithTax = total + (total * currentTax);

            if (currentPaymentMethod === 'cash') {
                const paidAmount = parseFloat(document.getElementById('paidAmount').value) || 0;
                if (paidAmount < totalWithTax) {
                    alert('المبلغ المدفوع أقل من المطلوب');
                    return;
                }
            }

            // إنشاء الفاتورة
            const invoice = createInvoice();

            // حفظ البيع
            saveSale(invoice);

            // تحديث المخزون
            updateStock();

            // طباعة الفاتورة
            printInvoice(invoice);

            // مسح السلة
            cart = [];
            currentDiscount = 0;
            document.getElementById('discountAmount').value = '';
            document.getElementById('discountPercent').value = '';
            document.getElementById('paidAmount').value = '';
            document.getElementById('customerName').value = '';
            document.getElementById('customerPhone').value = '';

            updateCartDisplay();
            updateSummary();

            alert('تم إتمام البيع بنجاح!');
        }

        function createInvoice() {
            const now = new Date();
            const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            const discountAmount = currentDiscount;
            const taxableAmount = subtotal - discountAmount;
            const taxAmount = taxableAmount * currentTax;
            const total = taxableAmount + taxAmount;

            return {
                id: `INV-${lastInvoiceNumber}`,
                number: lastInvoiceNumber++,
                date: now.toISOString(),
                cashier: { name: 'الكاشير', role: 'cashier', roleText: 'الكاشير', roleIcon: '💰' },
                customer: {
                    name: document.getElementById('customerName').value || 'عميل نقدي',
                    phone: document.getElementById('customerPhone').value || ''
                },
                items: [...cart],
                saleType: currentSaleType,
                paymentMethod: currentPaymentMethod,
                subtotal: subtotal,
                discount: discountAmount,
                tax: taxAmount,
                total: total,
                paidAmount: currentPaymentMethod === 'cash' ? parseFloat(document.getElementById('paidAmount').value) || total : total,
                change: currentPaymentMethod === 'cash' ? Math.max(0, (parseFloat(document.getElementById('paidAmount').value) || 0) - total) : 0
            };
        }

        function saveSale(invoice) {
            const sales = JSON.parse(localStorage.getItem('cashierSales')) || [];
            sales.push(invoice);
            localStorage.setItem('cashierSales', JSON.stringify(sales));
            localStorage.setItem('lastInvoiceNumber', lastInvoiceNumber.toString());
        }

        function updateStock() {
            const products = JSON.parse(localStorage.getItem('products')) || [];

            cart.forEach(cartItem => {
                const productIndex = products.findIndex(p => p.id === cartItem.id);
                if (productIndex !== -1) {
                    products[productIndex].quantity = Math.max(0, products[productIndex].quantity - cartItem.quantity);
                }
            });

            localStorage.setItem('products', JSON.stringify(products));
            loadProducts(); // إعادة تحميل المنتجات لتحديث العرض
        }

        function formatCurrency(amount) {
            return new Intl.NumberFormat('ar-IQ', {
                style: 'currency',
                currency: 'IQD',
                minimumFractionDigits: 0
            }).format(amount);
        }

        // وظائف إضافية
        function searchProducts() {
            const searchTerm = document.getElementById('productSearch').value.toLowerCase();
            const products = JSON.parse(localStorage.getItem('products')) || [];
            const filteredProducts = products.filter(product =>
                product.name.toLowerCase().includes(searchTerm) ||
                product.nameEn?.toLowerCase().includes(searchTerm) ||
                product.barcode?.includes(searchTerm)
            );

            const productsGrid = document.getElementById('productsGrid');
            productsGrid.innerHTML = filteredProducts.map(product => {
                const displayPrice = currentSaleType === 'wholesale' && product.wholesalePrice ?
                    product.wholesalePrice : product.price;
                const priceLabel = currentSaleType === 'wholesale' ? 'جملة' : 'مفرد';

                return `
                    <div class="product-card" onclick="addToCart('${product.id}')">
                        <div class="product-image">
                            ${product.image ? `<img src="${product.image}" alt="${product.name}">` : '<i class="fas fa-image"></i>'}
                        </div>
                        <div class="product-info">
                            <h4>${product.name}</h4>
                            <div class="product-prices">
                                <p class="product-price current-price">${formatCurrency(displayPrice)} <span class="price-type">(${priceLabel})</span></p>
                                ${currentSaleType === 'wholesale' && product.price !== product.wholesalePrice ?
                                    `<p class="product-price retail-price">مفرد: ${formatCurrency(product.price)}</p>` : ''}
                                ${currentSaleType === 'retail' && product.wholesalePrice ?
                                    `<p class="product-price wholesale-price">جملة: ${formatCurrency(product.wholesalePrice)}</p>` : ''}
                            </div>
                            <p class="product-stock">المخزون: ${product.quantity || 0}</p>
                        </div>
                    </div>
                `;
            }).join('');
        }

        function filterProducts(type) {
            document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            if (type === 'all') {
                loadProducts();
            }
        }

        function holdSale() {
            if (cart.length === 0) {
                alert('السلة فارغة');
                return;
            }

            const heldSale = {
                id: Date.now(),
                items: [...cart],
                discount: currentDiscount,
                saleType: currentSaleType,
                timestamp: new Date().toISOString()
            };

            heldSales.push(heldSale);
            localStorage.setItem('heldSales', JSON.stringify(heldSales));

            cart = [];
            currentDiscount = 0;
            updateCartDisplay();
            updateSummary();

            alert('تم تعليق البيع بنجاح');
        }

        function showHeldSales() {
            // يمكن إضافة نافذة منبثقة لعرض المبيعات المعلقة
            alert('ميزة المبيعات المعلقة قيد التطوير');
        }





        function printInvoice(invoice) {
            if (!invoice) {
                alert('لا توجد فاتورة للطباعة');
                return;
            }

            const printWindow = window.open('', '_blank');
            const invoiceHTML = generateInvoiceHTML(invoice);

            printWindow.document.write(invoiceHTML);
            printWindow.document.close();
            printWindow.print();
        }

        function generateInvoiceHTML(invoice) {
            return `
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>فاتورة رقم ${invoice.number}</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        .invoice-header { text-align: center; margin-bottom: 20px; }
                        .invoice-details { margin-bottom: 20px; }
                        .items-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                        .items-table th, .items-table td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                        .items-table th { background-color: #f2f2f2; }
                        .totals { text-align: right; }
                        .total-row { font-weight: bold; font-size: 1.2em; }
                    </style>
                </head>
                <body>
                    <div class="invoice-header">
                        <h1>فاتورة مبيعات</h1>
                        <h2>رقم الفاتورة: ${invoice.number}</h2>
                    </div>

                    <div class="invoice-details">
                        <p><strong>التاريخ:</strong> ${new Date(invoice.date).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric'
                        })}</p>
                        <p><strong>الوقت:</strong> ${new Date(invoice.date).toLocaleTimeString('en-US', {
                            hour: '2-digit',
                            minute: '2-digit',
                            hour12: true
                        })}</p>
                        <p><strong>الكاشير:</strong> ${invoice.cashier.name}</p>
                        <p><strong>العميل:</strong> ${invoice.customer.name}</p>
                        ${invoice.customer.phone ? `<p><strong>الهاتف:</strong> ${invoice.customer.phone}</p>` : ''}
                        <p><strong>نوع البيع:</strong> ${invoice.saleType === 'retail' ? 'مفرد' : 'جملة'}</p>
                        <p><strong>طريقة الدفع:</strong> ${getPaymentMethodText(invoice.paymentMethod)}</p>
                    </div>

                    <table class="items-table">
                        <thead>
                            <tr>
                                <th>المنتج</th>
                                <th>الكمية</th>
                                <th>السعر</th>
                                <th>المجموع</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${invoice.items.map(item => `
                                <tr>
                                    <td>
                                        ${item.name}
                                        <br><small style="color: #666; font-size: 0.8em;">(${item.priceType === 'wholesale' ? 'جملة' : 'مفرد'})</small>
                                    </td>
                                    <td>${item.quantity}</td>
                                    <td>${formatCurrency(item.price)}</td>
                                    <td>${formatCurrency(item.price * item.quantity)}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>

                    <div class="totals">
                        <p>المجموع الفرعي: ${formatCurrency(invoice.subtotal)}</p>
                        ${invoice.discount > 0 ? `<p>الخصم: ${formatCurrency(invoice.discount)}</p>` : ''}
                        <p>الضريبة (15%): ${formatCurrency(invoice.tax)}</p>
                        <p class="total-row">المجموع الكلي: ${formatCurrency(invoice.total)}</p>
                        ${invoice.paymentMethod === 'cash' ? `
                            <p>المبلغ المدفوع: ${formatCurrency(invoice.paidAmount)}</p>
                            <p>الباقي: ${formatCurrency(invoice.change)}</p>
                        ` : ''}
                    </div>

                    <div style="text-align: center; margin-top: 30px;">
                        <p>شكراً لتسوقكم معنا</p>
                    </div>
                </body>
                </html>
            `;
        }

        function getPaymentMethodText(method) {
            const methods = {
                'cash': 'نقدي',
                'card': 'بطاقة',
                'transfer': 'تحويل',
                'mixed': 'مختلط'
            };
            return methods[method] || method;
        }





        // === 🔔 وظيفة الإشعارات ===
        function showNotification(message, type = 'info') {
            // إزالة الإشعارات السابقة
            const existingNotifications = document.querySelectorAll('.notification');
            existingNotifications.forEach(notification => {
                notification.remove();
            });

            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.innerHTML = `
                <div class="notification-content">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'}"></i>
                    <span>${message}</span>
                </div>
                <button class="notification-close" onclick="this.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            `;

            document.body.appendChild(notification);

            // إزالة تلقائية بعد 4 ثوان
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.style.animation = 'slideInRight 0.3s ease reverse';
                    setTimeout(() => {
                        notification.remove();
                    }, 300);
                }
            }, 4000);
        }

        // === 💰 وظيفة تنسيق العملة ===
        function formatCurrency(amount) {
            return `${amount.toLocaleString()} دينار`;
        }

        // === ⚙️ إعدادات الكاشير البسيطة ===
        function openSettings() {
            alert('إعدادات الكاشير - قيد التطوير');
        }

    </script>

    <!-- CSS الاحترافي الجديد للآلة الحاسبة والإعدادات -->
    <style>










    </style>
</body>
</html>


    </script>
</body>
</html>
    <style>
        /* الآلة الحاسبة */
        .calculator-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1000;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .calculator-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
        }

        .calculator-container {
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            max-width: 400px;
            width: 90%;
            position: relative;
            z-index: 1001;
            animation: slideUp 0.3s ease;
        }

        .calculator-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e9ecef;
        }

        .calculator-header h3 {
            margin: 0;
            color: #2c3e50;
            font-size: 1.5rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .calculator-header h3 i {
            color: #667eea;
        }

        .close-btn {
            background: #e74c3c;
            color: white;
            border: none;
            width: 35px;
            height: 35px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            transform: scale(1.1);
        }

        .calculator-display {
            background: linear-gradient(145deg, #2c3e50, #34495e);
            color: white;
            padding: 1.5rem;
            border-radius: 15px;
            margin-bottom: 1.5rem;
            text-align: right;
            font-size: 2rem;
            font-weight: 600;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            box-shadow: inset 0 4px 8px rgba(0, 0, 0, 0.3);
            font-family: 'Courier New', monospace;
            letter-spacing: 2px;
        }

        .calculator-memory {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .memory-btn {
            background: linear-gradient(145deg, #95a5a6, #7f8c8d);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 600;
            transition: all 0.3s ease;
            flex: 1;
        }

        .memory-btn:hover {
            transform: translateY(-2px);
        }

        .calculator-buttons {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 0.75rem;
            margin-bottom: 1.5rem;
        }

        .calc-btn {
            border: none;
            padding: 1rem;
            border-radius: 12px;
            cursor: pointer;
            font-size: 1.2rem;
            font-weight: 700;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .number-btn {
            background: linear-gradient(145deg, #34495e, #2c3e50);
            color: white;
            box-shadow: 0 4px 8px rgba(52, 73, 94, 0.3);
        }

        .operator-btn {
            background: linear-gradient(145deg, #f39c12, #e67e22);
            color: white;
            box-shadow: 0 4px 8px rgba(243, 156, 18, 0.3);
        }

        .clear-btn {
            background: linear-gradient(145deg, #e74c3c, #c0392b);
            color: white;
            box-shadow: 0 4px 8px rgba(231, 76, 60, 0.3);
            grid-column: span 2;
        }

        .equals-btn {
            background: linear-gradient(145deg, #27ae60, #229954);
            color: white;
            box-shadow: 0 4px 8px rgba(39, 174, 96, 0.3);
            grid-row: span 2;
        }

        .zero-btn {
            grid-column: span 2;
        }

        .calc-btn:hover {
            transform: translateY(-3px);
        }

        .calculator-actions {
            display: flex;
            gap: 0.75rem;
        }

        .action-btn {
            border: none;
            padding: 0.75rem 1rem;
            border-radius: 10px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 600;
            transition: all 0.3s ease;
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .total-btn {
            background: linear-gradient(145deg, #3498db, #2980b9);
            color: white;
        }

        .discount-btn {
            background: linear-gradient(145deg, #e67e22, #d35400);
            color: white;
        }

        .action-btn:hover {
            transform: translateY(-2px);
        }

        /* الإعدادات */
        .settings-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1000;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .settings-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
        }

        .settings-container {
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            max-width: 600px;
            width: 90%;
            max-height: 85vh;
            overflow-y: auto;
            position: relative;
            z-index: 1001;
            animation: slideUp 0.3s ease;
        }

        .settings-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e9ecef;
        }

        .settings-header h3 {
            margin: 0;
            color: #2c3e50;
            font-size: 1.8rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }

        .settings-header h3 i {
            color: #667eea;
            animation: spin 2s linear infinite;
        }

        .settings-tabs {
            display: flex;
            margin-bottom: 2rem;
            background: #f8f9fa;
            border-radius: 12px;
            padding: 0.5rem;
            gap: 0.5rem;
        }

        .tab-btn {
            background: transparent;
            color: #6c757d;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .tab-btn.active {
            background: linear-gradient(145deg, #667eea, #764ba2);
            color: white;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .settings-section {
            background: linear-gradient(145deg, #f8f9fa, #ffffff);
            padding: 1.5rem;
            border-radius: 15px;
            border: 1px solid #e9ecef;
        }

        .settings-section h4 {
            color: #495057;
            margin-bottom: 1.5rem;
            font-size: 1.2rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .setting-item {
            margin-bottom: 1rem;
        }

        .setting-label {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            cursor: pointer;
            padding: 0.75rem;
            background: white;
            border-radius: 10px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
            position: relative;
        }

        .setting-label:hover {
            border-color: #667eea;
        }

        .setting-label input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: #667eea;
        }

        .setting-text {
            font-weight: 500;
        }

        .input-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #495057;
        }

        .setting-input, .setting-select {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid #ced4da;
            border-radius: 8px;
            font-size: 1rem;
            background: white;
            transition: all 0.3s ease;
        }

        .setting-input:focus, .setting-select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .settings-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 2rem;
            padding-top: 1.5rem;
            border-top: 1px solid #e9ecef;
        }

        .save-btn {
            background: linear-gradient(145deg, #28a745, #20c997);
            box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
        }

        .reset-btn {
            background: linear-gradient(145deg, #ffc107, #fd7e14);
            box-shadow: 0 4px 8px rgba(255, 193, 7, 0.3);
        }

        .save-btn:hover, .reset-btn:hover {
            transform: translateY(-2px);
        }

        /* الإشعارات */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            color: white;
            font-weight: 600;
            z-index: 10000;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            animation: slideInRight 0.3s ease;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .notification.success {
            background: #28a745;
        }

        .notification.error {
            background: #dc3545;
        }

        .notification.info {
            background: #17a2b8;
        }

        .notification.fade-out {
            animation: slideOutRight 0.3s ease;
        }

        /* الحركات */
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideOutRight {
            from {
                opacity: 1;
                transform: translateX(0);
            }
            to {
                opacity: 0;
                transform: translateX(100%);
            }
        }

        @keyframes spin {
            from {
                transform: rotate(0deg);
            }
            to {
                transform: rotate(360deg);
            }
        }

        /* استجابة للشاشات الصغيرة */
        @media (max-width: 768px) {
            .calculator-container, .settings-container {
                width: 95%;
                padding: 1.5rem;
            }

            .calculator-buttons {
                gap: 0.5rem;
            }

            .calc-btn {
                padding: 0.75rem;
                font-size: 1rem;
            }

            .settings-tabs {
                flex-direction: column;
                gap: 0.25rem;
            }

            .tab-btn {
                padding: 0.5rem 1rem;
            }
        }
    </style>
</body>
</html>
                        margin-bottom: 1.5rem;
                    ">
                        <!-- الصف الأول -->
                        <button onclick="clearCalc()" style="
                            grid-column: span 2;
                            background: linear-gradient(145deg, #e74c3c, #c0392b);
                            color: white;
                            border: none;
                            padding: 1rem;
                            border-radius: 12px;
                            cursor: pointer;
                            font-size: 1.1rem;
                            font-weight: 700;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 8px rgba(231, 76, 60, 0.3);
                        " onmouseover="this.style.transform='translateY(-3px)'; this.style.boxShadow='0 6px 12px rgba(231, 76, 60, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 8px rgba(231, 76, 60, 0.3)'">مسح</button>

                        <button onclick="calcInput('/')" style="
                            background: linear-gradient(145deg, #f39c12, #e67e22);
                            color: white;
                            border: none;
                            padding: 1rem;
                            border-radius: 12px;
                            cursor: pointer;
                            font-size: 1.3rem;
                            font-weight: 700;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 8px rgba(243, 156, 18, 0.3);
                        " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">÷</button>

                        <button onclick="calcBackspace()" style="
                            background: linear-gradient(145deg, #9b59b6, #8e44ad);
                            color: white;
                            border: none;
                            padding: 1rem;
                            border-radius: 12px;
                            cursor: pointer;
                            font-size: 1.1rem;
                            font-weight: 700;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 8px rgba(155, 89, 182, 0.3);
                        " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">⌫</button>

                        <!-- الصف الثاني -->
                        <button onclick="calcInput('7')" style="
                            background: linear-gradient(145deg, #34495e, #2c3e50);
                            color: white;
                            border: none;
                            padding: 1rem;
                            border-radius: 12px;
                            cursor: pointer;
                            font-size: 1.3rem;
                            font-weight: 700;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 8px rgba(52, 73, 94, 0.3);
                        " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">7</button>

                        <button onclick="calcInput('8')" style="
                            background: linear-gradient(145deg, #34495e, #2c3e50);
                            color: white;
                            border: none;
                            padding: 1rem;
                            border-radius: 12px;
                            cursor: pointer;
                            font-size: 1.3rem;
                            font-weight: 700;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 8px rgba(52, 73, 94, 0.3);
                        " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">8</button>

                        <button onclick="calcInput('9')" style="
                            background: linear-gradient(145deg, #34495e, #2c3e50);
                            color: white;
                            border: none;
                            padding: 1rem;
                            border-radius: 12px;
                            cursor: pointer;
                            font-size: 1.3rem;
                            font-weight: 700;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 8px rgba(52, 73, 94, 0.3);
                        " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">9</button>

                        <button onclick="calcInput('*')" style="
                            background: linear-gradient(145deg, #f39c12, #e67e22);
                            color: white;
                            border: none;
                            padding: 1rem;
                            border-radius: 12px;
                            cursor: pointer;
                            font-size: 1.3rem;
                            font-weight: 700;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 8px rgba(243, 156, 18, 0.3);
                        " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">×</button>

                        <!-- الصف الثالث -->
                        <button onclick="calcInput('4')" style="
                            background: linear-gradient(145deg, #34495e, #2c3e50);
                            color: white;
                            border: none;
                            padding: 1rem;
                            border-radius: 12px;
                            cursor: pointer;
                            font-size: 1.3rem;
                            font-weight: 700;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 8px rgba(52, 73, 94, 0.3);
                        " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">4</button>

                        <button onclick="calcInput('5')" style="
                            background: linear-gradient(145deg, #34495e, #2c3e50);
                            color: white;
                            border: none;
                            padding: 1rem;
                            border-radius: 12px;
                            cursor: pointer;
                            font-size: 1.3rem;
                            font-weight: 700;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 8px rgba(52, 73, 94, 0.3);
                        " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">5</button>

                        <button onclick="calcInput('6')" style="
                            background: linear-gradient(145deg, #34495e, #2c3e50);
                            color: white;
                            border: none;
                            padding: 1rem;
                            border-radius: 12px;
                            cursor: pointer;
                            font-size: 1.3rem;
                            font-weight: 700;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 8px rgba(52, 73, 94, 0.3);
                        " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">6</button>

                        <button onclick="calcInput('-')" style="
                            background: linear-gradient(145deg, #f39c12, #e67e22);
                            color: white;
                            border: none;
                            padding: 1rem;
                            border-radius: 12px;
                            cursor: pointer;
                            font-size: 1.3rem;
                            font-weight: 700;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 8px rgba(243, 156, 18, 0.3);
                        " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">-</button>

                        <!-- الصف الرابع -->
                        <button onclick="calcInput('1')" style="
                            background: linear-gradient(145deg, #34495e, #2c3e50);
                            color: white;
                            border: none;
                            padding: 1rem;
                            border-radius: 12px;
                            cursor: pointer;
                            font-size: 1.3rem;
                            font-weight: 700;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 8px rgba(52, 73, 94, 0.3);
                        " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">1</button>

                        <button onclick="calcInput('2')" style="
                            background: linear-gradient(145deg, #34495e, #2c3e50);
                            color: white;
                            border: none;
                            padding: 1rem;
                            border-radius: 12px;
                            cursor: pointer;
                            font-size: 1.3rem;
                            font-weight: 700;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 8px rgba(52, 73, 94, 0.3);
                        " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">2</button>

                        <button onclick="calcInput('3')" style="
                            background: linear-gradient(145deg, #34495e, #2c3e50);
                            color: white;
                            border: none;
                            padding: 1rem;
                            border-radius: 12px;
                            cursor: pointer;
                            font-size: 1.3rem;
                            font-weight: 700;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 8px rgba(52, 73, 94, 0.3);
                        " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">3</button>

                        <button onclick="calcInput('+')" style="
                            background: linear-gradient(145deg, #f39c12, #e67e22);
                            color: white;
                            border: none;
                            padding: 1rem;
                            border-radius: 12px;
                            cursor: pointer;
                            font-size: 1.3rem;
                            font-weight: 700;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 8px rgba(243, 156, 18, 0.3);
                        " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">+</button>

                        <!-- الصف الخامس -->
                        <button onclick="calcInput('0')" style="
                            grid-column: span 2;
                            background: linear-gradient(145deg, #34495e, #2c3e50);
                            color: white;
                            border: none;
                            padding: 1rem;
                            border-radius: 12px;
                            cursor: pointer;
                            font-size: 1.3rem;
                            font-weight: 700;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 8px rgba(52, 73, 94, 0.3);
                        " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">0</button>

                        <button onclick="calcInput('.')" style="
                            background: linear-gradient(145deg, #34495e, #2c3e50);
                            color: white;
                            border: none;
                            padding: 1rem;
                            border-radius: 12px;
                            cursor: pointer;
                            font-size: 1.3rem;
                            font-weight: 700;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 8px rgba(52, 73, 94, 0.3);
                        " onmouseover="this.style.transform='translateY(-3px)'" onmouseout="this.style.transform='translateY(0)'">.</button>

                        <button onclick="calcEquals()" style="
                            background: linear-gradient(145deg, #27ae60, #229954);
                            color: white;
                            border: none;
                            padding: 1rem;
                            border-radius: 12px;
                            cursor: pointer;
                            font-size: 1.3rem;
                            font-weight: 700;
                            transition: all 0.3s ease;
                            box-shadow: 0 4px 8px rgba(39, 174, 96, 0.3);
                        " onmouseover="this.style.transform='translateY(-3px)'; this.style.boxShadow='0 6px 12px rgba(39, 174, 96, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 8px rgba(39, 174, 96, 0.3)'">＝</button>
                    </div>

                    <!-- أزرار الكاشير -->
                    <div style="
                        display: flex;
                        gap: 0.75rem;
                        margin-bottom: 1rem;
                    ">
                        <button onclick="copyToTotal()" style="
                            background: linear-gradient(145deg, #3498db, #2980b9);
                            color: white;
                            border: none;
                            padding: 0.75rem 1rem;
                            border-radius: 10px;
                            cursor: pointer;
                            font-size: 0.9rem;
                            font-weight: 600;
                            transition: all 0.3s ease;
                            flex: 1;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            gap: 0.5rem;
                        " onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                            <i class="fas fa-arrow-right"></i>
                            نسخ للمجموع
                        </button>

                        <button onclick="copyToDiscount()" style="
                            background: linear-gradient(145deg, #e67e22, #d35400);
                            color: white;
                            border: none;
                            padding: 0.75rem 1rem;
                            border-radius: 10px;
                            cursor: pointer;
                            font-size: 0.9rem;
                            font-weight: 600;
                            transition: all 0.3s ease;
                            flex: 1;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            gap: 0.5rem;
                        " onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                            <i class="fas fa-percent"></i>
                            نسخ للخصم
                        </button>
                    </div>
                </div>

                <style>
                    @keyframes fadeIn {
                        from { opacity: 0; }
                        to { opacity: 1; }
                    }

                    @keyframes slideUp {
                        from {
                            opacity: 0;
                            transform: translateY(30px);
                        }
                        to {
                            opacity: 1;
                            transform: translateY(0);
                        }
                    }
                </style>
            `;

            document.body.appendChild(modal);

            // متغيرات الآلة الحاسبة
            window.calcCurrentInput = '0';
            window.calcOperator = null;
            window.calcPreviousInput = null;
            window.calcWaitingForOperand = false;
            window.calcMemory = 0;

            updateCalcDisplay();
        }

        function createEnhancedSettings() {
            console.log('⚙️ فتح الإعدادات المحسنة');

            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: linear-gradient(135deg, rgba(0,0,0,0.7), rgba(0,0,0,0.5));
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
                backdrop-filter: blur(5px);
                animation: fadeIn 0.3s ease;
            `;

            modal.innerHTML = `
                <div style="
                    background: linear-gradient(145deg, #ffffff, #f8f9fa);
                    padding: 2.5rem;
                    border-radius: 20px;
                    box-shadow: 0 25px 50px rgba(0,0,0,0.3);
                    max-width: 600px;
                    width: 90%;
                    max-height: 85vh;
                    overflow-y: auto;
                    border: 1px solid rgba(255,255,255,0.2);
                    animation: slideUp 0.3s ease;
                ">
                    <!-- رأس النافذة -->
                    <div style="
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 2rem;
                        padding-bottom: 1rem;
                        border-bottom: 2px solid #e9ecef;
                    ">
                        <h3 style="
                            margin: 0;
                            color: #2c3e50;
                            font-size: 1.8rem;
                            font-weight: 700;
                            display: flex;
                            align-items: center;
                            gap: 0.75rem;
                        ">
                            <i class="fas fa-cog" style="color: #667eea; animation: spin 2s linear infinite;"></i>
                            إعدادات الكاشير
                        </h3>
                        <button onclick="closeSettings()" style="
                            background: #e74c3c;
                            color: white;
                            border: none;
                            width: 40px;
                            height: 40px;
                            border-radius: 50%;
                            cursor: pointer;
                            font-size: 1.1rem;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            transition: all 0.3s ease;
                        " onmouseover="this.style.transform='scale(1.1)'" onmouseout="this.style.transform='scale(1)'">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <!-- التبويبات -->
                    <div style="
                        display: flex;
                        margin-bottom: 2rem;
                        background: #f8f9fa;
                        border-radius: 12px;
                        padding: 0.5rem;
                        gap: 0.5rem;
                    ">
                        <button onclick="switchTab('general')" id="tab-general" style="
                            background: linear-gradient(145deg, #667eea, #764ba2);
                            color: white;
                            border: none;
                            padding: 0.75rem 1.5rem;
                            border-radius: 8px;
                            cursor: pointer;
                            font-weight: 600;
                            transition: all 0.3s ease;
                            flex: 1;
                        ">عام</button>
                        <button onclick="switchTab('invoice')" id="tab-invoice" style="
                            background: transparent;
                            color: #6c757d;
                            border: none;
                            padding: 0.75rem 1.5rem;
                            border-radius: 8px;
                            cursor: pointer;
                            font-weight: 600;
                            transition: all 0.3s ease;
                            flex: 1;
                        ">الفواتير</button>
                        <button onclick="switchTab('display')" id="tab-display" style="
                            background: transparent;
                            color: #6c757d;
                            border: none;
                            padding: 0.75rem 1.5rem;
                            border-radius: 8px;
                            cursor: pointer;
                            font-weight: 600;
                            transition: all 0.3s ease;
                            flex: 1;
                        ">العرض</button>
                    </div>

                    <!-- محتوى التبويبات -->
                    <div id="settings-content">
                        <!-- تبويب الإعدادات العامة -->
                        <div id="general-tab" style="display: block;">
                            <div style="
                                background: linear-gradient(145deg, #f8f9fa, #ffffff);
                                padding: 1.5rem;
                                border-radius: 15px;
                                margin-bottom: 1.5rem;
                                border: 1px solid #e9ecef;
                            ">
                                <h4 style="
                                    color: #495057;
                                    margin-bottom: 1.5rem;
                                    font-size: 1.2rem;
                                    font-weight: 600;
                                    display: flex;
                                    align-items: center;
                                    gap: 0.5rem;
                                ">
                                    <i class="fas fa-cogs" style="color: #667eea;"></i>
                                    الإعدادات العامة
                                </h4>

                                <div style="display: grid; gap: 1rem;">
                                    <label style="
                                        display: flex;
                                        align-items: center;
                                        gap: 0.75rem;
                                        cursor: pointer;
                                        padding: 0.75rem;
                                        background: white;
                                        border-radius: 10px;
                                        border: 1px solid #e9ecef;
                                        transition: all 0.3s ease;
                                    " onmouseover="this.style.borderColor='#667eea'" onmouseout="this.style.borderColor='#e9ecef'">
                                        <input type="checkbox" id="autoSave" checked style="
                                            width: 18px;
                                            height: 18px;
                                            accent-color: #667eea;
                                        ">
                                        <span style="font-weight: 500;">حفظ تلقائي للمبيعات</span>
                                    </label>

                                    <label style="
                                        display: flex;
                                        align-items: center;
                                        gap: 0.75rem;
                                        cursor: pointer;
                                        padding: 0.75rem;
                                        background: white;
                                        border-radius: 10px;
                                        border: 1px solid #e9ecef;
                                        transition: all 0.3s ease;
                                    " onmouseover="this.style.borderColor='#667eea'" onmouseout="this.style.borderColor='#e9ecef'">
                                        <input type="checkbox" id="showCustomerInfo" style="
                                            width: 18px;
                                            height: 18px;
                                            accent-color: #667eea;
                                        ">
                                        <span style="font-weight: 500;">إظهار معلومات العميل</span>
                                    </label>

                                    <label style="
                                        display: flex;
                                        align-items: center;
                                        gap: 0.75rem;
                                        cursor: pointer;
                                        padding: 0.75rem;
                                        background: white;
                                        border-radius: 10px;
                                        border: 1px solid #e9ecef;
                                        transition: all 0.3s ease;
                                    " onmouseover="this.style.borderColor='#667eea'" onmouseout="this.style.borderColor='#e9ecef'">
                                        <input type="checkbox" id="enableSounds" checked style="
                                            width: 18px;
                                            height: 18px;
                                            accent-color: #667eea;
                                        ">
                                        <span style="font-weight: 500;">تفعيل الأصوات</span>
                                    </label>

                                    <div style="
                                        padding: 0.75rem;
                                        background: white;
                                        border-radius: 10px;
                                        border: 1px solid #e9ecef;
                                    ">
                                        <label style="
                                            display: block;
                                            margin-bottom: 0.5rem;
                                            font-weight: 600;
                                            color: #495057;
                                        ">معدل الضريبة (%)</label>
                                        <input type="number" id="taxRate" value="15" min="0" max="100" style="
                                            width: 100%;
                                            padding: 0.75rem;
                                            border: 1px solid #ced4da;
                                            border-radius: 8px;
                                            font-size: 1rem;
                                            transition: all 0.3s ease;
                                        " onfocus="this.style.borderColor='#667eea'" onblur="this.style.borderColor='#ced4da'">
                                    </div>

                                    <div style="
                                        padding: 0.75rem;
                                        background: white;
                                        border-radius: 10px;
                                        border: 1px solid #e9ecef;
                                    ">
                                        <label style="
                                            display: block;
                                            margin-bottom: 0.5rem;
                                            font-weight: 600;
                                            color: #495057;
                                        ">نوع البيع الافتراضي</label>
                                        <select id="defaultSaleType" style="
                                            width: 100%;
                                            padding: 0.75rem;
                                            border: 1px solid #ced4da;
                                            border-radius: 8px;
                                            font-size: 1rem;
                                            background: white;
                                            transition: all 0.3s ease;
                                        " onfocus="this.style.borderColor='#667eea'" onblur="this.style.borderColor='#ced4da'">
                                            <option value="retail">مفرد</option>
                                            <option value="wholesale">جملة</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تبويب إعدادات الفواتير -->
                        <div id="invoice-tab" style="display: none;">
                            <div style="
                                background: linear-gradient(145deg, #f8f9fa, #ffffff);
                                padding: 1.5rem;
                                border-radius: 15px;
                                margin-bottom: 1.5rem;
                                border: 1px solid #e9ecef;
                            ">
                                <h4 style="
                                    color: #495057;
                                    margin-bottom: 1.5rem;
                                    font-size: 1.2rem;
                                    font-weight: 600;
                                    display: flex;
                                    align-items: center;
                                    gap: 0.5rem;
                                ">
                                    <i class="fas fa-file-invoice" style="color: #28a745;"></i>
                                    إعدادات الفواتير
                                </h4>

                                <div style="display: grid; gap: 1rem;">
                                    <label style="
                                        display: flex;
                                        align-items: center;
                                        gap: 0.75rem;
                                        cursor: pointer;
                                        padding: 0.75rem;
                                        background: white;
                                        border-radius: 10px;
                                        border: 1px solid #e9ecef;
                                        transition: all 0.3s ease;
                                    " onmouseover="this.style.borderColor='#28a745'" onmouseout="this.style.borderColor='#e9ecef'">
                                        <input type="checkbox" id="printAfterSale" checked style="
                                            width: 18px;
                                            height: 18px;
                                            accent-color: #28a745;
                                        ">
                                        <span style="font-weight: 500;">طباعة تلقائية بعد البيع</span>
                                    </label>

                                    <div style="
                                        padding: 0.75rem;
                                        background: white;
                                        border-radius: 10px;
                                        border: 1px solid #e9ecef;
                                    ">
                                        <label style="
                                            display: block;
                                            margin-bottom: 0.5rem;
                                            font-weight: 600;
                                            color: #495057;
                                        ">قالب الفاتورة</label>
                                        <select id="invoiceTemplate" style="
                                            width: 100%;
                                            padding: 0.75rem;
                                            border: 1px solid #ced4da;
                                            border-radius: 8px;
                                            font-size: 1rem;
                                            background: white;
                                            transition: all 0.3s ease;
                                        " onfocus="this.style.borderColor='#28a745'" onblur="this.style.borderColor='#ced4da'">
                                            <option value="standard">قياسي</option>
                                            <option value="detailed">مفصل</option>
                                            <option value="compact">مضغوط</option>
                                        </select>
                                    </div>

                                    <div style="
                                        padding: 0.75rem;
                                        background: white;
                                        border-radius: 10px;
                                        border: 1px solid #e9ecef;
                                    ">
                                        <label style="
                                            display: block;
                                            margin-bottom: 0.5rem;
                                            font-weight: 600;
                                            color: #495057;
                                        ">العملة</label>
                                        <select id="currency" style="
                                            width: 100%;
                                            padding: 0.75rem;
                                            border: 1px solid #ced4da;
                                            border-radius: 8px;
                                            font-size: 1rem;
                                            background: white;
                                            transition: all 0.3s ease;
                                        " onfocus="this.style.borderColor='#28a745'" onblur="this.style.borderColor='#ced4da'">
                                            <option value="IQD">دينار عراقي (IQD)</option>
                                            <option value="USD">دولار أمريكي (USD)</option>
                                            <option value="EUR">يورو (EUR)</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تبويب إعدادات العرض -->
                        <div id="display-tab" style="display: none;">
                            <div style="
                                background: linear-gradient(145deg, #f8f9fa, #ffffff);
                                padding: 1.5rem;
                                border-radius: 15px;
                                margin-bottom: 1.5rem;
                                border: 1px solid #e9ecef;
                            ">
                                <h4 style="
                                    color: #495057;
                                    margin-bottom: 1.5rem;
                                    font-size: 1.2rem;
                                    font-weight: 600;
                                    display: flex;
                                    align-items: center;
                                    gap: 0.5rem;
                                ">
                                    <i class="fas fa-desktop" style="color: #6f42c1;"></i>
                                    إعدادات العرض
                                </h4>

                                <div style="display: grid; gap: 1rem;">
                                    <div style="
                                        padding: 0.75rem;
                                        background: white;
                                        border-radius: 10px;
                                        border: 1px solid #e9ecef;
                                    ">
                                        <label style="
                                            display: block;
                                            margin-bottom: 0.5rem;
                                            font-weight: 600;
                                            color: #495057;
                                        ">المظهر</label>
                                        <select id="theme" style="
                                            width: 100%;
                                            padding: 0.75rem;
                                            border: 1px solid #ced4da;
                                            border-radius: 8px;
                                            font-size: 1rem;
                                            background: white;
                                            transition: all 0.3s ease;
                                        " onfocus="this.style.borderColor='#6f42c1'" onblur="this.style.borderColor='#ced4da'">
                                            <option value="light">فاتح</option>
                                            <option value="dark">داكن</option>
                                            <option value="auto">تلقائي</option>
                                        </select>
                                    </div>

                                    <div style="
                                        padding: 0.75rem;
                                        background: white;
                                        border-radius: 10px;
                                        border: 1px solid #e9ecef;
                                    ">
                                        <label style="
                                            display: block;
                                            margin-bottom: 0.5rem;
                                            font-weight: 600;
                                            color: #495057;
                                        ">اللغة</label>
                                        <select id="language" style="
                                            width: 100%;
                                            padding: 0.75rem;
                                            border: 1px solid #ced4da;
                                            border-radius: 8px;
                                            font-size: 1rem;
                                            background: white;
                                            transition: all 0.3s ease;
                                        " onfocus="this.style.borderColor='#6f42c1'" onblur="this.style.borderColor='#ced4da'">
                                            <option value="ar">العربية</option>
                                            <option value="en">English</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الحفظ والإغلاق -->
                    <div style="
                        display: flex;
                        gap: 1rem;
                        justify-content: center;
                        margin-top: 2rem;
                        padding-top: 1.5rem;
                        border-top: 1px solid #e9ecef;
                    ">
                        <button onclick="saveSettings()" style="
                            background: linear-gradient(145deg, #28a745, #20c997);
                            color: white;
                            border: none;
                            padding: 1rem 2rem;
                            border-radius: 12px;
                            cursor: pointer;
                            font-size: 1rem;
                            font-weight: 600;
                            transition: all 0.3s ease;
                            display: flex;
                            align-items: center;
                            gap: 0.5rem;
                            box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
                        " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 12px rgba(40, 167, 69, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 8px rgba(40, 167, 69, 0.3)'">
                            <i class="fas fa-save"></i>
                            حفظ الإعدادات
                        </button>

                        <button onclick="resetSettings()" style="
                            background: linear-gradient(145deg, #ffc107, #fd7e14);
                            color: white;
                            border: none;
                            padding: 1rem 2rem;
                            border-radius: 12px;
                            cursor: pointer;
                            font-size: 1rem;
                            font-weight: 600;
                            transition: all 0.3s ease;
                            display: flex;
                            align-items: center;
                            gap: 0.5rem;
                            box-shadow: 0 4px 8px rgba(255, 193, 7, 0.3);
                        " onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                            <i class="fas fa-undo"></i>
                            إعادة تعيين
                        </button>
                    </div>
                </div>

                <style>
                    @keyframes fadeIn {
                        from { opacity: 0; }
                        to { opacity: 1; }
                    }

                    @keyframes slideUp {
                        from {
                            opacity: 0;
                            transform: translateY(30px);
                        }
                        to {
                            opacity: 1;
                            transform: translateY(0);
                        }
                    }

                    @keyframes spin {
                        from { transform: rotate(0deg); }
                        to { transform: rotate(360deg); }
                    }
                </style>
            `;

            document.body.appendChild(modal);

            // تحميل الإعدادات المحفوظة
            loadSavedSettings();
        }

        // وظائف الآلة الحاسبة الاحتياطية
        function calcInput(value) {
            // التحقق من نوع الإدخال
            if (['+', '-', '*', '/'].includes(value)) {
                // عملية حسابية
                if (window.calcOperator && !window.calcWaitingForOperand) {
                    calcEquals();
                }
                window.calcPreviousInput = window.calcCurrentInput;
                window.calcOperator = value;
                window.calcWaitingForOperand = true;
            } else {
                // رقم أو نقطة عشرية
                if (window.calcWaitingForOperand) {
                    window.calcCurrentInput = value;
                    window.calcWaitingForOperand = false;
                } else {
                    if (value === '.' && window.calcCurrentInput.includes('.')) {
                        return; // منع إدخال أكثر من نقطة عشرية
                    }
                    window.calcCurrentInput = window.calcCurrentInput === '0' ? value : window.calcCurrentInput + value;
                }
            }
            updateCalcDisplay();
        }

        function calcEquals() {
            if (window.calcOperator && window.calcPreviousInput !== null) {
                const prev = parseFloat(window.calcPreviousInput);
                const current = parseFloat(window.calcCurrentInput);
                let result = 0;

                switch (window.calcOperator) {
                    case '+': result = prev + current; break;
                    case '-': result = prev - current; break;
                    case '*': result = prev * current; break;
                    case '/': result = current !== 0 ? prev / current : 0; break;
                }

                window.calcCurrentInput = result.toString();
                window.calcOperator = null;
                window.calcPreviousInput = null;
                window.calcWaitingForOperand = true;
                updateCalcDisplay();
            }
        }

        function clearCalc() {
            window.calcCurrentInput = '0';
            window.calcOperator = null;
            window.calcPreviousInput = null;
            window.calcWaitingForOperand = false;
            updateCalcDisplay();
        }

        function calcBackspace() {
            if (window.calcCurrentInput.length > 1) {
                window.calcCurrentInput = window.calcCurrentInput.slice(0, -1);
            } else {
                window.calcCurrentInput = '0';
            }
            updateCalcDisplay();
        }

        function updateCalcDisplay() {
            const display = document.getElementById('calcDisplay');
            if (display) {
                display.textContent = window.calcCurrentInput;
            }
        }

        function closeCalc() {
            const modal = document.querySelector('.modal-overlay');
            if (modal) {
                modal.remove();
            }
        }

        function saveSettings() {
            const settings = {
                autoSave: document.getElementById('autoSave').checked,
                printAfterSale: document.getElementById('printAfterSale').checked,
                taxRate: parseFloat(document.getElementById('taxRate').value) || 15,
                currency: document.getElementById('currency').value
            };

            localStorage.setItem('cashierSettings', JSON.stringify(settings));
            alert('تم حفظ الإعدادات بنجاح');
            console.log('💾 تم حفظ الإعدادات:', settings);
            closeSettings();
        }

        function closeSettings() {
            const modal = document.querySelector('.modal-overlay');
            if (modal) {
                modal.remove();
            }
        }

        // وظائف مساعدة للإعدادات
        function switchTab(tabName) {
            // إخفاء جميع التبويبات
            document.getElementById('general-tab').style.display = 'none';
            document.getElementById('invoice-tab').style.display = 'none';
            document.getElementById('display-tab').style.display = 'none';

            // إزالة التفعيل من جميع الأزرار
            document.getElementById('tab-general').style.background = 'transparent';
            document.getElementById('tab-general').style.color = '#6c757d';
            document.getElementById('tab-invoice').style.background = 'transparent';
            document.getElementById('tab-invoice').style.color = '#6c757d';
            document.getElementById('tab-display').style.background = 'transparent';
            document.getElementById('tab-display').style.color = '#6c757d';

            // إظهار التبويب المحدد وتفعيل الزر
            if (tabName === 'general') {
                document.getElementById('general-tab').style.display = 'block';
                document.getElementById('tab-general').style.background = 'linear-gradient(145deg, #667eea, #764ba2)';
                document.getElementById('tab-general').style.color = 'white';
            } else if (tabName === 'invoice') {
                document.getElementById('invoice-tab').style.display = 'block';
                document.getElementById('tab-invoice').style.background = 'linear-gradient(145deg, #28a745, #20c997)';
                document.getElementById('tab-invoice').style.color = 'white';
            } else if (tabName === 'display') {
                document.getElementById('display-tab').style.display = 'block';
                document.getElementById('tab-display').style.background = 'linear-gradient(145deg, #6f42c1, #e83e8c)';
                document.getElementById('tab-display').style.color = 'white';
            }
        }

        function loadSavedSettings() {
            const settings = JSON.parse(localStorage.getItem('cashierSettings')) || {};

            // تحميل الإعدادات العامة
            if (settings.autoSave !== undefined) {
                document.getElementById('autoSave').checked = settings.autoSave;
            }
            if (settings.showCustomerInfo !== undefined) {
                document.getElementById('showCustomerInfo').checked = settings.showCustomerInfo;
            }
            if (settings.enableSounds !== undefined) {
                document.getElementById('enableSounds').checked = settings.enableSounds;
            }
            if (settings.taxRate !== undefined) {
                document.getElementById('taxRate').value = settings.taxRate;
            }
            if (settings.defaultSaleType !== undefined) {
                document.getElementById('defaultSaleType').value = settings.defaultSaleType;
            }

            // تحميل إعدادات الفواتير
            if (settings.printAfterSale !== undefined) {
                document.getElementById('printAfterSale').checked = settings.printAfterSale;
            }
            if (settings.invoiceTemplate !== undefined) {
                document.getElementById('invoiceTemplate').value = settings.invoiceTemplate;
            }
            if (settings.currency !== undefined) {
                document.getElementById('currency').value = settings.currency;
            }

            // تحميل إعدادات العرض
            if (settings.theme !== undefined) {
                document.getElementById('theme').value = settings.theme;
            }
            if (settings.language !== undefined) {
                document.getElementById('language').value = settings.language;
            }
        }

        function resetSettings() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟')) {
                // إعادة تعيين القيم الافتراضية
                document.getElementById('autoSave').checked = true;
                document.getElementById('showCustomerInfo').checked = false;
                document.getElementById('enableSounds').checked = true;
                document.getElementById('taxRate').value = 15;
                document.getElementById('defaultSaleType').value = 'retail';
                document.getElementById('printAfterSale').checked = true;
                document.getElementById('invoiceTemplate').value = 'standard';
                document.getElementById('currency').value = 'IQD';
                document.getElementById('theme').value = 'light';
                document.getElementById('language').value = 'ar';

                alert('تم إعادة تعيين الإعدادات بنجاح');
            }
        }

        // وظائف مساعدة للآلة الحاسبة
        function memoryOperation(operation) {
            const currentValue = parseFloat(window.calcCurrentInput) || 0;

            switch (operation) {
                case 'clear':
                    window.calcMemory = 0;
                    showNotification('تم مسح الذاكرة', 'info');
                    break;
                case 'recall':
                    window.calcCurrentInput = window.calcMemory.toString();
                    updateCalcDisplay();
                    break;
                case 'add':
                    window.calcMemory += currentValue;
                    showNotification(`تم إضافة ${currentValue} إلى الذاكرة`, 'success');
                    break;
                case 'subtract':
                    window.calcMemory -= currentValue;
                    showNotification(`تم طرح ${currentValue} من الذاكرة`, 'success');
                    break;
            }
        }

        function copyToTotal() {
            const value = parseFloat(window.calcCurrentInput);
            if (!isNaN(value) && value > 0) {
                const totalElement = document.getElementById('total');
                if (totalElement) {
                    totalElement.textContent = `${value.toFixed(0)} دينار`;
                    showNotification(`تم نسخ ${value.toFixed(0)} دينار إلى المجموع`, 'success');
                } else {
                    window.calculatedTotal = value;
                    showNotification(`تم حفظ القيمة: ${value.toFixed(0)} دينار`, 'success');
                }
                closeCalc();
            } else {
                showNotification('يرجى إدخال قيمة صحيحة أكبر من صفر', 'error');
            }
        }

        function copyToDiscount() {
            const value = parseFloat(window.calcCurrentInput);
            if (!isNaN(value) && value >= 0) {
                const discountAmountInput = document.getElementById('discountAmount');
                const discountPercentInput = document.getElementById('discountPercent');

                if (value <= 100 && discountPercentInput) {
                    discountPercentInput.value = value;
                    applyDiscountPercent();
                    showNotification(`تم تطبيق خصم: ${value}%`, 'success');
                } else if (discountAmountInput) {
                    discountAmountInput.value = value;
                    applyDiscount();
                    showNotification(`تم تطبيق خصم: ${value} دينار`, 'success');
                } else {
                    window.calculatedDiscount = value;
                    showNotification(`تم حفظ خصم: ${value}`, 'success');
                }
                closeCalc();
            } else {
                showNotification('قيمة الخصم يجب أن تكون أكبر من أو تساوي صفر', 'error');
            }
        }

        // وظيفة إظهار الإشعارات
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#17a2b8'};
                color: white;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                box-shadow: 0 4px 8px rgba(0,0,0,0.2);
                z-index: 10000;
                font-weight: 600;
                animation: slideInRight 0.3s ease;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }
    </script>

    <style>
        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideOutRight {
            from {
                opacity: 1;
                transform: translateX(0);
            }
            to {
                opacity: 0;
                transform: translateX(100%);
            }
        }
    </style>
</body>
</html>
