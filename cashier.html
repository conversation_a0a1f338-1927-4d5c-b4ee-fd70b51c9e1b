<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام الكاشير - البيع المباشر</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="cashier-styles.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/quagga/0.12.1/quagga.min.js"></script>
</head>
<body>
    <!-- Header -->
    <header class="cashier-header">
        <div class="header-content">
            <div class="logo-section">
                <i class="fas fa-cash-register"></i>
                <h1>نظام الكاشير</h1>
            </div>
            <div class="header-info">
                <div class="cashier-info">
                    <i class="fas fa-user"></i>
                    <span id="cashierName">💰 الكاشير: نظام المبيعات</span>
                </div>
                <div class="date-time">
                    <i class="fas fa-clock"></i>
                    <span id="currentDateTime"></span>
                </div>
                <div class="shift-info">
                    <i class="fas fa-calendar-day"></i>
                    <span>الوردية: صباحية</span>
                </div>
            </div>
            <div class="header-actions">
                <button class="btn btn-secondary" onclick="showReportsModal()">
                    <i class="fas fa-chart-bar"></i>
                    التقارير
                </button>
                <button class="btn btn-warning" onclick="showSettingsModal()">
                    <i class="fas fa-cog"></i>
                    الإعدادات
                </button>
                <button class="btn btn-primary" onclick="goToMainSite()">
                    <i class="fas fa-home"></i>
                    الموقع الرئيسي
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="cashier-container">
        <!-- Left Panel - Products -->
        <div class="products-panel">
            <div class="panel-header">
                <h3><i class="fas fa-boxes"></i> المنتجات والباركود</h3>
            </div>

            <!-- Modern Search & Filter Section -->
            <div class="modern-search-section">
                <div class="search-header">
                    <h3><i class="fas fa-search"></i> البحث والفلترة</h3>
                    <div class="search-stats" id="searchStats">
                        <span class="products-count">0 منتج</span>
                    </div>
                </div>

                <div class="search-controls">
                    <!-- Main Search Bar -->
                    <div class="main-search-bar">
                        <div class="search-input-wrapper">
                            <i class="fas fa-search search-icon"></i>
                            <input type="text"
                                   id="productSearch"
                                   placeholder="ابحث عن المنتجات بالاسم أو الباركود..."
                                   onkeyup="smartSearch()"
                                   autocomplete="off">
                            <button class="clear-search-btn" onclick="clearSearch()" style="display: none;">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Quick Filters -->
                    <div class="quick-filters">
                        <div class="filter-tabs">
                            <button class="filter-tab active" onclick="setQuickFilter('all')" data-filter="all">
                                <i class="fas fa-th-large"></i>
                                <span>الكل</span>
                            </button>
                            <button class="filter-tab" onclick="setQuickFilter('available')" data-filter="available">
                                <i class="fas fa-check-circle"></i>
                                <span>متوفر</span>
                            </button>
                            <button class="filter-tab" onclick="setQuickFilter('low')" data-filter="low">
                                <i class="fas fa-exclamation-triangle"></i>
                                <span>قليل</span>
                            </button>
                            <button class="filter-tab" onclick="setQuickFilter('featured')" data-filter="featured">
                                <i class="fas fa-star"></i>
                                <span>مميز</span>
                            </button>
                        </div>

                        <div class="advanced-filters">
                            <select id="categoryFilter" onchange="applyFilters()">
                                <option value="">جميع الفئات</option>
                                <option value="فواكه">🍎 فواكه</option>
                                <option value="خضروات">🥬 خضروات</option>
                                <option value="لحوم">🥩 لحوم</option>
                                <option value="مخبوزات">🍞 مخبوزات</option>
                                <option value="مشروبات">🥤 مشروبات</option>
                                <option value="منتجات ألبان">🥛 منتجات ألبان</option>
                            </select>

                            <button class="refresh-products-btn" onclick="refreshAllProducts()" title="تحديث المنتجات">
                                <i class="fas fa-sync-alt"></i>
                                <span>تحديث</span>
                            </button>
                        </div>
                    </div>

                    <!-- Barcode Scanner -->
                    <div class="barcode-scanner-section">
                        <div class="barcode-input-group">
                            <div class="barcode-icon">
                                <i class="fas fa-barcode"></i>
                            </div>
                            <input type="text"
                                   id="barcodeInput"
                                   placeholder="امسح الباركود أو اكتبه هنا..."
                                   onkeydown="handleBarcodeInput(event)"
                                   autocomplete="off">
                            <button class="camera-scan-btn" onclick="startBarcodeScanner()" title="مسح بالكاميرا">
                                <i class="fas fa-camera"></i>
                            </button>
                        </div>
                        <div class="barcode-status" id="barcodeStatus">
                            <i class="fas fa-circle text-success"></i>
                            <span>جاهز لمسح الباركود</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Categories Tabs -->
            <div class="categories-tabs" id="categoriesTabs">
                <button class="category-tab active" onclick="filterByCategory('all')">الكل</button>
                <button class="category-tab" onclick="filterByCategory('فواكه')">فواكه</button>
                <button class="category-tab" onclick="filterByCategory('خضروات')">خضروات</button>
                <button class="category-tab" onclick="filterByCategory('لحوم')">لحوم</button>
                <button class="category-tab" onclick="filterByCategory('مخبوزات')">مخبوزات</button>
                <button class="category-tab" onclick="filterByCategory('مشروبات')">مشروبات</button>
            </div>

            <!-- Modern Products Display -->
            <div class="products-display-container">
                <!-- View Mode Toggle -->
                <div class="view-controls">
                    <div class="view-mode-toggle">
                        <button class="view-mode-btn active" onclick="setViewMode('grid')" data-mode="grid">
                            <i class="fas fa-th-large"></i>
                            <span>شبكة</span>
                        </button>
                        <button class="view-mode-btn" onclick="setViewMode('list')" data-mode="list">
                            <i class="fas fa-list"></i>
                            <span>قائمة</span>
                        </button>
                    </div>

                    <div class="sort-controls">
                        <select id="sortBy" onchange="applySorting()">
                            <option value="name">ترتيب بالاسم</option>
                            <option value="price-low">السعر: من الأقل للأعلى</option>
                            <option value="price-high">السعر: من الأعلى للأقل</option>
                            <option value="stock">المخزون</option>
                            <option value="category">الفئة</option>
                        </select>
                    </div>
                </div>

                <!-- Products Grid/List -->
                <div class="products-container" id="productsContainer">
                    <div class="products-grid" id="productsGrid">
                        <!-- Products will be loaded here -->
                    </div>
                </div>

                <!-- Loading Indicator -->
                <div class="loading-indicator" id="loadingIndicator" style="display: none;">
                    <div class="loading-spinner">
                        <i class="fas fa-spinner fa-spin"></i>
                    </div>
                    <p>جاري تحميل المنتجات...</p>
                </div>

                <!-- No Products Message -->
                <div class="no-products-found" id="noProductsFound" style="display: none;">
                    <div class="no-products-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h3>لم يتم العثور على منتجات</h3>
                    <p>جرب تغيير معايير البحث أو الفلترة</p>
                    <button class="reset-filters-btn" onclick="resetAllFilters()">
                        <i class="fas fa-undo"></i>
                        إعادة تعيين الفلاتر
                    </button>
                </div>
            </div>
        </div>

        <!-- Center Panel - Cart -->
        <div class="cart-panel">
            <div class="panel-header">
                <h3><i class="fas fa-shopping-cart"></i> سلة المشتريات</h3>
                <div class="cart-actions">
                    <button class="btn btn-sm btn-warning" onclick="holdSale()">
                        <i class="fas fa-pause"></i>
                        تعليق
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="clearCart()">
                        <i class="fas fa-trash"></i>
                        مسح
                    </button>
                </div>
            </div>
            
            <div class="cart-items" id="cartItems">
                <div class="empty-cart">
                    <i class="fas fa-shopping-cart"></i>
                    <p>السلة فارغة</p>
                    <small>أضف منتجات للبدء في البيع</small>
                </div>
            </div>
            
            <div class="cart-summary">
                <div class="summary-row">
                    <span>المجموع الفرعي:</span>
                    <span id="subtotal">0 دينار</span>
                </div>
                <div class="summary-row">
                    <span>الخصم:</span>
                    <span id="discount">0 دينار</span>
                </div>
                <div class="summary-row">
                    <span>الضريبة (15%):</span>
                    <span id="tax">0 دينار</span>
                </div>
                <div class="summary-row total">
                    <span>المجموع الكلي:</span>
                    <span id="total">0 دينار</span>
                </div>
            </div>
        </div>

        <!-- Right Panel - Payment -->
        <div class="payment-panel">
            <div class="panel-header">
                <h3><i class="fas fa-credit-card"></i> الدفع</h3>
                <div class="sale-type">
                    <label>
                        <input type="radio" name="saleType" value="retail" checked onchange="updateSaleType()">
                        <span>مفرد</span>
                    </label>
                    <label>
                        <input type="radio" name="saleType" value="wholesale" onchange="updateSaleType()">
                        <span>جملة</span>
                    </label>
                </div>
            </div>
            
            <div class="customer-section">
                <h4><i class="fas fa-user"></i> معلومات العميل</h4>
                <div class="form-group">
                    <input type="text" id="customerName" placeholder="اسم العميل (اختياري)">
                </div>
                <div class="form-group">
                    <input type="tel" id="customerPhone" placeholder="رقم الهاتف (اختياري)">
                </div>
            </div>
            
            <!-- Professional Discount Section -->
            <div class="discount-section">
                <h4><i class="fas fa-percentage"></i> الخصم</h4>

                <div class="discount-grid">
                    <!-- Discount Amount Field -->
                    <div class="discount-field">
                        <label for="discountAmount">مبلغ الخصم:</label>
                        <div class="input-group">
                            <input type="number"
                                   id="discountAmount"
                                   placeholder="0"
                                   min="0"
                                   step="100"
                                   onchange="applyDiscount()">
                            <span class="input-addon">دينار</span>
                        </div>
                    </div>

                    <!-- Discount Percentage Field -->
                    <div class="discount-field">
                        <label for="discountPercent">نسبة الخصم:</label>
                        <div class="input-group">
                            <input type="number"
                                   id="discountPercent"
                                   placeholder="0"
                                   min="0"
                                   max="100"
                                   step="1"
                                   onchange="applyDiscountPercent()">
                            <span class="input-addon">%</span>
                        </div>
                    </div>
                </div>

                <!-- Discount Actions -->
                <div class="discount-actions">
                    <button class="discount-btn clear-btn" onclick="clearDiscount()">
                        <i class="fas fa-eraser"></i>
                        مسح الخصم
                    </button>
                </div>
            </div>
            
            <div class="payment-methods">
                <h4><i class="fas fa-money-bill-wave"></i> طريقة الدفع</h4>
                <div class="payment-buttons">
                    <button class="payment-btn active" data-method="cash" onclick="selectPaymentMethod('cash')">
                        <i class="fas fa-money-bill"></i>
                        نقدي
                    </button>
                    <button class="payment-btn" data-method="card" onclick="selectPaymentMethod('card')">
                        <i class="fas fa-credit-card"></i>
                        بطاقة
                    </button>
                    <button class="payment-btn" data-method="transfer" onclick="selectPaymentMethod('transfer')">
                        <i class="fas fa-exchange-alt"></i>
                        تحويل
                    </button>
                    <button class="payment-btn" data-method="mixed" onclick="selectPaymentMethod('mixed')">
                        <i class="fas fa-coins"></i>
                        مختلط
                    </button>
                </div>
            </div>
            
            <div class="payment-amount" id="paymentAmount">
                <div class="form-group">
                    <label>المبلغ المدفوع:</label>
                    <input type="number" id="paidAmount" placeholder="0" onchange="calculateChange()">
                </div>
                <div class="change-amount">
                    <span>الباقي:</span>
                    <span id="changeAmount">0 دينار</span>
                </div>
            </div>
            
            <div class="action-buttons">
                <button class="btn btn-success btn-large" onclick="completeSale()" id="completeSaleBtn" disabled>
                    <i class="fas fa-check"></i>
                    إتمام البيع
                </button>
                <button class="btn btn-info btn-large" onclick="printInvoice()">
                    <i class="fas fa-print"></i>
                    طباعة الفاتورة
                </button>
            </div>
        </div>
    </div>

    <!-- Quick Actions Bar -->
    <div class="quick-actions">
        <button class="quick-btn" onclick="showHeldSales()">
            <i class="fas fa-pause-circle"></i>
            <span>المبيعات المعلقة</span>
        </button>
        <button class="quick-btn" onclick="showLastSales()">
            <i class="fas fa-history"></i>
            <span>آخر المبيعات</span>
        </button>
        <button class="quick-btn" onclick="openCashDrawer()">
            <i class="fas fa-cash-register"></i>
            <span>فتح الدرج</span>
        </button>
        <button class="quick-btn" onclick="showCalculator()">
            <i class="fas fa-calculator"></i>
            <span>الآلة الحاسبة</span>
        </button>
    </div>

    <!-- Barcode Scanner Modal -->
    <div id="barcodeScannerModal" class="barcode-scanner-modal" style="display: none;">
        <div class="scanner-container">
            <div class="scanner-header">
                <h3>
                    <i class="fas fa-barcode"></i>
                    مسح الباركود
                </h3>
                <button class="close-scanner" onclick="closeBarcodeScanner()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="scanner-body">
                <!-- Camera View -->
                <div class="camera-container">
                    <video id="barcodeVideo" autoplay muted playsinline></video>
                    <canvas id="barcodeCanvas" style="display: none;"></canvas>

                    <!-- Scanner Overlay -->
                    <div class="scanner-overlay">
                        <div class="scanner-frame">
                            <div class="corner top-left"></div>
                            <div class="corner top-right"></div>
                            <div class="corner bottom-left"></div>
                            <div class="corner bottom-right"></div>
                            <div class="scanner-line"></div>
                        </div>

                        <div class="scanner-instructions">
                            <p>وجه الكاميرا نحو الباركود</p>
                            <small>تأكد من وضوح الباركود في الإطار</small>
                        </div>
                    </div>
                </div>

                <!-- Scanner Controls -->
                <div class="scanner-controls">
                    <button class="btn btn-primary" onclick="captureBarcode()">
                        <i class="fas fa-camera"></i>
                        التقاط
                    </button>
                    <button class="btn btn-secondary" onclick="toggleFlashlight()">
                        <i class="fas fa-flashlight" id="flashIcon"></i>
                        الفلاش
                    </button>
                    <button class="btn btn-info" onclick="switchCamera()">
                        <i class="fas fa-sync-alt"></i>
                        تبديل الكاميرا
                    </button>
                </div>

                <!-- Manual Input Fallback -->
                <div class="manual-input-section">
                    <h4>أو أدخل الباركود يدوياً:</h4>
                    <div class="manual-barcode-input">
                        <input type="text" id="manualBarcodeInput" placeholder="أدخل رقم الباركود...">
                        <button class="btn btn-success" onclick="processManualBarcode()">
                            <i class="fas fa-check"></i>
                            إضافة
                        </button>
                    </div>
                </div>

                <!-- Recent Scans -->
                <div class="recent-scans">
                    <h4>آخر المسوحات:</h4>
                    <div class="scans-list" id="recentScansList">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Barcode History Modal -->
    <div id="barcodeHistoryModal" class="barcode-history-modal" style="display: none;">
        <div class="history-container">
            <div class="history-header">
                <h3>
                    <i class="fas fa-history"></i>
                    سجل مسح الباركود
                </h3>
                <button class="close-history" onclick="closeBarcodeHistory()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="history-body">
                <div class="history-stats">
                    <div class="stat-card">
                        <div class="stat-number" id="totalScans">0</div>
                        <div class="stat-label">إجمالي المسوحات</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="successfulScans">0</div>
                        <div class="stat-label">مسوحات ناجحة</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="failedScans">0</div>
                        <div class="stat-label">مسوحات فاشلة</div>
                    </div>
                </div>

                <div class="history-filters">
                    <select id="historyFilter" onchange="filterBarcodeHistory()">
                        <option value="all">جميع المسوحات</option>
                        <option value="successful">الناجحة فقط</option>
                        <option value="failed">الفاشلة فقط</option>
                        <option value="today">اليوم</option>
                    </select>
                    <button class="btn btn-danger" onclick="clearBarcodeHistory()">
                        <i class="fas fa-trash"></i>
                        مسح السجل
                    </button>
                </div>

                <div class="history-list" id="barcodeHistoryList">
                    <!-- سيتم ملؤها بـ JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div id="settingsModal" class="settings-modal" style="display: none;">
        <div class="settings-container">
            <div class="settings-header">
                <h3>
                    <i class="fas fa-cog"></i>
                    إعدادات نظام الكاشير
                </h3>
                <button class="close-settings" onclick="hideSettings()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="settings-body">
                <!-- Navigation Tabs -->
                <div class="settings-tabs">
                    <button class="settings-tab active" onclick="showSettingsTab('general')">
                        <i class="fas fa-sliders-h"></i>
                        عام
                    </button>
                    <button class="settings-tab" onclick="showSettingsTab('display')">
                        <i class="fas fa-desktop"></i>
                        العرض
                    </button>
                    <button class="settings-tab" onclick="showSettingsTab('printer')">
                        <i class="fas fa-print"></i>
                        الطباعة
                    </button>
                    <button class="settings-tab" onclick="showSettingsTab('currency')">
                        <i class="fas fa-coins"></i>
                        العملة
                    </button>
                    <button class="settings-tab" onclick="showSettingsTab('backup')">
                        <i class="fas fa-database"></i>
                        النسخ الاحتياطي
                    </button>
                </div>

                <!-- General Settings -->
                <div id="generalSettings" class="settings-panel active">
                    <h4><i class="fas fa-sliders-h"></i> الإعدادات العامة</h4>

                    <div class="setting-group">
                        <label for="cashierName">اسم الكاشير:</label>
                        <input type="text" id="cashierName" placeholder="أدخل اسم الكاشير">
                        <small>سيظهر هذا الاسم في الفواتير والتقارير</small>
                    </div>

                    <div class="setting-group">
                        <label for="storeName">اسم المتجر:</label>
                        <input type="text" id="storeName" placeholder="أدخل اسم المتجر">
                        <small>اسم المتجر الذي سيظهر في الفواتير</small>
                    </div>

                    <div class="setting-group">
                        <label for="storeAddress">عنوان المتجر:</label>
                        <textarea id="storeAddress" placeholder="أدخل عنوان المتجر"></textarea>
                        <small>العنوان الكامل للمتجر</small>
                    </div>

                    <div class="setting-group">
                        <label for="storePhone">رقم الهاتف:</label>
                        <input type="tel" id="storePhone" placeholder="أدخل رقم الهاتف">
                        <small>رقم هاتف المتجر للتواصل</small>
                    </div>

                    <div class="setting-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="autoSave">
                            <span class="checkmark"></span>
                            حفظ تلقائي للمبيعات
                        </label>
                        <small>حفظ المبيعات تلقائياً كل دقيقة</small>
                    </div>

                    <div class="setting-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="soundEffects">
                            <span class="checkmark"></span>
                            تفعيل الأصوات
                        </label>
                        <small>تشغيل أصوات التنبيه والإشعارات</small>
                    </div>
                </div>

                <!-- Display Settings -->
                <div id="displaySettings" class="settings-panel">
                    <h4><i class="fas fa-desktop"></i> إعدادات العرض</h4>

                    <div class="setting-group">
                        <label for="theme">المظهر:</label>
                        <select id="theme">
                            <option value="default">افتراضي</option>
                            <option value="dark">داكن</option>
                            <option value="light">فاتح</option>
                            <option value="blue">أزرق</option>
                            <option value="green">أخضر</option>
                        </select>
                        <small>اختر مظهر واجهة الكاشير</small>
                    </div>

                    <div class="setting-group">
                        <label for="fontSize">حجم الخط:</label>
                        <select id="fontSize">
                            <option value="small">صغير</option>
                            <option value="medium">متوسط</option>
                            <option value="large">كبير</option>
                            <option value="xlarge">كبير جداً</option>
                        </select>
                        <small>حجم النص في الواجهة</small>
                    </div>

                    <div class="setting-group">
                        <label for="productsPerPage">المنتجات في الصفحة:</label>
                        <select id="productsPerPage">
                            <option value="12">12 منتج</option>
                            <option value="24">24 منتج</option>
                            <option value="36">36 منتج</option>
                            <option value="48">48 منتج</option>
                        </select>
                        <small>عدد المنتجات المعروضة في كل صفحة</small>
                    </div>

                    <div class="setting-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="showProductImages">
                            <span class="checkmark"></span>
                            عرض صور المنتجات
                        </label>
                        <small>إظهار أو إخفاء صور المنتجات</small>
                    </div>

                    <div class="setting-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="showProductCodes">
                            <span class="checkmark"></span>
                            عرض أكواد المنتجات
                        </label>
                        <small>إظهار أكواد المنتجات في القائمة</small>
                    </div>
                </div>

                <!-- Printer Settings -->
                <div id="printerSettings" class="settings-panel">
                    <h4><i class="fas fa-print"></i> إعدادات الطباعة</h4>

                    <div class="setting-group">
                        <label for="printerType">نوع الطابعة:</label>
                        <select id="printerType">
                            <option value="thermal">طابعة حرارية</option>
                            <option value="inkjet">طابعة نافثة للحبر</option>
                            <option value="laser">طابعة ليزر</option>
                        </select>
                        <small>نوع الطابعة المستخدمة</small>
                    </div>

                    <div class="setting-group">
                        <label for="paperSize">حجم الورق:</label>
                        <select id="paperSize">
                            <option value="80mm">80 مم (حراري)</option>
                            <option value="58mm">58 مم (حراري)</option>
                            <option value="a4">A4</option>
                            <option value="a5">A5</option>
                        </select>
                        <small>حجم ورق الطباعة</small>
                    </div>

                    <div class="setting-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="autoPrint">
                            <span class="checkmark"></span>
                            طباعة تلقائية
                        </label>
                        <small>طباعة الفاتورة تلقائياً بعد إتمام البيع</small>
                    </div>

                    <div class="setting-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="printLogo">
                            <span class="checkmark"></span>
                            طباعة شعار المتجر
                        </label>
                        <small>إضافة شعار المتجر في أعلى الفاتورة</small>
                    </div>

                    <div class="setting-group">
                        <label for="receiptFooter">تذييل الفاتورة:</label>
                        <textarea id="receiptFooter" placeholder="رسالة شكر أو معلومات إضافية"></textarea>
                        <small>نص يظهر في نهاية كل فاتورة</small>
                    </div>
                </div>

                <!-- Currency Settings -->
                <div id="currencySettings" class="settings-panel">
                    <h4><i class="fas fa-coins"></i> إعدادات العملة</h4>

                    <div class="setting-group">
                        <label for="mainCurrency">العملة الرئيسية:</label>
                        <select id="mainCurrency">
                            <option value="IQD">دينار عراقي (IQD)</option>
                            <option value="USD">دولار أمريكي (USD)</option>
                            <option value="EUR">يورو (EUR)</option>
                            <option value="SAR">ريال سعودي (SAR)</option>
                            <option value="AED">درهم إماراتي (AED)</option>
                        </select>
                        <small>العملة المستخدمة في المعاملات</small>
                    </div>

                    <div class="setting-group">
                        <label for="currencySymbol">رمز العملة:</label>
                        <input type="text" id="currencySymbol" placeholder="دينار">
                        <small>الرمز الذي يظهر مع الأسعار</small>
                    </div>

                    <div class="setting-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="enableMultiCurrency">
                            <span class="checkmark"></span>
                            تفعيل العملات المتعددة
                        </label>
                        <small>السماح بالدفع بعملات مختلفة</small>
                    </div>

                    <div class="setting-group">
                        <label for="taxRate">معدل الضريبة (%):</label>
                        <input type="number" id="taxRate" min="0" max="100" step="0.1" placeholder="0">
                        <small>معدل الضريبة المضافة على المبيعات</small>
                    </div>

                    <div class="setting-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="includeTax">
                            <span class="checkmark"></span>
                            الأسعار تشمل الضريبة
                        </label>
                        <small>الأسعار المعروضة تتضمن الضريبة</small>
                    </div>
                </div>

                <!-- Backup Settings -->
                <div id="backupSettings" class="settings-panel">
                    <h4><i class="fas fa-database"></i> النسخ الاحتياطي والاستعادة</h4>

                    <div class="setting-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="autoBackup">
                            <span class="checkmark"></span>
                            نسخ احتياطي تلقائي
                        </label>
                        <small>إنشاء نسخة احتياطية تلقائياً كل يوم</small>
                    </div>

                    <div class="setting-group">
                        <label for="backupTime">وقت النسخ الاحتياطي:</label>
                        <input type="time" id="backupTime" value="23:00">
                        <small>الوقت اليومي لإنشاء النسخة الاحتياطية</small>
                    </div>

                    <div class="backup-actions">
                        <button class="btn btn-success" onclick="createBackup()">
                            <i class="fas fa-download"></i>
                            إنشاء نسخة احتياطية
                        </button>

                        <button class="btn btn-warning" onclick="restoreBackup()">
                            <i class="fas fa-upload"></i>
                            استعادة نسخة احتياطية
                        </button>

                        <button class="btn btn-danger" onclick="resetSettings()">
                            <i class="fas fa-undo"></i>
                            إعادة تعيين الإعدادات
                        </button>
                    </div>

                    <div class="backup-info">
                        <h5>معلومات النظام:</h5>
                        <div class="info-grid">
                            <div class="info-item">
                                <span class="info-label">عدد المنتجات:</span>
                                <span class="info-value" id="productsCount">0</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">عدد المبيعات:</span>
                                <span class="info-value" id="salesCount">0</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">حجم البيانات:</span>
                                <span class="info-value" id="dataSize">0 KB</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">آخر نسخة احتياطية:</span>
                                <span class="info-value" id="lastBackup">لا توجد</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Settings Actions -->
                <div class="settings-actions">
                    <button class="btn btn-success" onclick="saveSettings()">
                        <i class="fas fa-save"></i>
                        حفظ الإعدادات
                    </button>
                    <button class="btn btn-secondary" onclick="loadDefaultSettings()">
                        <i class="fas fa-undo"></i>
                        الإعدادات الافتراضية
                    </button>
                    <button class="btn btn-info" onclick="exportSettings()">
                        <i class="fas fa-file-export"></i>
                        تصدير الإعدادات
                    </button>
                    <button class="btn btn-warning" onclick="importSettings()">
                        <i class="fas fa-file-import"></i>
                        استيراد الإعدادات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Calculator Modal -->
    <div id="calculatorModal" class="calculator-modal" style="display: none;">
        <div class="calculator-container">
            <div class="calculator-header">
                <h3>
                    <i class="fas fa-calculator"></i>
                    الآلة الحاسبة المتقدمة
                </h3>
                <button class="close-calculator" onclick="hideCalculator()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="calculator-body">
                <!-- Display Screen -->
                <div class="calculator-display">
                    <div class="display-history" id="calcHistory"></div>
                    <div class="display-current" id="calcDisplay">0</div>
                </div>

                <!-- Memory Functions -->
                <div class="memory-functions">
                    <button class="memory-btn" onclick="memoryRecall()" title="استدعاء من الذاكرة">MR</button>
                    <button class="memory-btn" onclick="memoryClear()" title="مسح الذاكرة">MC</button>
                    <button class="memory-btn" onclick="memoryAdd()" title="إضافة للذاكرة">M+</button>
                    <button class="memory-btn" onclick="memorySubtract()" title="طرح من الذاكرة">M-</button>
                    <button class="memory-btn" onclick="memoryStore()" title="حفظ في الذاكرة">MS</button>
                </div>

                <!-- Calculator Buttons -->
                <div class="calculator-buttons">
                    <!-- Row 1: Advanced Functions -->
                    <button class="calc-btn function-btn" onclick="clearAll()">C</button>
                    <button class="calc-btn function-btn" onclick="clearEntry()">CE</button>
                    <button class="calc-btn function-btn" onclick="backspace()">⌫</button>
                    <button class="calc-btn function-btn" onclick="toggleSign()">±</button>

                    <!-- Row 2: Scientific Functions -->
                    <button class="calc-btn scientific-btn" onclick="calculateSquareRoot()">√</button>
                    <button class="calc-btn scientific-btn" onclick="calculateSquare()">x²</button>
                    <button class="calc-btn scientific-btn" onclick="calculatePercentage()">%</button>
                    <button class="calc-btn operator-btn" onclick="setOperation('÷')">÷</button>

                    <!-- Row 3: Numbers and Operations -->
                    <button class="calc-btn number-btn" onclick="inputNumber('7')">7</button>
                    <button class="calc-btn number-btn" onclick="inputNumber('8')">8</button>
                    <button class="calc-btn number-btn" onclick="inputNumber('9')">9</button>
                    <button class="calc-btn operator-btn" onclick="setOperation('×')">×</button>

                    <!-- Row 4 -->
                    <button class="calc-btn number-btn" onclick="inputNumber('4')">4</button>
                    <button class="calc-btn number-btn" onclick="inputNumber('5')">5</button>
                    <button class="calc-btn number-btn" onclick="inputNumber('6')">6</button>
                    <button class="calc-btn operator-btn" onclick="setOperation('-')">-</button>

                    <!-- Row 5 -->
                    <button class="calc-btn number-btn" onclick="inputNumber('1')">1</button>
                    <button class="calc-btn number-btn" onclick="inputNumber('2')">2</button>
                    <button class="calc-btn number-btn" onclick="inputNumber('3')">3</button>
                    <button class="calc-btn operator-btn" onclick="setOperation('+')">+</button>

                    <!-- Row 6 -->
                    <button class="calc-btn number-btn zero-btn" onclick="inputNumber('0')">0</button>
                    <button class="calc-btn number-btn" onclick="inputDecimal()">.</button>
                    <button class="calc-btn equals-btn" onclick="calculate()">=</button>
                </div>



                <!-- History Panel -->
                <div class="calculation-history">
                    <h4>سجل العمليات</h4>
                    <div class="history-list" id="historyList"></div>
                    <button class="clear-history-btn" onclick="clearHistory()">مسح السجل</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modals will be added here -->
    <div id="modalContainer"></div>

    <!-- Scripts -->
    <script>
        // متغيرات النظام العامة
        let cart = [];
        let currentSaleType = 'retail';
        let currentPaymentMethod = 'cash';
        let currentDiscount = 0;
        let currentTax = 0.15; // 15% ضريبة
        let heldSales = [];
        let lastInvoiceNumber = 1;

        // تحميل نظام الكاشير مباشرة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 بدء تهيئة نظام الكاشير...');

            // تهيئة النظام
            initializeCashierSystem();
            initializeSettings();
            initializeBarcodeSystem();
            updateDateTime();
            setInterval(updateDateTime, 1000);

            // مزامنة المنتجات مع الموقع الإلكتروني
            syncWithMainSite();

            // الاستماع لتحديثات المنتجات من لوحة الإدارة
            window.addEventListener('productsUpdated', function(event) {
                console.log('📢 تم استلام إشعار تحديث المنتجات في الكاشير:', event.detail);
                setTimeout(() => {
                    syncWithMainSite();
                    loadProducts();
                }, 500);
            });

            // الاستماع للرسائل من النوافذ الأخرى
            window.addEventListener('message', function(event) {
                if (event.data && event.data.type === 'productsUpdated') {
                    console.log('📨 تم استلام رسالة تحديث المنتجات في الكاشير:', event.data.data);
                    setTimeout(() => {
                        syncWithMainSite();
                        loadProducts();
                        showCashierUpdateNotification(event.data.data);
                    }, 500);
                }
            });

            // مزامنة دورية كل 30 ثانية
            setInterval(syncWithMainSite, 30000);

            // تهيئة نظام المنتجات المحسن
            enhancedProductsInit();

            console.log('✅ تم تهيئة نظام الكاشير بنجاح');
        });

        // مزامنة المنتجات مع الموقع الإلكتروني
        function syncWithMainSite() {
            console.log('🔄 مزامنة المنتجات مع الموقع الإلكتروني...');

            try {
                // تحميل المنتجات من الموقع الرئيسي
                const mainSiteProducts = JSON.parse(localStorage.getItem('adminProducts')) ||
                                       JSON.parse(localStorage.getItem('products')) || [];

                if (mainSiteProducts.length > 0) {
                    console.log(`📦 تم العثور على ${mainSiteProducts.length} منتج من الموقع الرئيسي`);

                    // تطبيع البيانات للكاشير
                    const normalizedProducts = mainSiteProducts.map(product => normalizeProductData(product));

                    // حفظ في كاشير localStorage
                    localStorage.setItem('cashierProducts', JSON.stringify(normalizedProducts));

                    console.log('✅ تم مزامنة المنتجات بنجاح');

                    // إرسال إشعار للواجهات الأخرى
                    window.dispatchEvent(new CustomEvent('cashierProductsUpdated', {
                        detail: {
                            source: 'cashier_sync',
                            count: normalizedProducts.length,
                            timestamp: new Date().toISOString()
                        }
                    }));
                } else {
                    console.log('⚠️ لا توجد منتجات في الموقع الرئيسي');
                }
            } catch (error) {
                console.error('❌ خطأ في مزامنة المنتجات:', error);
            }
        }

        // تم حذف وظيفة التحقق من تسجيل الدخول

        // تم حذف وظيفة redirectToLogin

        // تم حذف وظيفة updateUserInterface

        // تم حذف جميع الوظائف المرتبطة بواجهة المستخدم

        function initializeCashierSystem() {
            console.log('🚀 تهيئة نظام الكاشير...');

            // تهيئة المنتجات أولاً
            initializeProducts();

            // تحميل المنتجات
            loadProducts();

            // تحديث عرض السلة
            updateCartDisplay();
            updateSummary();

            // تحميل آخر رقم فاتورة
            const lastInvoice = localStorage.getItem('lastInvoiceNumber');
            if (lastInvoice) {
                lastInvoiceNumber = parseInt(lastInvoice) + 1;
            }

            console.log('✅ تم تهيئة نظام الكاشير بنجاح');
        }

        // Initialize products if not exists
        function initializeProducts() {
            let products = JSON.parse(localStorage.getItem('adminProducts')) || [];

            // إذا لم توجد منتجات، أنشئ منتجات تجريبية
            if (products.length === 0) {
                products = [
                    {
                        id: 1,
                        nameAr: 'تفاح أحمر',
                        nameEn: 'Red Apple',
                        price: 2500,
                        wholesalePrice: 2000,
                        category: 'فواكه',
                        barcode: '1234567890123',
                        stock: 50,
                        image: 'https://via.placeholder.com/100x100?text=تفاح',
                        description: 'تفاح أحمر طازج عالي الجودة'
                    },
                    {
                        id: 2,
                        nameAr: 'خبز أبيض',
                        nameEn: 'White Bread',
                        price: 1500,
                        wholesalePrice: 1200,
                        category: 'مخبوزات',
                        barcode: '2345678901234',
                        stock: 30,
                        image: 'https://via.placeholder.com/100x100?text=خبز',
                        description: 'خبز أبيض طازج يومياً'
                    },
                    {
                        id: 3,
                        nameAr: 'حليب كامل الدسم',
                        nameEn: 'Full Fat Milk',
                        price: 3000,
                        wholesalePrice: 2500,
                        category: 'منتجات ألبان',
                        barcode: '3456789012345',
                        stock: 25,
                        image: 'https://via.placeholder.com/100x100?text=حليب',
                        description: 'حليب كامل الدسم طبيعي 100%'
                    },
                    {
                        id: 4,
                        nameAr: 'دجاج مجمد',
                        nameEn: 'Frozen Chicken',
                        price: 8000,
                        wholesalePrice: 7000,
                        category: 'لحوم',
                        barcode: '4567890123456',
                        stock: 15,
                        image: 'https://via.placeholder.com/100x100?text=دجاج',
                        description: 'دجاج مجمد طازج عالي الجودة'
                    },
                    {
                        id: 5,
                        nameAr: 'طماطم طازجة',
                        nameEn: 'Fresh Tomatoes',
                        price: 2000,
                        wholesalePrice: 1500,
                        category: 'خضروات',
                        barcode: '5678901234567',
                        stock: 40,
                        image: 'https://via.placeholder.com/100x100?text=طماطم',
                        description: 'طماطم طازجة من المزرعة'
                    }
                ];

                localStorage.setItem('adminProducts', JSON.stringify(products));
                console.log('تم إنشاء منتجات تجريبية:', products.length);
            }

            console.log('المنتجات المحملة:', products.length);
            console.log('عينة من المنتجات:', products.slice(0, 2));
        }

        function updateDateTime() {
            const now = new Date();
            const dateTimeElement = document.getElementById('currentDateTime');
            if (dateTimeElement) {
                const options = {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                };
                dateTimeElement.textContent = now.toLocaleDateString('en-US', options);
            }
        }

        // تحميل وعرض المنتجات من الموقع الإلكتروني
        function loadProducts() {
            console.log('🔄 تحميل المنتجات من الموقع الإلكتروني...');

            // إظهار مؤشر التحميل
            const loadingIndicator = document.getElementById('loadingIndicator');
            if (loadingIndicator) {
                loadingIndicator.style.display = 'flex';
            }

            // تحميل المنتجات من adminProducts (الأولوية) ثم products كبديل
            let products = JSON.parse(localStorage.getItem('adminProducts')) ||
                          JSON.parse(localStorage.getItem('products')) || [];

            console.log('📦 تم العثور على', products.length, 'منتج من الموقع الإلكتروني');

            // إذا لم توجد منتجات، إنشاء منتجات تجريبية متوافقة
            if (products.length === 0) {
                console.log('⚠️ لا توجد منتجات، إنشاء منتجات تجريبية...');
                products = createCompatibleSampleProducts();

                // حفظ المنتجات التجريبية
                localStorage.setItem('adminProducts', JSON.stringify(products));
                localStorage.setItem('products', JSON.stringify(products));
            }

            // التأكد من توافق بنية البيانات مع الكاشير
            products = products.map(product => normalizeProductData(product));

            // التأكد من وجود باركود لكل منتج
            let needsUpdate = false;
            products = products.map(product => {
                if (!product.barcode) {
                    product.barcode = generateUniqueBarcode();
                    needsUpdate = true;
                    console.log(`تم إضافة باركود للمنتج ${product.nameAr || product.name}: ${product.barcode}`);
                }
                return product;
            });

            // حفظ التحديثات إذا لزم الأمر
            if (needsUpdate) {
                localStorage.setItem('adminProducts', JSON.stringify(products));
                localStorage.setItem('products', JSON.stringify(products));
            }

            // تحديث المتغيرات العامة
            allProducts = products;
            filteredProducts = products;

            console.log('✅ تم تحميل وتطبيع', products.length, 'منتج للكاشير');

            // تطبيق الفلاتر والعرض
            setTimeout(() => {
                applyFilters();
            }, 500);
        }

        // تطبيع بيانات المنتج للتوافق مع الكاشير
        function normalizeProductData(product) {
            return {
                id: product.id,
                nameAr: product.nameAr || product.name || 'منتج غير محدد',
                nameEn: product.nameEn || product.name || 'Unknown Product',
                name: product.nameAr || product.name || 'منتج غير محدد', // للتوافق
                category: product.category || 'أخرى',
                price: parseFloat(product.price) || 0,
                wholesalePrice: parseFloat(product.wholesalePrice) || parseFloat(product.price) || 0,
                oldPrice: parseFloat(product.oldPrice) || null,
                stock: parseInt(product.stock) || parseInt(product.quantity) || 0,
                quantity: parseInt(product.stock) || parseInt(product.quantity) || 0, // للتوافق
                image: product.image || '',
                description: product.descriptionAr || product.description || '',
                descriptionAr: product.descriptionAr || product.description || '',
                descriptionEn: product.descriptionEn || product.description || '',
                barcode: product.barcode || '',
                status: product.status || 'available',
                featured: product.featured || false,
                available: (parseInt(product.stock) || parseInt(product.quantity) || 0) > 0,
                inStock: (parseInt(product.stock) || parseInt(product.quantity) || 0) > 0,
                createdAt: product.createdAt || new Date().toISOString(),
                updatedAt: product.updatedAt || new Date().toISOString()
            };
        }

        // إنشاء منتجات تجريبية متوافقة مع الموقع الإلكتروني
        function createCompatibleSampleProducts() {
            return [
                {
                    id: 'ecom_1',
                    nameAr: 'تفاح أحمر طازج',
                    nameEn: 'Fresh Red Apples',
                    name: 'تفاح أحمر طازج',
                    category: 'فواكه',
                    price: 2500,
                    wholesalePrice: 2000,
                    oldPrice: 3000,
                    stock: 50,
                    quantity: 50,
                    image: 'https://images.unsplash.com/photo-1560806887-1e4cd0b6cbd6?w=400&h=300&fit=crop',
                    description: 'تفاح أحمر طازج وعالي الجودة مستورد من أفضل المزارع',
                    descriptionAr: 'تفاح أحمر طازج وعالي الجودة مستورد من أفضل المزارع',
                    descriptionEn: 'Fresh and high quality red apples imported from the best farms',
                    barcode: '1234567890123',
                    status: 'available',
                    featured: true,
                    available: true,
                    inStock: true,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                },
                {
                    id: 'ecom_2',
                    nameAr: 'خبز أبيض طازج',
                    nameEn: 'Fresh White Bread',
                    name: 'خبز أبيض طازج',
                    category: 'مخبوزات',
                    price: 1500,
                    wholesalePrice: 1200,
                    stock: 30,
                    quantity: 30,
                    image: 'https://images.unsplash.com/photo-1509440159596-0249088772ff?w=400&h=300&fit=crop',
                    description: 'خبز أبيض طازج يومياً من أجود أنواع الدقيق',
                    descriptionAr: 'خبز أبيض طازج يومياً من أجود أنواع الدقيق',
                    descriptionEn: 'Fresh white bread daily from the finest flour',
                    barcode: '2345678901234',
                    status: 'available',
                    featured: false,
                    available: true,
                    inStock: true,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                },
                {
                    id: 'ecom_3',
                    nameAr: 'حليب كامل الدسم',
                    nameEn: 'Full Fat Milk',
                    name: 'حليب كامل الدسم',
                    category: 'منتجات ألبان',
                    price: 3000,
                    wholesalePrice: 2500,
                    stock: 25,
                    quantity: 25,
                    image: 'https://images.unsplash.com/photo-1550583724-b2692b85b150?w=400&h=300&fit=crop',
                    description: 'حليب كامل الدسم طبيعي 100% غني بالفيتامينات',
                    descriptionAr: 'حليب كامل الدسم طبيعي 100% غني بالفيتامينات',
                    descriptionEn: '100% natural full fat milk rich in vitamins',
                    barcode: '3456789012345',
                    status: 'available',
                    featured: true,
                    available: true,
                    inStock: true,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                },
                {
                    id: 'ecom_4',
                    nameAr: 'دجاج مجمد',
                    nameEn: 'Frozen Chicken',
                    name: 'دجاج مجمد',
                    category: 'لحوم',
                    price: 8000,
                    wholesalePrice: 7000,
                    stock: 15,
                    quantity: 15,
                    image: 'https://images.unsplash.com/photo-1604503468506-a8da13d82791?w=400&h=300&fit=crop',
                    description: 'دجاج مجمد طازج عالي الجودة خالي من الهرمونات',
                    descriptionAr: 'دجاج مجمد طازج عالي الجودة خالي من الهرمونات',
                    descriptionEn: 'Fresh frozen chicken high quality hormone-free',
                    barcode: '4567890123456',
                    status: 'available',
                    featured: false,
                    available: true,
                    inStock: true,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                },
                {
                    id: 'ecom_5',
                    nameAr: 'طماطم طازجة',
                    nameEn: 'Fresh Tomatoes',
                    name: 'طماطم طازجة',
                    category: 'خضروات',
                    price: 2000,
                    wholesalePrice: 1500,
                    stock: 40,
                    quantity: 40,
                    image: 'https://images.unsplash.com/photo-1546470427-e26264be0b0d?w=400&h=300&fit=crop',
                    description: 'طماطم طازجة من المزرعة غنية بالفيتامينات والمعادن',
                    descriptionAr: 'طماطم طازجة من المزرعة غنية بالفيتامينات والمعادن',
                    descriptionEn: 'Fresh farm tomatoes rich in vitamins and minerals',
                    barcode: '5678901234567',
                    status: 'available',
                    featured: false,
                    available: true,
                    inStock: true,
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString()
                }
            ];
        }

        // متغيرات العرض والفلترة
        let currentViewMode = 'grid';
        let currentFilter = 'all';
        let currentSort = 'name';
        let allProducts = [];
        let filteredProducts = [];

        // عرض المنتجات الجديد والمحسن
        function displayProducts(products) {
            const productsGrid = document.getElementById('productsGrid');
            const loadingIndicator = document.getElementById('loadingIndicator');
            const noProductsFound = document.getElementById('noProductsFound');
            const searchStats = document.getElementById('searchStats');

            if (!productsGrid) {
                console.error('❌ لم يتم العثور على عنصر productsGrid');
                return;
            }

            // إخفاء مؤشر التحميل
            loadingIndicator.style.display = 'none';

            // تحديث إحصائيات البحث
            if (searchStats) {
                searchStats.innerHTML = `<span class="products-count">${products.length} منتج</span>`;
            }

            console.log('🎨 عرض', products.length, 'منتج في الشبكة');

            if (products.length === 0) {
                productsGrid.style.display = 'none';
                noProductsFound.style.display = 'flex';
                return;
            }

            productsGrid.style.display = 'grid';
            noProductsFound.style.display = 'none';

            // تطبيق نمط العرض
            productsGrid.className = currentViewMode === 'grid' ? 'products-grid' : 'products-list';

            productsGrid.innerHTML = products.map((product, index) => {
                const price = getCurrentPrice(product);
                const stock = product.stock || product.quantity || 0;
                const stockClass = getStockClass(stock);
                const hasDiscount = product.oldPrice && product.oldPrice > product.price;
                const discountPercent = hasDiscount ? Math.round(((product.oldPrice - product.price) / product.oldPrice) * 100) : 0;
                const isOutOfStock = stock <= 0;

                if (currentViewMode === 'grid') {
                    return createGridProductCard(product, index, price, stock, stockClass, hasDiscount, discountPercent, isOutOfStock);
                } else {
                    return createListProductCard(product, index, price, stock, stockClass, hasDiscount, discountPercent, isOutOfStock);
                }
            }).join('');

            console.log('✅ تم عرض المنتجات بنجاح بالتصميم الجديد');
        }

        // إنشاء بطاقة منتج في نمط الشبكة
        function createGridProductCard(product, index, price, stock, stockClass, hasDiscount, discountPercent, isOutOfStock) {
            return `
                <div class="product-card-modern ${stockClass} ${isOutOfStock ? 'out-of-stock' : ''}"
                     data-product-id="${product.id}"
                     onclick="quickAddProduct('${product.id}')"
                     style="animation-delay: ${index * 0.05}s">

                    <!-- Product Image -->
                    <div class="product-image-container">
                        <div class="product-image-wrapper">
                            ${product.image ?
                                `<img src="${product.image}"
                                      alt="${product.nameAr || product.name}"
                                      class="product-image"
                                      loading="lazy"
                                      onerror="this.style.display='none'; this.parentElement.innerHTML='<div class=\\"image-placeholder\\"><i class=\\"fas fa-image\\"></i></div>'">` :
                                '<div class="image-placeholder"><i class="fas fa-image"></i></div>'
                            }
                        </div>

                        <!-- Product Badges -->
                        <div class="product-badges">
                            ${hasDiscount ? `<span class="discount-badge">-${discountPercent}%</span>` : ''}
                            ${product.featured ? '<span class="featured-badge"><i class="fas fa-star"></i></span>' : ''}
                            ${isOutOfStock ? '<span class="stock-badge out-stock">نفذ</span>' :
                              stock <= 5 ? '<span class="stock-badge low-stock">قليل</span>' : ''}
                        </div>

                        <!-- Quick Actions Overlay -->
                        <div class="quick-actions-overlay">
                            <button class="quick-action-btn primary"
                                    onclick="event.stopPropagation(); quickAddProduct('${product.id}')"
                                    ${isOutOfStock ? 'disabled' : ''}
                                    title="إضافة سريعة">
                                <i class="fas fa-cart-plus"></i>
                            </button>
                            <button class="quick-action-btn secondary"
                                    onclick="event.stopPropagation(); showProductDetails('${product.id}')"
                                    title="عرض التفاصيل">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Product Info -->
                    <div class="product-info">
                        <div class="product-header">
                            <h4 class="product-name" title="${product.nameAr || product.name}">
                                ${product.nameAr || product.name}
                            </h4>
                            <span class="product-category">
                                <i class="fas fa-tag"></i>
                                ${product.category || 'أخرى'}
                            </span>
                        </div>

                        <div class="product-pricing">
                            <div class="current-price">
                                ${price.toLocaleString()}
                                <span class="currency">د.ع</span>
                            </div>
                            ${hasDiscount ? `
                                <div class="old-price">
                                    ${parseFloat(product.oldPrice).toLocaleString()} د.ع
                                </div>
                            ` : ''}
                        </div>

                        <div class="product-stock">
                            <i class="fas fa-boxes"></i>
                            <span class="stock-count ${stock <= 0 ? 'out' : stock <= 5 ? 'low' : 'good'}">
                                ${stock} قطعة
                            </span>
                        </div>

                        <!-- Add to Cart Button -->
                        <button class="add-to-cart-btn ${isOutOfStock ? 'disabled' : ''}"
                                onclick="event.stopPropagation(); quickAddProduct('${product.id}')"
                                ${isOutOfStock ? 'disabled' : ''}>
                            <i class="fas fa-cart-plus"></i>
                            <span>${isOutOfStock ? 'غير متوفر' : 'إضافة للسلة'}</span>
                        </button>
                    </div>
                </div>
            `;
        }

        // إنشاء بطاقة منتج في نمط القائمة
        function createListProductCard(product, index, price, stock, stockClass, hasDiscount, discountPercent, isOutOfStock) {
            return `
                <div class="product-card-list ${stockClass} ${isOutOfStock ? 'out-of-stock' : ''}"
                     data-product-id="${product.id}"
                     style="animation-delay: ${index * 0.03}s">

                    <!-- Product Image -->
                    <div class="product-image-list">
                        ${product.image ?
                            `<img src="${product.image}"
                                  alt="${product.nameAr || product.name}"
                                  loading="lazy"
                                  onerror="this.style.display='none'; this.parentElement.innerHTML='<div class=\\"image-placeholder-small\\"><i class=\\"fas fa-image\\"></i></div>'">` :
                            '<div class="image-placeholder-small"><i class="fas fa-image"></i></div>'
                        }

                        ${hasDiscount ? `<span class="discount-badge-small">-${discountPercent}%</span>` : ''}
                    </div>

                    <!-- Product Details -->
                    <div class="product-details-list">
                        <div class="product-main-info">
                            <h4 class="product-name-list">${product.nameAr || product.name}</h4>
                            <span class="product-category-list">
                                <i class="fas fa-tag"></i>
                                ${product.category || 'أخرى'}
                            </span>
                        </div>

                        <div class="product-meta-list">
                            <div class="product-price-list">
                                <span class="current-price-list">
                                    ${price.toLocaleString()} د.ع
                                </span>
                                ${hasDiscount ? `
                                    <span class="old-price-list">
                                        ${parseFloat(product.oldPrice).toLocaleString()} د.ع
                                    </span>
                                ` : ''}
                            </div>

                            <div class="product-stock-list">
                                <i class="fas fa-boxes"></i>
                                <span class="stock-count-list ${stock <= 0 ? 'out' : stock <= 5 ? 'low' : 'good'}">
                                    ${stock} قطعة
                                </span>
                            </div>

                            ${product.barcode ? `
                                <div class="product-barcode-list">
                                    <i class="fas fa-barcode"></i>
                                    <span>${product.barcode}</span>
                                </div>
                            ` : ''}
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="product-actions-list">
                        <button class="action-btn-list primary"
                                onclick="quickAddProduct('${product.id}')"
                                ${isOutOfStock ? 'disabled' : ''}
                                title="إضافة للسلة">
                            <i class="fas fa-cart-plus"></i>
                            <span>إضافة</span>
                        </button>
                        <button class="action-btn-list secondary"
                                onclick="showProductDetails('${product.id}')"
                                title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                            <span>تفاصيل</span>
                        </button>
                    </div>
                </div>
            `;
        }

        // الوظائف الجديدة للبحث والفلترة

        // البحث الذكي
        function smartSearch() {
            const searchTerm = document.getElementById('productSearch').value.toLowerCase().trim();
            const clearBtn = document.querySelector('.clear-search-btn');

            // إظهار/إخفاء زر المسح
            clearBtn.style.display = searchTerm ? 'flex' : 'none';

            applyFilters();
        }

        // تطبيق الفلاتر
        function applyFilters() {
            const searchTerm = document.getElementById('productSearch').value.toLowerCase().trim();
            const categoryFilter = document.getElementById('categoryFilter').value;

            let filtered = [...allProducts];

            // فلتر البحث النصي
            if (searchTerm) {
                filtered = filtered.filter(product => {
                    const nameAr = (product.nameAr || '').toLowerCase();
                    const nameEn = (product.nameEn || '').toLowerCase();
                    const barcode = (product.barcode || '').toLowerCase();
                    const category = (product.category || '').toLowerCase();
                    const price = (product.price || 0).toString();

                    return nameAr.includes(searchTerm) ||
                           nameEn.includes(searchTerm) ||
                           barcode.includes(searchTerm) ||
                           category.includes(searchTerm) ||
                           price.includes(searchTerm);
                });
            }

            // فلتر الفئة
            if (categoryFilter) {
                filtered = filtered.filter(product =>
                    (product.category || '').toLowerCase() === categoryFilter.toLowerCase()
                );
            }

            // فلتر سريع
            if (currentFilter !== 'all') {
                filtered = filtered.filter(product => {
                    const stock = product.stock || product.quantity || 0;
                    switch (currentFilter) {
                        case 'available':
                            return stock > 5;
                        case 'low':
                            return stock > 0 && stock <= 5;
                        case 'featured':
                            return product.featured;
                        default:
                            return true;
                    }
                });
            }

            filteredProducts = filtered;
            applySorting();
        }

        // تطبيق الترتيب
        function applySorting() {
            const sortBy = document.getElementById('sortBy').value;

            let sorted = [...filteredProducts];

            switch (sortBy) {
                case 'name':
                    sorted.sort((a, b) => (a.nameAr || a.name || '').localeCompare(b.nameAr || b.name || ''));
                    break;
                case 'price-low':
                    sorted.sort((a, b) => (a.price || 0) - (b.price || 0));
                    break;
                case 'price-high':
                    sorted.sort((a, b) => (b.price || 0) - (a.price || 0));
                    break;
                case 'stock':
                    sorted.sort((a, b) => (b.stock || b.quantity || 0) - (a.stock || a.quantity || 0));
                    break;
                case 'category':
                    sorted.sort((a, b) => (a.category || '').localeCompare(b.category || ''));
                    break;
            }

            displayProducts(sorted);
        }

        // تعيين الفلتر السريع
        function setQuickFilter(filter) {
            currentFilter = filter;

            // تحديث أزرار الفلتر
            document.querySelectorAll('.filter-tab').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-filter="${filter}"]`).classList.add('active');

            applyFilters();
        }

        // تعيين نمط العرض
        function setViewMode(mode) {
            currentViewMode = mode;

            // تحديث أزرار نمط العرض
            document.querySelectorAll('.view-mode-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-mode="${mode}"]`).classList.add('active');

            // إعادة عرض المنتجات
            displayProducts(filteredProducts);
        }

        // مسح البحث
        function clearSearch() {
            document.getElementById('productSearch').value = '';
            document.querySelector('.clear-search-btn').style.display = 'none';
            applyFilters();
        }

        // إعادة تعيين جميع الفلاتر
        function resetAllFilters() {
            document.getElementById('productSearch').value = '';
            document.getElementById('categoryFilter').value = '';
            document.getElementById('sortBy').value = 'name';
            document.querySelector('.clear-search-btn').style.display = 'none';

            currentFilter = 'all';
            currentSort = 'name';

            // إعادة تعيين أزرار الفلتر
            document.querySelectorAll('.filter-tab').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector('[data-filter="all"]').classList.add('active');

            applyFilters();
        }

        // تحديث جميع المنتجات
        function refreshAllProducts() {
            const refreshBtn = document.querySelector('.refresh-products-btn');
            refreshBtn.classList.add('loading');

            // مزامنة مع الموقع الرئيسي
            syncWithMainSite();

            // إعادة تحميل المنتجات
            setTimeout(() => {
                loadProducts();
                refreshBtn.classList.remove('loading');
                showNotification('تم تحديث المنتجات بنجاح', 'success');
            }, 1000);
        }

        // إضافة سريعة للمنتج
        function quickAddProduct(productId) {
            console.log('🛒 إضافة سريعة للمنتج:', productId);

            const productCard = document.querySelector(`[data-product-id="${productId}"]`);
            if (!productCard) {
                console.error('❌ لم يتم العثور على بطاقة المنتج');
                return;
            }

            // تأثير النقر
            productCard.classList.add('clicking');
            setTimeout(() => productCard.classList.remove('clicking'), 200);

            // البحث عن المنتج
            const product = allProducts.find(p => p.id == productId);

            if (!product) {
                console.error('❌ المنتج غير موجود:', productId);
                showNotification('المنتج غير موجود', 'error');
                return;
            }

            const stock = product.stock || product.quantity || 0;
            if (stock <= 0) {
                console.warn('⚠️ المنتج غير متوفر في المخزون');
                showNotification('المنتج غير متوفر في المخزون', 'error');
                productCard.classList.add('shake');
                setTimeout(() => productCard.classList.remove('shake'), 500);
                return;
            }

            // التحقق من وجود المنتج في السلة
            const existingItem = cart.find(item => item.id == product.id);
            if (existingItem && existingItem.quantity >= stock) {
                showNotification('لا يمكن إضافة كمية أكثر من المتوفر', 'warning');
                productCard.classList.add('shake');
                setTimeout(() => productCard.classList.remove('shake'), 500);
                return;
            }

            // إضافة المنتج للسلة
            const success = addProductToCart(productId);

            if (success) {
                // تأثير النجاح
                productCard.classList.add('added-successfully');
                setTimeout(() => productCard.classList.remove('added-successfully'), 1000);

                // إشعار مخصص
                showAddToCartNotification(product.nameAr || product.name);

                // تحديث عداد السلة
                updateCartCounter();
            }
        }

        // عرض تفاصيل المنتج
        function showProductDetails(productId) {
            openProductDetailsModal(productId);
        }

        // إشعار مخصص لإضافة المنتج
        function showAddToCartNotification(productName) {
            const notification = document.createElement('div');
            notification.className = 'add-to-cart-notification';
            notification.innerHTML = `
                <div class="notification-content">
                    <div class="notification-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="notification-text">
                        <strong>تمت الإضافة!</strong>
                        <span>${productName}</span>
                    </div>
                    <div class="cart-info">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="cart-count">${cart.length}</span>
                    </div>
                </div>
            `;

            // تطبيق الأنماط
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #27ae60, #2ecc71);
                color: white;
                padding: 15px 20px;
                border-radius: 15px;
                box-shadow: 0 8px 25px rgba(39, 174, 96, 0.4);
                z-index: 10000;
                transform: translateX(100%);
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                min-width: 280px;
                font-weight: 600;
                border: 2px solid rgba(255, 255, 255, 0.2);
            `;

            document.body.appendChild(notification);

            // إظهار الإشعار
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            // إخفاء الإشعار
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 400);
            }, 2500);
        }

        // تحديث عداد السلة
        function updateCartCounter() {
            const cartCounters = document.querySelectorAll('.cart-count');
            cartCounters.forEach(counter => {
                counter.textContent = cart.length;
                counter.classList.add('bounce');
                setTimeout(() => counter.classList.remove('bounce'), 300);
            });
        }

        // عرض تفاصيل المنتج في modal
        function showProductDetails(productId) {
            const products = JSON.parse(localStorage.getItem('adminProducts')) ||
                            JSON.parse(localStorage.getItem('products')) || [];
            const product = products.find(p => p.id === productId);

            if (!product) {
                showNotification('لم يتم العثور على المنتج', 'error');
                return;
            }

            const modal = document.createElement('div');
            modal.className = 'product-details-modal';
            modal.innerHTML = `
                <div class="modal-overlay" onclick="closeProductDetails()"></div>
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>${product.nameAr || product.name}</h3>
                        <button class="close-btn" onclick="closeProductDetails()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <div class="modal-body">
                        <div class="product-image-large">
                            ${product.image ?
                                `<img src="${product.image}" alt="${product.nameAr || product.name}">` :
                                '<i class="fas fa-image"></i>'
                            }
                        </div>

                        <div class="product-details-content">
                            <div class="detail-row">
                                <span class="label">الاسم:</span>
                                <span class="value">${product.nameAr || product.name}</span>
                            </div>

                            ${product.nameEn ? `
                            <div class="detail-row">
                                <span class="label">الاسم بالإنجليزية:</span>
                                <span class="value">${product.nameEn}</span>
                            </div>
                            ` : ''}

                            <div class="detail-row">
                                <span class="label">الفئة:</span>
                                <span class="value">${product.category || 'أخرى'}</span>
                            </div>

                            <div class="detail-row">
                                <span class="label">السعر:</span>
                                <span class="value">${product.price.toLocaleString()} دينار</span>
                            </div>

                            ${product.wholesalePrice && product.wholesalePrice !== product.price ? `
                            <div class="detail-row">
                                <span class="label">سعر الجملة:</span>
                                <span class="value">${product.wholesalePrice.toLocaleString()} دينار</span>
                            </div>
                            ` : ''}

                            <div class="detail-row">
                                <span class="label">المخزون:</span>
                                <span class="value">${product.stock || product.quantity || 0} قطعة</span>
                            </div>

                            ${product.barcode ? `
                            <div class="detail-row">
                                <span class="label">الباركود:</span>
                                <span class="value barcode">${product.barcode}</span>
                            </div>
                            ` : ''}

                            ${product.description || product.descriptionAr ? `
                            <div class="detail-row description">
                                <span class="label">الوصف:</span>
                                <span class="value">${product.description || product.descriptionAr}</span>
                            </div>
                            ` : ''}
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button class="btn btn-primary" onclick="addProductToCart('${product.id}'); closeProductDetails();">
                            <i class="fas fa-cart-plus"></i>
                            إضافة للسلة
                        </button>
                        <button class="btn btn-secondary" onclick="closeProductDetails()">
                            <i class="fas fa-times"></i>
                            إغلاق
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // إضافة تأثير الظهور
            setTimeout(() => {
                modal.classList.add('show');
            }, 10);
        }

        // إغلاق تفاصيل المنتج
        function closeProductDetails() {
            const modal = document.querySelector('.product-details-modal');
            if (modal) {
                modal.classList.remove('show');
                setTimeout(() => {
                    document.body.removeChild(modal);
                }, 300);
            }
        }

        // الحصول على السعر الحالي للمنتج
        function getCurrentPrice(product) {
            const saleType = document.querySelector('input[name="saleType"]:checked')?.value || 'retail';
            return saleType === 'wholesale' && product.wholesalePrice ?
                parseFloat(product.wholesalePrice) : parseFloat(product.price);
        }

        // الحصول على فئة CSS للمخزون
        function getStockClass(stock) {
            if (stock <= 0) return 'out-of-stock';
            if (stock <= 5) return 'low-stock';
            return 'in-stock';
        }

        // البحث المتقدم في المنتجات
        function performAdvancedSearch() {
            const searchTerm = document.getElementById('productSearch').value.toLowerCase().trim();
            const categoryFilter = document.getElementById('categoryFilter').value;
            const stockFilter = document.getElementById('stockFilter').value;
            const products = JSON.parse(localStorage.getItem('adminProducts')) || [];

            console.log('🔍 البحث المتقدم:', { searchTerm, categoryFilter, stockFilter });

            let filteredProducts = products;

            // تطبيق فلتر البحث النصي
            if (searchTerm !== '') {
                filteredProducts = filteredProducts.filter(product => {
                    const nameAr = (product.nameAr || '').toLowerCase();
                    const nameEn = (product.nameEn || '').toLowerCase();
                    const name = (product.name || '').toLowerCase();
                    const barcode = (product.barcode || '').toLowerCase();
                    const category = (product.category || '').toLowerCase();
                    const price = (product.price || 0).toString();

                    return nameAr.includes(searchTerm) ||
                           nameEn.includes(searchTerm) ||
                           name.includes(searchTerm) ||
                           barcode.includes(searchTerm) ||
                           category.includes(searchTerm) ||
                           price.includes(searchTerm);
                });
            }

            // تطبيق فلتر الفئة
            if (categoryFilter !== '') {
                filteredProducts = filteredProducts.filter(product =>
                    (product.category || '').toLowerCase() === categoryFilter.toLowerCase()
                );
            }

            // تطبيق فلتر المخزون
            if (stockFilter !== '') {
                filteredProducts = filteredProducts.filter(product => {
                    const stock = parseInt(product.stock) || parseInt(product.quantity) || 0;
                    switch (stockFilter) {
                        case 'available':
                            return stock > 5;
                        case 'low':
                            return stock > 0 && stock <= 5;
                        case 'out':
                            return stock <= 0;
                        default:
                            return true;
                    }
                });
            }

            console.log(`📦 تم العثور على ${filteredProducts.length} منتج`);
            displayProducts(filteredProducts);
        }

        // البحث السريع (للتوافق مع الكود القديم)
        function searchProducts() {
            performAdvancedSearch();
        }

        // مسح جميع فلاتر البحث
        function clearAllSearch() {
            document.getElementById('productSearch').value = '';
            document.getElementById('categoryFilter').value = '';
            document.getElementById('stockFilter').value = '';

            const products = JSON.parse(localStorage.getItem('adminProducts')) || [];
            displayProducts(products);

            console.log('🧹 تم مسح جميع فلاتر البحث');
        }

        // مسح البحث (للتوافق مع الكود القديم)
        function clearSearch() {
            clearAllSearch();
        }

        // تحديث المنتجات
        function refreshProducts() {
            console.log('🔄 تحديث المنتجات...');

            // مزامنة مع الموقع الرئيسي
            syncWithMainSite();

            // إعادة تحميل المنتجات
            loadProducts();

            // مسح فلاتر البحث
            clearAllSearch();

            showNotification('تم تحديث المنتجات بنجاح', 'success');
        }

        // متغير لحفظ المنتج المحدد في الـ modal
        let selectedModalProduct = null;

        // فتح modal تفاصيل المنتج
        function openProductDetailsModal(productId) {
            const products = JSON.parse(localStorage.getItem('adminProducts')) || [];
            const product = products.find(p => p.id == productId);

            if (!product) {
                showNotification('لم يتم العثور على المنتج', 'error');
                return;
            }

            selectedModalProduct = product;

            // تحديث صورة المنتج
            const modalImage = document.getElementById('modalProductImage');
            if (product.image) {
                modalImage.innerHTML = `<img src="${product.image}" alt="${product.nameAr || product.name}">`;
            } else {
                modalImage.innerHTML = '<i class="fas fa-image"></i>';
            }

            // تحديث تفاصيل المنتج
            const modalDetails = document.getElementById('modalProductDetails');
            const currentPrice = getCurrentPrice(product);
            const stock = product.stock || product.quantity || 0;

            modalDetails.innerHTML = `
                <div class="detail-row">
                    <span class="label">الاسم:</span>
                    <span class="value">${product.nameAr || product.name}</span>
                </div>

                ${product.nameEn ? `
                <div class="detail-row">
                    <span class="label">الاسم بالإنجليزية:</span>
                    <span class="value">${product.nameEn}</span>
                </div>
                ` : ''}

                <div class="detail-row">
                    <span class="label">الفئة:</span>
                    <span class="value">${product.category || 'أخرى'}</span>
                </div>

                <div class="detail-row">
                    <span class="label">السعر:</span>
                    <span class="value">${currentPrice.toLocaleString()} دينار</span>
                </div>

                ${product.wholesalePrice && product.wholesalePrice !== product.price ? `
                <div class="detail-row">
                    <span class="label">سعر الجملة:</span>
                    <span class="value">${parseFloat(product.wholesalePrice).toLocaleString()} دينار</span>
                </div>
                ` : ''}

                <div class="detail-row">
                    <span class="label">المخزون:</span>
                    <span class="value ${stock <= 0 ? 'text-danger' : stock <= 5 ? 'text-warning' : 'text-success'}">
                        ${stock} قطعة
                        ${stock <= 0 ? '(نفذ المخزون)' : stock <= 5 ? '(مخزون قليل)' : ''}
                    </span>
                </div>

                ${product.barcode ? `
                <div class="detail-row">
                    <span class="label">الباركود:</span>
                    <span class="value barcode">${product.barcode}</span>
                </div>
                ` : ''}

                ${product.description || product.descriptionAr ? `
                <div class="detail-row description">
                    <span class="label">الوصف:</span>
                    <span class="value">${product.description || product.descriptionAr}</span>
                </div>
                ` : ''}

                ${product.oldPrice && product.oldPrice > product.price ? `
                <div class="detail-row">
                    <span class="label">السعر القديم:</span>
                    <span class="value" style="text-decoration: line-through; color: #e74c3c;">
                        ${parseFloat(product.oldPrice).toLocaleString()} دينار
                    </span>
                </div>
                <div class="detail-row">
                    <span class="label">نسبة الخصم:</span>
                    <span class="value" style="color: #27ae60; font-weight: bold;">
                        ${Math.round(((product.oldPrice - product.price) / product.oldPrice) * 100)}%
                    </span>
                </div>
                ` : ''}
            `;

            // عرض الـ modal
            const modal = document.getElementById('productDetailsModal');
            modal.classList.add('show');
        }

        // إغلاق modal تفاصيل المنتج
        function closeProductDetailsModal() {
            const modal = document.getElementById('productDetailsModal');
            modal.classList.remove('show');
            selectedModalProduct = null;
        }

        // إضافة المنتج من الـ modal للسلة
        function addModalProductToCart() {
            if (selectedModalProduct) {
                addProductToCart(selectedModalProduct.id);
                closeProductDetailsModal();
            }
        }

        // مزامنة مع الموقع الرئيسي
        function syncWithMainSite() {
            try {
                // تحميل المنتجات من جميع المصادر المحتملة
                const adminProducts = JSON.parse(localStorage.getItem('adminProducts')) || [];
                const mainProducts = JSON.parse(localStorage.getItem('products')) || [];
                const cashierProducts = JSON.parse(localStorage.getItem('cashierProducts')) || [];

                // دمج المنتجات وإزالة التكرار
                const allProducts = [...adminProducts];

                // إضافة المنتجات من المصادر الأخرى إذا لم تكن موجودة
                mainProducts.forEach(product => {
                    if (!allProducts.find(p => p.id === product.id)) {
                        allProducts.push(normalizeProductData(product));
                    }
                });

                cashierProducts.forEach(product => {
                    if (!allProducts.find(p => p.id === product.id)) {
                        allProducts.push(normalizeProductData(product));
                    }
                });

                // حفظ المنتجات المدمجة
                localStorage.setItem('adminProducts', JSON.stringify(allProducts));
                localStorage.setItem('products', JSON.stringify(allProducts));
                localStorage.setItem('cashierProducts', JSON.stringify(allProducts.map(p => normalizeProductData(p))));

                console.log(`🔄 تم مزامنة ${allProducts.length} منتج مع الموقع الرئيسي`);
                return allProducts;

            } catch (error) {
                console.error('خطأ في المزامنة:', error);
                return JSON.parse(localStorage.getItem('adminProducts')) || [];
            }
        }

        // تطبيع بيانات المنتج
        function normalizeProductData(product) {
            return {
                id: product.id || generateUniqueId(),
                nameAr: product.nameAr || product.name || 'منتج بدون اسم',
                nameEn: product.nameEn || product.name || '',
                price: parseFloat(product.price) || 0,
                wholesalePrice: parseFloat(product.wholesalePrice) || parseFloat(product.price) || 0,
                stock: parseInt(product.stock) || parseInt(product.quantity) || 0,
                quantity: parseInt(product.quantity) || parseInt(product.stock) || 0,
                category: product.category || 'أخرى',
                barcode: product.barcode || generateUniqueBarcode(),
                image: product.image || null,
                description: product.description || product.descriptionAr || '',
                descriptionAr: product.descriptionAr || product.description || '',
                featured: product.featured || false,
                oldPrice: parseFloat(product.oldPrice) || null,
                createdAt: product.createdAt || new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
        }

        // توليد معرف فريد
        function generateUniqueId() {
            return 'prod_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }

        // فلترة حسب الفئة
        function filterByCategory(category) {
            console.log('🏷️ فلترة حسب الفئة:', category);

            // تحديث الأزرار النشطة
            document.querySelectorAll('.category-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            event.target.classList.add('active');

            const products = JSON.parse(localStorage.getItem('adminProducts')) || [];

            if (category === 'all') {
                displayProducts(products);
            } else {
                const filteredProducts = products.filter(product =>
                    product.category === category
                );
                displayProducts(filteredProducts);
            }
        }

        function loadCategories() {
            const categories = JSON.parse(localStorage.getItem('adminCategories')) || [];
            const categoriesTabs = document.getElementById('categoriesTabs');

            categoriesTabs.innerHTML = `
                <button class="category-tab active" onclick="filterByCategory('all')">الكل</button>
                ${categories.map(category => `
                    <button class="category-tab" onclick="filterByCategory('${category.id}')">${category.nameAr}</button>
                `).join('')}
            `;
        }

        function filterByCategory(categoryId) {
            const products = JSON.parse(localStorage.getItem('adminProducts')) || [];

            // تحديث الأزرار النشطة
            document.querySelectorAll('.category-tab').forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');

            let filteredProducts = products;
            if (categoryId !== 'all') {
                const category = JSON.parse(localStorage.getItem('adminCategories')).find(c => c.id === categoryId);
                if (category) {
                    filteredProducts = products.filter(p => p.category === category.nameAr);
                }
            }

            // استخدام displayProducts الجديدة
            displayProducts(filteredProducts);
        }

        function goToMainSite() {
            if (confirm('هل تريد الانتقال إلى الموقع الرئيسي؟')) {
                window.location.href = 'index.html';
            }
        }

        // إضافة منتج للسلة (الوظيفة الرئيسية)
        function addProductToCart(productId) {
            console.log('🛒 إضافة منتج للسلة - ID:', productId);

            const products = JSON.parse(localStorage.getItem('adminProducts')) || [];
            const product = products.find(p => p.id == productId);

            if (!product) {
                console.error('❌ المنتج غير موجود:', productId);
                showNotification('المنتج غير موجود', 'error');
                return false;
            }

            if (product.stock <= 0) {
                console.warn('⚠️ المنتج غير متوفر في المخزون');
                showNotification('المنتج غير متوفر في المخزون', 'error');
                return false;
            }

            // البحث عن المنتج في السلة
            const existingItem = cart.find(item => item.id == product.id);

            if (existingItem) {
                if (existingItem.quantity >= product.stock) {
                    showNotification('لا يمكن إضافة كمية أكثر من المتوفر', 'error');
                    return false;
                }
                existingItem.quantity++;
                console.log('📈 زيادة كمية المنتج:', product.nameAr);
            } else {
                const price = getCurrentPrice(product);
                cart.push({
                    id: product.id,
                    name: product.nameAr,
                    price: price,
                    quantity: 1,
                    stock: product.stock,
                    image: product.image,
                    barcode: product.barcode
                });
                console.log('➕ إضافة منتج جديد:', product.nameAr);
            }

            // تحديث العرض
            updateCartDisplay();
            updateSummary();

            // تأثير بصري على المنتج
            addProductAnimation(productId);

            // إشعار نجح
            showNotification(`تم إضافة ${product.nameAr} للسلة`, 'success');

            console.log('✅ تم إضافة المنتج بنجاح. السلة:', cart.length, 'عنصر');
            return true;
        }

        // تأثير بصري عند إضافة المنتج
        function addProductAnimation(productId) {
            const productCard = document.querySelector(`[data-product-id="${productId}"]`);
            if (productCard) {
                productCard.classList.add('adding');
                setTimeout(() => {
                    productCard.classList.remove('adding');
                }, 600);
            }
        }

        // وظيفة مساعدة للتوافق مع الكود القديم
        function addToCart(productId) {
            return addProductToCart(productId);
        }

        function updateCartDisplay() {
            const cartItems = document.getElementById('cartItems');

            if (cart.length === 0) {
                cartItems.innerHTML = `
                    <div class="empty-cart">
                        <i class="fas fa-shopping-cart"></i>
                        <p>السلة فارغة</p>
                        <small>أضف منتجات للبدء في البيع</small>
                    </div>
                `;
                return;
            }

            cartItems.innerHTML = cart.map((item, index) => `
                <div class="cart-item">
                    <div class="item-image">
                        ${item.image ? `<img src="${item.image}" alt="${item.name}">` : '<i class="fas fa-box"></i>'}
                    </div>
                    <div class="item-details">
                        <h5>${item.name}</h5>
                        <p class="item-price">${formatCurrency(item.price)}</p>
                    </div>
                    <div class="item-controls">
                        <button class="qty-btn" onclick="decreaseQuantity(${index})">-</button>
                        <span class="quantity">${item.quantity}</span>
                        <button class="qty-btn" onclick="increaseQuantity(${index})">+</button>
                    </div>
                    <div class="item-total">
                        ${formatCurrency(item.price * item.quantity)}
                    </div>
                    <button class="remove-btn" onclick="removeFromCart(${index})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `).join('');
        }

        function increaseQuantity(index) {
            const item = cart[index];
            if (item.quantity >= item.stock) {
                alert('لا يمكن إضافة كمية أكثر من المتوفر في المخزون');
                return;
            }
            item.quantity++;
            updateCartDisplay();
            updateSummary();
        }

        function decreaseQuantity(index) {
            const item = cart[index];
            if (item.quantity > 1) {
                item.quantity--;
                updateCartDisplay();
                updateSummary();
            }
        }

        function removeFromCart(index) {
            cart.splice(index, 1);
            updateCartDisplay();
            updateSummary();
        }

        function clearCart() {
            if (cart.length === 0) return;

            if (confirm('هل أنت متأكد من مسح جميع المنتجات من السلة؟')) {
                cart = [];
                updateCartDisplay();
                updateSummary();
            }
        }

        function updateSummary() {
            const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            const discountAmount = currentDiscount;
            const taxableAmount = Math.max(0, subtotal - discountAmount);
            const taxAmount = taxableAmount * currentTax;
            const total = taxableAmount + taxAmount;

            document.getElementById('subtotal').textContent = formatCurrency(subtotal);
            document.getElementById('discount').textContent = formatCurrency(discountAmount);
            document.getElementById('tax').textContent = formatCurrency(taxAmount);
            document.getElementById('total').textContent = formatCurrency(total);

            // تفعيل/تعطيل زر إتمام البيع
            const completeSaleBtn = document.getElementById('completeSaleBtn');
            if (completeSaleBtn) {
                completeSaleBtn.disabled = cart.length === 0;
            }

            console.log(`تحديث الملخص: المجموع=${subtotal}, الخصم=${discountAmount}, الضريبة=${taxAmount}, الإجمالي=${total}`);
        }

        function updateSaleType() {
            const saleType = document.querySelector('input[name="saleType"]:checked').value;
            currentSaleType = saleType;

            // تطبيق أسعار مختلفة للجملة والمفرد
            cart.forEach(item => {
                const products = JSON.parse(localStorage.getItem('adminProducts')) || [];
                const product = products.find(p => p.id === item.id);
                if (product) {
                    if (saleType === 'wholesale' && product.wholesalePrice) {
                        item.price = parseFloat(product.wholesalePrice);
                    } else {
                        item.price = parseFloat(product.price);
                    }
                }
            });

            updateCartDisplay();
            updateSummary();
        }

        function selectPaymentMethod(method) {
            currentPaymentMethod = method;

            document.querySelectorAll('.payment-btn').forEach(btn => btn.classList.remove('active'));
            document.querySelector(`[data-method="${method}"]`).classList.add('active');

            const paymentAmount = document.getElementById('paymentAmount');
            if (method === 'cash') {
                paymentAmount.style.display = 'block';
            } else if (method === 'mixed') {
                paymentAmount.style.display = 'block';
                // يمكن إضافة واجهة للدفع المختلط
            } else {
                paymentAmount.style.display = 'none';
                calculateChange();
            }
        }

        function calculateChange() {
            const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0) - currentDiscount;
            const totalWithTax = total + (total * currentTax);
            const paidAmount = parseFloat(document.getElementById('paidAmount').value) || 0;
            const change = paidAmount - totalWithTax;

            document.getElementById('changeAmount').textContent = formatCurrency(Math.max(0, change));
        }

        // Professional Discount Functions
        function applyDiscount() {
            const discountAmount = parseFloat(document.getElementById('discountAmount').value) || 0;
            const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);

            // التحقق من صحة مبلغ الخصم
            if (discountAmount > subtotal) {
                showNotification('مبلغ الخصم لا يمكن أن يكون أكبر من المجموع الفرعي', 'error');
                document.getElementById('discountAmount').value = '';
                return;
            }

            currentDiscount = discountAmount;

            // حساب النسبة المئوية المقابلة
            const discountPercent = subtotal > 0 ? (discountAmount / subtotal) * 100 : 0;
            document.getElementById('discountPercent').value = discountPercent.toFixed(1);

            updateSummary();
            console.log(`تم تطبيق خصم: ${discountAmount} دينار (${discountPercent.toFixed(1)}%)`);
        }

        function applyDiscountPercent() {
            const discountPercent = parseFloat(document.getElementById('discountPercent').value) || 0;

            // التحقق من صحة النسبة المئوية
            if (discountPercent < 0 || discountPercent > 100) {
                showNotification('نسبة الخصم يجب أن تكون بين 0% و 100%', 'error');
                document.getElementById('discountPercent').value = '';
                return;
            }

            const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            currentDiscount = (subtotal * discountPercent) / 100;

            document.getElementById('discountAmount').value = currentDiscount.toFixed(0);

            updateSummary();
            console.log(`تم تطبيق خصم: ${discountPercent}% (${currentDiscount.toFixed(0)} دينار)`);
        }

        // مسح الخصم
        function clearDiscount() {
            currentDiscount = 0;
            document.getElementById('discountAmount').value = '';
            document.getElementById('discountPercent').value = '';

            updateSummary();
            showNotification('تم مسح الخصم', 'info');
            console.log('تم مسح الخصم');
        }

        function completeSale() {
            if (cart.length === 0) {
                alert('السلة فارغة');
                return;
            }

            const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0) - currentDiscount;
            const totalWithTax = total + (total * currentTax);

            if (currentPaymentMethod === 'cash') {
                const paidAmount = parseFloat(document.getElementById('paidAmount').value) || 0;
                if (paidAmount < totalWithTax) {
                    alert('المبلغ المدفوع أقل من المطلوب');
                    return;
                }
            }

            // إنشاء الفاتورة
            const invoice = createInvoice();

            // حفظ البيع
            saveSale(invoice);

            // تحديث المخزون
            updateStock();

            // طباعة الفاتورة
            printInvoice(invoice);

            // مسح السلة
            cart = [];
            currentDiscount = 0;
            document.getElementById('discountAmount').value = '';
            document.getElementById('discountPercent').value = '';
            document.getElementById('paidAmount').value = '';
            document.getElementById('customerName').value = '';
            document.getElementById('customerPhone').value = '';

            updateCartDisplay();
            updateSummary();

            alert('تم إتمام البيع بنجاح!');
        }

        function createInvoice() {
            const now = new Date();
            const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            const discountAmount = currentDiscount;
            const taxableAmount = subtotal - discountAmount;
            const taxAmount = taxableAmount * currentTax;
            const total = taxableAmount + taxAmount;

            return {
                id: `INV-${lastInvoiceNumber}`,
                number: lastInvoiceNumber++,
                date: now.toISOString(),
                cashier: { name: 'الكاشير', role: 'cashier', roleText: 'الكاشير', roleIcon: '💰' },
                customer: {
                    name: document.getElementById('customerName').value || 'عميل نقدي',
                    phone: document.getElementById('customerPhone').value || ''
                },
                items: [...cart],
                saleType: currentSaleType,
                paymentMethod: currentPaymentMethod,
                subtotal: subtotal,
                discount: discountAmount,
                tax: taxAmount,
                total: total,
                paidAmount: currentPaymentMethod === 'cash' ? parseFloat(document.getElementById('paidAmount').value) || total : total,
                change: currentPaymentMethod === 'cash' ? Math.max(0, (parseFloat(document.getElementById('paidAmount').value) || 0) - total) : 0
            };
        }

        function saveSale(invoice) {
            const sales = JSON.parse(localStorage.getItem('cashierSales')) || [];
            sales.push(invoice);
            localStorage.setItem('cashierSales', JSON.stringify(sales));
            localStorage.setItem('lastInvoiceNumber', lastInvoiceNumber.toString());
        }

        function updateStock() {
            console.log('📦 تحديث المخزون بعد البيع...');

            // تحميل المنتجات من الموقع الرئيسي
            const products = JSON.parse(localStorage.getItem('adminProducts')) ||
                           JSON.parse(localStorage.getItem('products')) || [];

            const updatedProducts = [];

            cart.forEach(cartItem => {
                const productIndex = products.findIndex(p => p.id === cartItem.id);
                if (productIndex !== -1) {
                    const oldStock = products[productIndex].stock || products[productIndex].quantity || 0;
                    const newStock = Math.max(0, oldStock - cartItem.quantity);

                    // تحديث المخزون
                    products[productIndex].stock = newStock;
                    products[productIndex].quantity = newStock; // مزامنة quantity مع stock
                    products[productIndex].updatedAt = new Date().toISOString();

                    updatedProducts.push({
                        id: cartItem.id,
                        name: cartItem.name,
                        oldStock: oldStock,
                        newStock: newStock,
                        soldQuantity: cartItem.quantity
                    });

                    console.log(`📉 تحديث مخزون ${cartItem.name}: ${oldStock} → ${newStock} (بيع: ${cartItem.quantity})`);
                }
            });

            // حفظ في جميع المواقع للتوافق
            localStorage.setItem('adminProducts', JSON.stringify(products));
            localStorage.setItem('products', JSON.stringify(products));
            localStorage.setItem('cashierProducts', JSON.stringify(products.map(p => normalizeProductData(p))));

            // إرسال إشعار للموقع الإلكتروني بتحديث المنتجات
            notifyMainSiteUpdate(updatedProducts);

            // إعادة تحميل المنتجات لتحديث العرض
            loadProducts();

            console.log(`✅ تم تحديث مخزون ${updatedProducts.length} منتج`);
        }

        // إشعار تحديث المنتجات للموقع الرئيسي
        function notifyMainSiteUpdate(updatedProducts) {
            try {
                // إرسال إشعار للموقع الرئيسي
                window.dispatchEvent(new CustomEvent('productsUpdated', {
                    detail: {
                        source: 'cashier',
                        action: 'stock_update',
                        timestamp: new Date().toISOString(),
                        updatedProducts: updatedProducts
                    }
                }));

                // إرسال رسالة للنوافذ الأخرى
                if (window.opener) {
                    window.opener.postMessage({
                        type: 'productsUpdated',
                        data: {
                            source: 'cashier',
                            action: 'stock_update',
                            updatedProducts: updatedProducts
                        }
                    }, '*');
                }

                console.log('📢 تم إرسال إشعار تحديث المنتجات للموقع الرئيسي');
            } catch (error) {
                console.log('⚠️ تعذر إرسال إشعار التحديث:', error);
            }
        }

        function formatCurrency(amount) {
            return new Intl.NumberFormat('ar-IQ', {
                style: 'currency',
                currency: 'IQD',
                minimumFractionDigits: 0
            }).format(amount);
        }

        // وظائف إضافية
        function searchProducts() {
            const searchTerm = document.getElementById('productSearch').value.toLowerCase();
            const products = JSON.parse(localStorage.getItem('adminProducts')) || [];
            const filteredProducts = products.filter(product =>
                product.nameAr.toLowerCase().includes(searchTerm) ||
                product.nameEn.toLowerCase().includes(searchTerm) ||
                product.barcode?.includes(searchTerm)
            );

            // استخدام displayProducts الجديدة
            displayProducts(filteredProducts);
        }

        function filterProducts(type) {
            document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            if (type === 'all') {
                loadProducts();
            }
        }

        function holdSale() {
            if (cart.length === 0) {
                alert('السلة فارغة');
                return;
            }

            const heldSale = {
                id: Date.now(),
                items: [...cart],
                discount: currentDiscount,
                saleType: currentSaleType,
                timestamp: new Date().toISOString()
            };

            heldSales.push(heldSale);
            localStorage.setItem('heldSales', JSON.stringify(heldSales));

            cart = [];
            currentDiscount = 0;
            updateCartDisplay();
            updateSummary();

            alert('تم تعليق البيع بنجاح');
        }

        function showHeldSales() {
            // عرض المبيعات المعلقة
            const heldSales = JSON.parse(localStorage.getItem('heldSales')) || [];

            if (heldSales.length === 0) {
                showNotification('لا توجد مبيعات معلقة', 'info');
                return;
            }

            let salesList = heldSales.map((sale, index) =>
                `${index + 1}. ${sale.customerName || 'عميل غير محدد'} - ${sale.total} دينار - ${new Date(sale.date).toLocaleString('ar-EG')}`
            ).join('\n');

            showNotification(`المبيعات المعلقة (${heldSales.length}):\n${salesList}`, 'info');
        }

        function showLastSales() {
            // عرض آخر المبيعات
            const sales = JSON.parse(localStorage.getItem('sales')) || [];
            const lastSales = sales.slice(-5); // آخر 5 مبيعات

            if (lastSales.length === 0) {
                showNotification('لا توجد مبيعات سابقة', 'info');
                return;
            }

            let salesList = lastSales.map((sale, index) =>
                `${index + 1}. فاتورة #${sale.invoiceNumber} - ${sale.total} دينار - ${new Date(sale.date).toLocaleString('ar-EG')}`
            ).join('\n');

            showNotification(`آخر المبيعات (${lastSales.length}):\n${salesList}`, 'info');
        }

        function openCashDrawer() {
            showNotification('تم فتح درج النقد 💰', 'success');

            // إضافة صوت إذا كان مفعل
            if (cashierSettings.soundEffects) {
                // يمكن إضافة صوت هنا
                console.log('🔊 صوت فتح الدرج');
            }
        }



        function showReportsModal() {
            showNotification('ميزة التقارير قيد التطوير', 'info');
        }

        // Settings Variables
        let cashierSettings = {
            // General Settings
            cashierName: 'الكاشير',
            storeName: 'متجري',
            storeAddress: '',
            storePhone: '',
            autoSave: true,
            soundEffects: true,

            // Display Settings
            theme: 'default',
            fontSize: 'medium',
            productsPerPage: 24,
            showProductImages: true,
            showProductCodes: false,

            // Printer Settings
            printerType: 'thermal',
            paperSize: '80mm',
            autoPrint: false,
            printLogo: true,
            receiptFooter: 'شكراً لزيارتكم - نتطلع لخدمتكم مرة أخرى',

            // Currency Settings
            mainCurrency: 'IQD',
            currencySymbol: 'دينار',
            enableMultiCurrency: false,
            taxRate: 0,
            includeTax: false,

            // Backup Settings
            autoBackup: false,
            backupTime: '23:00',
            lastBackup: null
        };

        // Show Settings Modal
        function showSettingsModal() {
            document.getElementById('settingsModal').style.display = 'flex';
            loadSettings();
            updateSystemInfo();
        }

        // Hide Settings Modal
        function hideSettings() {
            document.getElementById('settingsModal').style.display = 'none';
        }

        // Show Settings Tab
        function showSettingsTab(tabName) {
            // Hide all panels
            document.querySelectorAll('.settings-panel').forEach(panel => {
                panel.classList.remove('active');
            });

            // Remove active class from all tabs
            document.querySelectorAll('.settings-tab').forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected panel
            document.getElementById(tabName + 'Settings').classList.add('active');

            // Add active class to clicked tab
            event.target.classList.add('active');
        }

        // Load Modern Settings
        function loadModernSettings() {
            const savedSettings = localStorage.getItem('modernSettings');
            if (savedSettings) {
                const parsed = JSON.parse(savedSettings);
                modernSettings = { ...modernSettings, ...parsed };
            }

            // Also load legacy settings for compatibility
            const legacySettings = localStorage.getItem('cashierSettings');
            if (legacySettings) {
                cashierSettings = { ...cashierSettings, ...JSON.parse(legacySettings) };
            }

            // Populate form fields
            populateModernSettingsForm();
        }

        // Load Settings from localStorage (Legacy)
        function loadSettings() {
            loadModernSettings();
        }

        // Populate Modern Settings Form
        function populateModernSettingsForm() {
            try {
                // General Settings
                const general = modernSettings.general || {};
                setElementValue('storeName', general.storeName || cashierSettings.storeName);
                setElementValue('storeAddress', general.storeAddress || cashierSettings.storeAddress);
                setElementValue('storePhone', general.storePhone || cashierSettings.storePhone);
                setElementValue('cashierName', general.cashierName || cashierSettings.cashierName);
                setElementValue('cashierCode', general.cashierCode || 'CSH001');
                setElementChecked('autoSave', general.autoSave !== undefined ? general.autoSave : cashierSettings.autoSave);
                setElementChecked('soundEffects', general.soundEffects !== undefined ? general.soundEffects : cashierSettings.soundEffects);
                setElementChecked('notifications', general.notifications !== undefined ? general.notifications : true);

                // Display Settings
                const display = modernSettings.display || {};
                setElementValue('theme', display.theme || cashierSettings.theme);
                setElementValue('fontSize', display.fontSize || cashierSettings.fontSize);
                setElementValue('language', display.language || 'ar');
                setElementValue('productsPerPage', display.productsPerPage || cashierSettings.productsPerPage);
                setElementChecked('showProductImages', display.showProductImages !== undefined ? display.showProductImages : cashierSettings.showProductImages);
                setElementChecked('showProductCodes', display.showProductCodes !== undefined ? display.showProductCodes : cashierSettings.showProductCodes);
                setElementChecked('showStock', display.showStock !== undefined ? display.showStock : true);
                setElementChecked('compactMode', display.compactMode !== undefined ? display.compactMode : false);
                setElementChecked('showQuickActions', display.showQuickActions !== undefined ? display.showQuickActions : true);

                // Printer Settings
                const printer = modernSettings.printer || {};
                setElementValue('printerType', printer.printerType || cashierSettings.printerType);
                setElementValue('paperSize', printer.paperSize || cashierSettings.paperSize);
                setElementValue('printQuality', printer.printQuality || 'normal');
                setElementValue('receiptHeader', printer.receiptHeader || '');
                setElementValue('receiptFooter', printer.receiptFooter || cashierSettings.receiptFooter);
                setElementChecked('printLogo', printer.printLogo !== undefined ? printer.printLogo : cashierSettings.printLogo);
                setElementChecked('printBarcode', printer.printBarcode !== undefined ? printer.printBarcode : false);
                setElementChecked('autoPrint', printer.autoPrint !== undefined ? printer.autoPrint : cashierSettings.autoPrint);
                setElementChecked('printCopy', printer.printCopy !== undefined ? printer.printCopy : false);
                setElementChecked('printDetails', printer.printDetails !== undefined ? printer.printDetails : true);
                setElementValue('copies', printer.copies || 1);

                // System Settings
                const system = modernSettings.system || {};
                setElementValue('mainCurrency', system.mainCurrency || cashierSettings.mainCurrency);
                setElementValue('currencySymbol', system.currencySymbol || cashierSettings.currencySymbol);
                setElementValue('taxRate', system.taxRate !== undefined ? system.taxRate : cashierSettings.taxRate);
                setElementChecked('enableMultiCurrency', system.enableMultiCurrency !== undefined ? system.enableMultiCurrency : cashierSettings.enableMultiCurrency);
                setElementChecked('includeTax', system.includeTax !== undefined ? system.includeTax : cashierSettings.includeTax);
                setElementChecked('autoBackup', system.autoBackup !== undefined ? system.autoBackup : cashierSettings.autoBackup);
                setElementValue('backupTime', system.backupTime || cashierSettings.backupTime);

            } catch (error) {
                console.error('خطأ في تحميل الإعدادات:', error);
                showNotification('خطأ في تحميل الإعدادات', 'error');
            }
        }

        // Helper functions for setting form values
        function setElementValue(id, value) {
            const element = document.getElementById(id);
            if (element) {
                element.value = value;
            }
        }

        function setElementChecked(id, checked) {
            const element = document.getElementById(id);
            if (element) {
                element.checked = checked;
            }
        }

        // Legacy function for compatibility
        function populateSettingsForm() {
            populateModernSettingsForm();
        }

        // Save Settings
        function saveSettings() {
            try {
                // Collect form data
                cashierSettings = {
                    // General Settings
                    cashierName: document.getElementById('cashierName').value || 'الكاشير',
                    storeName: document.getElementById('storeName').value || 'متجري',
                    storeAddress: document.getElementById('storeAddress').value,
                    storePhone: document.getElementById('storePhone').value,
                    autoSave: document.getElementById('autoSave').checked,
                    soundEffects: document.getElementById('soundEffects').checked,

                    // Display Settings
                    theme: document.getElementById('theme').value,
                    fontSize: document.getElementById('fontSize').value,
                    productsPerPage: parseInt(document.getElementById('productsPerPage').value),
                    showProductImages: document.getElementById('showProductImages').checked,
                    showProductCodes: document.getElementById('showProductCodes').checked,

                    // Printer Settings
                    printerType: document.getElementById('printerType').value,
                    paperSize: document.getElementById('paperSize').value,
                    autoPrint: document.getElementById('autoPrint').checked,
                    printLogo: document.getElementById('printLogo').checked,
                    receiptFooter: document.getElementById('receiptFooter').value,

                    // Currency Settings
                    mainCurrency: document.getElementById('mainCurrency').value,
                    currencySymbol: document.getElementById('currencySymbol').value,
                    enableMultiCurrency: document.getElementById('enableMultiCurrency').checked,
                    taxRate: parseFloat(document.getElementById('taxRate').value) || 0,
                    includeTax: document.getElementById('includeTax').checked,

                    // Backup Settings
                    autoBackup: document.getElementById('autoBackup').checked,
                    backupTime: document.getElementById('backupTime').value,
                    lastBackup: cashierSettings.lastBackup
                };

                // Save to localStorage
                localStorage.setItem('cashierSettings', JSON.stringify(cashierSettings));

                // Apply settings immediately
                applySettings();

                showNotification('تم حفظ الإعدادات بنجاح', 'success');

            } catch (error) {
                console.error('خطأ في حفظ الإعدادات:', error);
                showNotification('حدث خطأ في حفظ الإعدادات', 'error');
            }
        }

        // Apply Settings
        function applySettings() {
            // Apply theme
            applyTheme(cashierSettings.theme);

            // Apply font size
            applyFontSize(cashierSettings.fontSize);

            // Apply product display settings
            applyProductDisplaySettings();

            // Update cashier name in header
            updateCashierName();

            console.log('تم تطبيق الإعدادات بنجاح');
        }

        // Apply Theme
        function applyTheme(theme) {
            const body = document.body;

            // Remove existing theme classes
            body.classList.remove('theme-default', 'theme-dark', 'theme-light', 'theme-blue', 'theme-green');

            // Add new theme class
            body.classList.add(`theme-${theme}`);

            // Update CSS variables based on theme
            const root = document.documentElement;

            switch (theme) {
                case 'dark':
                    root.style.setProperty('--primary-color', '#2c3e50');
                    root.style.setProperty('--secondary-color', '#34495e');
                    root.style.setProperty('--text-color', '#ecf0f1');
                    root.style.setProperty('--bg-color', '#1a1a1a');
                    break;
                case 'light':
                    root.style.setProperty('--primary-color', '#3498db');
                    root.style.setProperty('--secondary-color', '#ecf0f1');
                    root.style.setProperty('--text-color', '#2c3e50');
                    root.style.setProperty('--bg-color', '#ffffff');
                    break;
                case 'blue':
                    root.style.setProperty('--primary-color', '#2980b9');
                    root.style.setProperty('--secondary-color', '#3498db');
                    root.style.setProperty('--text-color', '#ecf0f1');
                    root.style.setProperty('--bg-color', '#1e3a5f');
                    break;
                case 'green':
                    root.style.setProperty('--primary-color', '#27ae60');
                    root.style.setProperty('--secondary-color', '#2ecc71');
                    root.style.setProperty('--text-color', '#ecf0f1');
                    root.style.setProperty('--bg-color', '#1e5f3a');
                    break;
                default:
                    root.style.setProperty('--primary-color', '#2c5530');
                    root.style.setProperty('--secondary-color', '#34495e');
                    root.style.setProperty('--text-color', '#333');
                    root.style.setProperty('--bg-color', '#f8f9fa');
            }
        }

        // Apply Font Size
        function applyFontSize(fontSize) {
            const root = document.documentElement;

            switch (fontSize) {
                case 'small':
                    root.style.setProperty('--base-font-size', '14px');
                    break;
                case 'large':
                    root.style.setProperty('--base-font-size', '18px');
                    break;
                case 'xlarge':
                    root.style.setProperty('--base-font-size', '20px');
                    break;
                default: // medium
                    root.style.setProperty('--base-font-size', '16px');
            }
        }

        // Apply Product Display Settings
        function applyProductDisplaySettings() {
            const productsGrid = document.querySelector('.products-grid');
            if (productsGrid) {
                // Apply products per page (will be used in loadProducts)
                loadProducts();

                // Show/hide product images
                const productImages = document.querySelectorAll('.product-image img');
                productImages.forEach(img => {
                    img.style.display = cashierSettings.showProductImages ? 'block' : 'none';
                });

                // Show/hide product codes
                const productCodes = document.querySelectorAll('.product-code');
                productCodes.forEach(code => {
                    code.style.display = cashierSettings.showProductCodes ? 'block' : 'none';
                });
            }
        }

        // Update Cashier Name
        function updateCashierName() {
            const cashierNameElements = document.querySelectorAll('.cashier-name');
            cashierNameElements.forEach(element => {
                element.textContent = cashierSettings.cashierName;
            });
        }

        // Load Default Settings
        function loadDefaultSettings() {
            if (confirm('هل تريد استعادة الإعدادات الافتراضية؟ سيتم فقدان الإعدادات الحالية.')) {
                // Reset to default values
                cashierSettings = {
                    cashierName: 'الكاشير',
                    storeName: 'متجري',
                    storeAddress: '',
                    storePhone: '',
                    autoSave: true,
                    soundEffects: true,
                    theme: 'default',
                    fontSize: 'medium',
                    productsPerPage: 24,
                    showProductImages: true,
                    showProductCodes: false,
                    printerType: 'thermal',
                    paperSize: '80mm',
                    autoPrint: false,
                    printLogo: true,
                    receiptFooter: 'شكراً لزيارتكم - نتطلع لخدمتكم مرة أخرى',
                    mainCurrency: 'IQD',
                    currencySymbol: 'دينار',
                    enableMultiCurrency: false,
                    taxRate: 0,
                    includeTax: false,
                    autoBackup: false,
                    backupTime: '23:00',
                    lastBackup: null
                };

                populateSettingsForm();
                showNotification('تم استعادة الإعدادات الافتراضية', 'success');
            }
        }

        // Create Backup
        function createBackup() {
            try {
                const backupData = {
                    timestamp: new Date().toISOString(),
                    version: '1.0',
                    settings: cashierSettings,
                    products: JSON.parse(localStorage.getItem('adminProducts') || '[]'),
                    sales: JSON.parse(localStorage.getItem('lastSales') || '[]'),
                    heldSales: JSON.parse(localStorage.getItem('heldSales') || '[]'),
                    coupons: JSON.parse(localStorage.getItem('mainSiteCoupons') || '{}'),
                    calculatorHistory: JSON.parse(localStorage.getItem('calculatorHistory') || '[]')
                };

                const dataStr = JSON.stringify(backupData, null, 2);
                const dataBlob = new Blob([dataStr], { type: 'application/json' });

                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `cashier_backup_${new Date().toISOString().split('T')[0]}.json`;
                link.click();

                // Update last backup time
                cashierSettings.lastBackup = new Date().toISOString();
                localStorage.setItem('cashierSettings', JSON.stringify(cashierSettings));
                updateSystemInfo();

                showNotification('تم إنشاء النسخة الاحتياطية بنجاح', 'success');

            } catch (error) {
                console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
                showNotification('حدث خطأ في إنشاء النسخة الاحتياطية', 'error');
            }
        }

        // Restore Backup
        function restoreBackup() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';

            input.onchange = function(event) {
                const file = event.target.files[0];
                if (!file) return;

                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const backupData = JSON.parse(e.target.result);

                        if (!backupData.version || !backupData.settings) {
                            throw new Error('ملف النسخة الاحتياطية غير صحيح');
                        }

                        if (confirm('هل تريد استعادة النسخة الاحتياطية؟ سيتم استبدال جميع البيانات الحالية.')) {
                            // Restore settings
                            cashierSettings = backupData.settings;
                            localStorage.setItem('cashierSettings', JSON.stringify(cashierSettings));

                            // Restore data
                            if (backupData.products) {
                                localStorage.setItem('adminProducts', JSON.stringify(backupData.products));
                                localStorage.setItem('products', JSON.stringify(backupData.products));
                            }

                            if (backupData.sales) {
                                localStorage.setItem('lastSales', JSON.stringify(backupData.sales));
                            }

                            if (backupData.heldSales) {
                                localStorage.setItem('heldSales', JSON.stringify(backupData.heldSales));
                            }

                            if (backupData.coupons) {
                                localStorage.setItem('mainSiteCoupons', JSON.stringify(backupData.coupons));
                            }

                            if (backupData.calculatorHistory) {
                                localStorage.setItem('calculatorHistory', JSON.stringify(backupData.calculatorHistory));
                            }

                            // Apply restored settings
                            populateSettingsForm();
                            applySettings();
                            updateSystemInfo();
                            loadProducts();

                            showNotification('تم استعادة النسخة الاحتياطية بنجاح', 'success');
                        }

                    } catch (error) {
                        console.error('خطأ في استعادة النسخة الاحتياطية:', error);
                        showNotification('حدث خطأ في استعادة النسخة الاحتياطية', 'error');
                    }
                };

                reader.readAsText(file);
            };

            input.click();
        }

        // Reset Settings
        function resetSettings() {
            if (confirm('هل تريد إعادة تعيين جميع الإعدادات والبيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
                if (confirm('تأكيد: سيتم حذف جميع البيانات نهائياً. هل تريد المتابعة؟')) {
                    // Clear all localStorage
                    const keysToKeep = ['adminProducts', 'products']; // Keep products
                    const allKeys = Object.keys(localStorage);

                    allKeys.forEach(key => {
                        if (!keysToKeep.includes(key)) {
                            localStorage.removeItem(key);
                        }
                    });

                    // Reset settings to default
                    loadDefaultSettings();

                    showNotification('تم إعادة تعيين النظام بنجاح', 'success');

                    // Reload page after 2 seconds
                    setTimeout(() => {
                        location.reload();
                    }, 2000);
                }
            }
        }

        // Export Settings
        function exportSettings() {
            try {
                const settingsData = {
                    timestamp: new Date().toISOString(),
                    version: '1.0',
                    settings: cashierSettings
                };

                const dataStr = JSON.stringify(settingsData, null, 2);
                const dataBlob = new Blob([dataStr], { type: 'application/json' });

                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `cashier_settings_${new Date().toISOString().split('T')[0]}.json`;
                link.click();

                showNotification('تم تصدير الإعدادات بنجاح', 'success');

            } catch (error) {
                console.error('خطأ في تصدير الإعدادات:', error);
                showNotification('حدث خطأ في تصدير الإعدادات', 'error');
            }
        }

        // Import Settings
        function importSettings() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';

            input.onchange = function(event) {
                const file = event.target.files[0];
                if (!file) return;

                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const settingsData = JSON.parse(e.target.result);

                        if (!settingsData.version || !settingsData.settings) {
                            throw new Error('ملف الإعدادات غير صحيح');
                        }

                        if (confirm('هل تريد استيراد هذه الإعدادات؟ سيتم استبدال الإعدادات الحالية.')) {
                            cashierSettings = { ...cashierSettings, ...settingsData.settings };
                            populateSettingsForm();
                            showNotification('تم استيراد الإعدادات بنجاح', 'success');
                        }

                    } catch (error) {
                        console.error('خطأ في استيراد الإعدادات:', error);
                        showNotification('حدث خطأ في استيراد الإعدادات', 'error');
                    }
                };

                reader.readAsText(file);
            };

            input.click();
        }

        // Update System Info
        function updateSystemInfo() {
            try {
                const products = JSON.parse(localStorage.getItem('adminProducts') || '[]');
                const sales = JSON.parse(localStorage.getItem('lastSales') || '[]');

                // Calculate data size
                let totalSize = 0;
                Object.keys(localStorage).forEach(key => {
                    totalSize += localStorage.getItem(key).length;
                });

                const dataSizeKB = (totalSize / 1024).toFixed(2);

                // Update display
                document.getElementById('productsCount').textContent = products.length;
                document.getElementById('salesCount').textContent = sales.length;
                document.getElementById('dataSize').textContent = `${dataSizeKB} KB`;

                const lastBackupDate = cashierSettings.lastBackup
                    ? new Date(cashierSettings.lastBackup).toLocaleString('ar-SA')
                    : 'لا توجد';
                document.getElementById('lastBackup').textContent = lastBackupDate;

            } catch (error) {
                console.error('خطأ في تحديث معلومات النظام:', error);
            }
        }

        // Auto Save Function
        function autoSaveData() {
            if (cashierSettings.autoSave) {
                try {
                    // Save current cart and sales data
                    localStorage.setItem('currentCart', JSON.stringify(cart));
                    localStorage.setItem('lastActivity', new Date().toISOString());

                    console.log('تم الحفظ التلقائي للبيانات');
                } catch (error) {
                    console.error('خطأ في الحفظ التلقائي:', error);
                }
            }
        }

        // Play Sound Effect
        function playSound(type) {
            if (cashierSettings.soundEffects) {
                try {
                    const audio = new Audio();

                    switch (type) {
                        case 'success':
                            // Success sound (high pitch beep)
                            audio.src = 'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT';
                            break;
                        case 'error':
                            // Error sound (low pitch beep)
                            audio.src = 'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT';
                            break;
                        case 'click':
                            // Click sound
                            audio.src = 'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT';
                            break;
                    }

                    audio.volume = 0.3;
                    audio.play().catch(() => {
                        // Ignore audio play errors
                    });
                } catch (error) {
                    // Ignore sound errors
                }
            }
        }

        // Initialize Settings on Page Load
        function initializeSettings() {
            loadSettings();
            applySettings();

            // Set up auto-save interval
            if (cashierSettings.autoSave) {
                setInterval(autoSaveData, 60000); // Auto-save every minute
            }

            // Set up auto-backup if enabled
            if (cashierSettings.autoBackup) {
                checkAutoBackup();
            }
        }

        // Check Auto Backup
        function checkAutoBackup() {
            const now = new Date();
            const backupTime = cashierSettings.backupTime.split(':');
            const backupHour = parseInt(backupTime[0]);
            const backupMinute = parseInt(backupTime[1]);

            const lastBackup = cashierSettings.lastBackup ? new Date(cashierSettings.lastBackup) : null;
            const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

            // Check if backup is needed
            if (!lastBackup || lastBackup < today) {
                if (now.getHours() >= backupHour && now.getMinutes() >= backupMinute) {
                    createBackup();
                }
            }
        }

        // Enhanced notification with sound
        function showNotification(message, type = 'info') {
            // Play sound
            playSound(type);

            // Create notification element
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#27ae60' : type === 'error' ? '#e74c3c' : '#3498db'};
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                box-shadow: 0 5px 15px rgba(0,0,0,0.3);
                z-index: 10001;
                animation: slideInRight 0.3s ease;
                max-width: 300px;
                font-weight: 600;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            // Remove after 3 seconds
            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        function showCouponsModal() {
            showNotification('ميزة الكوبونات قيد التطوير', 'info');
        }

        // Modern Settings Functions
        function saveAllSettings() {
            try {
                // Collect form data
                const newSettings = {
                    general: {
                        storeName: getElementValue('storeName') || 'متجر الأمانة',
                        storeAddress: getElementValue('storeAddress') || '',
                        storePhone: getElementValue('storePhone') || '',
                        cashierName: getElementValue('cashierName') || 'أحمد علي',
                        cashierCode: getElementValue('cashierCode') || 'CSH001',
                        autoSave: getElementChecked('autoSave'),
                        soundEffects: getElementChecked('soundEffects'),
                        notifications: getElementChecked('notifications')
                    },

                    display: {
                        theme: getElementValue('theme') || 'light',
                        fontSize: getElementValue('fontSize') || 'medium',
                        language: getElementValue('language') || 'ar',
                        productsPerPage: parseInt(getElementValue('productsPerPage')) || 24,
                        showProductImages: getElementChecked('showProductImages'),
                        showProductCodes: getElementChecked('showProductCodes'),
                        showStock: getElementChecked('showStock'),
                        compactMode: getElementChecked('compactMode'),
                        showQuickActions: getElementChecked('showQuickActions')
                    },

                    printer: {
                        printerType: getElementValue('printerType') || 'thermal',
                        paperSize: getElementValue('paperSize') || '80mm',
                        printQuality: getElementValue('printQuality') || 'normal',
                        receiptHeader: getElementValue('receiptHeader') || '',
                        receiptFooter: getElementValue('receiptFooter') || 'شكراً لزيارتكم - نتطلع لخدمتكم مرة أخرى',
                        printLogo: getElementChecked('printLogo'),
                        printBarcode: getElementChecked('printBarcode'),
                        autoPrint: getElementChecked('autoPrint'),
                        printCopy: getElementChecked('printCopy'),
                        printDetails: getElementChecked('printDetails'),
                        copies: parseInt(getElementValue('copies')) || 1
                    },

                    system: {
                        mainCurrency: getElementValue('mainCurrency') || 'IQD',
                        currencySymbol: getElementValue('currencySymbol') || 'د.ع',
                        taxRate: parseFloat(getElementValue('taxRate')) || 0,
                        enableMultiCurrency: getElementChecked('enableMultiCurrency'),
                        includeTax: getElementChecked('includeTax'),
                        autoBackup: getElementChecked('autoBackup'),
                        backupTime: getElementValue('backupTime') || '23:00',
                        lastBackup: modernSettings.system?.lastBackup || null
                    }
                };

                // Update global settings
                modernSettings = newSettings;

                // Update legacy settings for compatibility
                cashierSettings = {
                    ...cashierSettings,
                    ...newSettings.general,
                    ...newSettings.display,
                    ...newSettings.printer,
                    ...newSettings.system
                };

                // Save to localStorage
                localStorage.setItem('modernSettings', JSON.stringify(modernSettings));
                localStorage.setItem('cashierSettings', JSON.stringify(cashierSettings));

                // Apply settings immediately
                applyModernSettings();

                // Update last saved time
                const lastSavedElement = document.getElementById('lastSaved');
                if (lastSavedElement) {
                    lastSavedElement.textContent = new Date().toLocaleString('ar-EG');
                }

                showNotification('تم حفظ الإعدادات بنجاح', 'success');
                console.log('✅ تم حفظ الإعدادات الحديثة');

            } catch (error) {
                console.error('❌ خطأ في حفظ الإعدادات:', error);
                showNotification('خطأ في حفظ الإعدادات', 'error');
            }
        }

        // Apply Modern Settings
        function applyModernSettings() {
            try {
                // Apply theme
                applyTheme(modernSettings.display?.theme || 'light');

                // Apply font size
                applyFontSize(modernSettings.display?.fontSize || 'medium');

                // Apply product display settings
                applyProductDisplaySettings();

                // Update cashier name in header
                updateCashierName();

                // Apply compact mode if enabled
                if (modernSettings.display?.compactMode) {
                    document.body.classList.add('compact-mode');
                } else {
                    document.body.classList.remove('compact-mode');
                }

                // Show/hide quick actions
                const quickActions = document.querySelector('.quick-actions');
                if (quickActions) {
                    quickActions.style.display = modernSettings.display?.showQuickActions ? 'flex' : 'none';
                }

                console.log('تم تطبيق الإعدادات الحديثة بنجاح');
            } catch (error) {
                console.error('خطأ في تطبيق الإعدادات:', error);
            }
        }

        // Reset to Defaults
        function resetToDefaults() {
            if (confirm('هل تريد إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟')) {
                // Reset modern settings
                modernSettings = {
                    general: {
                        storeName: 'متجر الأمانة',
                        storeAddress: 'شارع الحبيبية، بغداد، العراق',
                        storePhone: '+964 XXX XXX XXXX',
                        cashierName: 'أحمد علي',
                        cashierCode: 'CSH001',
                        autoSave: true,
                        soundEffects: true,
                        notifications: true
                    },
                    display: {
                        theme: 'light',
                        fontSize: 'medium',
                        language: 'ar',
                        productsPerPage: 24,
                        showProductImages: true,
                        showProductCodes: true,
                        showStock: true,
                        compactMode: false,
                        showQuickActions: true
                    },
                    printer: {
                        printerType: 'thermal',
                        paperSize: '80mm',
                        printQuality: 'normal',
                        receiptHeader: '',
                        receiptFooter: 'شكراً لزيارتكم - نتطلع لخدمتكم مرة أخرى',
                        printLogo: true,
                        printBarcode: false,
                        autoPrint: false,
                        printCopy: false,
                        printDetails: true,
                        copies: 1
                    },
                    system: {
                        mainCurrency: 'IQD',
                        currencySymbol: 'د.ع',
                        taxRate: 0,
                        enableMultiCurrency: false,
                        includeTax: false,
                        autoBackup: false,
                        backupTime: '23:00',
                        lastBackup: null
                    }
                };

                // Update legacy settings
                cashierSettings = {
                    ...cashierSettings,
                    ...modernSettings.general,
                    ...modernSettings.display,
                    ...modernSettings.printer,
                    ...modernSettings.system
                };

                // Save and apply
                localStorage.setItem('modernSettings', JSON.stringify(modernSettings));
                localStorage.setItem('cashierSettings', JSON.stringify(cashierSettings));

                populateModernSettingsForm();
                applyModernSettings();

                showNotification('تم إعادة تعيين الإعدادات بنجاح', 'success');
            }
        }

        // Additional utility functions
        function resetAllData() {
            if (confirm('هل تريد مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه!')) {
                if (confirm('تأكيد: سيتم حذف جميع البيانات نهائياً. هل تريد المتابعة؟')) {
                    localStorage.clear();
                    showNotification('تم مسح جميع البيانات', 'success');
                    setTimeout(() => location.reload(), 2000);
                }
            }
        }

        function exportData() {
            try {
                const allData = {
                    timestamp: new Date().toISOString(),
                    version: '2.0',
                    modernSettings: modernSettings,
                    legacySettings: cashierSettings,
                    products: JSON.parse(localStorage.getItem('adminProducts') || '[]'),
                    sales: JSON.parse(localStorage.getItem('lastSales') || '[]'),
                    heldSales: JSON.parse(localStorage.getItem('heldSales') || '[]'),
                    coupons: JSON.parse(localStorage.getItem('mainSiteCoupons') || '{}')
                };

                const dataStr = JSON.stringify(allData, null, 2);
                const dataBlob = new Blob([dataStr], { type: 'application/json' });

                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `cashier_data_export_${new Date().toISOString().split('T')[0]}.json`;
                link.click();

                showNotification('تم تصدير البيانات بنجاح', 'success');
            } catch (error) {
                console.error('خطأ في تصدير البيانات:', error);
                showNotification('خطأ في تصدير البيانات', 'error');
            }
        }

        // Barcode Scanner Variables
        let barcodeStream = null;
        let barcodeScanner = null;
        let isScanning = false;
        let barcodeHistory = JSON.parse(localStorage.getItem('barcodeHistory')) || [];
        let currentCamera = 0;
        let availableCameras = [];
        let flashlightOn = false;

        // Handle Barcode Input
        function handleBarcodeInput(event) {
            const input = event.target;
            const barcode = input.value.trim();

            // تحديث حالة الباركود
            updateBarcodeStatus('جاري البحث...', 'scanning');

            if (event.key === 'Enter' && barcode) {
                processBarcodeInput(barcode);
                input.value = '';
            } else if (barcode.length >= 8) {
                // البحث التلقائي عند إدخال 8 أرقام أو أكثر
                setTimeout(() => {
                    if (input.value.trim() === barcode) {
                        processBarcodeInput(barcode);
                        input.value = '';
                    }
                }, 500);
            } else if (barcode === '') {
                updateBarcodeStatus('جاهز لمسح الباركود', 'ready');
            }
        }

        // Process Barcode Input
        function processBarcodeInput(barcode) {
            console.log('🔍 معالجة الباركود:', barcode);

            // إضافة تأثير بصري للمعالجة
            const barcodeSection = document.querySelector('.barcode-section');
            barcodeSection.style.transform = 'scale(1.02)';
            barcodeSection.style.boxShadow = '0 12px 35px rgba(102, 126, 234, 0.25)';

            // البحث عن المنتج بالباركود
            const products = JSON.parse(localStorage.getItem('adminProducts')) || [];
            const product = products.find(p =>
                p.barcode === barcode ||
                p.id === barcode ||
                p.nameAr?.includes(barcode) ||
                p.name?.includes(barcode)
            );

            if (product) {
                // تم العثور على المنتج
                addToCart(product.id);
                updateBarcodeStatus(`تم إضافة: ${product.nameAr || product.name}`, 'success');
                addToBarcodeHistory(barcode, product.nameAr || product.name, true);
                playSound('success');

                // تأثير نجاح
                showSuccessEffect();

                // إعادة تعيين الحالة بعد 3 ثوان
                setTimeout(() => {
                    updateBarcodeStatus('جاهز لمسح الباركود', 'ready');
                    resetBarcodeSection();
                }, 3000);

            } else {
                // لم يتم العثور على المنتج
                updateBarcodeStatus('المنتج غير موجود', 'error');
                addToBarcodeHistory(barcode, 'غير موجود', false);
                playSound('error');

                // تأثير خطأ
                showErrorEffect();

                // عرض خيارات إضافية
                showBarcodeNotFoundOptions(barcode);

                // إعادة تعيين الحالة بعد 5 ثوان
                setTimeout(() => {
                    updateBarcodeStatus('جاهز لمسح الباركود', 'ready');
                    resetBarcodeSection();
                }, 5000);
            }
        }

        // Show Success Effect
        function showSuccessEffect() {
            const barcodeSection = document.querySelector('.barcode-section');
            barcodeSection.style.background = 'linear-gradient(145deg, rgba(39, 174, 96, 0.1), rgba(46, 204, 113, 0.1))';
            barcodeSection.style.borderColor = 'rgba(39, 174, 96, 0.3)';

            // إضافة تأثير الجسيمات
            createParticleEffect('success');
        }

        // Show Error Effect
        function showErrorEffect() {
            const barcodeSection = document.querySelector('.barcode-section');
            barcodeSection.style.background = 'linear-gradient(145deg, rgba(231, 76, 60, 0.1), rgba(192, 57, 43, 0.1))';
            barcodeSection.style.borderColor = 'rgba(231, 76, 60, 0.3)';
            barcodeSection.style.animation = 'shake 0.5s ease-in-out';

            // إضافة تأثير الاهتزاز
            setTimeout(() => {
                barcodeSection.style.animation = '';
            }, 500);
        }

        // Reset Barcode Section
        function resetBarcodeSection() {
            const barcodeSection = document.querySelector('.barcode-section');
            barcodeSection.style.transform = '';
            barcodeSection.style.boxShadow = '';
            barcodeSection.style.background = '';
            barcodeSection.style.borderColor = '';
        }

        // Create Particle Effect
        function createParticleEffect(type) {
            const colors = type === 'success' ? ['#27ae60', '#2ecc71', '#58d68d'] : ['#e74c3c', '#c0392b', '#ec7063'];
            const container = document.querySelector('.barcode-section');

            for (let i = 0; i < 15; i++) {
                const particle = document.createElement('div');
                particle.style.cssText = `
                    position: absolute;
                    width: 6px;
                    height: 6px;
                    background: ${colors[Math.floor(Math.random() * colors.length)]};
                    border-radius: 50%;
                    pointer-events: none;
                    z-index: 1000;
                    top: 50%;
                    left: 50%;
                    animation: particle${type} 1s ease-out forwards;
                `;

                container.appendChild(particle);

                setTimeout(() => {
                    if (particle.parentNode) {
                        particle.parentNode.removeChild(particle);
                    }
                }, 1000);
            }
        }

        // Update Barcode Status
        function updateBarcodeStatus(text, status) {
            const statusElement = document.getElementById('barcodeStatus');
            const statusText = statusElement.querySelector('.status-text');
            const statusIndicator = statusElement.querySelector('.status-indicator');

            // إضافة تأثير الانتقال
            statusElement.classList.add('active');
            setTimeout(() => statusElement.classList.remove('active'), 600);

            // تحديث النص مع تأثير
            statusText.style.opacity = '0';
            setTimeout(() => {
                statusText.textContent = text;
                statusText.style.opacity = '1';
            }, 150);

            // تحديث المؤشر
            statusIndicator.className = `status-indicator ${status}`;

            // إضافة أيقونة حسب الحالة
            const icon = getStatusIcon(status);
            if (icon) {
                statusText.innerHTML = `${icon} ${text}`;
            }
        }

        // Get Status Icon
        function getStatusIcon(status) {
            const icons = {
                'ready': '🟢',
                'scanning': '🔍',
                'success': '✅',
                'error': '❌'
            };
            return icons[status] || '';
        }

        // Add to Barcode History
        function addToBarcodeHistory(barcode, productName, success) {
            const historyItem = {
                barcode: barcode,
                productName: productName,
                success: success,
                timestamp: new Date().toISOString(),
                date: new Date().toLocaleDateString('ar-SA'),
                time: new Date().toLocaleTimeString('ar-SA')
            };

            barcodeHistory.unshift(historyItem);

            // الاحتفاظ بآخر 100 مسح فقط
            if (barcodeHistory.length > 100) {
                barcodeHistory = barcodeHistory.slice(0, 100);
            }

            localStorage.setItem('barcodeHistory', JSON.stringify(barcodeHistory));
            updateRecentScans();
        }

        // Show Barcode Not Found Options
        function showBarcodeNotFoundOptions(barcode) {
            const options = confirm(
                `لم يتم العثور على منتج بالباركود: ${barcode}\n\n` +
                'هل تريد:\n' +
                '• موافق: البحث في جميع المنتجات\n' +
                '• إلغاء: تجاهل الباركود'
            );

            if (options) {
                // البحث في جميع المنتجات
                document.getElementById('productSearch').value = barcode;
                searchProducts();
            }
        }

        // Start Barcode Scanner
        async function startBarcodeScanner() {
            try {
                // طلب إذن الكاميرا
                await requestCameraPermission();

                // عرض نافذة المسح
                document.getElementById('barcodeScannerModal').style.display = 'flex';

                // بدء تشغيل الكاميرا
                await initializeCamera();

                updateBarcodeStatus('الكاميرا نشطة - جاهز للمسح', 'scanning');

            } catch (error) {
                console.error('خطأ في تشغيل مسح الباركود:', error);
                updateBarcodeStatus('خطأ في تشغيل الكاميرا', 'error');
                showNotification('تعذر تشغيل الكاميرا. تحقق من الأذونات.', 'error');
            }
        }

        // Request Camera Permission
        async function requestCameraPermission() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        facingMode: 'environment' // الكاميرا الخلفية
                    }
                });

                // إيقاف الدفق المؤقت
                stream.getTracks().forEach(track => track.stop());

                return true;
            } catch (error) {
                throw new Error('تم رفض إذن الكاميرا أو الكاميرا غير متوفرة');
            }
        }

        // Initialize Camera
        async function initializeCamera() {
            try {
                // الحصول على قائمة الكاميرات المتاحة
                const devices = await navigator.mediaDevices.enumerateDevices();
                availableCameras = devices.filter(device => device.kind === 'videoinput');

                if (availableCameras.length === 0) {
                    throw new Error('لا توجد كاميرات متاحة');
                }

                // تشغيل الكاميرا
                const constraints = {
                    video: {
                        deviceId: availableCameras[currentCamera]?.deviceId,
                        facingMode: 'environment',
                        width: { ideal: 1280 },
                        height: { ideal: 720 }
                    }
                };

                barcodeStream = await navigator.mediaDevices.getUserMedia(constraints);
                const video = document.getElementById('barcodeVideo');
                video.srcObject = barcodeStream;

                // بدء المسح التلقائي
                startContinuousScanning();

            } catch (error) {
                console.error('خطأ في تهيئة الكاميرا:', error);
                throw error;
            }
        }

        // Start Continuous Scanning with QuaggaJS
        function startContinuousScanning() {
            if (isScanning) return;

            isScanning = true;

            // تكوين QuaggaJS
            Quagga.init({
                inputStream: {
                    name: "Live",
                    type: "LiveStream",
                    target: document.querySelector('#barcodeVideo'),
                    constraints: {
                        width: 640,
                        height: 480,
                        facingMode: "environment"
                    }
                },
                locator: {
                    patchSize: "medium",
                    halfSample: true
                },
                numOfWorkers: 2,
                frequency: 10,
                decoder: {
                    readers: [
                        "code_128_reader",
                        "ean_reader",
                        "ean_8_reader",
                        "code_39_reader",
                        "code_39_vin_reader",
                        "codabar_reader",
                        "upc_reader",
                        "upc_e_reader",
                        "i2of5_reader"
                    ]
                },
                locate: true
            }, function(err) {
                if (err) {
                    console.error('خطأ في تهيئة QuaggaJS:', err);
                    showNotification('خطأ في تهيئة مسح الباركود', 'error');
                    return;
                }

                console.log("تم تهيئة QuaggaJS بنجاح");
                Quagga.start();

                // إضافة مستمع للباركود المكتشف
                Quagga.onDetected(function(result) {
                    const barcode = result.codeResult.code;
                    console.log('تم اكتشاف باركود:', barcode);

                    // إيقاف المسح
                    isScanning = false;
                    Quagga.stop();

                    // معالجة الباركود
                    processBarcodeInput(barcode);
                    closeBarcodeScanner();
                });

                // إضافة مستمع للأخطاء
                Quagga.onProcessed(function(result) {
                    const drawingCtx = Quagga.canvas.ctx.overlay;
                    const drawingCanvas = Quagga.canvas.dom.overlay;

                    if (result) {
                        if (result.boxes) {
                            drawingCtx.clearRect(0, 0, parseInt(drawingCanvas.getAttribute("width")), parseInt(drawingCanvas.getAttribute("height")));
                            result.boxes.filter(function (box) {
                                return box !== result.box;
                            }).forEach(function (box) {
                                Quagga.ImageDebug.drawPath(box, {x: 0, y: 1}, drawingCtx, {color: "green", lineWidth: 2});
                            });
                        }

                        if (result.box) {
                            Quagga.ImageDebug.drawPath(result.box, {x: 0, y: 1}, drawingCtx, {color: "#00F", lineWidth: 2});
                        }

                        if (result.codeResult && result.codeResult.code) {
                            Quagga.ImageDebug.drawPath(result.line, {x: 'x', y: 'y'}, drawingCtx, {color: 'red', lineWidth: 3});
                        }
                    }
                });
            });
        }

        // Capture Barcode (Manual)
        function captureBarcode() {
            if (!isScanning) {
                showNotification('المسح غير نشط', 'error');
                return;
            }

            // محاولة التقاط صورة وتحليلها
            try {
                const canvas = document.createElement('canvas');
                const video = document.getElementById('barcodeVideo');
                const context = canvas.getContext('2d');

                if (!video.videoWidth) {
                    showNotification('الكاميرا غير جاهزة', 'error');
                    return;
                }

                // التقاط الصورة
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;
                context.drawImage(video, 0, 0, canvas.width, canvas.height);

                // تحليل الصورة باستخدام QuaggaJS
                Quagga.decodeSingle({
                    src: canvas.toDataURL(),
                    numOfWorkers: 0,
                    inputStream: {
                        size: 800
                    },
                    decoder: {
                        readers: [
                            "code_128_reader",
                            "ean_reader",
                            "ean_8_reader",
                            "code_39_reader",
                            "code_39_vin_reader",
                            "codabar_reader",
                            "upc_reader",
                            "upc_e_reader",
                            "i2of5_reader"
                        ]
                    }
                }, function(result) {
                    if (result && result.codeResult) {
                        const barcode = result.codeResult.code;
                        console.log('تم التقاط باركود:', barcode);

                        processBarcodeInput(barcode);
                        closeBarcodeScanner();
                    } else {
                        showNotification('لم يتم العثور على باركود. جرب الإدخال اليدوي.', 'warning');
                    }
                });

            } catch (error) {
                console.error('خطأ في التقاط الباركود:', error);
                showNotification('خطأ في قراءة الباركود', 'error');
            }
        }

        // Toggle Flashlight
        async function toggleFlashlight() {
            try {
                if (!barcodeStream) return;

                const track = barcodeStream.getVideoTracks()[0];
                const capabilities = track.getCapabilities();

                if (capabilities.torch) {
                    flashlightOn = !flashlightOn;
                    await track.applyConstraints({
                        advanced: [{ torch: flashlightOn }]
                    });

                    const flashIcon = document.getElementById('flashIcon');
                    flashIcon.className = flashlightOn ? 'fas fa-lightbulb' : 'fas fa-flashlight';

                    showNotification(flashlightOn ? 'تم تشغيل الفلاش' : 'تم إيقاف الفلاش', 'info');
                } else {
                    showNotification('الفلاش غير متوفر في هذا الجهاز', 'warning');
                }
            } catch (error) {
                showNotification('خطأ في التحكم بالفلاش', 'error');
            }
        }

        // Switch Camera
        async function switchCamera() {
            if (availableCameras.length <= 1) {
                showNotification('كاميرا واحدة فقط متاحة', 'info');
                return;
            }

            try {
                // إيقاف الكاميرا الحالية
                if (barcodeStream) {
                    barcodeStream.getTracks().forEach(track => track.stop());
                }

                // التبديل للكاميرا التالية
                currentCamera = (currentCamera + 1) % availableCameras.length;

                // تشغيل الكاميرا الجديدة
                await initializeCamera();

                showNotification(`تم التبديل للكاميرا ${currentCamera + 1}`, 'success');

            } catch (error) {
                showNotification('خطأ في تبديل الكاميرا', 'error');
            }
        }

        // Process Manual Barcode
        function processManualBarcode() {
            const input = document.getElementById('manualBarcodeInput');
            const barcode = input.value.trim();

            if (barcode) {
                processBarcodeInput(barcode);
                input.value = '';
                closeBarcodeScanner();
            } else {
                showNotification('يرجى إدخال رقم الباركود', 'warning');
            }
        }

        // Close Barcode Scanner
        function closeBarcodeScanner() {
            // إيقاف المسح
            isScanning = false;

            // إيقاف QuaggaJS
            try {
                Quagga.stop();
            } catch (error) {
                console.log('QuaggaJS already stopped');
            }

            // إيقاف الكاميرا
            if (barcodeStream) {
                barcodeStream.getTracks().forEach(track => track.stop());
                barcodeStream = null;
            }

            // إخفاء النافذة
            document.getElementById('barcodeScannerModal').style.display = 'none';

            // إعادة تعيين المتغيرات
            flashlightOn = false;
            currentCamera = 0;

            // مسح محتوى الفيديو
            const video = document.getElementById('barcodeVideo');
            video.srcObject = null;

            updateBarcodeStatus('جاهز لمسح الباركود', 'ready');
        }

        // Toggle Manual Barcode Input
        function toggleManualBarcode() {
            const barcodeInput = document.getElementById('barcodeInput');
            const isManual = barcodeInput.hasAttribute('data-manual');

            if (isManual) {
                barcodeInput.removeAttribute('data-manual');
                barcodeInput.placeholder = 'امسح الباركود أو أدخله يدوياً...';
                barcodeInput.style.backgroundColor = '';
            } else {
                barcodeInput.setAttribute('data-manual', 'true');
                barcodeInput.placeholder = 'الإدخال اليدوي مفعل - اكتب الباركود...';
                barcodeInput.style.backgroundColor = '#e8f4fd';
                barcodeInput.focus();
            }
        }

        // Show Barcode History
        function showBarcodeHistory() {
            document.getElementById('barcodeHistoryModal').style.display = 'flex';
            updateBarcodeHistoryStats();
            displayBarcodeHistory();
        }

        // Close Barcode History
        function closeBarcodeHistory() {
            document.getElementById('barcodeHistoryModal').style.display = 'none';
        }

        // Update Barcode History Stats
        function updateBarcodeHistoryStats() {
            const totalScans = barcodeHistory.length;
            const successfulScans = barcodeHistory.filter(item => item.success).length;
            const failedScans = totalScans - successfulScans;

            document.getElementById('totalScans').textContent = totalScans;
            document.getElementById('successfulScans').textContent = successfulScans;
            document.getElementById('failedScans').textContent = failedScans;
        }

        // Display Barcode History
        function displayBarcodeHistory(filter = 'all') {
            const historyList = document.getElementById('barcodeHistoryList');
            let filteredHistory = [...barcodeHistory];

            // تطبيق الفلتر
            switch (filter) {
                case 'successful':
                    filteredHistory = barcodeHistory.filter(item => item.success);
                    break;
                case 'failed':
                    filteredHistory = barcodeHistory.filter(item => !item.success);
                    break;
                case 'today':
                    const today = new Date().toLocaleDateString('ar-SA');
                    filteredHistory = barcodeHistory.filter(item => item.date === today);
                    break;
            }

            if (filteredHistory.length === 0) {
                historyList.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #95a5a6;">
                        <i class="fas fa-history" style="font-size: 3rem; margin-bottom: 15px; opacity: 0.5;"></i>
                        <p>لا يوجد سجل مسوحات</p>
                    </div>
                `;
                return;
            }

            historyList.innerHTML = filteredHistory.map(item => `
                <div class="history-item ${item.success ? 'success' : 'failed'}" onclick="reuseBarcodeFromHistory('${item.barcode}')">
                    <div class="history-item-info">
                        <div class="history-barcode">${item.barcode}</div>
                        <div class="history-product">${item.productName}</div>
                        <div class="history-time">${item.date} - ${item.time}</div>
                    </div>
                    <div class="history-status ${item.success ? 'success' : 'failed'}">
                        ${item.success ? 'نجح' : 'فشل'}
                    </div>
                </div>
            `).join('');
        }

        // Filter Barcode History
        function filterBarcodeHistory() {
            const filter = document.getElementById('historyFilter').value;
            displayBarcodeHistory(filter);
        }

        // Clear Barcode History
        function clearBarcodeHistory() {
            if (confirm('هل تريد مسح سجل مسح الباركود بالكامل؟')) {
                barcodeHistory = [];
                localStorage.removeItem('barcodeHistory');
                updateBarcodeHistoryStats();
                displayBarcodeHistory();
                updateRecentScans();
                showNotification('تم مسح سجل الباركود', 'success');
            }
        }

        // Reuse Barcode from History
        function reuseBarcodeFromHistory(barcode) {
            closeBarcodeHistory();
            document.getElementById('barcodeInput').value = barcode;
            processBarcodeInput(barcode);
        }

        // Update Recent Scans
        function updateRecentScans() {
            const recentScansList = document.getElementById('recentScansList');
            if (!recentScansList) return;

            const recentScans = barcodeHistory.slice(0, 5);

            if (recentScans.length === 0) {
                recentScansList.innerHTML = `
                    <div style="text-align: center; color: #95a5a6; padding: 20px;">
                        لا توجد مسوحات حديثة
                    </div>
                `;
                return;
            }

            recentScansList.innerHTML = recentScans.map(item => `
                <div class="scan-item ${item.success ? 'success' : 'failed'}" onclick="reuseBarcodeFromHistory('${item.barcode}')">
                    <div>
                        <div class="scan-barcode">${item.barcode}</div>
                        <div style="font-size: 0.8rem; color: #bdc3c7;">${item.productName}</div>
                    </div>
                    <div class="scan-time">${item.time}</div>
                </div>
            `).join('');
        }

        // Generate Unique Barcode
        function generateUniqueBarcode() {
            const timestamp = Date.now().toString();
            const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
            const checksum = calculateBarcodeChecksum(timestamp + random);
            return (timestamp + random + checksum).slice(-13); // 13 رقم للباركود
        }

        // Calculate Barcode Checksum (EAN-13 style)
        function calculateBarcodeChecksum(barcode) {
            let sum = 0;
            for (let i = 0; i < barcode.length; i++) {
                const digit = parseInt(barcode[i]);
                sum += (i % 2 === 0) ? digit : digit * 3;
            }
            return ((10 - (sum % 10)) % 10).toString();
        }

        // Generate Barcode for Product
        function generateBarcodeForProduct(productId) {
            const products = JSON.parse(localStorage.getItem('adminProducts')) || [];
            const productIndex = products.findIndex(p => p.id === productId);

            if (productIndex !== -1) {
                const barcode = generateUniqueBarcode();
                products[productIndex].barcode = barcode;

                // التأكد من عدم تكرار الباركود
                const existingBarcode = products.find((p, index) =>
                    index !== productIndex && p.barcode === barcode
                );

                if (existingBarcode) {
                    // إعادة توليد باركود جديد
                    return generateBarcodeForProduct(productId);
                }

                localStorage.setItem('adminProducts', JSON.stringify(products));
                localStorage.setItem('products', JSON.stringify(products));

                console.log(`تم توليد باركود للمنتج ${productId}: ${barcode}`);
                return barcode;
            }

            return null;
        }

        // Add Barcode to Product when Adding
        function addBarcodeToNewProduct(product) {
            if (!product.barcode) {
                product.barcode = generateUniqueBarcode();
                console.log(`تم إضافة باركود للمنتج الجديد: ${product.barcode}`);
            }
            return product;
        }

        // Bulk Generate Barcodes
        function bulkGenerateBarcodes() {
            const products = JSON.parse(localStorage.getItem('adminProducts')) || [];
            let generatedCount = 0;

            products.forEach(product => {
                if (!product.barcode) {
                    const timestamp = Date.now();
                    const random = Math.floor(Math.random() * 1000);
                    product.barcode = `${timestamp}${random}`.slice(-12);
                    generatedCount++;
                }
            });

            if (generatedCount > 0) {
                localStorage.setItem('adminProducts', JSON.stringify(products));
                localStorage.setItem('products', JSON.stringify(products));

                showNotification(`تم توليد ${generatedCount} باركود للمنتجات`, 'success');
                loadProducts(); // إعادة تحميل المنتجات
            } else {
                showNotification('جميع المنتجات لديها باركود بالفعل', 'info');
            }
        }

        // Initialize Barcode System
        function initializeBarcodeSystem() {
            // تحميل سجل الباركود
            barcodeHistory = JSON.parse(localStorage.getItem('barcodeHistory')) || [];

            // تحديث العرض
            updateRecentScans();

            // إضافة مستمع للوحة المفاتيح للمسح السريع
            document.addEventListener('keydown', function(event) {
                // F2 لفتح مسح الباركود
                if (event.key === 'F2') {
                    event.preventDefault();
                    startBarcodeScanner();
                }

                // F3 لفتح سجل الباركود
                if (event.key === 'F3') {
                    event.preventDefault();
                    showBarcodeHistory();
                }

                // Escape لإغلاق النوافذ
                if (event.key === 'Escape') {
                    closeBarcodeScanner();
                    closeBarcodeHistory();
                }
            });

            // توليد باركود للمنتجات التي لا تملك باركود
            setTimeout(() => {
                const products = JSON.parse(localStorage.getItem('adminProducts')) || [];
                const productsWithoutBarcode = products.filter(p => !p.barcode);

                if (productsWithoutBarcode.length > 0) {
                    console.log(`تم العثور على ${productsWithoutBarcode.length} منتج بدون باركود`);
                    // يمكن تفعيل التوليد التلقائي هنا إذا رغبت
                    // bulkGenerateBarcodes();
                }
            }, 1000);

            console.log('✅ تم تهيئة نظام الباركود');
        }

        // Calculator Variables
        let calculatorDisplay = '0';
        let calculatorHistory = '';
        let currentOperation = null;
        let previousValue = null;
        let waitingForNewValue = false;
        let calculatorMemory = 0;
        let calculationHistory = [];

        // Show Calculator
        function showCalculator() {
            document.getElementById('calculatorModal').style.display = 'flex';
            updateCalculatorDisplay();
            loadCalculationHistory();
        }

        // Hide Calculator
        function hideCalculator() {
            document.getElementById('calculatorModal').style.display = 'none';
        }

        // Update Calculator Display
        function updateCalculatorDisplay() {
            document.getElementById('calcDisplay').textContent = calculatorDisplay;
            document.getElementById('calcHistory').textContent = calculatorHistory;
        }

        // Input Number
        function inputNumber(num) {
            if (waitingForNewValue) {
                calculatorDisplay = num;
                waitingForNewValue = false;
            } else {
                calculatorDisplay = calculatorDisplay === '0' ? num : calculatorDisplay + num;
            }
            updateCalculatorDisplay();
        }

        // Input Decimal
        function inputDecimal() {
            if (waitingForNewValue) {
                calculatorDisplay = '0.';
                waitingForNewValue = false;
            } else if (calculatorDisplay.indexOf('.') === -1) {
                calculatorDisplay += '.';
            }
            updateCalculatorDisplay();
        }

        // Clear All
        function clearAll() {
            calculatorDisplay = '0';
            calculatorHistory = '';
            currentOperation = null;
            previousValue = null;
            waitingForNewValue = false;
            updateCalculatorDisplay();
        }

        // Clear Entry
        function clearEntry() {
            calculatorDisplay = '0';
            updateCalculatorDisplay();
        }

        // Backspace
        function backspace() {
            if (calculatorDisplay.length > 1) {
                calculatorDisplay = calculatorDisplay.slice(0, -1);
            } else {
                calculatorDisplay = '0';
            }
            updateCalculatorDisplay();
        }

        // Toggle Sign
        function toggleSign() {
            if (calculatorDisplay !== '0') {
                calculatorDisplay = calculatorDisplay.startsWith('-')
                    ? calculatorDisplay.slice(1)
                    : '-' + calculatorDisplay;
                updateCalculatorDisplay();
            }
        }

        // Set Operation
        function setOperation(operation) {
            const inputValue = parseFloat(calculatorDisplay);

            if (previousValue === null) {
                previousValue = inputValue;
            } else if (currentOperation) {
                const result = performCalculation();
                calculatorDisplay = String(result);
                previousValue = result;
                updateCalculatorDisplay();
            }

            waitingForNewValue = true;
            currentOperation = operation;
            calculatorHistory = `${previousValue} ${operation}`;
            updateCalculatorDisplay();
        }

        // Perform Calculation
        function performCalculation() {
            const current = parseFloat(calculatorDisplay);
            const previous = parseFloat(previousValue);

            if (isNaN(previous) || isNaN(current)) return current;

            switch (currentOperation) {
                case '+':
                    return previous + current;
                case '-':
                    return previous - current;
                case '×':
                    return previous * current;
                case '÷':
                    return current !== 0 ? previous / current : 0;
                default:
                    return current;
            }
        }

        // Calculate
        function calculate() {
            if (currentOperation && previousValue !== null && !waitingForNewValue) {
                const result = performCalculation();
                const calculation = `${previousValue} ${currentOperation} ${calculatorDisplay} = ${result}`;

                // Add to history
                addToCalculationHistory(calculation);

                calculatorDisplay = String(result);
                calculatorHistory = '';
                currentOperation = null;
                previousValue = null;
                waitingForNewValue = true;
                updateCalculatorDisplay();
            }
        }

        // Scientific Functions
        function calculateSquareRoot() {
            const value = parseFloat(calculatorDisplay);
            if (value >= 0) {
                const result = Math.sqrt(value);
                addToCalculationHistory(`√${value} = ${result}`);
                calculatorDisplay = String(result);
                waitingForNewValue = true;
                updateCalculatorDisplay();
            } else {
                alert('لا يمكن حساب الجذر التربيعي لرقم سالب');
            }
        }

        function calculateSquare() {
            const value = parseFloat(calculatorDisplay);
            const result = value * value;
            addToCalculationHistory(`${value}² = ${result}`);
            calculatorDisplay = String(result);
            waitingForNewValue = true;
            updateCalculatorDisplay();
        }

        function calculatePercentage() {
            if (previousValue !== null && currentOperation) {
                const current = parseFloat(calculatorDisplay);
                const result = (previousValue * current) / 100;
                calculatorDisplay = String(result);
                updateCalculatorDisplay();
            } else {
                const value = parseFloat(calculatorDisplay);
                const result = value / 100;
                calculatorDisplay = String(result);
                updateCalculatorDisplay();
            }
        }

        // Memory Functions
        function memoryRecall() {
            calculatorDisplay = String(calculatorMemory);
            waitingForNewValue = true;
            updateCalculatorDisplay();
        }

        function memoryClear() {
            calculatorMemory = 0;
            showNotification('تم مسح الذاكرة', 'info');
        }

        function memoryAdd() {
            calculatorMemory += parseFloat(calculatorDisplay);
            showNotification(`تم إضافة ${calculatorDisplay} للذاكرة`, 'success');
        }

        function memorySubtract() {
            calculatorMemory -= parseFloat(calculatorDisplay);
            showNotification(`تم طرح ${calculatorDisplay} من الذاكرة`, 'success');
        }

        function memoryStore() {
            calculatorMemory = parseFloat(calculatorDisplay);
            showNotification(`تم حفظ ${calculatorDisplay} في الذاكرة`, 'success');
        }

        // History Functions
        function addToCalculationHistory(calculation) {
            calculationHistory.unshift({
                calculation: calculation,
                timestamp: new Date().toLocaleTimeString('ar-SA'),
                result: calculatorDisplay
            });

            // Keep only last 20 calculations
            if (calculationHistory.length > 20) {
                calculationHistory = calculationHistory.slice(0, 20);
            }

            saveCalculationHistory();
            updateHistoryDisplay();
        }

        function loadCalculationHistory() {
            const saved = localStorage.getItem('calculatorHistory');
            if (saved) {
                calculationHistory = JSON.parse(saved);
                updateHistoryDisplay();
            }
        }

        function saveCalculationHistory() {
            localStorage.setItem('calculatorHistory', JSON.stringify(calculationHistory));
        }

        function updateHistoryDisplay() {
            const historyList = document.getElementById('historyList');
            historyList.innerHTML = '';

            if (calculationHistory.length === 0) {
                historyList.innerHTML = '<div style="text-align: center; color: #7f8c8d; padding: 20px;">لا يوجد سجل عمليات</div>';
                return;
            }

            calculationHistory.forEach((item, index) => {
                const historyItem = document.createElement('div');
                historyItem.className = 'history-item';
                historyItem.innerHTML = `
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <span>${item.calculation}</span>
                        <small style="color: #95a5a6;">${item.timestamp}</small>
                    </div>
                `;
                historyItem.onclick = () => {
                    calculatorDisplay = item.result;
                    waitingForNewValue = true;
                    updateCalculatorDisplay();
                };
                historyList.appendChild(historyItem);
            });
        }

        function clearHistory() {
            calculationHistory = [];
            saveCalculationHistory();
            updateHistoryDisplay();
            showNotification('تم مسح سجل العمليات', 'info');
        }

        // Currency Functions
        function addToCart() {
            const amount = parseFloat(calculatorDisplay);
            if (amount > 0) {
                // Create a temporary product for the calculated amount
                const tempProduct = {
                    id: 'calc_' + Date.now(),
                    nameAr: `مبلغ محسوب - ${amount} دينار`,
                    nameEn: `Calculated Amount - ${amount} IQD`,
                    price: amount,
                    stock: 1,
                    category: 'حسابات',
                    image: null
                };

                addToCartFromCalculator(tempProduct);
                showNotification(`تم إضافة ${amount} دينار للسلة`, 'success');
                hideCalculator();
            } else {
                showNotification('يرجى إدخال مبلغ صحيح', 'error');
            }
        }

        function addToCartFromCalculator(product) {
            const existingItem = cart.find(item => item.id === product.id);

            if (existingItem) {
                existingItem.quantity += 1;
            } else {
                cart.push({
                    ...product,
                    quantity: 1
                });
            }

            updateCartDisplay();
            updateCartSummary();
        }

        function applyDiscount() {
            const discountPercent = parseFloat(calculatorDisplay);
            if (discountPercent >= 0 && discountPercent <= 100) {
                const currentTotal = calculateCartTotal();
                const discountAmount = (currentTotal * discountPercent) / 100;
                const newTotal = currentTotal - discountAmount;

                // Apply discount to cart
                cartDiscount = discountPercent;
                updateCartSummary();

                showNotification(`تم تطبيق خصم ${discountPercent}% - توفير ${discountAmount.toFixed(0)} دينار`, 'success');
                hideCalculator();
            } else {
                showNotification('يرجى إدخال نسبة خصم صحيحة (0-100)', 'error');
            }
        }

        function calculateTax() {
            const taxPercent = parseFloat(calculatorDisplay);
            if (taxPercent >= 0) {
                const currentTotal = calculateCartTotal();
                const taxAmount = (currentTotal * taxPercent) / 100;
                const totalWithTax = currentTotal + taxAmount;

                addToCalculationHistory(`ضريبة ${taxPercent}% على ${currentTotal} = ${taxAmount}`);
                calculatorDisplay = String(totalWithTax);
                updateCalculatorDisplay();

                showNotification(`ضريبة ${taxPercent}%: ${taxAmount.toFixed(0)} دينار - المجموع: ${totalWithTax.toFixed(0)} دينار`, 'info');
            } else {
                showNotification('يرجى إدخال نسبة ضريبة صحيحة', 'error');
            }
        }

        function convertCurrency() {
            const amount = parseFloat(calculatorDisplay);
            if (amount > 0) {
                // Example conversion rates (can be made dynamic)
                const rates = {
                    'USD': 0.00068, // IQD to USD
                    'EUR': 0.00062, // IQD to EUR
                    'SAR': 0.0025,  // IQD to SAR
                    'AED': 0.0025   // IQD to AED
                };

                let conversionText = `تحويل ${amount} دينار عراقي:\n\n`;

                Object.entries(rates).forEach(([currency, rate]) => {
                    const converted = (amount * rate).toFixed(2);
                    conversionText += `${currency}: ${converted}\n`;
                });

                alert(conversionText);
            } else {
                showNotification('يرجى إدخال مبلغ صحيح للتحويل', 'error');
            }
        }

        // Keyboard Support for Calculator
        document.addEventListener('keydown', function(event) {
            if (document.getElementById('calculatorModal').style.display === 'flex') {
                event.preventDefault();

                const key = event.key;

                if (key >= '0' && key <= '9') {
                    inputNumber(key);
                } else if (key === '.') {
                    inputDecimal();
                } else if (key === '+') {
                    setOperation('+');
                } else if (key === '-') {
                    setOperation('-');
                } else if (key === '*') {
                    setOperation('×');
                } else if (key === '/') {
                    setOperation('÷');
                } else if (key === 'Enter' || key === '=') {
                    calculate();
                } else if (key === 'Escape') {
                    hideCalculator();
                } else if (key === 'Backspace') {
                    backspace();
                } else if (key === 'Delete') {
                    clearEntry();
                } else if (event.ctrlKey && key === 'a') {
                    clearAll();
                }
            }
        });

        // Enhanced notification function
        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#27ae60' : type === 'error' ? '#e74c3c' : '#3498db'};
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                box-shadow: 0 5px 15px rgba(0,0,0,0.3);
                z-index: 10001;
                animation: slideInRight 0.3s ease;
                max-width: 300px;
                font-weight: 600;
            `;
            notification.textContent = message;

            document.body.appendChild(notification);

            // Remove after 3 seconds
            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // عرض إشعار تحديث في الكاشير
        function showCashierUpdateNotification(updateData) {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 80px;
                right: 20px;
                background: linear-gradient(135deg, #3498db, #2980b9);
                color: white;
                padding: 12px 18px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
                z-index: 10000;
                font-weight: 600;
                font-size: 0.9rem;
                max-width: 300px;
                animation: slideInRight 0.3s ease;
            `;

            let message = '';
            switch (updateData.source) {
                case 'admin_excel':
                    message = `منتجات جديدة: ${updateData.count}`;
                    break;
                case 'manual_sync':
                    message = `تم تحديث المنتجات`;
                    break;
                default:
                    message = `تحديث المنتجات`;
            }

            notification.innerHTML = `
                <div style="display: flex; align-items: center; gap: 8px;">
                    <i class="fas fa-box" style="font-size: 0.9rem;"></i>
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(notification);

            // إزالة الإشعار بعد 3 ثوان
            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease';
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }















        function printInvoice(invoice) {
            if (!invoice) {
                alert('لا توجد فاتورة للطباعة');
                return;
            }

            const printWindow = window.open('', '_blank');
            const invoiceHTML = generateInvoiceHTML(invoice);

            printWindow.document.write(invoiceHTML);
            printWindow.document.close();
            printWindow.print();
        }

        function generateInvoiceHTML(invoice) {
            return `
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>فاتورة رقم ${invoice.number}</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        .invoice-header { text-align: center; margin-bottom: 20px; }
                        .invoice-details { margin-bottom: 20px; }
                        .items-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                        .items-table th, .items-table td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                        .items-table th { background-color: #f2f2f2; }
                        .totals { text-align: right; }
                        .total-row { font-weight: bold; font-size: 1.2em; }
                    </style>
                </head>
                <body>
                    <div class="invoice-header">
                        <h1>فاتورة مبيعات</h1>
                        <h2>رقم الفاتورة: ${invoice.number}</h2>
                    </div>

                    <div class="invoice-details">
                        <p><strong>التاريخ:</strong> ${new Date(invoice.date).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'short',
                            day: 'numeric'
                        })}</p>
                        <p><strong>الوقت:</strong> ${new Date(invoice.date).toLocaleTimeString('en-US', {
                            hour: '2-digit',
                            minute: '2-digit',
                            hour12: true
                        })}</p>
                        <p><strong>الكاشير:</strong> ${invoice.cashier.name}</p>
                        <p><strong>العميل:</strong> ${invoice.customer.name}</p>
                        ${invoice.customer.phone ? `<p><strong>الهاتف:</strong> ${invoice.customer.phone}</p>` : ''}
                        <p><strong>نوع البيع:</strong> ${invoice.saleType === 'retail' ? 'مفرد' : 'جملة'}</p>
                        <p><strong>طريقة الدفع:</strong> ${getPaymentMethodText(invoice.paymentMethod)}</p>
                    </div>

                    <table class="items-table">
                        <thead>
                            <tr>
                                <th>المنتج</th>
                                <th>الكمية</th>
                                <th>السعر</th>
                                <th>المجموع</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${invoice.items.map(item => `
                                <tr>
                                    <td>${item.name}</td>
                                    <td>${item.quantity}</td>
                                    <td>${formatCurrency(item.price)}</td>
                                    <td>${formatCurrency(item.price * item.quantity)}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>

                    <div class="totals">
                        <p>المجموع الفرعي: ${formatCurrency(invoice.subtotal)}</p>
                        ${invoice.discount > 0 ? `<p>الخصم: ${formatCurrency(invoice.discount)}</p>` : ''}
                        <p>الضريبة (15%): ${formatCurrency(invoice.tax)}</p>
                        <p class="total-row">المجموع الكلي: ${formatCurrency(invoice.total)}</p>
                        ${invoice.paymentMethod === 'cash' ? `
                            <p>المبلغ المدفوع: ${formatCurrency(invoice.paidAmount)}</p>
                            <p>الباقي: ${formatCurrency(invoice.change)}</p>
                        ` : ''}
                    </div>

                    <div style="text-align: center; margin-top: 30px;">
                        <p>شكراً لتسوقكم معنا</p>
                    </div>
                </body>
                </html>
            `;
        }

        function getPaymentMethodText(method) {
            const methods = {
                'cash': 'نقدي',
                'card': 'بطاقة',
                'transfer': 'تحويل',
                'mixed': 'مختلط'
            };
            return methods[method] || method;
        }

        // Advanced Search Functions
        let filteredProducts = [];
        let searchStats = { total: 0, filtered: 0 };

        function performAdvancedSearch() {
            const nameSearch = document.getElementById('productSearch').value.toLowerCase();
            const priceSearch = document.getElementById('priceSearch').value;
            const barcodeSearch = document.getElementById('barcodeSearch').value;
            const categoryFilter = document.getElementById('categoryFilter').value;
            const stockFilter = document.getElementById('stockFilter').value;
            const priceRangeFilter = document.getElementById('priceRangeFilter').value;

            const products = JSON.parse(localStorage.getItem('adminProducts')) || [];

            // إذا كان البحث بالباركود فقط وتم الضغط على Enter، ابحث وأضف للسلة
            if (barcodeSearch && !nameSearch && !priceSearch && !categoryFilter && !stockFilter && !priceRangeFilter) {
                const exactMatch = products.find(p => p.barcode === barcodeSearch);
                if (exactMatch) {
                    addToCart(exactMatch.id);
                    showNotification(`تم إضافة ${exactMatch.nameAr} للسلة`, 'success');
                    // مسح حقل الباركود بعد الإضافة
                    setTimeout(() => {
                        document.getElementById('barcodeSearch').value = '';
                        performAdvancedSearch();
                    }, 1000);
                    return;
                }
            }

            filteredProducts = products.filter(product => {
                // Name search
                if (nameSearch && !product.nameAr.toLowerCase().includes(nameSearch)) {
                    return false;
                }

                // Price search
                if (priceSearch && Math.abs(product.price - parseFloat(priceSearch)) > 100) {
                    return false;
                }

                // Barcode search
                if (barcodeSearch && !product.barcode.includes(barcodeSearch)) {
                    return false;
                }

                // Category filter
                if (categoryFilter && product.category !== categoryFilter) {
                    return false;
                }

                // Stock filter
                if (stockFilter) {
                    if (stockFilter === 'available' && product.stock <= 0) return false;
                    if (stockFilter === 'low' && product.stock > 10) return false;
                    if (stockFilter === 'out' && product.stock > 0) return false;
                }

                // Price range filter
                if (priceRangeFilter) {
                    const price = parseFloat(product.price);
                    if (priceRangeFilter === '0-1000' && (price < 0 || price > 1000)) return false;
                    if (priceRangeFilter === '1000-5000' && (price < 1000 || price > 5000)) return false;
                    if (priceRangeFilter === '5000-10000' && (price < 5000 || price > 10000)) return false;
                    if (priceRangeFilter === '10000+' && price < 10000) return false;
                }

                return true;
            });

            updateSearchStats(products.length, filteredProducts.length);
            displayProducts(filteredProducts);
            updateClearButtons();
        }

        function updateSearchStats(total, filtered) {
            searchStats = { total, filtered };
            document.querySelector('.total-products').textContent = `${total} منتج`;
            document.querySelector('.filtered-products').textContent = `${filtered} ظاهر`;
        }

        function updateClearButtons() {
            const inputs = ['productSearch', 'priceSearch', 'barcodeSearch'];
            inputs.forEach(inputId => {
                const input = document.getElementById(inputId);
                const clearBtn = input.parentElement.querySelector('.clear-search');
                if (input.value.trim()) {
                    clearBtn.style.display = 'block';
                } else {
                    clearBtn.style.display = 'none';
                }
            });
        }

        function clearSearch(inputId) {
            document.getElementById(inputId).value = '';
            performAdvancedSearch();
        }

        function clearAllFilters() {
            document.getElementById('productSearch').value = '';
            document.getElementById('priceSearch').value = '';
            document.getElementById('barcodeSearch').value = '';
            document.getElementById('categoryFilter').value = '';
            document.getElementById('stockFilter').value = '';
            document.getElementById('priceRangeFilter').value = '';
            performAdvancedSearch();
        }

        function refreshProducts() {
            loadProducts();
            performAdvancedSearch();
            showNotification('تم تحديث قائمة المنتجات', 'success');
        }

        // Enhanced Product Display
        function displayProducts(products) {
            const productsGrid = document.getElementById('productsGrid');

            console.log('عرض المنتجات:', products.length);
            console.log('أول منتج:', products[0]);

            if (products.length === 0) {
                productsGrid.innerHTML = `
                    <div class="no-products">
                        <i class="fas fa-search"></i>
                        <p>لا توجد منتجات تطابق البحث</p>
                        <small>جرب تغيير معايير البحث أو الفلاتر</small>
                    </div>
                `;
                return;
            }

            productsGrid.innerHTML = products.map(product => {
                const stockStatus = getStockStatus(product);
                const currentPrice = getCurrentPrice(product);
                const wholesalePrice = product.wholesalePrice || product.price;

                console.log(`عرض المنتج: ${product.nameAr} - ID: ${product.id}`);

                return `
                    <div class="product-card" data-product-id="${product.id}" onclick="addProductDirectly('${product.id}')">
                        <div class="product-availability ${stockStatus.class}"></div>
                        <div class="direct-add-indicator">
                            <i class="fas fa-plus"></i>
                        </div>
                        <div class="product-info">
                            <div class="product-image">
                                ${product.image ?
                                    `<img src="${product.image}" alt="${product.nameAr}" onerror="this.style.display='none'">` :
                                    '<i class="fas fa-box"></i>'
                                }
                            </div>
                            <div class="product-name">${product.nameAr}</div>
                            <div class="product-price">${currentPrice.toLocaleString()} دينار</div>
                            <div class="product-barcode">
                                <i class="fas fa-barcode"></i>
                                ${product.barcode}
                            </div>
                        </div>
                        <div class="product-actions">
                            <button class="btn-add-to-cart" onclick="event.stopPropagation(); addToCart('${product.id}');">
                                <i class="fas fa-cart-plus"></i>
                                إضافة
                            </button>
                            <button class="btn-quick-add" onclick="event.stopPropagation(); addToCart('${product.id}');" title="إضافة سريعة">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>

                        <!-- Product Details Overlay -->
                        <div class="product-details">
                            <div class="product-details-content">
                                <div class="detail-row">
                                    <span class="detail-label">الفئة:</span>
                                    <span class="detail-value category-tag">${product.category || 'غير محدد'}</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">المخزون:</span>
                                    <span class="detail-value">
                                        <span class="stock-status ${stockStatus.class}">${product.stock} قطعة</span>
                                    </span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">سعر الجملة:</span>
                                    <span class="detail-value wholesale-price">${wholesalePrice.toLocaleString()} دينار</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">الحالة:</span>
                                    <span class="detail-value">
                                        <span class="stock-status ${stockStatus.class}">${stockStatus.text}</span>
                                    </span>
                                </div>
                                ${product.description ? `
                                <div class="detail-row">
                                    <span class="detail-label">الوصف:</span>
                                    <span class="detail-value">${product.description}</span>
                                </div>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                `;
            }).join('');

            console.log('تم عرض المنتجات في الشبكة');
        }

        function getStockStatus(product) {
            if (product.stock <= 0) {
                return { class: 'out-of-stock', text: 'نفذ المخزون' };
            } else if (product.stock <= (product.lowStockThreshold || 5)) {
                return { class: 'low-stock', text: 'مخزون قليل' };
            } else {
                return { class: 'in-stock', text: 'متوفر' };
            }
        }

        function getCurrentPrice(product) {
            const saleType = document.querySelector('input[name="saleType"]:checked').value;
            return saleType === 'wholesale' ? product.wholesalePrice || product.price : product.price;
        }



        // Enhanced Barcode Functions
        function updateBarcodeStatus(status, details, type = 'ready') {
            const statusText = document.getElementById('barcodeStatusText');
            const statusDetails = document.getElementById('barcodeStatusDetails');
            const statusIndicator = document.getElementById('barcodeStatusIndicator');

            statusText.textContent = status;
            statusDetails.textContent = details;

            // Update indicator color based on type
            statusIndicator.className = 'status-indicator';
            if (type === 'scanning') {
                statusIndicator.style.background = '#f39c12';
                statusIndicator.style.boxShadow = '0 0 10px rgba(243, 156, 18, 0.5)';
            } else if (type === 'success') {
                statusIndicator.style.background = '#27ae60';
                statusIndicator.style.boxShadow = '0 0 10px rgba(39, 174, 96, 0.5)';
            } else if (type === 'error') {
                statusIndicator.style.background = '#e74c3c';
                statusIndicator.style.boxShadow = '0 0 10px rgba(231, 76, 60, 0.5)';
            } else {
                statusIndicator.style.background = '#27ae60';
                statusIndicator.style.boxShadow = '0 0 10px rgba(39, 174, 96, 0.5)';
            }
        }

        function searchByBarcode(barcode) {
            const products = JSON.parse(localStorage.getItem('adminProducts')) || [];
            const product = products.find(p => p.barcode === barcode || p.barcode === barcode.toString());

            console.log('البحث عن باركود:', barcode);
            console.log('المنتجات المتاحة:', products.map(p => ({ id: p.id, name: p.nameAr, barcode: p.barcode })));

            if (product) {
                // Clear other search fields
                document.getElementById('productSearch').value = '';
                document.getElementById('priceSearch').value = '';

                // Set barcode search
                document.getElementById('barcodeSearch').value = barcode;

                // Perform search
                performAdvancedSearch();

                // Auto-add to cart
                addToCart(product.id);

                updateBarcodeStatus('تم العثور على المنتج وإضافته للسلة', `${product.nameAr} - ${product.price} دينار`, 'success');

                // Clear barcode input after successful scan
                setTimeout(() => {
                    document.getElementById('barcodeSearch').value = '';
                    updateBarcodeStatus('جاهز لمسح الباركود', 'استخدم الكاميرا أو أدخل الباركود يدوياً', 'ready');
                }, 2000);

                return true;
            } else {
                updateBarcodeStatus('المنتج غير موجود', `الباركود: ${barcode}`, 'error');
                console.log('لم يتم العثور على منتج بالباركود:', barcode);
                return false;
            }
        }



        // التعامل مع إدخال الباركود
        function handleBarcodeInput(event) {
            const barcode = event.target.value.trim();
            console.log('🔍 إدخال باركود:', barcode);

            // إذا تم الضغط على Enter
            if (event.key === 'Enter' && barcode) {
                event.preventDefault();
                console.log('⏎ البحث عن الباركود:', barcode);

                const product = allProducts.find(p => p.barcode === barcode);

                if (product) {
                    console.log('✅ تم العثور على المنتج:', product.nameAr);

                    // إضافة المنتج للسلة
                    if (addProductToCart(product.id)) {
                        // مسح حقل الباركود بعد الإضافة الناجحة
                        setTimeout(() => {
                            event.target.value = '';
                            updateBarcodeStatus('تم إضافة المنتج بنجاح', 'success');
                        }, 1000);
                    }
                } else {
                    console.warn('❌ لم يتم العثور على منتج بهذا الباركود');
                    updateBarcodeStatus('لم يتم العثور على المنتج', 'error');
                }

                return;
            }

            // تحديث حالة الباركود أثناء الكتابة
            if (barcode) {
                updateBarcodeStatus('جاري البحث...', 'searching');
            } else {
                updateBarcodeStatus('جاهز لمسح الباركود', 'ready');
            }
        }

        // تحديث حالة الباركود
        function updateBarcodeStatus(message, type = 'ready') {
            const statusElement = document.getElementById('barcodeStatus');
            if (statusElement) {
                const statusText = statusElement.querySelector('.status-text');
                if (statusText) {
                    statusText.textContent = message;
                    statusElement.className = `barcode-status ${type}`;
                }
            }
        }

        // إشعار مخصص للإضافة المباشرة
        function showDirectAddNotification(productName) {
            const notification = document.createElement('div');
            notification.className = 'direct-add-notification';
            notification.innerHTML = `
                <div class="notification-content">
                    <div class="notification-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="notification-text">
                        <strong>تمت الإضافة!</strong>
                        <span>${productName}</span>
                    </div>
                    <div class="notification-cart">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="cart-count">${cart.length}</span>
                    </div>
                </div>
            `;

            // تطبيق الأنماط
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #27ae60, #2ecc71);
                color: white;
                padding: 15px 20px;
                border-radius: 15px;
                box-shadow: 0 8px 25px rgba(39, 174, 96, 0.4);
                z-index: 10000;
                transform: translateX(100%);
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                min-width: 280px;
                font-weight: 600;
                border: 2px solid rgba(255, 255, 255, 0.2);
            `;

            document.body.appendChild(notification);

            // إظهار الإشعار
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            // إخفاء الإشعار
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 400);
            }, 2500);
        }

        // تحديث وظيفة البحث القديمة للتوافق
        function searchProducts() {
            smartSearch();
        }

        // تحديث وظيفة مسح البحث القديمة
        function clearSearch() {
            clearAllSearch();
        }

        // تحديث وظيفة تحديث المنتجات القديمة
        function refreshProducts() {
            refreshAllProducts();
        }

        // تحسين تحميل المنتجات عند بدء التشغيل
        function enhancedProductsInit() {
            console.log('🚀 بدء تشغيل نظام المنتجات المحسن...');

            // مزامنة مع الموقع الرئيسي أولاً
            syncWithMainSite();

            // تحميل البيانات الأساسية
            loadProducts();

            // إضافة مستمعات الأحداث للتحسينات
            addEnhancedEventListeners();

            console.log('✅ تم تشغيل نظام المنتجات المحسن بنجاح');
        }

        // إضافة مستمعات الأحداث المحسنة
        function addEnhancedEventListeners() {
            // إغلاق الـ modal بالضغط على Escape
            document.addEventListener('keydown', function(event) {
                if (event.key === 'Escape') {
                    closeProductDetailsModal();
                }
            });

            // تحديث تلقائي للمنتجات كل 5 دقائق
            setInterval(() => {
                console.log('🔄 تحديث تلقائي للمنتجات...');
                syncWithMainSite();

                // إعادة تطبيق البحث الحالي إذا كان موجوداً
                const searchTerm = document.getElementById('productSearch').value;
                if (searchTerm) {
                    performAdvancedSearch();
                } else {
                    loadProducts();
                }
            }, 5 * 60 * 1000); // 5 دقائق

            // حفظ تلقائي للسلة كل دقيقة
            setInterval(() => {
                if (cart.length > 0) {
                    localStorage.setItem('currentCart', JSON.stringify(cart));
                    localStorage.setItem('lastCartUpdate', new Date().toISOString());
                }
            }, 60 * 1000); // دقيقة واحدة

            console.log('✅ تم إضافة مستمعات الأحداث المحسنة');
        }

        // إشعار محسن للنجاح والأخطاء
        function showNotification(message, type = 'info') {
            // إزالة الإشعارات السابقة من نفس النوع
            const existingNotifications = document.querySelectorAll(`.modern-notification.${type}`);
            existingNotifications.forEach(notif => notif.remove());

            const notification = document.createElement('div');
            notification.className = `modern-notification ${type}`;

            const icons = {
                success: 'fas fa-check-circle',
                error: 'fas fa-exclamation-circle',
                warning: 'fas fa-exclamation-triangle',
                info: 'fas fa-info-circle'
            };

            const colors = {
                success: 'linear-gradient(135deg, #27ae60, #2ecc71)',
                error: 'linear-gradient(135deg, #e74c3c, #c0392b)',
                warning: 'linear-gradient(135deg, #f39c12, #e67e22)',
                info: 'linear-gradient(135deg, #3498db, #2980b9)'
            };

            notification.innerHTML = `
                <div class="notification-content">
                    <div class="notification-icon">
                        <i class="${icons[type]}"></i>
                    </div>
                    <div class="notification-text">
                        ${message}
                    </div>
                    <button class="notification-close" onclick="this.parentElement.parentElement.remove()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;

            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${colors[type]};
                color: white;
                padding: 16px 20px;
                border-radius: 12px;
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
                z-index: 10000;
                transform: translateX(100%);
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                min-width: 300px;
                max-width: 400px;
                font-weight: 600;
                border: 2px solid rgba(255, 255, 255, 0.2);
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            `;

            // إضافة أنماط للمحتوى
            if (!document.getElementById('notification-styles')) {
                const style = document.createElement('style');
                style.id = 'notification-styles';
                style.textContent = `
                    .modern-notification .notification-content {
                        display: flex;
                        align-items: center;
                        gap: 12px;
                    }
                    .modern-notification .notification-icon {
                        width: 40px;
                        height: 40px;
                        background: rgba(255, 255, 255, 0.2);
                        border-radius: 50%;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 1.2rem;
                        flex-shrink: 0;
                    }
                    .modern-notification .notification-text {
                        flex: 1;
                        font-size: 0.95rem;
                        line-height: 1.4;
                    }
                    .modern-notification .notification-close {
                        background: rgba(255, 255, 255, 0.2);
                        border: none;
                        color: white;
                        width: 30px;
                        height: 30px;
                        border-radius: 50%;
                        cursor: pointer;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        transition: all 0.3s ease;
                        flex-shrink: 0;
                    }
                    .modern-notification .notification-close:hover {
                        background: rgba(255, 255, 255, 0.3);
                        transform: scale(1.1);
                    }
                `;
                document.head.appendChild(style);
            }

            document.body.appendChild(notification);

            // إظهار الإشعار
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 100);

            // إخفاء الإشعار تلقائياً
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.style.transform = 'translateX(100%)';
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.parentNode.removeChild(notification);
                        }
                    }, 400);
                }
            }, type === 'error' ? 5000 : 3000);
        }
    </script>

    <style>
        /* Notification Styles */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 15px 20px;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            gap: 10px;
            z-index: 10000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            border-left: 4px solid #3498db;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification-success {
            border-left-color: #27ae60;
            color: #27ae60;
        }

        .notification-error {
            border-left-color: #e74c3c;
            color: #e74c3c;
        }

        .notification-info {
            border-left-color: #3498db;
            color: #3498db;
        }

        .notification i {
            font-size: 1.2rem;
        }

        .notification span {
            font-weight: 600;
        }
    </style>

    <!-- Product Details Modal -->
    <div class="product-details-modal" id="productDetailsModal">
        <div class="modal-overlay" onclick="closeProductDetailsModal()"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h3>تفاصيل المنتج</h3>
                <button class="close-btn" onclick="closeProductDetailsModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="product-image-large" id="modalProductImage">
                    <i class="fas fa-image"></i>
                </div>
                <div class="product-details-content" id="modalProductDetails">
                    <!-- Product details will be populated here -->
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" onclick="addModalProductToCart()">
                    <i class="fas fa-cart-plus"></i>
                    إضافة للسلة
                </button>
                <button class="btn btn-secondary" onclick="closeProductDetailsModal()">
                    <i class="fas fa-times"></i>
                    إغلاق
                </button>
            </div>
        </div>
    </div>

    <script src="cashier-script.js"></script>
</body>
</html>
