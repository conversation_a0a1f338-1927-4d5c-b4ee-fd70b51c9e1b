<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تنظيف بيانات تسجيل الدخول</title>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .container {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }
        h1 {
            color: #333;
            margin-bottom: 1rem;
        }
        .btn {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            font-size: 1.1rem;
            cursor: pointer;
            margin: 0.5rem;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
        }
        .success {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            padding: 1rem;
            border-radius: 8px;
            color: white;
            margin: 1rem 0;
            display: none;
        }
        .info {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧹 تنظيف بيانات تسجيل الدخول</h1>
        
        <div class="info">
            <p>هذه الأداة ستقوم بحذف جميع بيانات تسجيل الدخول المحفوظة في المتصفح</p>
            <p>بما في ذلك جميع حسابات المستخدمين المحفوظة</p>
        </div>

        <button class="btn" onclick="clearAllLoginData()">
            🗑️ حذف جميع بيانات تسجيل الدخول
        </button>

        <button class="btn" onclick="clearAllEmployees()">
            👥 حذف جميع حسابات الموظفين
        </button>
        
        <div class="success" id="successMessage">
            ✅ تم حذف البيانات بنجاح!
        </div>
        
        <div class="info">
            <p><strong>ملاحظة:</strong> بعد التنظيف، ستحتاج لإنشاء حسابات جديدة من لوحة التحكم</p>
        </div>
    </div>

    <script>
        function clearAllLoginData() {
            if (confirm('هل أنت متأكد من حذف جميع بيانات تسجيل الدخول؟')) {
                // حذف جميع البيانات المتعلقة بتسجيل الدخول
                localStorage.removeItem('employees');
                localStorage.removeItem('adminLoggedIn');
                localStorage.removeItem('adminUsername');
                localStorage.removeItem('loginTime');
                
                sessionStorage.removeItem('userSession');
                sessionStorage.removeItem('isLoggedIn');
                sessionStorage.removeItem('cashierUser');
                sessionStorage.removeItem('currentUser');
                
                // حذف أي بيانات أخرى متعلقة بالمستخدمين
                const keysToRemove = [];
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && (key.includes('user') || key.includes('login') || key.includes('employee'))) {
                        keysToRemove.push(key);
                    }
                }
                
                keysToRemove.forEach(key => localStorage.removeItem(key));
                
                showSuccess('تم حذف جميع بيانات تسجيل الدخول بنجاح!');
            }
        }
        
        function clearSpecificUser() {
            if (confirm('هل أنت متأكد من حذف بيانات المستخدم "ali"؟')) {
                try {
                    const employees = JSON.parse(localStorage.getItem('employees')) || [];
                    const filteredEmployees = employees.filter(emp => emp.username !== 'ali');
                    localStorage.setItem('employees', JSON.stringify(filteredEmployees));
                    
                    showSuccess('تم حذف بيانات المستخدم "ali" بنجاح!');
                } catch (error) {
                    showSuccess('لم يتم العثور على بيانات للمستخدم "ali"');
                }
            }
        }
        
        function showSuccess(message) {
            const successDiv = document.getElementById('successMessage');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
            
            setTimeout(() => {
                successDiv.style.display = 'none';
            }, 5000);
        }
        
        // تشغيل تلقائي عند تحميل الصفحة
        window.addEventListener('load', function() {
            console.log('🧹 أداة تنظيف بيانات تسجيل الدخول جاهزة');
        });
    </script>
</body>
</html>
