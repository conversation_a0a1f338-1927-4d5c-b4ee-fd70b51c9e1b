// Contact Page JavaScript

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    initializeContactForm();
    initializeMapPlaceholder();
});

// Contact Form Handling
function initializeContactForm() {
    const form = document.getElementById('contactForm');
    if (form) {
        form.addEventListener('submit', handleFormSubmit);
    }
}

function handleFormSubmit(e) {
    e.preventDefault();
    
    const formData = {
        name: document.getElementById('contactName').value,
        email: document.getElementById('contactEmail').value,
        phone: document.getElementById('contactPhone').value,
        subject: document.getElementById('contactSubject').value,
        message: document.getElementById('contactMessage').value
    };
    
    // Validate form
    if (!validateForm(formData)) {
        return;
    }
    
    // Show loading state
    const submitBtn = document.querySelector('.submit-btn');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>جاري الإرسال...</span>';
    submitBtn.disabled = true;
    
    // Simulate form submission
    setTimeout(() => {
        // Reset form
        document.getElementById('contactForm').reset();
        
        // Reset button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        
        // Show success message
        showSuccessMessage();
        
        // Send to WhatsApp (optional)
        sendToWhatsApp(formData);
    }, 2000);
}

function validateForm(data) {
    const errors = [];
    
    if (!data.name.trim()) {
        errors.push('الاسم مطلوب');
    }
    
    if (!data.email.trim()) {
        errors.push('البريد الإلكتروني مطلوب');
    } else if (!isValidEmail(data.email)) {
        errors.push('البريد الإلكتروني غير صحيح');
    }
    
    if (!data.phone.trim()) {
        errors.push('رقم الهاتف مطلوب');
    }
    
    if (!data.subject) {
        errors.push('الموضوع مطلوب');
    }
    
    if (!data.message.trim()) {
        errors.push('الرسالة مطلوبة');
    }
    
    if (errors.length > 0) {
        showErrorMessage(errors.join('\n'));
        return false;
    }
    
    return true;
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function showSuccessMessage() {
    const message = currentLanguage === 'ar' 
        ? 'تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.'
        : 'Your message has been sent successfully! We will contact you soon.';
    
    showNotification(message, 'success');
}

function showErrorMessage(message) {
    showNotification(message, 'error');
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'}"></i>
            <span>${message}</span>
        </div>
    `;
    
    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#27ae60' : '#e74c3c'};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        z-index: 10000;
        transform: translateX(100%);
        transition: transform 0.3s ease;
        max-width: 400px;
        font-family: 'Cairo', sans-serif;
        direction: ${currentLanguage === 'ar' ? 'rtl' : 'ltr'};
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Remove after 5 seconds
    setTimeout(() => {
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 5000);
}

function sendToWhatsApp(data) {
    const message = `
*رسالة جديدة من موقع برومت هايبر ماركت*

*الاسم:* ${data.name}
*البريد الإلكتروني:* ${data.email}
*الهاتف:* ${data.phone}
*الموضوع:* ${getSubjectText(data.subject)}

*الرسالة:*
${data.message}

---
تم الإرسال من صفحة اتصل بنا
    `.trim();
    
    const whatsappUrl = `https://wa.me/966111234567?text=${encodeURIComponent(message)}`;
    
    // Optional: Open WhatsApp automatically
    // window.open(whatsappUrl, '_blank');
}

function getSubjectText(value) {
    const subjects = {
        'general': currentLanguage === 'ar' ? 'استفسار عام' : 'General Inquiry',
        'order': currentLanguage === 'ar' ? 'استفسار عن طلب' : 'Order Inquiry',
        'complaint': currentLanguage === 'ar' ? 'شكوى' : 'Complaint',
        'suggestion': currentLanguage === 'ar' ? 'اقتراح' : 'Suggestion'
    };
    return subjects[value] || value;
}

// Map Placeholder
function initializeMapPlaceholder() {
    const mapPlaceholder = document.querySelector('.map-placeholder');
    if (mapPlaceholder) {
        mapPlaceholder.addEventListener('click', openMap);
    }
}

function openMap() {
    // Coordinates for Riyadh (example)
    const lat = 24.7136;
    const lng = 46.6753;
    
    // Try to open in Google Maps app first, fallback to web
    const googleMapsApp = `comgooglemaps://?q=${lat},${lng}`;
    const googleMapsWeb = `https://www.google.com/maps?q=${lat},${lng}`;
    
    // For mobile devices, try app first
    if (/Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
        window.location.href = googleMapsApp;
        setTimeout(() => {
            window.open(googleMapsWeb, '_blank');
        }, 1000);
    } else {
        window.open(googleMapsWeb, '_blank');
    }
}

// Smooth scrolling for anchor links
document.addEventListener('click', function(e) {
    if (e.target.tagName === 'A' && e.target.getAttribute('href').startsWith('#')) {
        e.preventDefault();
        const targetId = e.target.getAttribute('href').substring(1);
        const targetElement = document.getElementById(targetId);
        if (targetElement) {
            targetElement.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    }
});

// Add CSS for notifications
const notificationStyles = document.createElement('style');
notificationStyles.textContent = `
    .notification-content {
        display: flex;
        align-items: center;
        gap: 0.8rem;
    }
    
    .notification-content i {
        font-size: 1.2rem;
    }
    
    .notification-content span {
        font-weight: 600;
        white-space: pre-line;
    }
`;
document.head.appendChild(notificationStyles);
