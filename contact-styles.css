/* Contact Page Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: #333;
    background: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.8rem;
}

.logo i {
    font-size: 1.8rem;
    color: #2c5530;
}

.logo h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c5530;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 600;
    transition: color 0.3s ease;
    padding: 0.5rem 1rem;
    border-radius: 6px;
}

.nav-link:hover,
.nav-link.active {
    color: #2c5530;
    background: #f0f8f0;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.language-switcher {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 20px;
    background: #f0f8f0;
    transition: all 0.3s ease;
    font-weight: 600;
    font-size: 0.9rem;
}

.language-switcher:hover {
    background: #2c5530;
    color: white;
}

.cart-icon {
    cursor: pointer;
    padding: 10px;
    border-radius: 50%;
    background: #f0f8f0;
    transition: all 0.3s ease;
}

.cart-icon:hover {
    background: #2c5530;
    color: white;
}

.cart-icon i {
    font-size: 1.5rem;
    color: #2c5530;
    transition: color 0.3s ease;
}

.cart-icon:hover i {
    color: white;
}

/* Contact Hero */
.contact-hero {
    background: linear-gradient(135deg, #2c5530, #1e3a23);
    color: white;
    padding: 80px 0;
    text-align: center;
}

.hero-content h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.hero-content p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* Contact Content */
.contact-content {
    padding: 80px 0;
}

.contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.section-header {
    margin-bottom: 2rem;
}

.section-header h2 {
    font-size: 2rem;
    font-weight: 700;
    color: #2c5530;
    margin-bottom: 0.5rem;
}

.section-header p {
    color: #666;
    font-size: 1.1rem;
}

/* Contact Form */
.contact-form-section {
    background: white;
    padding: 2.5rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 0.8rem;
    border: 2px solid #e1e1e1;
    border-radius: 8px;
    font-family: 'Cairo', sans-serif;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #2c5530;
}

.submit-btn {
    background: linear-gradient(135deg, #2c5530, #1e3a23);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.submit-btn:hover {
    transform: translateY(-2px);
}

/* Contact Info */
.contact-info-section {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-cards {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.contact-card {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    transition: transform 0.3s ease;
}

.contact-card:hover {
    transform: translateY(-3px);
}

.card-icon {
    background: linear-gradient(135deg, #2c5530, #1e3a23);
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.card-content h3 {
    font-size: 1.1rem;
    font-weight: 700;
    color: #2c5530;
    margin-bottom: 0.5rem;
}

.card-content p {
    color: #666;
    line-height: 1.6;
}

.card-content a {
    color: #2c5530;
    text-decoration: none;
    font-weight: 600;
}

.card-content a:hover {
    text-decoration: underline;
}

/* Social Section */
.social-section {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.social-section h3 {
    font-size: 1.2rem;
    font-weight: 700;
    color: #2c5530;
    margin-bottom: 1rem;
}

.social-links {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.8rem;
}

.social-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.social-link.facebook {
    background: #f0f8ff;
    color: #1877f2;
}

.social-link.twitter {
    background: #f0f9ff;
    color: #1da1f2;
}

.social-link.instagram {
    background: #fef7f0;
    color: #e4405f;
}

.social-link.whatsapp {
    background: #f0fdf4;
    color: #25d366;
}

.social-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

/* Map Section */
.map-section {
    padding: 60px 0;
    background: white;
}

.map-section h2 {
    text-align: center;
    font-size: 2rem;
    font-weight: 700;
    color: #2c5530;
    margin-bottom: 2rem;
}

.map-container {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.map-placeholder {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    height: 400px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #666;
    cursor: pointer;
    transition: all 0.3s ease;
}

.map-placeholder:hover {
    background: linear-gradient(135deg, #e9ecef, #dee2e6);
}

.map-placeholder i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #2c5530;
}

.map-placeholder p {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.map-placeholder small {
    opacity: 0.7;
}

/* Quick Contact */
.quick-contact {
    background: linear-gradient(135deg, #2c5530, #1e3a23);
    color: white;
    padding: 60px 0;
}

.quick-contact-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
}

.quick-contact-text h2 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.quick-contact-text p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.quick-contact-actions {
    display: flex;
    gap: 1rem;
}

.whatsapp-btn,
.call-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.whatsapp-btn {
    background: #25d366;
    color: white;
}

.call-btn {
    background: white;
    color: #2c5530;
}

.whatsapp-btn:hover,
.call-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* Footer */
.footer {
    background: #1e3a23;
    color: white;
    padding: 2rem 0;
    text-align: center;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-content a {
    color: white;
    text-decoration: none;
    font-weight: 600;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    background: rgba(255,255,255,0.1);
    transition: all 0.3s ease;
}

.footer-content a:hover {
    background: rgba(255,255,255,0.2);
}

/* Language Support */
.lang-en {
    font-family: 'Arial', sans-serif;
}

.lang-en .hero-content h1,
.lang-en .section-header h2 {
    font-family: 'Arial', sans-serif;
    font-weight: 700;
}

/* RTL/LTR Support */
[dir="ltr"] .contact-grid {
    text-align: left;
}

[dir="ltr"] .contact-card {
    text-align: left;
}

[dir="ltr"] .social-section {
    text-align: left;
}

[dir="ltr"] .quick-contact-content {
    flex-direction: row;
}

[dir="rtl"] .quick-contact-content {
    flex-direction: row-reverse;
}

[dir="ltr"] .footer-content {
    flex-direction: row;
}

[dir="rtl"] .footer-content {
    flex-direction: row-reverse;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .hero-content h1 {
        font-size: 2rem;
    }

    .contact-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .social-links {
        grid-template-columns: 1fr;
    }

    .quick-contact-content {
        flex-direction: column;
        text-align: center;
    }

    .quick-contact-actions {
        flex-direction: column;
        width: 100%;
    }

    .footer-content {
        flex-direction: column;
        gap: 1rem;
    }

    .contact-form-section {
        padding: 1.5rem;
    }
}
