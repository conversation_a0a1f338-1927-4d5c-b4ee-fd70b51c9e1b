<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل دخول الموظفين</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 3rem;
            width: 100%;
            max-width: 450px;
            text-align: center;
        }

        .logo {
            font-size: 3rem;
            color: #6c5ce7;
            margin-bottom: 1rem;
        }

        .title {
            font-size: 2rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .subtitle {
            color: #666;
            margin-bottom: 2rem;
            font-size: 1.1rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
            text-align: right;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 600;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #6c5ce7;
            box-shadow: 0 0 0 3px rgba(108, 92, 231, 0.1);
        }

        .login-btn {
            width: 100%;
            padding: 1rem;
            background: linear-gradient(135deg, #6c5ce7, #a29bfe);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(108, 92, 231, 0.3);
        }

        .back-btn {
            width: 100%;
            padding: 1rem;
            background: #6c757d;
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .back-btn:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            display: none;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            display: none;
        }

        .info-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            color: #856404;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .login-container {
                padding: 2rem;
                margin: 1rem;
            }
            
            .title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">
            <i class="fas fa-user-tie"></i>
        </div>
        <h1 class="title">تسجيل دخول الموظفين</h1>
        <p class="subtitle">نظام دخول الموظفين العاديين</p>
        
        <div class="info-box">
            <i class="fas fa-info-circle"></i>
            هذا النظام مخصص للموظفين العاديين للوصول إلى الأقسام المحدودة
        </div>
        
        <div class="error-message" id="errorMessage"></div>
        <div class="success-message" id="successMessage"></div>
        
        <form id="employeeLoginForm">
            <div class="form-group">
                <label for="username">اسم المستخدم:</label>
                <input type="text" id="username" name="username" required placeholder="أدخل اسم المستخدم">
            </div>
            
            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <input type="password" id="password" name="password" required placeholder="أدخل كلمة المرور">
            </div>
            
            <button type="submit" class="login-btn">
                <i class="fas fa-sign-in-alt"></i> تسجيل الدخول
            </button>
        </form>
        
        <a href="index.html" class="back-btn">
            <i class="fas fa-arrow-right"></i> العودة للموقع الرئيسي
        </a>
    </div>

    <script>
        document.getElementById('employeeLoginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            // التحقق من البيانات
            if (!username || !password) {
                showError('يرجى ملء جميع الحقول');
                return;
            }
            
            // التحقق من الموظفين في قاعدة البيانات
            const employees = JSON.parse(localStorage.getItem('employees')) || [];
            const employee = employees.find(emp => 
                emp.username === username && 
                emp.password === password && 
                emp.role === 'employee' &&
                emp.status === 'active'
            );
            
            if (!employee) {
                showError('اسم المستخدم أو كلمة المرور غير صحيحة أو أنك لست موظف عادي');
                return;
            }
            
            // حفظ جلسة الموظف
            const userSession = {
                username: employee.username,
                name: employee.name,
                role: employee.role,
                isLoggedIn: true,
                loginTime: new Date().toISOString()
            };
            
            sessionStorage.setItem('userSession', JSON.stringify(userSession));
            sessionStorage.setItem('isLoggedIn', 'true');
            
            showSuccess(`مرحباً ${employee.name}! جاري التحويل للوحة التحكم...`);
            
            // التوجيه للوحة التحكم مع صلاحيات محدودة
            setTimeout(() => {
                window.location.href = 'admin.html';
            }, 1500);
        });
        
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
        }
        
        function showSuccess(message) {
            const successDiv = document.getElementById('successMessage');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
        }
        
        // التحقق من وجود جلسة سابقة
        window.addEventListener('load', function() {
            const userSession = sessionStorage.getItem('userSession');
            if (userSession) {
                try {
                    const user = JSON.parse(userSession);
                    if (user && user.isLoggedIn && user.role === 'employee') {
                        window.location.href = 'admin.html';
                    }
                } catch (error) {
                    sessionStorage.removeItem('userSession');
                    sessionStorage.removeItem('isLoggedIn');
                }
            }
        });
    </script>
</body>
</html>
