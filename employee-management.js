/**
 * Employee Management System - Professional & Clean
 * Complete system for managing employee login accounts
 */

// Employee Management System
class EmployeeManager {
    constructor() {
        this.employees = [];
        this.currentAdmin = null;
        this.init();
    }

    init() {
        console.log('🚀 Initializing Employee Management System...');
        this.loadEmployees();
        this.bindEvents();
        console.log('✅ Employee Management System ready');
    }

    loadEmployees() {
        try {
            this.employees = JSON.parse(localStorage.getItem('employees')) || this.getDefaultEmployees();
            console.log(`👥 Loaded ${this.employees.length} employee accounts`);
        } catch (error) {
            console.error('❌ Error loading employees:', error);
            this.employees = this.getDefaultEmployees();
        }
    }

    getDefaultEmployees() {
        return [
            {
                id: 'admin_001',
                name: 'علي المدير',
                username: 'ali',
                password: '12',
                phone: '***********',
                email: '<EMAIL>',
                role: 'admin',
                permissions: ['all'],
                status: 'active',
                salary: 1000000,
                hireDate: '2024-01-01',
                createdAt: new Date().toISOString()
            }
        ];
    }

    bindEvents() {
        document.addEventListener('DOMContentLoaded', () => {
            const employeeBtn = document.getElementById('newEmployeeBtn');
            if (employeeBtn) {
                employeeBtn.addEventListener('click', () => {
                    this.openEmployeeSystem();
                });
                console.log('✅ Employee button event bound');
            }
        });
    }

    openEmployeeSystem() {
        console.log('🔐 Opening Employee Management System...');
        this.showAdminVerification();
    }

    showAdminVerification() {
        const modalHTML = `
            <div id="employeeModalOverlay" style="
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.85);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
                backdrop-filter: blur(10px);
                animation: fadeIn 0.4s ease;
            ">
                <style>
                    @keyframes fadeIn {
                        from { opacity: 0; }
                        to { opacity: 1; }
                    }
                    @keyframes slideUp {
                        from { opacity: 0; transform: translateY(30px) scale(0.95); }
                        to { opacity: 1; transform: translateY(0) scale(1); }
                    }
                    @keyframes pulse {
                        0%, 100% { transform: scale(1); }
                        50% { transform: scale(1.03); }
                    }

                    /* Responsive Design */
                    @media (max-width: 768px) {
                        .modal-container {
                            padding: 1rem !important;
                            max-width: 350px !important;
                        }
                        .modal-icon {
                            width: 50px !important;
                            height: 50px !important;
                            font-size: 1.5rem !important;
                        }
                        .modal-badge {
                            width: 18px !important;
                            height: 18px !important;
                            font-size: 0.6rem !important;
                        }
                        .modal-title {
                            font-size: 1.1rem !important;
                        }
                        .modal-subtitle {
                            font-size: 0.8rem !important;
                        }
                        .modal-input {
                            padding: 0.7rem !important;
                            font-size: 0.9rem !important;
                        }
                        .modal-button {
                            padding: 0.7rem 1.2rem !important;
                            font-size: 0.9rem !important;
                            min-width: 90px !important;
                        }
                    }

                    @media (max-width: 480px) {
                        .modal-container {
                            padding: 0.8rem !important;
                            max-width: 320px !important;
                        }
                        .modal-buttons {
                            flex-direction: column !important;
                            gap: 0.8rem !important;
                        }
                        .modal-button {
                            width: 100% !important;
                        }
                    }
                </style>
                
                <div class="modal-container" style="
                    background: linear-gradient(135deg, #ffffff, #f8f9fa);
                    padding: 1.5rem;
                    border-radius: 20px;
                    max-width: 400px;
                    width: 95%;
                    text-align: center;
                    box-shadow: 0 20px 60px rgba(0,0,0,0.4);
                    border: 1px solid rgba(255,255,255,0.2);
                    animation: slideUp 0.5s ease;
                    position: relative;
                    max-height: 90vh;
                    overflow-y: auto;
                ">
                    <!-- Header -->
                    <div style="margin-bottom: 1.5rem; margin-top: 0.5rem;">
                        <div class="modal-icon" style="
                            width: 60px;
                            height: 60px;
                            background: linear-gradient(135deg, #e74c3c, #c0392b);
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            margin: 0 auto 1rem;
                            color: white;
                            font-size: 1.8rem;
                            box-shadow: 0 8px 20px rgba(231, 76, 60, 0.3);
                            position: relative;
                        ">
                            <i class="fas fa-shield-alt"></i>
                            <div class="modal-badge" style="
                                position: absolute;
                                top: -5px;
                                right: -5px;
                                width: 20px;
                                height: 20px;
                                background: linear-gradient(135deg, #27ae60, #2ecc71);
                                border-radius: 50%;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                color: white;
                                font-size: 0.7rem;
                                border: 2px solid white;
                                box-shadow: 0 2px 6px rgba(39, 174, 96, 0.3);
                            ">
                                <i class="fas fa-users"></i>
                            </div>
                        </div>

                        <h3 class="modal-title" style="
                            color: #2c3e50;
                            margin: 0 0 0.5rem 0;
                            font-size: 1.3rem;
                            font-weight: 700;
                            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
                        ">
                            تأكيد هوية المدير
                        </h3>

                        <p class="modal-subtitle" style="
                            color: #7f8c8d;
                            margin: 0 0 1rem 0;
                            font-size: 0.9rem;
                            font-weight: 500;
                        ">
                            نظام إدارة حسابات دخول الموظفين
                        </p>

                        <div style="
                            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
                            border: 1px solid #f39c12;
                            border-radius: 10px;
                            padding: 0.8rem;
                            margin-top: 1rem;
                            font-size: 0.85rem;
                            color: #856404;
                            font-weight: 500;
                            box-shadow: 0 2px 8px rgba(243, 156, 18, 0.15);
                        ">
                            <i class="fas fa-exclamation-triangle" style="margin-left: 0.3rem; font-size: 0.9rem;"></i>
                            منطقة محمية - يتطلب صلاحيات المدير
                        </div>
                    </div>
                    
                    <!-- Login Form -->
                    <div style="text-align: right; margin-bottom: 1.5rem;">
                        <div style="margin-bottom: 1.2rem;">
                            <label style="
                                display: block;
                                margin-bottom: 0.5rem;
                                color: #2c3e50;
                                font-weight: 600;
                                font-size: 0.9rem;
                                display: flex;
                                align-items: center;
                                justify-content: flex-end;
                            ">
                                <span>اسم المستخدم</span>
                                <i class="fas fa-user" style="margin-right: 0.5rem; color: #3498db; font-size: 1rem;"></i>
                            </label>
                            <input type="text" id="adminUsername" class="modal-input" placeholder="أدخل اسم المستخدم" style="
                                width: 100%;
                                padding: 0.8rem;
                                border: 2px solid #ecf0f1;
                                border-radius: 10px;
                                font-size: 1rem;
                                transition: all 0.3s ease;
                                background: #ffffff;
                                box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
                                font-family: 'Cairo', sans-serif;
                            " onfocus="this.style.borderColor='#3498db'; this.style.boxShadow='0 0 0 3px rgba(52,152,219,0.15)'" onblur="this.style.borderColor='#ecf0f1'; this.style.boxShadow='inset 0 2px 4px rgba(0,0,0,0.1)'">
                        </div>

                        <div style="margin-bottom: 1.2rem;">
                            <label style="
                                display: block;
                                margin-bottom: 0.5rem;
                                color: #2c3e50;
                                font-weight: 600;
                                font-size: 0.9rem;
                                display: flex;
                                align-items: center;
                                justify-content: flex-end;
                            ">
                                <span>كلمة المرور</span>
                                <i class="fas fa-lock" style="margin-right: 0.5rem; color: #e74c3c; font-size: 1rem;"></i>
                            </label>
                            <input type="password" id="adminPassword" class="modal-input" placeholder="أدخل كلمة المرور" style="
                                width: 100%;
                                padding: 0.8rem;
                                border: 2px solid #ecf0f1;
                                border-radius: 10px;
                                font-size: 1rem;
                                transition: all 0.3s ease;
                                background: #ffffff;
                                box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
                                font-family: 'Cairo', sans-serif;
                            " onfocus="this.style.borderColor='#e74c3c'; this.style.boxShadow='0 0 0 3px rgba(231,76,60,0.15)'" onblur="this.style.borderColor='#ecf0f1'; this.style.boxShadow='inset 0 2px 4px rgba(0,0,0,0.1)'">
                        </div>

                        <div id="errorMessage" style="
                            color: #e74c3c;
                            background: linear-gradient(135deg, #fadbd8, #f5b7b1);
                            border: 1px solid #e74c3c;
                            border-radius: 10px;
                            padding: 0.8rem;
                            margin-bottom: 1rem;
                            display: none;
                            font-size: 0.9rem;
                            font-weight: 600;
                            text-align: center;
                            box-shadow: 0 3px 10px rgba(231, 76, 60, 0.15);
                        "></div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="modal-buttons" style="
                        display: flex;
                        gap: 1rem;
                        justify-content: center;
                        margin-top: 1.5rem;
                    ">
                        <button onclick="employeeManager.closeModal()" class="modal-button" style="
                            background: linear-gradient(135deg, #95a5a6, #7f8c8d);
                            color: white;
                            border: none;
                            padding: 0.8rem 1.5rem;
                            border-radius: 10px;
                            cursor: pointer;
                            font-size: 1rem;
                            font-weight: 600;
                            transition: all 0.3s ease;
                            min-width: 100px;
                            box-shadow: 0 3px 12px rgba(149,165,166,0.3);
                            font-family: 'Cairo', sans-serif;
                        " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 5px 15px rgba(149,165,166,0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 3px 12px rgba(149,165,166,0.3)'">
                            <i class="fas fa-times"></i> إلغاء
                        </button>
                        <button onclick="employeeManager.verifyAdmin()" class="modal-button" style="
                            background: linear-gradient(135deg, #e74c3c, #c0392b);
                            color: white;
                            border: none;
                            padding: 0.8rem 1.5rem;
                            border-radius: 10px;
                            cursor: pointer;
                            font-size: 1rem;
                            font-weight: 600;
                            transition: all 0.3s ease;
                            min-width: 120px;
                            box-shadow: 0 3px 12px rgba(231,76,60,0.4);
                            font-family: 'Cairo', sans-serif;
                        " onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 5px 15px rgba(231,76,60,0.5)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 3px 12px rgba(231,76,60,0.4)'">
                            <i class="fas fa-check-circle"></i> تأكيد الهوية
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.getElementById('modalContainer').innerHTML = modalHTML;
        
        // Focus on username field
        setTimeout(() => {
            const usernameField = document.getElementById('adminUsername');
            if (usernameField) {
                usernameField.focus();
                usernameField.select();
            }
        }, 300);
        
        console.log('✅ Admin verification modal displayed');
    }

    verifyAdmin() {
        const username = document.getElementById('adminUsername')?.value.trim();
        const password = document.getElementById('adminPassword')?.value;
        const errorDiv = document.getElementById('errorMessage');
        
        // Clear previous errors
        if (errorDiv) {
            errorDiv.style.display = 'none';
        }
        
        if (!username || !password) {
            this.showError('يرجى ملء جميع الحقول المطلوبة');
            return;
        }
        
        // Check admin credentials
        const admin = this.employees.find(emp =>
            emp.username === username &&
            emp.password === password &&
            (emp.role === 'admin' || emp.permissions.includes('admin') || emp.permissions.includes('all')) &&
            emp.status === 'active'
        );
        
        if (admin) {
            console.log('✅ Admin verified successfully');
            this.currentAdmin = admin;
            this.closeModal();
            this.showEmployeeManagement();
        } else {
            this.showError('اسم المستخدم أو كلمة المرور غير صحيحة، أو ليس لديك صلاحيات مدير');
        }
    }

    showError(message) {
        const errorDiv = document.getElementById('errorMessage');
        if (errorDiv) {
            errorDiv.innerHTML = `<i class="fas fa-exclamation-circle"></i> ${message}`;
            errorDiv.style.display = 'block';
            
            // Auto hide after 5 seconds
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
        }
    }

    showEmployeeManagement() {
        const modalHTML = `
            <div id="employeeModalOverlay" style="
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.85);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
                backdrop-filter: blur(10px);
            ">
                <div style="
                    background: linear-gradient(135deg, #ffffff, #f8f9fa);
                    padding: 3rem;
                    border-radius: 25px;
                    max-width: 800px;
                    width: 95%;
                    max-height: 90vh;
                    overflow-y: auto;
                    text-align: center;
                    box-shadow: 0 30px 100px rgba(0,0,0,0.5);
                    animation: slideUp 0.5s ease;
                ">
                    <!-- Success Header -->
                    <div style="margin-bottom: 3rem;">
                        <div style="
                            width: 100px;
                            height: 100px;
                            background: linear-gradient(135deg, #27ae60, #2ecc71);
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            margin: 0 auto 2rem;
                            color: white;
                            font-size: 3rem;
                            box-shadow: 0 15px 40px rgba(39, 174, 96, 0.4);
                            animation: pulse 2s infinite;
                        ">
                            <i class="fas fa-check-circle"></i>
                        </div>

                        <h2 style="
                            color: #27ae60;
                            margin: 0 0 1rem 0;
                            font-size: 2.2rem;
                            font-weight: 800;
                            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
                        ">
                            تم الوصول بنجاح!
                        </h2>

                        <p style="
                            color: #2c3e50;
                            font-size: 1.3rem;
                            margin-bottom: 2rem;
                            font-weight: 600;
                        ">
                            مرحباً بك في نظام إدارة حسابات دخول الموظفين
                        </p>

                        <div style="
                            background: linear-gradient(135deg, #e8f5e8, #d5f4e6);
                            border: 2px solid #27ae60;
                            border-radius: 15px;
                            padding: 1.5rem;
                            margin-bottom: 2rem;
                            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.2);
                        ">
                            <h4 style="
                                color: #27ae60;
                                margin: 0 0 0.5rem 0;
                                font-size: 1.3rem;
                                font-weight: 700;
                            ">
                                <i class="fas fa-check-double"></i> النظام يعمل بشكل مثالي!
                            </h4>
                            <p style="
                                margin: 0;
                                color: #2c3e50;
                                font-size: 1.1rem;
                                font-weight: 500;
                            ">
                                تم تحميل جميع الوظائف بنجاح ويمكنك الآن استخدام النظام بكامل إمكانياته
                            </p>
                        </div>
                    </div>

                    <!-- Features Grid -->
                    <div style="
                        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
                        padding: 2.5rem;
                        border-radius: 20px;
                        margin-bottom: 3rem;
                        border-left: 6px solid #3498db;
                        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
                    ">
                        <h4 style="
                            color: #2c3e50;
                            margin-bottom: 2rem;
                            font-size: 1.5rem;
                            font-weight: 700;
                        ">
                            <i class="fas fa-cogs" style="margin-left: 0.5rem; color: #3498db;"></i>
                            الوظائف المتاحة:
                        </h4>

                        <div style="
                            display: grid;
                            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                            gap: 1.5rem;
                            text-align: right;
                        ">
                            <div style="
                                padding: 1.5rem;
                                background: white;
                                border-radius: 15px;
                                box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                                border-left: 4px solid #3498db;
                                transition: transform 0.3s ease;
                            " onmouseover="this.style.transform='translateY(-5px)'" onmouseout="this.style.transform='translateY(0)'">
                                <i class="fas fa-user-plus" style="
                                    color: #3498db;
                                    margin-left: 1rem;
                                    font-size: 1.5rem;
                                "></i>
                                <strong style="font-size: 1.1rem;">إنشاء حسابات دخول جديدة للموظفين</strong>
                                <p style="margin: 0.5rem 0 0 0; color: #7f8c8d; font-size: 0.9rem;">
                                    إضافة موظفين جدد مع تحديد الصلاحيات والمناصب
                                </p>
                            </div>

                            <div style="
                                padding: 1.5rem;
                                background: white;
                                border-radius: 15px;
                                box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                                border-left: 4px solid #f39c12;
                                transition: transform 0.3s ease;
                            " onmouseover="this.style.transform='translateY(-5px)'" onmouseout="this.style.transform='translateY(0)'">
                                <i class="fas fa-edit" style="
                                    color: #f39c12;
                                    margin-left: 1rem;
                                    font-size: 1.5rem;
                                "></i>
                                <strong style="font-size: 1.1rem;">تعديل بيانات الحسابات الموجودة</strong>
                                <p style="margin: 0.5rem 0 0 0; color: #7f8c8d; font-size: 0.9rem;">
                                    تحديث معلومات الموظفين وبياناتهم الشخصية
                                </p>
                            </div>

                            <div style="
                                padding: 1.5rem;
                                background: white;
                                border-radius: 15px;
                                box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                                border-left: 4px solid #9b59b6;
                                transition: transform 0.3s ease;
                            " onmouseover="this.style.transform='translateY(-5px)'" onmouseout="this.style.transform='translateY(0)'">
                                <i class="fas fa-key" style="
                                    color: #9b59b6;
                                    margin-left: 1rem;
                                    font-size: 1.5rem;
                                "></i>
                                <strong style="font-size: 1.1rem;">إدارة الصلاحيات والمناصب</strong>
                                <p style="margin: 0.5rem 0 0 0; color: #7f8c8d; font-size: 0.9rem;">
                                    تحديد مستويات الوصول والصلاحيات لكل موظف
                                </p>
                            </div>

                            <div style="
                                padding: 1.5rem;
                                background: white;
                                border-radius: 15px;
                                box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                                border-left: 4px solid #27ae60;
                                transition: transform 0.3s ease;
                            " onmouseover="this.style.transform='translateY(-5px)'" onmouseout="this.style.transform='translateY(0)'">
                                <i class="fas fa-toggle-on" style="
                                    color: #27ae60;
                                    margin-left: 1rem;
                                    font-size: 1.5rem;
                                "></i>
                                <strong style="font-size: 1.1rem;">تفعيل وتعطيل الحسابات</strong>
                                <p style="margin: 0.5rem 0 0 0; color: #7f8c8d; font-size: 0.9rem;">
                                    التحكم في حالة الحسابات وإمكانية الوصول
                                </p>
                            </div>

                            <div style="
                                padding: 1.5rem;
                                background: white;
                                border-radius: 15px;
                                box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                                border-left: 4px solid #34495e;
                                transition: transform 0.3s ease;
                            " onmouseover="this.style.transform='translateY(-5px)'" onmouseout="this.style.transform='translateY(0)'">
                                <i class="fas fa-download" style="
                                    color: #34495e;
                                    margin-left: 1rem;
                                    font-size: 1.5rem;
                                "></i>
                                <strong style="font-size: 1.1rem;">تصدير بيانات الحسابات</strong>
                                <p style="margin: 0.5rem 0 0 0; color: #7f8c8d; font-size: 0.9rem;">
                                    حفظ نسخة احتياطية من بيانات جميع الموظفين
                                </p>
                            </div>

                            <div style="
                                padding: 1.5rem;
                                background: white;
                                border-radius: 15px;
                                box-shadow: 0 5px 15px rgba(0,0,0,0.1);
                                border-left: 4px solid #e74c3c;
                                transition: transform 0.3s ease;
                            " onmouseover="this.style.transform='translateY(-5px)'" onmouseout="this.style.transform='translateY(0)'">
                                <i class="fas fa-chart-line" style="
                                    color: #e74c3c;
                                    margin-left: 1rem;
                                    font-size: 1.5rem;
                                "></i>
                                <strong style="font-size: 1.1rem;">تقارير وإحصائيات الموظفين</strong>
                                <p style="margin: 0.5rem 0 0 0; color: #7f8c8d; font-size: 0.9rem;">
                                    عرض تقارير مفصلة عن أداء وحضور الموظفين
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Close Button -->
                    <button onclick="employeeManager.closeModal()" style="
                        background: linear-gradient(135deg, #3498db, #2980b9);
                        color: white;
                        border: none;
                        padding: 1.5rem 4rem;
                        border-radius: 15px;
                        cursor: pointer;
                        font-size: 1.3rem;
                        font-weight: 700;
                        transition: all 0.3s ease;
                        box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
                        font-family: 'Cairo', sans-serif;
                    " onmouseover="this.style.transform='translateY(-3px)'; this.style.boxShadow='0 12px 30px rgba(52, 152, 219, 0.5)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 8px 25px rgba(52, 152, 219, 0.4)'">
                        <i class="fas fa-times-circle"></i> إغلاق النظام
                    </button>
                </div>
            </div>
        `;

        document.getElementById('modalContainer').innerHTML = modalHTML;
        console.log('✅ Employee management interface displayed');
    }

    closeModal() {
        const modal = document.getElementById('employeeModalOverlay');
        if (modal) {
            modal.remove();
        }

        // Also clear modal container
        const container = document.getElementById('modalContainer');
        if (container) {
            container.innerHTML = '';
        }

        console.log('✅ Modal closed');
    }
}

// Initialize the system
const employeeManager = new EmployeeManager();

// Make it globally available
window.employeeManager = employeeManager;

console.log('🎉 Employee Management System loaded successfully!');
