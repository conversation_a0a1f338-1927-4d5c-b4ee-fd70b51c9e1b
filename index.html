<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <!-- =============================================== -->
    <!-- إعدادات الصفحة الأساسية -->
    <!-- =============================================== -->
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>هايبر ماركت - الصفحة الرئيسية</title>

    <!-- ملفات التنسيق -->
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- =============================================== -->
    <!-- رأس الصفحة والقائمة الرئيسية -->
    <!-- =============================================== -->
    <header class="header">
        <nav class="navbar">
            <div class="container">
                <!-- شعار الموقع واسم المتجر -->
                <div class="nav-brand">
                    <div class="logo">
                        <i class="fas fa-store"></i>
                        <h2>هايبر ماركت</h2>
                    </div>
                </div>
                <!-- قائمة التنقل الرئيسية -->
                <ul class="nav-menu">
                    <li class="nav-item">
                        <a href="#home" class="nav-link active" data-translate="nav-home">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a href="#products" class="nav-link" data-translate="nav-products">المنتجات</a>
                    </li>
                    <li class="nav-item">
                        <a href="#offers" class="nav-link" data-translate="nav-offers">العروض</a>
                    </li>
                    <li class="nav-item">
                        <a href="contact.html" class="nav-link" data-translate="nav-contact">اتصل بنا</a>
                    </li>
                </ul>
                <!-- أزرار الإجراءات في الهيدر -->
                <div class="header-actions">
                    <!-- مبدل اللغة - Language Switcher -->
                    <div class="language-switcher" onclick="toggleLanguage()">
                        <i class="fas fa-globe"></i>
                        <span id="currentLang">EN</span>
                    </div>

                    <!-- أيقونة سلة التسوق - Shopping Cart Icon -->
                    <div class="cart-icon" onclick="toggleCart()">
                        <i class="fas fa-shopping-cart"></i>
                        <span class="cart-count" id="cartCount">0</span>
                    </div>
                </div>
                <!-- قائمة الهامبرغر للهواتف - Mobile Hamburger Menu -->
                <div class="hamburger">
                    <span class="bar"></span>
                    <span class="bar"></span>
                    <span class="bar"></span>
                </div>
            </div>
        </nav>
    </header>

    <!-- Professional Sliders Section -->
    <section id="home" class="sliders-section">
        <div class="sliders-container" id="slidersContainer">
            <!-- السلايدرات ستتم إضافتها ديناميكياً من JavaScript -->
        </div>

        <!-- مؤشرات السلايدرات -->
        <div class="slider-indicators" id="sliderIndicators">
            <!-- المؤشرات ستتم إضافتها ديناميكياً -->
        </div>

        <!-- أزرار التنقل -->
        <div class="slider-navigation">
            <button class="slider-nav-btn prev" id="prevSlider" onclick="previousSlider()">
                <i class="fas fa-chevron-right"></i>
            </button>
            <button class="slider-nav-btn next" id="nextSlider" onclick="nextSlider()">
                <i class="fas fa-chevron-left"></i>
            </button>
        </div>

        <!-- زر التشغيل/الإيقاف -->
        <div class="slider-controls">
            <button class="slider-control-btn" id="playPauseBtn" onclick="toggleSliderAutoplay()">
                <i class="fas fa-pause"></i>
            </button>
        </div>
    </section>

    <!-- Categories Section -->
    <section id="categories" class="categories">
        <div class="container">
            <h2 class="section-title" data-translate="categories-title">أقسام المتجر</h2>
            <div class="categories-grid" id="mainCategoriesGrid">
                <!-- Categories will be loaded dynamically -->
            </div>
        </div>
    </section>

    <!-- Special Offers Section -->
    <section id="offers" class="offers">
        <div class="container">
            <h2 class="section-title" data-translate="offers-title">العروض الخاصة</h2>
            <div class="offer-timer">
                <h3 data-translate="offer-ends">العرض ينتهي خلال:</h3>
                <div class="countdown">
                    <div class="time-unit">
                        <span id="days">00</span>
                        <label data-translate="days">يوم</label>
                    </div>
                    <div class="time-unit">
                        <span id="hours">00</span>
                        <label data-translate="hours">ساعة</label>
                    </div>
                    <div class="time-unit">
                        <span id="minutes">00</span>
                        <label data-translate="minutes">دقيقة</label>
                    </div>
                    <div class="time-unit">
                        <span id="seconds">00</span>
                        <label data-translate="seconds">ثانية</label>
                    </div>
                </div>
            </div>
            <div class="offers-grid">
                <div class="offer-card">
                    <div class="offer-badge">خصم 40%</div>
                    <div class="product-image">
                        <img src="assets/banana.jpg" alt="موز">
                    </div>
                    <div class="product-info">
                        <h3 class="product-name">موز طازج</h3>
                        <p class="product-price">
                            <span class="current-price">12000 دينار</span>
                            <span class="old-price">20000 دينار</span>
                        </p>
                        <button class="add-to-cart" data-id="5" data-name="موز طازج" data-price="12000" data-image="assets/banana.jpg">أضف للسلة</button>
                    </div>
                </div>
                <div class="offer-card">
                    <div class="offer-badge">خصم 30%</div>
                    <div class="product-image">
                        <img src="assets/milk.jpg" alt="حليب">
                    </div>
                    <div class="product-info">
                        <h3 class="product-name">حليب طازج</h3>
                        <p class="product-price">
                            <span class="current-price">7000 دينار</span>
                            <span class="old-price">10000 دينار</span>
                        </p>
                        <button class="add-to-cart" data-id="6" data-name="حليب طازج" data-price="7000" data-image="assets/milk.jpg">أضف للسلة</button>
                    </div>
                </div>
                <div class="offer-card">
                    <div class="offer-badge">خصم 25%</div>
                    <div class="product-image">
                        <img src="assets/bread.jpg" alt="خبز">
                    </div>
                    <div class="product-info">
                        <h3 class="product-name">خبز طازج</h3>
                        <p class="product-price">
                            <span class="current-price">3000 دينار</span>
                            <span class="old-price">4000 دينار</span>
                        </p>
                        <button class="add-to-cart" data-id="7" data-name="خبز طازج" data-price="3000" data-image="assets/bread.jpg">أضف للسلة</button>
                    </div>
                </div>
                <div class="offer-card">
                    <div class="offer-badge">خصم 35%</div>
                    <div class="product-image">
                        <img src="assets/cheese.jpg" alt="جبن">
                    </div>
                    <div class="product-info">
                        <h3 class="product-name">جبن طبيعي</h3>
                        <p class="product-price">
                            <span class="current-price">13000 دينار</span>
                            <span class="old-price">20000 دينار</span>
                        </p>
                        <button class="add-to-cart" data-id="8" data-name="جبن طبيعي" data-price="13000" data-image="assets/cheese.jpg">أضف للسلة</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Products Section -->
    <section id="products" class="products">
        <div class="container">
            <h2 class="section-title" data-translate="products-title">منتجات مميزة</h2>

            <!-- فلاتر المنتجات -->
            <div class="products-filters">
                <div class="filter-tabs">
                    <button class="filter-tab active" onclick="filterProducts('all')" data-translate="all-products">جميع المنتجات</button>
                    <button class="filter-tab" onclick="filterProducts('featured')" data-translate="featured-products">منتجات مميزة</button>
                    <button class="filter-tab" onclick="filterProducts('offers')" data-translate="special-offers">عروض خاصة</button>
                </div>

                <div class="search-filter">
                    <input type="text" id="productSearch" placeholder="البحث في المنتجات..." data-translate="search-placeholder" onkeyup="searchProducts()">
                    <i class="fas fa-search"></i>
                </div>
            </div>

            <!-- شبكة المنتجات الديناميكية -->
            <div class="products-grid" id="productsGrid">
                <!-- سيتم تحميل المنتجات هنا من JavaScript -->
                <div class="loading-products">
                    <div class="loading-spinner"></div>
                    <p data-translate="loading-products">جاري تحميل المنتجات...</p>
                </div>
            </div>

            <!-- رسالة عدم وجود منتجات -->
            <div class="no-products" id="noProducts" style="display: none;">
                <div class="no-products-icon">
                    <i class="fas fa-box-open"></i>
                </div>
                <h3 data-translate="no-products-title">لا توجد منتجات</h3>
                <p data-translate="no-products-desc">لم يتم العثور على منتجات تطابق البحث</p>
                <button class="btn-primary" onclick="loadProducts()" data-translate="reload-products">إعادة تحميل</button>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title" data-translate="contact-section-title">تواصل معنا</h2>
                <p class="section-subtitle" data-translate="contact-section-subtitle">نحن هنا لخدمتك ومساعدتك في أي وقت</p>
            </div>

            <div class="contact-content">
                <div class="contact-grid">
                    <!-- Contact Form -->
                    <div class="contact-form-section">
                        <div class="form-header">
                            <h3 data-translate="contact-form-title">أرسل لنا رسالة</h3>
                            <p data-translate="contact-form-subtitle">سنرد عليك في أقرب وقت ممكن</p>
                        </div>

                        <form class="contact-form" id="mainContactForm">
                            <div class="form-row">
                                <div class="form-group">
                                    <input type="text" id="mainContactName" required data-translate="form-name-placeholder" placeholder="الاسم الكامل">
                                </div>
                                <div class="form-group">
                                    <input type="email" id="mainContactEmail" required data-translate="form-email-placeholder" placeholder="البريد الإلكتروني">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <input type="tel" id="mainContactPhone" required data-translate="form-phone-placeholder" placeholder="رقم الهاتف">
                                </div>
                                <div class="form-group">
                                    <select id="mainContactSubject" required>
                                        <option value="" data-translate="form-subject-placeholder">اختر الموضوع</option>
                                        <option value="general" data-translate="subject-general">استفسار عام</option>
                                        <option value="order" data-translate="subject-order">استفسار عن طلب</option>
                                        <option value="complaint" data-translate="subject-complaint">شكوى</option>
                                        <option value="suggestion" data-translate="subject-suggestion">اقتراح</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <textarea id="mainContactMessage" rows="4" required data-translate="form-message-placeholder" placeholder="اكتب رسالتك هنا..."></textarea>
                            </div>

                            <button type="submit" class="contact-submit-btn">
                                <i class="fas fa-paper-plane"></i>
                                <span data-translate="form-submit">إرسال الرسالة</span>
                            </button>
                        </form>
                    </div>

                    <!-- Contact Info -->
                    <div class="contact-info-section">
                        <div class="contact-cards">
                            <div class="contact-card">
                                <div class="card-icon">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                <div class="card-content">
                                    <h4 data-translate="address-title">العنوان</h4>
                                    <p id="contactAddress" data-translate="address-text">الرياض، المملكة العربية السعودية</p>
                                </div>
                            </div>

                            <div class="contact-card">
                                <div class="card-icon">
                                    <i class="fas fa-phone"></i>
                                </div>
                                <div class="card-content">
                                    <h4 data-translate="phone-title">الهاتف</h4>
                                    <p>
                                        <a href="tel:+966111234567" id="contactPhone1" data-translate="phone-number">+966 11 123 4567</a><br>
                                        <a href="tel:+966111234568" id="contactPhone2" data-translate="phone-number-2">+966 11 123 4568</a>
                                    </p>
                                </div>
                            </div>

                            <div class="contact-card">
                                <div class="card-icon">
                                    <i class="fas fa-envelope"></i>
                                </div>
                                <div class="card-content">
                                    <h4 data-translate="email-title">البريد الإلكتروني</h4>
                                    <p>
                                        <a href="mailto:<EMAIL>" id="contactEmail1" data-translate="email-address"><EMAIL></a><br>
                                        <a href="mailto:<EMAIL>" id="contactEmail2" data-translate="email-support"><EMAIL></a>
                                    </p>
                                </div>
                            </div>

                            <div class="contact-card">
                                <div class="card-icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="card-content">
                                    <h4 data-translate="hours-title">ساعات العمل</h4>
                                    <p id="contactHours" data-translate="hours-text">السبت - الخميس: 8:00 ص - 12:00 م<br>الجمعة: 2:00 م - 12:00 م</p>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Contact Actions -->
                        <div class="quick-contact-actions">
                            <a href="https://wa.me/966111234567" class="whatsapp-btn" target="_blank" id="whatsappLink">
                                <i class="fab fa-whatsapp"></i>
                                <span data-translate="whatsapp-btn">تواصل عبر الواتس اب</span>
                            </a>
                            <a href="tel:+966111234567" class="call-btn" id="callLink">
                                <i class="fas fa-phone"></i>
                                <span data-translate="call-btn">اتصل بنا الآن</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-bottom">
                <p data-translate="footer-copyright">&copy; 2024 برومت هايبر ماركت. جميع الحقوق محفوظة.</p>
                <p style="margin-top: 0.5rem; font-size: 0.8rem; opacity: 0.7;">
                    <a href="login.html" style="color: rgba(255,255,255,0.8); text-decoration: none;">
                        <i class="fas fa-cog"></i> <span data-translate="login-link">لوحة التحكم</span>
                    </a>
                </p>
            </div>
        </div>
    </footer>

    <!-- =============================================== -->
    <!-- نافذة سلة التسوق - Shopping Cart Modal -->
    <!-- =============================================== -->
    <div id="cartModal" class="cart-modal">
        <div class="cart-content">
            <!-- رأس نافذة السلة - Cart Header -->
            <div class="cart-header">
                <h2 data-translate="cart-title">سلة التسوق</h2>
                <span class="close-cart" onclick="toggleCart()">&times;</span>
            </div>

            <!-- محتوى السلة - Cart Body -->
            <div class="cart-body">
                <!-- قائمة المنتجات في السلة - Cart Items List -->
                <div id="cartItems" class="cart-items">
                    <p class="empty-cart">السلة فارغة</p>
                </div>
                <!-- =============================================== -->
                <!-- قسم كوبونات الخصم - Discount Coupons Section -->
                <!-- =============================================== -->
                <div class="coupon-section">
                    <!-- عنوان قسم الكوبونات - Coupon Section Header -->
                    <div class="coupon-header">
                        <i class="fas fa-ticket-alt"></i>
                        <h4 data-translate="coupon-title">كوبون الخصم</h4>
                    </div>

                    <!-- حقل إدخال الكوبون - Coupon Input Field -->
                    <div class="coupon-input-group">
                        <input type="text" id="couponInput" placeholder="أدخل كود الخصم" data-translate="coupon-placeholder" maxlength="20">
                        <button onclick="applyCoupon()" class="apply-coupon-btn">
                            <i class="fas fa-check"></i>
                            <span data-translate="apply-coupon">تطبيق</span>
                        </button>
                    </div>

                    <!-- رسائل حالة الكوبون - Coupon Status Messages -->
                    <div id="couponMessage" class="coupon-message"></div>

                    <!-- معلومات الكوبون المطبق - Applied Coupon Info -->
                    <div id="appliedCouponInfo" class="applied-coupon-info" style="display: none;">
                        <div class="coupon-success">
                            <i class="fas fa-check-circle"></i>
                            <span data-translate="applied-coupon">الكوبون المطبق:</span>
                            <strong id="appliedCouponCode"></strong>
                        </div>
                        <button onclick="removeCoupon()" class="remove-coupon-btn">
                            <i class="fas fa-times"></i>
                            <span data-translate="remove-coupon">إزالة</span>
                        </button>
                    </div>
                </div>

                <!-- Order Summary -->
                <!-- =============================================== -->
                <!-- ملخص الطلب والمبالغ - Order Summary -->
                <!-- =============================================== -->
                <div class="order-summary">
                    <!-- المجموع الفرعي - Subtotal -->
                    <div class="summary-row">
                        <span data-translate="subtotal">المجموع الفرعي:</span>
                        <span id="subtotal">0 دينار</span>
                    </div>

                    <!-- قيمة الخصم - Discount Amount -->
                    <div class="summary-row discount-row" id="discountRow" style="display: none;">
                        <span data-translate="discount">الخصم (<span id="discountCode"></span>):</span>
                        <span id="discountAmount" class="discount-value">-0 دينار</span>
                    </div>

                    <!-- خيارات التوصيل - Delivery Options -->
                    <div class="delivery-options">
                        <h4 data-translate="delivery-options">خيارات التوصيل:</h4>

                        <div class="delivery-option">
                            <input type="radio" id="pickup" name="deliveryType" value="pickup" checked onchange="updateDeliveryOption()">
                            <label for="pickup">
                                <i class="fas fa-store"></i>
                                <span data-translate="pickup">استلام من المحل</span>
                                <small data-translate="pickup-desc">مجاني</small>
                            </label>
                        </div>

                        <div class="delivery-option">
                            <input type="radio" id="delivery" name="deliveryType" value="delivery" onchange="updateDeliveryOption()">
                            <label for="delivery">
                                <i class="fas fa-truck"></i>
                                <span data-translate="home-delivery">التوصيل للمنزل</span>
                                <small data-translate="delivery-desc">حسب المنطقة</small>
                            </label>
                        </div>

                        <!-- اختيار المنطقة - Zone Selection -->
                        <div class="zone-selection" id="zoneSelection" style="display: none;">
                            <label for="deliveryZone" data-translate="select-zone">اختر منطقة التوصيل:</label>
                            <select id="deliveryZone" onchange="updateDeliveryFee()">
                                <option value="" data-translate="select-zone-placeholder">اختر المنطقة...</option>
                                <!-- سيتم ملء المناطق من JavaScript -->
                            </select>
                        </div>
                    </div>

                    <!-- عمولة التوصيل - Delivery Fee -->
                    <div class="summary-row delivery-fee-row" id="deliveryFeeRow" style="display: none;">
                        <span data-translate="delivery-fee">عمولة التوصيل:</span>
                        <span id="deliveryFeeAmount">0 دينار</span>
                    </div>

                    <!-- المجموع الكلي - Total Amount -->
                    <div class="summary-row total-row">
                        <span data-translate="total">المجموع الكلي:</span>
                        <span id="cartTotal">0 دينار</span>
                    </div>

                    <!-- معلومات التوفير - Savings Info -->
                    <div class="savings-info" id="savingsInfo" style="display: none;">
                        <i class="fas fa-piggy-bank"></i>
                        <span data-translate="you-saved">وفرت:</span>
                        <span id="totalSavings" class="savings-amount">0 دينار</span>
                    </div>
                </div>

                <!-- =============================================== -->
                <!-- معلومات العميل - Customer Information -->
                <!-- =============================================== -->
                <div class="customer-info">
                    <h4 data-translate="customer-info">معلومات العميل</h4>
                    <!-- حقل اسم العميل - Customer Name Field -->
                    <input type="text" id="customerName" placeholder="الاسم الكامل" data-translate="customer-name-placeholder" required>
                    <!-- حقل رقم الهاتف - Phone Number Field -->
                    <input type="tel" id="customerPhone" placeholder="رقم الهاتف" data-translate="customer-phone-placeholder" required>
                    <!-- حقل العنوان (اختياري) - Address Field (Optional) -->
                    <textarea id="customerAddress" placeholder="العنوان (اختياري)" data-translate="customer-address-placeholder" rows="2"></textarea>
                </div>

                <!-- =============================================== -->
                <!-- أزرار إجراءات السلة - Cart Action Buttons -->
                <!-- =============================================== -->
                <div class="cart-actions">
                    <!-- زر إفراغ السلة - Clear Cart Button -->
                    <button class="clear-cart" onclick="clearCart()">
                        <i class="fas fa-trash-alt"></i>
                        <span data-translate="clear-cart">إفراغ السلة</span>
                    </button>

                    <!-- زر إرسال الطلب عبر واتساب - WhatsApp Order Button -->
                    <button class="whatsapp-btn" onclick="sendToWhatsApp()">
                        <i class="fab fa-whatsapp"></i>
                        <span data-translate="send-whatsapp">إرسال الطلب واتس اب</span>
                    </button>
                </div>

                <!-- =============================================== -->
                <!-- معلومات إضافية - Additional Information -->
                <!-- =============================================== -->
                <div class="cart-footer-info">
                    <p class="delivery-info">
                        <i class="fas fa-truck"></i>
                        <span data-translate="delivery-info">التوصيل مجاني للطلبات أكثر من 50,000 دينار</span>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- =============================================== -->
    <!-- خلفية السلة المعتمة - Cart Overlay Background -->
    <!-- =============================================== -->
    <div id="cartOverlay" class="cart-overlay" onclick="toggleCart()"></div>

    <!-- =============================================== -->
    <!-- ملفات الجافاسكريبت - JavaScript Files -->
    <!-- =============================================== -->
    <script src="language.js"></script>  <!-- ملف إدارة اللغات - Language Management -->
    <script src="script.js"></script>    <!-- ملف الوظائف الرئيسية - Main Functions -->


</body>
</html>
