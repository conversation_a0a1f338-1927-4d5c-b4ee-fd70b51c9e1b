// Language Management System

// Language data
const translations = {
    ar: {
        // Navigation
        'nav-home': 'الرئيسية',
        'nav-products': 'المنتجات',
        'nav-offers': 'العروض',
        'nav-contact': 'اتصل بنا',
        
        // Hero Section
        'hero-title': 'مرحباً بكم في برومت هايبر ماركت',
        'hero-subtitle': 'أفضل المنتجات الطازجة والعالية الجودة بأسعار منافسة',
        'hero-button': 'تسوق الآن',
        
        // Categories
        'categories-title': 'فئات المنتجات',
        'category-fruits': 'خضروات وفواكه',
        'category-meat': 'لحوم ودواجن',
        'category-grocery': 'بقالة',
        'category-cleaning': 'منظفات',
        
        // Products
        'products-title': 'منتجاتنا المميزة',
        'add-to-cart': 'أضف للسلة',
        'out-of-stock': 'لقد نفذت الكمية حالياً',
        'out-of-stock-sub': 'سوف نوفرها في أقرب وقت',
        'special-offer': 'عرض خاص',
        'new-product': 'جديد',
        'limited-quantity': 'كمية محدودة',
        'sold-out': 'نفذت الكمية',
        
        // Offers
        'offers-title': 'العروض الخاصة',
        'offer-ends': 'العرض ينتهي خلال:',
        'days': 'يوم',
        'hours': 'ساعة',
        'minutes': 'دقيقة',
        'seconds': 'ثانية',
        'discount': 'خصم',
        
        // Cart
        'cart-title': 'سلة التسوق',
        'cart-empty': 'السلة فارغة',
        'cart-subtotal': 'المجموع الفرعي:',
        'cart-discount': 'الخصم',
        'cart-total': 'المجموع الكلي:',
        'clear-cart': 'إفراغ السلة',
        'checkout': 'إتمام الشراء',
        'send-whatsapp': 'إرسال الطلب واتس اب',
        
        // Coupon
        'coupon-title': 'كوبون الخصم',
        'coupon-placeholder': 'أدخل كود الخصم',
        'apply-coupon': 'تطبيق',
        'available-coupons': 'الكوبونات المتاحة:',
        
        // Customer Info
        'customer-info': 'معلومات العميل',
        'customer-name-placeholder': 'الاسم الكامل',
        'customer-phone-placeholder': 'رقم الهاتف',
        'customer-address-placeholder': 'العنوان (اختياري)',
        
        // Footer
        'footer-title': 'برومت هايبر ماركت',
        'footer-description': 'متجرك المفضل لجميع احتياجاتك اليومية بأفضل الأسعار وأعلى جودة',
        'footer-contact': 'معلومات التواصل',
        'footer-phone': 'الهاتف:',
        'footer-email': 'البريد الإلكتروني:',
        'footer-address': 'العنوان:',
        'footer-address-text': 'الرياض، المملكة العربية السعودية',
        'footer-phone-number': '+966 11 123 4567',
        'footer-email-address': '<EMAIL>',
        'footer-hours': 'ساعات العمل',
        'footer-hours-text': 'السبت - الخميس: 8:00 ص - 12:00 م<br>الجمعة: 2:00 م - 12:00 م',
        'footer-social': 'تابعونا',
        'footer-copyright': '© 2024 برومت هايبر ماركت. جميع الحقوق محفوظة.',
        'admin-panel': 'لوحة التحكم',

        // Contact Page
        'contact-title': 'تواصل معنا',
        'contact-subtitle': 'نحن هنا لخدمتك ومساعدتك في أي وقت',
        'contact-form-title': 'أرسل لنا رسالة',
        'contact-form-subtitle': 'سنرد عليك في أقرب وقت ممكن',
        'form-name': 'الاسم الكامل',
        'form-name-placeholder': 'أدخل اسمك الكامل',
        'form-email': 'البريد الإلكتروني',
        'form-email-placeholder': 'أدخل بريدك الإلكتروني',
        'form-phone': 'رقم الهاتف',
        'form-phone-placeholder': 'أدخل رقم هاتفك',
        'form-subject': 'الموضوع',
        'form-subject-placeholder': 'اختر الموضوع',
        'subject-general': 'استفسار عام',
        'subject-order': 'استفسار عن طلب',
        'subject-complaint': 'شكوى',
        'subject-suggestion': 'اقتراح',
        'form-message': 'الرسالة',
        'form-message-placeholder': 'اكتب رسالتك هنا...',
        'form-submit': 'إرسال الرسالة',
        'contact-info-title': 'معلومات التواصل',
        'contact-info-subtitle': 'تواصل معنا مباشرة',
        'address-title': 'العنوان',
        'address-text': 'الرياض، المملكة العربية السعودية\nحي النخيل، شارع الملك فهد',
        'phone-title': 'الهاتف',
        'phone-number': '+966 11 123 4567',
        'phone-number-2': '+966 11 123 4568',
        'email-title': 'البريد الإلكتروني',
        'email-address': '<EMAIL>',
        'email-support': '<EMAIL>',
        'hours-title': 'ساعات العمل',
        'hours-text': 'السبت - الخميس: 8:00 ص - 12:00 م\nالجمعة: 2:00 م - 12:00 م',
        'social-title': 'تابعونا على',
        'map-title': 'موقعنا على الخريطة',
        'map-placeholder': 'خريطة تفاعلية لموقع المتجر',
        'map-note': 'يمكنك النقر هنا لفتح الخريطة في تطبيق منفصل',
        'quick-contact-title': 'تحتاج مساعدة فورية؟',
        'quick-contact-subtitle': 'تواصل معنا عبر الواتس اب للحصول على رد سريع',
        'whatsapp-btn': 'تواصل عبر الواتس اب',
        'call-btn': 'اتصل بنا الآن',
        'back-home': 'العودة للرئيسية',

        // Contact Section in Main Page
        'contact-section-title': 'تواصل معنا',
        'contact-section-subtitle': 'نحن هنا لخدمتك ومساعدتك في أي وقت',
        
        // Messages
        'product-added': 'تم إضافة المنتج للسلة!',
        'product-unavailable': 'هذا المنتج غير متوفر حالياً',
        'quantity-available': 'الكمية المتوفرة:',
        'pieces-only': 'قطعة فقط',
        'additional-pieces': 'قطعة إضافية فقط',
        'currency': 'ريال'
    },
    en: {
        // Navigation
        'nav-home': 'Home',
        'nav-products': 'Products',
        'nav-offers': 'Offers',
        'nav-contact': 'Contact',
        
        // Hero Section
        'hero-title': 'Welcome to Bromet Hypermarket',
        'hero-subtitle': 'Best fresh and high-quality products at competitive prices',
        'hero-button': 'Shop Now',
        
        // Categories
        'categories-title': 'Product Categories',
        'category-fruits': 'Fruits & Vegetables',
        'category-meat': 'Meat & Poultry',
        'category-grocery': 'Grocery',
        'category-cleaning': 'Cleaning Products',
        
        // Products
        'products-title': 'Our Featured Products',
        'add-to-cart': 'Add to Cart',
        'out-of-stock': 'Currently out of stock',
        'out-of-stock-sub': 'Will be available soon',
        'special-offer': 'Special Offer',
        'new-product': 'New',
        'limited-quantity': 'Limited Quantity',
        'sold-out': 'Sold Out',
        
        // Offers
        'offers-title': 'Special Offers',
        'offer-ends': 'Offer ends in:',
        'days': 'Days',
        'hours': 'Hours',
        'minutes': 'Minutes',
        'seconds': 'Seconds',
        'discount': 'OFF',
        
        // Cart
        'cart-title': 'Shopping Cart',
        'cart-empty': 'Cart is empty',
        'cart-subtotal': 'Subtotal:',
        'cart-discount': 'Discount',
        'cart-total': 'Total:',
        'clear-cart': 'Clear Cart',
        'checkout': 'Checkout',
        'send-whatsapp': 'Send Order via WhatsApp',
        
        // Coupon
        'coupon-title': 'Discount Coupon',
        'coupon-placeholder': 'Enter discount code',
        'apply-coupon': 'Apply',
        'available-coupons': 'Available Coupons:',
        
        // Customer Info
        'customer-info': 'Customer Information',
        'customer-name-placeholder': 'Full Name',
        'customer-phone-placeholder': 'Phone Number',
        'customer-address-placeholder': 'Address (Optional)',
        
        // Footer
        'footer-title': 'Bromet Hypermarket',
        'footer-description': 'Your favorite store for all your daily needs at the best prices and highest quality',
        'footer-contact': 'Contact Information',
        'footer-phone': 'Phone:',
        'footer-email': 'Email:',
        'footer-address': 'Address:',
        'footer-address-text': 'Riyadh, Saudi Arabia',
        'footer-phone-number': '+966 11 123 4567',
        'footer-email-address': '<EMAIL>',
        'footer-hours': 'Working Hours',
        'footer-hours-text': 'Saturday - Thursday: 8:00 AM - 12:00 PM<br>Friday: 2:00 PM - 12:00 PM',
        'footer-social': 'Follow Us',
        'footer-copyright': '© 2024 Bromet Hypermarket. All rights reserved.',
        'admin-panel': 'Admin Panel',

        // Contact Page
        'contact-title': 'Contact Us',
        'contact-subtitle': 'We are here to serve and help you anytime',
        'contact-form-title': 'Send us a Message',
        'contact-form-subtitle': 'We will reply to you as soon as possible',
        'form-name': 'Full Name',
        'form-name-placeholder': 'Enter your full name',
        'form-email': 'Email Address',
        'form-email-placeholder': 'Enter your email address',
        'form-phone': 'Phone Number',
        'form-phone-placeholder': 'Enter your phone number',
        'form-subject': 'Subject',
        'form-subject-placeholder': 'Choose subject',
        'subject-general': 'General Inquiry',
        'subject-order': 'Order Inquiry',
        'subject-complaint': 'Complaint',
        'subject-suggestion': 'Suggestion',
        'form-message': 'Message',
        'form-message-placeholder': 'Write your message here...',
        'form-submit': 'Send Message',
        'contact-info-title': 'Contact Information',
        'contact-info-subtitle': 'Get in touch with us directly',
        'address-title': 'Address',
        'address-text': 'Riyadh, Saudi Arabia\nAl Nakheel District, King Fahd Street',
        'phone-title': 'Phone',
        'phone-number': '+966 11 123 4567',
        'phone-number-2': '+966 11 123 4568',
        'email-title': 'Email Address',
        'email-address': '<EMAIL>',
        'email-support': '<EMAIL>',
        'hours-title': 'Working Hours',
        'hours-text': 'Saturday - Thursday: 8:00 AM - 12:00 PM\nFriday: 2:00 PM - 12:00 PM',
        'social-title': 'Follow Us On',
        'map-title': 'Our Location on Map',
        'map-placeholder': 'Interactive map of store location',
        'map-note': 'You can click here to open the map in a separate app',
        'quick-contact-title': 'Need Immediate Help?',
        'quick-contact-subtitle': 'Contact us via WhatsApp for quick response',
        'whatsapp-btn': 'Contact via WhatsApp',
        'call-btn': 'Call Us Now',
        'back-home': 'Back to Home',

        // Contact Section in Main Page
        'contact-section-title': 'Contact Us',
        'contact-section-subtitle': 'We are here to serve and help you anytime',
        
        // Messages
        'product-added': 'Product added to cart!',
        'product-unavailable': 'This product is currently unavailable',
        'quantity-available': 'Available quantity:',
        'pieces-only': 'pieces only',
        'additional-pieces': 'additional pieces only',
        'currency': 'SAR'
    }
};

// Current language
let currentLanguage = localStorage.getItem('siteLanguage') || 'ar';

// Initialize language system
function initializeLanguage() {
    updateLanguageDisplay();
    translatePage();
    updateDirection();
}

// Toggle language
function toggleLanguage() {
    currentLanguage = currentLanguage === 'ar' ? 'en' : 'ar';
    localStorage.setItem('siteLanguage', currentLanguage);
    updateLanguageDisplay();
    translatePage();
    updateDirection();
    
    // Reload dynamic content
    if (typeof loadDynamicProducts === 'function') {
        loadDynamicProducts();
    }
    if (typeof updateCartDisplay === 'function') {
        updateCartDisplay();
    }
    if (typeof loadContactInfo === 'function') {
        loadContactInfo();
    }
    if (typeof updateLogoLanguage === 'function') {
        updateLogoLanguage();
    }
    if (typeof loadDynamicCategories === 'function') {
        loadDynamicCategories();
    }
}

// Update language display
function updateLanguageDisplay() {
    const langElement = document.getElementById('currentLang');
    if (langElement) {
        langElement.textContent = currentLanguage === 'ar' ? 'EN' : 'AR';
    }
}

// Update page direction
function updateDirection() {
    const isRTL = currentLanguage === 'ar';
    document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
    document.documentElement.lang = currentLanguage;

    // Update body class for styling
    document.body.className = document.body.className.replace(/lang-\w+/, '');
    document.body.classList.add(`lang-${currentLanguage}`);

    // Force layout recalculation for better rendering
    setTimeout(() => {
        document.body.style.display = 'none';
        document.body.offsetHeight; // Trigger reflow
        document.body.style.display = '';
    }, 50);
}

// Translate page elements
function translatePage() {
    const elements = document.querySelectorAll('[data-translate]');
    elements.forEach(element => {
        const key = element.getAttribute('data-translate');
        if (translations[currentLanguage] && translations[currentLanguage][key]) {
            const translation = translations[currentLanguage][key];

            if (element.tagName === 'INPUT' && (element.type === 'text' || element.type === 'tel')) {
                element.placeholder = translation;
            } else if (element.tagName === 'TEXTAREA') {
                element.placeholder = translation;
            } else {
                // Store original content if not already stored
                if (!element.hasAttribute('data-original-content')) {
                    element.setAttribute('data-original-content', element.innerHTML);
                }

                // Apply translation based on element type
                if (key.includes('footer-hours-text')) {
                    element.innerHTML = translation;
                } else if (element.tagName === 'SPAN' && element.parentElement) {
                    // For span elements, just replace text content
                    element.textContent = translation;
                } else if (element.children.length === 0) {
                    // Simple text element
                    element.textContent = translation;
                } else {
                    // Complex element - try to preserve structure
                    const textNodes = getDirectTextNodes(element);
                    if (textNodes.length > 0) {
                        textNodes[0].textContent = translation;
                        // Remove other text nodes
                        for (let i = 1; i < textNodes.length; i++) {
                            textNodes[i].textContent = '';
                        }
                    } else {
                        element.textContent = translation;
                    }
                }
            }
        }
    });
}

// Helper function to get direct text nodes
function getDirectTextNodes(element) {
    const textNodes = [];
    for (let node of element.childNodes) {
        if (node.nodeType === Node.TEXT_NODE && node.textContent.trim()) {
            textNodes.push(node);
        }
    }
    return textNodes;
}

// Set language
function setLanguage(lang) {
    if (lang && (lang === 'ar' || lang === 'en')) {
        currentLanguage = lang;
        localStorage.setItem('siteLanguage', currentLanguage);
        updateLanguageDisplay();
        translatePage();
        updateDirection();

        // Reload dynamic content
        if (typeof loadDynamicProducts === 'function') {
            loadDynamicProducts();
        }
        if (typeof updateCartDisplay === 'function') {
            updateCartDisplay();
        }
        if (typeof loadContactInfo === 'function') {
            loadContactInfo();
        }
        if (typeof updateLogoLanguage === 'function') {
            updateLogoLanguage();
        }
        if (typeof loadDynamicCategories === 'function') {
            loadDynamicCategories();
        }
    }
}

// Get translation
function t(key) {
    return translations[currentLanguage] && translations[currentLanguage][key]
        ? translations[currentLanguage][key]
        : key;
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    initializeLanguage();
});
