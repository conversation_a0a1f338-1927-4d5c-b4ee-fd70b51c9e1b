<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - برومت هايبر ماركت</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
            background-size: 400% 400%;
            animation: gradientBG 15s ease infinite;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            direction: rtl;
            position: relative;
            overflow-x: hidden;
        }

        @keyframes gradientBG {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* تأثيرات الخلفية */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
            z-index: 1;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            padding: 3rem;
            border-radius: 25px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.2);
            width: 100%;
            max-width: 450px;
            text-align: center;
            position: relative;
            z-index: 2;
            overflow: hidden;
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c, #4facfe);
            background-size: 300% 100%;
            animation: headerFlow 3s ease-in-out infinite;
        }

        @keyframes headerFlow {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .logo-section {
            margin-bottom: 2.5rem;
            position: relative;
            z-index: 2;
        }

        .logo-section i {
            font-size: 4.5rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
            animation: iconPulse 2s ease-in-out infinite;
        }

        @keyframes iconPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .logo-section h1 {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 2rem;
            font-weight: 800;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .logo-section p {
            color: #7f8c8d;
            font-size: 1rem;
            font-weight: 600;
            opacity: 0.8;
        }

        .login-form {
            margin-top: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
            text-align: right;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: #333;
            font-weight: 600;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e9ecef;
            border-radius: 15px;
            font-size: 1rem;
            font-family: 'Cairo', sans-serif;
            transition: all 0.3s ease;
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .form-group input {
            direction: ltr;
            text-align: left;
        }

        .form-group select {
            direction: rtl;
            text-align: right;
            cursor: pointer;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
            background: #ffffff;
        }

        .form-group select option {
            padding: 10px;
            font-size: 1rem;
        }

        .login-btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 18px 20px;
            border-radius: 15px;
            font-size: 1.1rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }

        .login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .login-btn:hover::before {
            left: 100%;
        }

        .login-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
        }

        .login-btn:active {
            transform: translateY(0);
        }

        .login-btn:disabled {
            background: #95a5a6;
            cursor: not-allowed;
            transform: none;
        }

        .loading-spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
            margin-right: 8px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .role-info {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            border: 2px solid rgba(102, 126, 234, 0.2);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            font-size: 0.95rem;
            color: #2c3e50;
            text-align: right;
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }

        .role-info::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 15px 15px 0 0;
        }

        .role-info h4 {
            margin: 0 0 1rem 0;
            color: #2c3e50;
            font-weight: 700;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .role-info h4::before {
            content: '👥';
            font-size: 1.2rem;
        }

        .role-info ul {
            margin: 0;
            padding-right: 1rem;
            line-height: 1.5;
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 1rem;
            border: 1px solid #f5c6cb;
            display: none;
        }

        .back-link {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #e1e1e1;
        }

        .back-link a {
            color: #2c5530;
            text-decoration: none;
            font-weight: 600;
            transition: color 0.3s ease;
        }

        .back-link a:hover {
            color: #1e3a21;
        }

        .back-link i {
            margin-left: 0.5rem;
        }

        @media (max-width: 480px) {
            .login-container {
                margin: 1rem;
                padding: 2rem;
            }

            .logo-section i {
                font-size: 3rem;
            }

            .logo-section h1 {
                font-size: 1.5rem;
            }
        }

        /* معلومات تسجيل الدخول في أسفل الصفحة */
        .login-info-footer {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 2rem;
            margin-top: 3rem;
            width: 100%;
            max-width: 1000px;
            position: relative;
            z-index: 2;
            overflow: hidden;
        }

        .login-info-footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c, #4facfe);
            background-size: 300% 100%;
            animation: footerFlow 3s ease-in-out infinite;
        }

        @keyframes footerFlow {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .footer-header {
            text-align: center;
            margin-bottom: 2rem;
            color: white;
        }

        .footer-header h2 {
            font-size: 1.8rem;
            font-weight: 700;
            margin: 0 0 0.5rem 0;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
        }

        .footer-header p {
            font-size: 1rem;
            opacity: 0.9;
            margin: 0;
        }

        .roles-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .role-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .role-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s;
        }

        .role-card:hover::before {
            left: 100%;
        }

        .role-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
        }

        .role-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
        }

        .role-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.3rem;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .role-card.admin .role-icon {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .role-card.supervisor .role-icon {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        .role-card.cashier .role-icon {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
        }

        .role-card.employee .role-icon {
            background: linear-gradient(135deg, #9b59b6, #8e44ad);
        }

        .role-title {
            color: white;
            font-size: 1.2rem;
            font-weight: 700;
            margin: 0;
        }

        .role-description {
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.9rem;
            margin-bottom: 1rem;
            line-height: 1.5;
        }

        .permissions-list {
            display: flex;
            flex-wrap: wrap;
            gap: 0.4rem;
        }

        .permission-item {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }

        .permission-item:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.05);
        }

        .security-notice {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: white;
        }

        .security-notice i {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: #4facfe;
        }

        .security-notice h3 {
            font-size: 1.3rem;
            font-weight: 700;
            margin: 0 0 0.75rem 0;
        }

        .security-notice p {
            font-size: 0.95rem;
            opacity: 0.9;
            margin: 0;
            line-height: 1.6;
        }

        /* تحسينات للشاشات الصغيرة */
        @media (max-width: 768px) {
            .login-info-footer {
                padding: 1.5rem;
                margin-top: 2rem;
            }

            .footer-header h2 {
                font-size: 1.5rem;
                flex-direction: column;
                gap: 0.5rem;
            }

            .roles-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .role-card {
                padding: 1rem;
            }

            .role-header {
                flex-direction: column;
                text-align: center;
                gap: 0.5rem;
            }

            .role-icon {
                width: 40px;
                height: 40px;
                font-size: 1.1rem;
            }

            .role-title {
                font-size: 1.1rem;
            }

            .security-notice {
                padding: 1rem;
            }

            .security-notice i {
                font-size: 2rem;
            }

            .security-notice h3 {
                font-size: 1.1rem;
            }

            .security-notice p {
                font-size: 0.85rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo-section">
            <i class="fas fa-store"></i>
            <h1>برومت هايبر ماركت</h1>
            <p>لوحة التحكم الإدارية</p>
        </div>



        <form class="login-form" id="loginForm">
            <div id="errorMessage" class="error-message"></div>

            <!-- اختيار صفة الدخول -->
            <div class="form-group">
                <label for="userRole">صفة الدخول</label>
                <select id="userRole" name="userRole" required>
                    <option value="">اختر صفة الدخول</option>
                    <option value="admin">👑 مدير النظام</option>
                    <option value="supervisor">👨‍💼 مشرف</option>
                    <option value="cashier">💰 كاشير</option>
                    <option value="employee">👤 موظف عادي</option>
                </select>
            </div>

            <div class="form-group">
                <label for="username">اسم المستخدم</label>
                <input type="text" id="username" name="username" required placeholder="أدخل اسم المستخدم">
            </div>

            <div class="form-group">
                <label for="password">كلمة المرور</label>
                <input type="password" id="password" name="password" required placeholder="أدخل كلمة المرور">
            </div>

            <button type="submit" class="login-btn" id="loginButton">
                <i class="fas fa-sign-in-alt"></i>
                تسجيل الدخول
                <div class="loading-spinner" id="loadingSpinner" style="display: none;"></div>
            </button>
        </form>

        <div class="back-link">
            <a href="index.html">
                <i class="fas fa-arrow-right"></i>
                العودة للموقع الرئيسي
            </a>
        </div>
    </div>

    <!-- معلومات تسجيل الدخول في أسفل الصفحة -->
    <div class="login-info-footer">
        <div class="footer-header">
            <h2>
                <i class="fas fa-users-cog"></i>
                معلومات تسجيل الدخول
            </h2>
            <p>جميع الصفات تدخل إلى لوحة التحكم الرئيسية مع صلاحيات مختلفة</p>
        </div>

        <div class="roles-grid">
            <div class="role-card admin">
                <div class="role-header">
                    <div class="role-icon">
                        <i class="fas fa-crown"></i>
                    </div>
                    <h3 class="role-title">المدير</h3>
                </div>
                <p class="role-description">صلاحية كاملة لجميع الأنظمة والإعدادات مع التحكم الكامل في النظام</p>
                <div class="permissions-list">
                    <span class="permission-item">إدارة المنتجات</span>
                    <span class="permission-item">إدارة الموظفين</span>
                    <span class="permission-item">التقارير المالية</span>
                    <span class="permission-item">الإعدادات العامة</span>
                    <span class="permission-item">النسخ الاحتياطي</span>
                </div>
            </div>

            <div class="role-card supervisor">
                <div class="role-header">
                    <div class="role-icon">
                        <i class="fas fa-user-tie"></i>
                    </div>
                    <h3 class="role-title">المشرف</h3>
                </div>
                <p class="role-description">إدارة العمليات اليومية والمنتجات مع صلاحيات الإشراف والمتابعة</p>
                <div class="permissions-list">
                    <span class="permission-item">إدارة المنتجات</span>
                    <span class="permission-item">إدارة المخزون</span>
                    <span class="permission-item">تقارير المبيعات</span>
                    <span class="permission-item">إدارة الخصومات</span>
                    <span class="permission-item">متابعة الطلبات</span>
                </div>
            </div>

            <div class="role-card cashier">
                <div class="role-header">
                    <div class="role-icon">
                        <i class="fas fa-cash-register"></i>
                    </div>
                    <h3 class="role-title">الكاشير</h3>
                </div>
                <p class="role-description">الوصول لنظام الكاشير والمبيعات مع إدارة العمليات المالية اليومية</p>
                <div class="permissions-list">
                    <span class="permission-item">نظام الكاشير</span>
                    <span class="permission-item">معالجة المبيعات</span>
                    <span class="permission-item">طباعة الفواتير</span>
                    <span class="permission-item">إدارة النقد</span>
                    <span class="permission-item">تقارير المبيعات</span>
                </div>
            </div>

            <div class="role-card employee">
                <div class="role-header">
                    <div class="role-icon">
                        <i class="fas fa-user"></i>
                    </div>
                    <h3 class="role-title">الموظف</h3>
                </div>
                <p class="role-description">صلاحيات محدودة للعرض والتقارير مع إمكانية المساعدة في العمليات الأساسية</p>
                <div class="permissions-list">
                    <span class="permission-item">عرض المنتجات</span>
                    <span class="permission-item">عرض التقارير</span>
                    <span class="permission-item">البحث والاستعلام</span>
                    <span class="permission-item">المساعدة</span>
                    <span class="permission-item">دعم العملاء</span>
                </div>
            </div>
        </div>

        <div class="security-notice">
            <i class="fas fa-shield-alt"></i>
            <h3>نظام الأمان والصلاحيات</h3>
            <p>يتم تحديد الصلاحيات تلقائياً حسب نوع المستخدم المسجل دخوله. جميع العمليات محمية ومراقبة لضمان أمان النظام والبيانات.</p>
        </div>
    </div>

    <script>
        // تهيئة النظام
        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من وجود جلسة نشطة
            checkExistingSession();

            // التركيز على القائمة المنسدلة
            document.getElementById('userRole').focus();
        });

        // معالج تسجيل الدخول
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            handleLogin();
        });

        function handleLogin() {
            const userRole = document.getElementById('userRole').value;
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            const errorDiv = document.getElementById('errorMessage');
            const loginButton = document.getElementById('loginButton');
            const spinner = document.getElementById('loadingSpinner');

            // إخفاء رسالة الخطأ السابقة
            errorDiv.style.display = 'none';

            // التحقق من البيانات المطلوبة
            if (!userRole) {
                showError('يرجى اختيار صفة الدخول');
                return;
            }

            if (!username || !password) {
                showError('يرجى إدخال اسم المستخدم وكلمة المرور');
                return;
            }

            // إظهار حالة التحميل
            loginButton.disabled = true;
            spinner.style.display = 'inline-block';

            // محاكاة تأخير الشبكة
            setTimeout(() => {
                authenticateUser(userRole, username, password);

                // إخفاء حالة التحميل
                loginButton.disabled = false;
                spinner.style.display = 'none';
            }, 1000);
        }

        function authenticateUser(selectedRole, username, password) {
            // تحميل بيانات الموظفين من localStorage
            const employees = JSON.parse(localStorage.getItem('employees')) || [];

            // البحث عن الموظف
            const employee = employees.find(emp =>
                emp.username === username &&
                emp.password === password &&
                emp.status === 'active'
            );

            if (employee) {
                // التحقق من تطابق الصفة المختارة مع صفة الموظف
                if (employee.role !== selectedRole) {
                    showError(`صفة الدخول المختارة (${getRoleText(selectedRole)}) لا تتطابق مع صفة حسابك (${getRoleText(employee.role)})`);
                    return;
                }

                // تسجيل دخول ناجح
                const userSession = {
                    id: employee.id,
                    name: employee.name,
                    username: employee.username,
                    role: employee.role,
                    loginTime: new Date().toISOString(),
                    isLoggedIn: true
                };

                // حفظ الجلسة
                sessionStorage.setItem('userSession', JSON.stringify(userSession));
                sessionStorage.setItem('isLoggedIn', 'true');

                // عرض رسالة نجاح
                showSuccess(`مرحباً ${employee.name}! جاري التحويل...`);

                // توجيه المستخدم حسب صفته
                setTimeout(() => {
                    redirectUserByRole(employee.role);
                }, 1500);

            } else {
                showError('اسم المستخدم أو كلمة المرور غير صحيحة، أو أن الحساب غير نشط');
            }
        }

        function redirectUserByRole(role) {
            // جميع الصفات تذهب إلى لوحة التحكم الرئيسية
            // مع تطبيق الصلاحيات المناسبة لكل صفة

            console.log(`🔄 توجيه المستخدم (${getRoleText(role)}) إلى لوحة التحكم الرئيسية`);

            // حفظ معلومات إضافية للكاشير إذا لزم الأمر
            if (role === 'cashier') {
                const cashierSession = JSON.parse(sessionStorage.getItem('userSession'));
                sessionStorage.setItem('cashierUser', JSON.stringify(cashierSession));
            }

            // توجيه جميع المستخدمين إلى لوحة التحكم الرئيسية
            window.location.href = 'admin.html';
        }

        function getRoleText(role) {
            const roleTexts = {
                'admin': 'مدير النظام',
                'supervisor': 'مشرف',
                'cashier': 'كاشير',
                'employee': 'موظف عادي'
            };
            return roleTexts[role] || 'غير محدد';
        }

        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            errorDiv.style.background = '#f8d7da';
            errorDiv.style.color = '#721c24';
            errorDiv.style.borderColor = '#f5c6cb';

            // إخفاء الرسالة بعد 5 ثوان
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
        }

        function showSuccess(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            errorDiv.style.background = '#d4edda';
            errorDiv.style.color = '#155724';
            errorDiv.style.borderColor = '#c3e6cb';
        }

        function checkExistingSession() {
            const userSession = sessionStorage.getItem('userSession');
            if (userSession) {
                try {
                    const user = JSON.parse(userSession);
                    if (user && user.isLoggedIn) {
                        // إعادة توجيه حسب الصفة
                        redirectUserByRole(user.role);
                    }
                } catch (error) {
                    // مسح الجلسة التالفة
                    sessionStorage.removeItem('userSession');
                    sessionStorage.removeItem('isLoggedIn');
                }
            }
        }

        // دعم لوحة المفاتيح
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                const form = document.getElementById('loginForm');
                if (form) {
                    form.dispatchEvent(new Event('submit'));
                }
            }
        });
    </script>
</body>
</html>
