/*
 * ===============================================
 * نظام سلة التسوق الرئيسي
 * ===============================================
 * هذا الملف يحتوي على جميع وظائف سلة التسوق والكوبونات
 * والتفاعل مع المنتجات والمخزون
 */

// متغيرات السلة الرئيسية
let cart = JSON.parse(localStorage.getItem('cart')) || []; // سلة التسوق المحفوظة في المتصفح
let appliedCoupon = JSON.parse(localStorage.getItem('appliedCoupon')) || null; // الكوبون المطبق حالياً

// متغيرات التوصيل
let deliveryZones = []; // مناطق التوصيل المتاحة
let selectedDeliveryZone = null; // المنطقة المختارة
let deliveryFee = 0; // عمولة التوصيل الحالية
let deliveryType = 'pickup'; // نوع التوصيل (pickup أو delivery)

/**
 * تحميل الكوبونات من لوحة الإدارة أو استخدام الافتراضية
 * @returns {Object} كائن يحتوي على جميع الكوبونات المتاحة
 */
function loadCoupons() {
    // محاولة تحميل الكوبونات من لوحة الإدارة
    const adminCoupons = JSON.parse(localStorage.getItem('mainSiteCoupons'));

    // إذا وجدت كوبونات في لوحة الإدارة، استخدمها
    if (adminCoupons && Object.keys(adminCoupons).length > 0) {
        return adminCoupons;
    }

    // الكوبونات الافتراضية إذا لم توجد كوبونات في لوحة الإدارة
    return {
        'WELCOME10': {
            type: 'percentage',        // نوع الخصم: نسبة مئوية
            value: 10,                 // قيمة الخصم: 10%
            description: 'خصم 10%',    // وصف الكوبون
            minAmount: 0               // الحد الأدنى للطلب (بالآلاف)
        },
        'SAVE20': {
            type: 'fixed',             // نوع الخصم: مبلغ ثابت
            value: 20,                 // قيمة الخصم: 20,000 دينار
            description: 'خصم 20000 دينار',
            minAmount: 50              // الحد الأدنى للطلب: 50,000 دينار
        },
        'FIRST50': {
            type: 'percentage',        // نوع الخصم: نسبة مئوية
            value: 50,                 // قيمة الخصم: 50%
            description: 'خصم 50%',
            minAmount: 100             // الحد الأدنى للطلب: 100,000 دينار
        }
    };
}

// تحميل الكوبونات الحالية
let coupons = loadCoupons();

// Mobile Menu Toggle
const hamburger = document.querySelector('.hamburger');
const navMenu = document.querySelector('.nav-menu');

if (hamburger) {
    hamburger.addEventListener('click', () => {
        hamburger.classList.toggle('active');
        navMenu.classList.toggle('active');
    });
}

// Close mobile menu when clicking on a link
document.querySelectorAll('.nav-link').forEach(n => n.addEventListener('click', () => {
    if (hamburger) {
        hamburger.classList.remove('active');
        navMenu.classList.remove('active');
    }
}));

// Smooth scrolling for navigation links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

/*
 * ===============================================
 * وظائف السلة الأساسية
 * ===============================================
 */

/**
 * تحديث عداد المنتجات في أيقونة السلة
 */
function updateCartCount() {
    const cartCount = document.getElementById('cartCount'); // عنصر عداد السلة في الواجهة
    const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0); // حساب إجمالي المنتجات
    if (cartCount) {
        cartCount.textContent = totalItems; // تحديث النص المعروض
    }
}

/**
 * حفظ السلة في التخزين المحلي للمتصفح
 */
function saveCart() {
    localStorage.setItem('cart', JSON.stringify(cart));
}

/**
 * إضافة منتج إلى السلة مع التحقق من المخزون
 * @param {string} id - معرف المنتج
 * @param {string} name - اسم المنتج
 * @param {number} price - سعر المنتج
 * @param {string} image - رابط صورة المنتج
 */
function addToCart(id, name, price, image) {
    // التحقق من توفر المنتج في المخزون
    const adminProducts = JSON.parse(localStorage.getItem('adminProducts')) || [];
    const product = adminProducts.find(p => p.id === id);

    // إذا كان المنتج غير متوفر، عرض رسالة خطأ
    if (product && product.stock <= 0) {
        showStockErrorMessage('هذا المنتج غير متوفر حالياً');
        return;
    }

    // البحث عن المنتج في السلة إذا كان موجوداً مسبقاً
    const existingItem = cart.find(item => item.id === id);
    const newQuantity = existingItem ? existingItem.quantity + 1 : 1;

    // التحقق من عدم تجاوز الكمية المطلوبة للمخزون المتاح
    if (product && newQuantity > product.stock) {
        showStockErrorMessage(`الكمية المتوفرة: ${product.stock} قطعة فقط`);
        return;
    }

    // إضافة المنتج أو زيادة الكمية
    if (existingItem) {
        existingItem.quantity += 1; // زيادة الكمية إذا كان المنتج موجوداً
    } else {
        // إضافة منتج جديد للسلة
        cart.push({
            id: id,
            name: name,
            price: parseFloat(price),
            image: image,
            quantity: 1
        });
    }

    // تحديث المخزون في قاعدة البيانات المحلية
    updateProductStock(id, 1);

    // حفظ السلة وتحديث الواجهة
    saveCart();
    updateCartCount();
    updateCartDisplay();
    showAddToCartFeedback(); // عرض رسالة تأكيد الإضافة
}

/**
 * عرض رسالة خطأ عند عدم توفر المخزون الكافي
 * @param {string} message - نص رسالة الخطأ
 */
function showStockErrorMessage(message) {
    // إنشاء عنصر الإشعار
    const notification = document.createElement('div');
    const isRTL = currentLanguage === 'ar'; // التحقق من اتجاه اللغة

    // تنسيق الإشعار
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        ${isRTL ? 'right: 20px' : 'left: 20px'};
        background: #e74c3c;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        z-index: 3000;
        font-weight: 600;
        box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        transform: translateX(${isRTL ? '100%' : '-100%'});
        transition: transform 0.3s ease;
        direction: ${isRTL ? 'rtl' : 'ltr'};
    `;

    // محتوى الإشعار مع أيقونة تحذير
    notification.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;

    // إضافة الإشعار للصفحة
    document.body.appendChild(notification);

    // تحريك الإشعار للظهور
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // إزالة الإشعار بعد 4 ثوان
    setTimeout(() => {
        notification.style.transform = `translateX(${isRTL ? '100%' : '-100%'})`;
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 4000);
}

/**
 * إزالة منتج من السلة وإعادة الكمية للمخزون
 * @param {string} id - معرف المنتج المراد إزالته
 */
function removeFromCart(id) {
    // البحث عن المنتج في السلة
    const item = cart.find(item => item.id === id);
    if (item) {
        // إعادة الكمية للمخزون عند إزالة المنتج
        returnProductStock(id, item.quantity);
    }

    // إزالة المنتج من السلة
    cart = cart.filter(item => item.id !== id);

    // تحديث السلة والواجهة
    saveCart();
    updateCartCount();
    updateCartDisplay();
}

/**
 * تحديث كمية منتج في السلة مع التحقق من المخزون
 * @param {string} id - معرف المنتج
 * @param {number} newQuantity - الكمية الجديدة
 */
function updateQuantity(id, newQuantity) {
    // البحث عن المنتج في السلة
    const item = cart.find(item => item.id === id);
    if (!item) return;

    // إذا كانت الكمية الجديدة صفر أو أقل، قم بإزالة المنتج
    if (newQuantity <= 0) {
        // إعادة الكمية للمخزون عند إزالة المنتج
        const quantityToReturn = item.quantity;
        returnProductStock(id, quantityToReturn);
        removeFromCart(id);
        return;
    }

    // التحقق من توفر المخزون عند زيادة الكمية
    if (newQuantity > item.quantity) {
        const additionalQuantity = newQuantity - item.quantity; // الكمية الإضافية المطلوبة
        const adminProducts = JSON.parse(localStorage.getItem('adminProducts')) || [];
        const product = adminProducts.find(p => p.id === id);

        // التحقق من توفر الكمية المطلوبة في المخزون
        if (product && additionalQuantity > product.stock) {
            showStockErrorMessage(`الكمية المتوفرة: ${product.stock} قطعة إضافية فقط`);
            return;
        }

        // تحديث المخزون في قاعدة البيانات
        updateProductStock(id, additionalQuantity);
    } else if (newQuantity < item.quantity) {
        // إعادة الكمية للمخزون عند تقليل الكمية
        const returnQuantity = item.quantity - newQuantity;
        returnProductStock(id, returnQuantity);
    }

    // تحديث الكمية في السلة
    item.quantity = newQuantity;

    // تحديث السلة والواجهة
    saveCart();
    updateCartCount();
    updateCartDisplay();
}

/**
 * إعادة الكمية للمخزون عند إزالة المنتج من السلة
 * @param {string} productId - معرف المنتج
 * @param {number} quantity - الكمية المراد إعادتها
 */
function returnProductStock(productId, quantity) {
    // تحميل قائمة المنتجات من لوحة الإدارة
    const adminProducts = JSON.parse(localStorage.getItem('adminProducts')) || [];
    const productIndex = adminProducts.findIndex(p => p.id === productId);

    // إذا وجد المنتج، قم بإعادة الكمية للمخزون
    if (productIndex !== -1) {
        adminProducts[productIndex].stock += quantity;
        localStorage.setItem('adminProducts', JSON.stringify(adminProducts));

        // إعادة تحميل المنتجات لإظهار المخزون المحدث
        loadDynamicProducts();
    }
}

/**
 * إفراغ السلة وإعادة جميع الكميات للمخزون
 */
function clearCart() {
    // إعادة جميع الكميات للمخزون قبل إفراغ السلة
    cart.forEach(item => {
        returnProductStock(item.id, item.quantity);
    });

    // إفراغ السلة وإزالة الكوبون المطبق
    cart = [];
    appliedCoupon = null;

    // تحديث التخزين المحلي والواجهة
    saveCart();
    localStorage.removeItem('appliedCoupon');
    updateCartCount();
    updateCartDisplay();
}

/**
 * إظهار أو إخفاء نافذة السلة
 */
function toggleCart() {
    const cartModal = document.getElementById('cartModal');   // نافذة السلة
    const cartOverlay = document.getElementById('cartOverlay'); // الخلفية المعتمة

    // تبديل حالة الظهور/الإخفاء للسلة والخلفية
    if (cartModal && cartOverlay) {
        cartModal.classList.toggle('active');
        cartOverlay.classList.toggle('active');
    }
}

function updateCartDisplay() {
    const cartItems = document.getElementById('cartItems');
    const subtotalEl = document.getElementById('subtotal');
    const cartTotal = document.getElementById('cartTotal');
    const discountRow = document.getElementById('discountRow');
    const discountCode = document.getElementById('discountCode');
    const discountAmount = document.getElementById('discountAmount');
    const savingsInfo = document.getElementById('savingsInfo');
    const totalSavings = document.getElementById('totalSavings');

    if (!cartItems) return;

    if (cart.length === 0) {
        cartItems.innerHTML = '<p class="empty-cart" data-translate="empty-cart">السلة فارغة</p>';
        if (subtotalEl) subtotalEl.textContent = '0 دينار';
        if (cartTotal) cartTotal.textContent = '0 دينار';
        if (discountRow) discountRow.style.display = 'none';
        if (savingsInfo) savingsInfo.style.display = 'none';
        return;
    }

    let subtotal = 0;
    cartItems.innerHTML = '';

    cart.forEach(item => {
        subtotal += item.price * item.quantity;
        const itemTotal = item.price * item.quantity;

        const cartItem = document.createElement('div');
        cartItem.className = 'cart-item';
        cartItem.innerHTML = `
            <img src="${item.image}" alt="${item.name}" onerror="this.src='https://via.placeholder.com/60x60?text=صورة'">
            <div class="cart-item-info">
                <div class="cart-item-name">${item.name}</div>
                <div class="cart-item-price">${item.price.toLocaleString()} دينار × ${item.quantity}</div>
                <div class="cart-item-total">المجموع: ${itemTotal.toLocaleString()} دينار</div>
                <div class="quantity-controls">
                    <button class="quantity-btn" onclick="updateQuantity('${item.id}', ${item.quantity - 1})" title="تقليل الكمية">
                        <i class="fas fa-minus"></i>
                    </button>
                    <span class="quantity">${item.quantity}</span>
                    <button class="quantity-btn" onclick="updateQuantity('${item.id}', ${item.quantity + 1})" title="زيادة الكمية">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
            <button class="remove-item" onclick="removeFromCart('${item.id}')" title="حذف من السلة">
                <i class="fas fa-trash-alt"></i>
            </button>
        `;
        cartItems.appendChild(cartItem);
    });

    // Calculate discount if coupon is applied
    let discount = 0;
    let finalTotal = subtotal;

    if (appliedCoupon && coupons[appliedCoupon.code]) {
        const coupon = coupons[appliedCoupon.code];
        if (subtotal >= coupon.minAmount) {
            if (coupon.type === 'percentage') {
                discount = subtotal * (coupon.value / 100);
            } else {
                discount = coupon.value * 1000; // Convert to dinars
            }
            finalTotal = subtotal - discount;
        }
    }

    // Update display
    if (subtotalEl) subtotalEl.textContent = `${subtotal.toLocaleString()} دينار`;

    // إضافة عمولة التوصيل للمجموع النهائي
    const totalWithDelivery = finalTotal + deliveryFee;
    if (cartTotal) cartTotal.textContent = `${totalWithDelivery.toLocaleString()} دينار`;

    // Show/hide discount row
    if (discountRow) {
        if (discount > 0) {
            discountRow.style.display = 'flex';
            if (discountCode) discountCode.textContent = appliedCoupon.code;
            if (discountAmount) discountAmount.textContent = `-${discount.toLocaleString()} دينار`;
        } else {
            discountRow.style.display = 'none';
        }
    }

    // Show savings info if there's a discount
    if (savingsInfo && totalSavings) {
        if (discount > 0) {
            savingsInfo.style.display = 'flex';
            totalSavings.textContent = `${discount.toLocaleString()} دينار`;
        } else {
            savingsInfo.style.display = 'none';
        }
    }

    // Update applied coupon info
    const appliedCouponInfo = document.getElementById('appliedCouponInfo');
    const appliedCouponCode = document.getElementById('appliedCouponCode');

    if (appliedCouponInfo && appliedCouponCode) {
        if (appliedCoupon && discount > 0) {
            appliedCouponInfo.style.display = 'flex';
            appliedCouponCode.textContent = appliedCoupon.code;
        } else {
            appliedCouponInfo.style.display = 'none';
        }
    }
}

// وظائف الكوبونات
function applyCoupon() {
    const couponInput = document.getElementById('couponInput');
    const couponMessage = document.getElementById('couponMessage');

    if (!couponInput || !couponMessage) return;

    // إعادة تحميل الكوبونات في حالة تحديثها من لوحة الإدارة
    coupons = loadCoupons();

    const code = couponInput.value.trim().toUpperCase();

    if (!code) {
        showCouponMessage('يرجى إدخال كود الخصم', 'error');
        couponInput.focus();
        return;
    }

    if (cart.length === 0) {
        showCouponMessage('أضف منتجات للسلة أولاً', 'error');
        return;
    }

    if (appliedCoupon && appliedCoupon.code === code) {
        showCouponMessage('هذا الكوبون مطبق بالفعل', 'error');
        return;
    }

    const coupon = coupons[code];
    if (!coupon) {
        showCouponMessage('كود الخصم غير صحيح أو منتهي الصلاحية', 'error');
        couponInput.focus();
        return;
    }

    // التحقق من انتهاء صلاحية الكوبون
    if (coupon.expiryDate && new Date() > new Date(coupon.expiryDate)) {
        showCouponMessage('انتهت صلاحية هذا الكوبون', 'error');
        return;
    }

    // التحقق من حد الاستخدام
    if (coupon.usageLimit && coupon.usedCount >= coupon.usageLimit) {
        showCouponMessage('تم استنفاد استخدامات هذا الكوبون', 'error');
        return;
    }

    const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    if (subtotal < coupon.minAmount) {
        showCouponMessage(`الحد الأدنى للطلب ${(coupon.minAmount * 1000).toLocaleString()} دينار`, 'error');
        return;
    }

    // حساب مبلغ الخصم للعرض
    let discountAmount = 0;
    if (coupon.type === 'percentage') {
        discountAmount = subtotal * (coupon.value / 100);
    } else {
        discountAmount = coupon.value * 1000;
    }

    appliedCoupon = { code: code, ...coupon };
    localStorage.setItem('appliedCoupon', JSON.stringify(appliedCoupon));

    showCouponMessage(`تم تطبيق الكوبون بنجاح! وفرت ${discountAmount.toLocaleString()} دينار`, 'success');
    couponInput.value = '';
    updateCartDisplay();

    // تحديث استخدام الكوبون في لوحة الإدارة
    updateCouponUsage(code);

    // إضافة تأثير حركي للنجاح
    const applyBtn = document.querySelector('.apply-coupon-btn');
    if (applyBtn) {
        applyBtn.style.transform = 'scale(0.95)';
        setTimeout(() => {
            applyBtn.style.transform = 'scale(1)';
        }, 150);
    }
}

function useCoupon(code) {
    const couponInput = document.getElementById('couponInput');
    if (couponInput) {
        couponInput.value = code;
        applyCoupon();
    }
}

function removeCoupon() {
    appliedCoupon = null;
    localStorage.removeItem('appliedCoupon');
    updateCartDisplay();
    showCouponMessage('تم إلغاء الكوبون', 'success');
}

function showCouponMessage(message, type) {
    const couponMessage = document.getElementById('couponMessage');
    if (!couponMessage) return;

    couponMessage.textContent = message;
    couponMessage.className = `coupon-message ${type}`;

    setTimeout(() => {
        couponMessage.textContent = '';
        couponMessage.className = 'coupon-message';
    }, 3000);
}

function updateCouponUsage(couponCode) {
    const adminCoupons = JSON.parse(localStorage.getItem('adminCoupons')) || [];
    const couponIndex = adminCoupons.findIndex(c => c.code === couponCode);

    if (couponIndex !== -1) {
        adminCoupons[couponIndex].currentUses += 1;
        localStorage.setItem('adminCoupons', JSON.stringify(adminCoupons));
    }
}

// Stock status helper function for main site
function getProductStockStatus(product) {
    const stock = product.stock || 0;

    if (stock === 0) {
        return {
            class: 'out-of-stock',
            badge: 'نفذت الكمية',
            badgeClass: 'out-of-stock',
            outOfStock: true
        };
    } else if (stock <= (product.minStock || 5)) {
        return {
            class: 'low-stock',
            badge: 'كمية محدودة',
            badgeClass: 'low-stock',
            outOfStock: false
        };
    } else {
        return {
            class: 'in-stock',
            badge: null,
            badgeClass: null,
            outOfStock: false
        };
    }
}

// Update stock when adding to cart
function updateProductStock(productId, quantity = 1) {
    const adminProducts = JSON.parse(localStorage.getItem('adminProducts')) || [];
    const productIndex = adminProducts.findIndex(p => p.id === productId);

    if (productIndex !== -1 && adminProducts[productIndex].stock >= quantity) {
        adminProducts[productIndex].stock -= quantity;
        localStorage.setItem('adminProducts', JSON.stringify(adminProducts));

        // Reload products to show updated stock
        loadDynamicProducts();
        return true;
    }
    return false;
}

function showAddToCartFeedback() {
    // Create notification
    const notification = document.createElement('div');
    const isRTL = currentLanguage === 'ar';
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        ${isRTL ? 'right: 20px' : 'left: 20px'};
        background: #27ae60;
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        z-index: 3000;
        font-weight: 600;
        box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
        transform: translateX(${isRTL ? '100%' : '-100%'});
        transition: transform 0.3s ease;
        direction: ${isRTL ? 'rtl' : 'ltr'};
    `;
    notification.textContent = t('product-added');

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.transform = `translateX(${isRTL ? '100%' : '-100%'})`;
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// WhatsApp Functions
function sendToWhatsApp() {
    // Validate customer info
    const customerName = document.getElementById('customerName').value.trim();
    const customerPhone = document.getElementById('customerPhone').value.trim();
    const customerAddress = document.getElementById('customerAddress').value.trim();

    if (!customerName) {
        alert('يرجى إدخال الاسم الكامل');
        document.getElementById('customerName').focus();
        return;
    }

    if (!customerPhone) {
        alert('يرجى إدخال رقم الهاتف');
        document.getElementById('customerPhone').focus();
        return;
    }

    if (cart.length === 0) {
        alert('السلة فارغة!');
        return;
    }

    // Calculate totals
    const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    let discount = 0;

    if (appliedCoupon && coupons[appliedCoupon.code]) {
        const coupon = coupons[appliedCoupon.code];
        if (subtotal >= coupon.minAmount) {
            if (coupon.type === 'percentage') {
                discount = (subtotal * coupon.value) / 100;
            } else if (coupon.type === 'fixed') {
                discount = Math.min(coupon.value, subtotal);
            }
        }
    }

    const finalTotal = subtotal - discount;
    const itemCount = cart.reduce((sum, item) => sum + item.quantity, 0);

    // التحقق من خيار التوصيل
    if (deliveryType === 'delivery' && !selectedDeliveryZone) {
        alert('يرجى اختيار منطقة التوصيل');
        return;
    }

    // Generate invoice
    const invoice = generateInvoice(customerName, customerPhone, customerAddress, subtotal, discount, finalTotal, itemCount, deliveryType, selectedDeliveryZone, deliveryFee);

    // Store number (replace with your actual WhatsApp number)
    const storeWhatsApp = '966501234567'; // رقم المتجر

    // Create WhatsApp URL
    const whatsappURL = `https://wa.me/${storeWhatsApp}?text=${encodeURIComponent(invoice)}`;

    // Open WhatsApp
    window.open(whatsappURL, '_blank');

    // Show success message and clear cart automatically
    setTimeout(() => {
        alert('تم إرسال الطلب بنجاح! شكراً لك.');

        // Clear cart automatically after successful order
        clearCart();
        toggleCart();

        // Clear customer info
        document.getElementById('customerName').value = '';
        document.getElementById('customerPhone').value = '';
        document.getElementById('customerAddress').value = '';

        // Clear coupon
        appliedCoupon = null;
        localStorage.removeItem('appliedCoupon');

        // Clear customer info from localStorage
        localStorage.removeItem('customerInfo');
    }, 1000);
}

function generateInvoice(name, phone, address, subtotal, discount, total, itemCount, deliveryType = 'pickup', deliveryZone = null, deliveryFee = 0) {
    const currentDate = new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
    const currentTime = new Date().toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
    });

    let invoice = `🛒 *فاتورة برومت هايبر ماركت*\n`;
    invoice += `━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n\n`;

    // Customer Info
    invoice += `👤 *معلومات العميل:*\n`;
    invoice += `الاسم: ${name}\n`;
    invoice += `الهاتف: ${phone}\n`;
    if (address) {
        invoice += `العنوان: ${address}\n`;
    }
    invoice += `التاريخ: ${currentDate}\n`;
    invoice += `الوقت: ${currentTime}\n\n`;

    // Order Details
    invoice += `📦 *تفاصيل الطلب:*\n`;
    invoice += `━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n`;

    cart.forEach((item, index) => {
        const itemTotal = item.price * item.quantity;
        invoice += `${index + 1}. ${item.name}\n`;
        invoice += `   الكمية: ${item.quantity} × ${item.price} دينار = ${itemTotal.toFixed(0)} دينار\n\n`;
    });

    // Totals
    invoice += `━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n`;
    invoice += `💰 *ملخص الفاتورة:*\n`;
    invoice += `المجموع الفرعي: ${subtotal.toFixed(0)} دينار\n`;

    if (discount > 0) {
        invoice += `الخصم (${appliedCoupon.code}): -${discount.toFixed(0)} دينار\n`;
    }

    // معلومات التوصيل
    invoice += `🚚 *طريقة التوصيل:*\n`;
    if (deliveryType === 'pickup') {
        invoice += `📍 استلام من المحل (مجاني)\n`;
    } else if (deliveryType === 'delivery' && deliveryZone) {
        invoice += `🏠 توصيل للمنزل\n`;
        invoice += `📍 المنطقة: ${deliveryZone.name}\n`;
        invoice += `⏰ وقت التوصيل المتوقع: ${deliveryZone.deliveryTime} دقيقة\n`;
        invoice += `💰 عمولة التوصيل: ${deliveryFee.toFixed(0)} دينار\n`;
    }

    const finalTotalWithDelivery = total + deliveryFee;
    invoice += `*المجموع النهائي: ${finalTotalWithDelivery.toFixed(0)} دينار*\n`;
    invoice += `عدد المنتجات: ${itemCount} قطعة\n\n`;

    invoice += `━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n`;
    invoice += `🏪 *برومت هايبر ماركت*\n`;
    invoice += `شكراً لاختياركم متجرنا! 🙏\n`;
    invoice += `سيتم التواصل معكم لتأكيد الطلب وترتيب التوصيل.\n\n`;
    invoice += `📞 للاستفسار: +966 11 123 4567\n`;
    invoice += `🌐 الموقع: برومت هايبر ماركت`;

    return invoice;
}

// Initialize coupon input event listener (disabled since coupons are hidden)
function initializeCouponInput() {
    // Coupons are disabled, no initialization needed
    return;
}

// Countdown Timer for Offers
function startCountdown() {
    // Set the date we're counting down to (3 days from now)
    const countDownDate = new Date().getTime() + (3 * 24 * 60 * 60 * 1000);

    const timer = setInterval(function() {
        const now = new Date().getTime();
        const distance = countDownDate - now;

        const days = Math.floor(distance / (1000 * 60 * 60 * 24));
        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);

        // Update the countdown display
        const daysEl = document.getElementById('days');
        const hoursEl = document.getElementById('hours');
        const minutesEl = document.getElementById('minutes');
        const secondsEl = document.getElementById('seconds');

        if (daysEl) daysEl.textContent = days.toString().padStart(2, '0');
        if (hoursEl) hoursEl.textContent = hours.toString().padStart(2, '0');
        if (minutesEl) minutesEl.textContent = minutes.toString().padStart(2, '0');
        if (secondsEl) secondsEl.textContent = seconds.toString().padStart(2, '0');

        // If the countdown is finished
        if (distance < 0) {
            clearInterval(timer);
            if (daysEl) daysEl.textContent = "00";
            if (hoursEl) hoursEl.textContent = "00";
            if (minutesEl) minutesEl.textContent = "00";
            if (secondsEl) secondsEl.textContent = "00";
        }
    }, 1000);
}

// Header scroll effect
window.addEventListener('scroll', () => {
    const header = document.querySelector('.header');
    if (header) {
        if (window.scrollY > 100) {
            header.style.background = 'rgba(255, 255, 255, 0.95)';
            header.style.backdropFilter = 'blur(10px)';
        } else {
            header.style.background = '#fff';
            header.style.backdropFilter = 'none';
        }
    }
});

// Intersection Observer for animations
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.style.opacity = '1';
            entry.target.style.transform = 'translateY(0)';
        }
    });
}, observerOptions);

// Observe elements for animation
document.querySelectorAll('.category-card, .product-card, .offer-card').forEach(el => {
    el.style.opacity = '0';
    el.style.transform = 'translateY(20px)';
    el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
    observer.observe(el);
});

// Load dynamic products from admin panel
function loadDynamicProducts() {
    // Load featured products for main section
    const featuredProducts = JSON.parse(localStorage.getItem('mainSiteFeatured')) || [];
    const adminProducts = JSON.parse(localStorage.getItem('adminProducts')) || [];

    // Update products section with featured products
    const productsGrid = document.querySelector('#products .products-grid');
    if (productsGrid) {
        let displayProducts = [];

        if (featuredProducts.length > 0) {
            // Use featured products (up to 4)
            displayProducts = featuredProducts.slice(0, 4);
        } else if (adminProducts.length > 0) {
            // Fallback to first 4 admin products
            displayProducts = adminProducts.slice(0, 4);
        }

        if (displayProducts.length > 0) {
            productsGrid.innerHTML = displayProducts.map(product => {
                const stockStatus = getProductStockStatus(product);
                const isFeatured = featuredProducts.find(f => f.id === product.id);

                return `
                    <div class="product-card ${stockStatus.class}">
                        <div class="product-image">
                            <img src="${product.image}" alt="${product.name}" onerror="this.src='https://via.placeholder.com/300x200?text=صورة'">
                            ${isFeatured ? '<div class="product-badge featured-badge">مميز</div>' :
                              (product.price < 20 ? '<div class="product-badge">عرض خاص</div>' : '')}
                            ${stockStatus.badge ? `<div class="stock-badge ${stockStatus.badgeClass}">${stockStatus.badge}</div>` : ''}
                        </div>
                        <div class="product-info">
                            <h3 class="product-name">${product.name}</h3>
                            <p class="product-price">
                                <span class="current-price">${product.price} دينار</span>
                            </p>
                            ${stockStatus.outOfStock ?
                                `<div class="out-of-stock-message">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <span data-translate="out-of-stock">لقد نفذت الكمية حالياً</span><br>
                                    <small data-translate="out-of-stock-sub">سوف نوفرها في أقرب وقت</small>
                                </div>` :
                                `<button class="add-to-cart" data-id="${product.id}" data-name="${product.name}" data-price="${product.price}" data-image="${product.image}" data-stock="${product.stock || 0}" data-translate="add-to-cart">أضف للسلة</button>`
                            }
                        </div>
                    </div>
                `;
            }).join('');
        }
    }

    // Update offers section with admin offers
    const offersGrid = document.querySelector('#offers .offers-grid');
    if (offersGrid) {
        const adminOffers = JSON.parse(localStorage.getItem('mainSiteOffers')) || [];

        if (adminOffers.length > 0) {
            // Use admin offers
            offersGrid.innerHTML = adminOffers.slice(0, 4).map(offer => {
                const product = adminProducts.find(p => p.id === offer.productId);
                const stockStatus = product ? getProductStockStatus(product) : { class: 'out-of-stock', outOfStock: true };

                return `
                    <div class="offer-card ${stockStatus.class}">
                        <div class="offer-badge">خصم ${offer.discountPercentage}%</div>
                        <div class="product-image">
                            <img src="${offer.productImage}" alt="${offer.productName}" onerror="this.src='https://via.placeholder.com/300x200?text=صورة'">
                            ${stockStatus.badge ? `<div class="stock-badge ${stockStatus.badgeClass}">${stockStatus.badge}</div>` : ''}
                        </div>
                        <div class="product-info">
                            <h3 class="product-name">${offer.productName}</h3>
                            <p class="product-price">
                                <span class="current-price">${offer.salePrice} دينار</span>
                                <span class="old-price">${offer.originalPrice} دينار</span>
                            </p>
                            ${stockStatus.outOfStock ?
                                `<div class="out-of-stock-message">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    لقد نفذت الكمية حالياً<br>
                                    <small>سوف نوفرها في أقرب وقت</small>
                                </div>` :
                                `<button class="add-to-cart" data-id="${offer.productId}" data-name="${offer.productName}" data-price="${offer.salePrice}" data-image="${offer.productImage}" data-stock="${product ? product.stock : 0}">أضف للسلة</button>`
                            }
                        </div>
                    </div>
                `;
            }).join('');
        } else if (adminProducts.length > 4) {
            // Fallback to default offers if no admin offers
            const offerProducts = adminProducts.slice(4, 8);
            offersGrid.innerHTML = offerProducts.map((product, index) => {
                const discounts = [40, 30, 25, 35];
                const discount = discounts[index] || 20;
                const originalPrice = Math.round(product.price / (1 - discount/100));
                const stockStatus = getProductStockStatus(product);

                return `
                    <div class="offer-card ${stockStatus.class}">
                        <div class="offer-badge">خصم ${discount}%</div>
                        <div class="product-image">
                            <img src="${product.image}" alt="${product.name}" onerror="this.src='https://via.placeholder.com/300x200?text=صورة'">
                            ${stockStatus.badge ? `<div class="stock-badge ${stockStatus.badgeClass}">${stockStatus.badge}</div>` : ''}
                        </div>
                        <div class="product-info">
                            <h3 class="product-name">${product.name}</h3>
                            <p class="product-price">
                                <span class="current-price">${product.price} دينار</span>
                                <span class="old-price">${originalPrice} دينار</span>
                            </p>
                            ${stockStatus.outOfStock ?
                                `<div class="out-of-stock-message">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    لقد نفذت الكمية حالياً<br>
                                    <small>سوف نوفرها في أقرب وقت</small>
                                </div>` :
                                `<button class="add-to-cart" data-id="${product.id}" data-name="${product.name}" data-price="${product.price}" data-image="${product.image}" data-stock="${product.stock || 0}">أضف للسلة</button>`
                            }
                        </div>
                    </div>
                `;
            }).join('');
        }
    }

    // Re-attach event listeners to new buttons
    attachCartEventListeners();
}

function attachCartEventListeners() {
    document.querySelectorAll('.add-to-cart').forEach(button => {
        button.addEventListener('click', function() {
            const id = this.getAttribute('data-id');
            const name = this.getAttribute('data-name');
            const price = this.getAttribute('data-price');
            const image = this.getAttribute('data-image');

            if (id && name && price && image) {
                addToCart(id, name, price, image);
            }
        });
    });
}

// Clear cart on page unload (when leaving the site)
window.addEventListener('beforeunload', function() {
    // Clear cart data
    clearCartData();
});

// Function to clear all cart-related data
function clearCartData() {
    cart = [];
    appliedCoupon = null;
    localStorage.removeItem('cart');
    localStorage.removeItem('appliedCoupon');
    localStorage.removeItem('customerInfo');

    // Return all stock
    const adminProducts = JSON.parse(localStorage.getItem('adminProducts')) || [];
    if (adminProducts.length > 0) {
        // Reset stock to original values if needed
        localStorage.setItem('adminProducts', JSON.stringify(adminProducts));
    }
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Start countdown timer
    startCountdown();

    // Load dynamic products from admin panel
    loadDynamicProducts();

    // Load dynamic categories
    loadDynamicCategories();

    // Update main site products from admin
    updateMainSiteProducts();

    // Load contact settings and logo
    loadContactInfo();

    // Initialize cart and coupon
    updateCartCount();
    updateCartDisplay();
    initializeCouponInput();

    // Add event listeners to add-to-cart buttons
    attachCartEventListeners();

    // Initialize contact form
    initializeMainContactForm();

    // Force logo update after a short delay
    setTimeout(() => {
        updateLogoLanguage();
    }, 500);

    // Listen for updates from admin panel
    window.addEventListener('message', function(event) {
        if (event.data && event.data.type === 'updateContactSettings') {
            console.log('Received contact settings update from admin panel');
            localStorage.setItem('mainSiteContact', JSON.stringify(event.data.settings));
            loadContactInfo();
        } else if (event.data && event.data.type === 'updateCategories') {
            console.log('Received categories update from admin panel');
            localStorage.setItem('mainSiteCategories', JSON.stringify(event.data.categories));
            loadDynamicCategories();
            updateMainSiteProducts();
        } else if (event.data && event.data.type === 'updateProducts') {
            console.log('Received products update from admin panel');
            localStorage.setItem('mainSiteProducts', JSON.stringify(event.data.products));
            updateMainSiteProducts();
        }
    });

    // Initialize customer info from localStorage if available
    const savedCustomerInfo = JSON.parse(localStorage.getItem('customerInfo')) || {};
    if (savedCustomerInfo.name) {
        const nameInput = document.getElementById('customerName');
        if (nameInput) nameInput.value = savedCustomerInfo.name;
    }
    if (savedCustomerInfo.phone) {
        const phoneInput = document.getElementById('customerPhone');
        if (phoneInput) phoneInput.value = savedCustomerInfo.phone;
    }
    if (savedCustomerInfo.address) {
        const addressInput = document.getElementById('customerAddress');
        if (addressInput) addressInput.value = savedCustomerInfo.address;
    }

    // Save customer info when typing
    ['customerName', 'customerPhone', 'customerAddress'].forEach(id => {
        const input = document.getElementById(id);
        if (input) {
            input.addEventListener('input', function() {
                const customerInfo = {
                    name: document.getElementById('customerName').value,
                    phone: document.getElementById('customerPhone').value,
                    address: document.getElementById('customerAddress').value
                };
                localStorage.setItem('customerInfo', JSON.stringify(customerInfo));
            });
        }
    });
});

// Contact Information Management
function loadContactInfo() {
    const contactSettings = JSON.parse(localStorage.getItem('mainSiteContact')) || getDefaultContactInfo();

    // Update contact information elements
    updateContactElements(contactSettings);
}

function getDefaultContactInfo() {
    return {
        storeNameAr: 'برومت هايبر ماركت',
        storeNameEn: 'Bromet Hypermarket',
        storeDescAr: 'متجرك المفضل لجميع احتياجاتك اليومية بأفضل الأسعار وأعلى جودة',
        storeDescEn: 'Your favorite store for all your daily needs at the best prices and highest quality',
        addressAr: 'الرياض، المملكة العربية السعودية',
        addressEn: 'Riyadh, Saudi Arabia',
        phone1: '+966 11 123 4567',
        phone2: '+966 11 123 4568',
        email1: '<EMAIL>',
        email2: '<EMAIL>',
        workingHoursAr: 'السبت - الخميس: 8:00 ص - 12:00 م\nالجمعة: 2:00 م - 12:00 م',
        workingHoursEn: 'Saturday - Thursday: 8:00 AM - 12:00 PM\nFriday: 2:00 PM - 12:00 PM',
        whatsappNumber: '966111234567',
        logoUrl: '' // Empty means use default icon
    };
}

function updateContactElements(settings) {
    const isArabic = currentLanguage === 'ar';

    console.log('Updating contact elements with settings:', settings); // Debug log

    // Update logo with current language store name
    const storeName = isArabic ? settings.storeNameAr : settings.storeNameEn;
    console.log('Updating logo with:', settings.logoUrl, storeName); // Debug log
    updateLogo(settings.logoUrl, storeName);

    // Update address
    const addressElement = document.getElementById('contactAddress');
    if (addressElement) {
        addressElement.textContent = isArabic ? settings.addressAr : settings.addressEn;
    }

    // Update phone numbers
    const phone1Element = document.getElementById('contactPhone1');
    const phone2Element = document.getElementById('contactPhone2');
    if (phone1Element) {
        phone1Element.textContent = settings.phone1;
        phone1Element.href = `tel:${settings.phone1}`;
    }
    if (phone2Element) {
        phone2Element.textContent = settings.phone2;
        phone2Element.href = `tel:${settings.phone2}`;
    }

    // Update emails
    const email1Element = document.getElementById('contactEmail1');
    const email2Element = document.getElementById('contactEmail2');
    if (email1Element) {
        email1Element.textContent = settings.email1;
        email1Element.href = `mailto:${settings.email1}`;
    }
    if (email2Element) {
        email2Element.textContent = settings.email2;
        email2Element.href = `mailto:${settings.email2}`;
    }

    // Update working hours
    const hoursElement = document.getElementById('contactHours');
    if (hoursElement) {
        const hours = isArabic ? settings.workingHoursAr : settings.workingHoursEn;
        hoursElement.innerHTML = hours.replace(/\n/g, '<br>');
    }

    // Update WhatsApp and call links
    const whatsappLink = document.getElementById('whatsappLink');
    const callLink = document.getElementById('callLink');
    if (whatsappLink) {
        whatsappLink.href = `https://wa.me/${settings.whatsappNumber}`;
    }
    if (callLink) {
        callLink.href = `tel:${settings.phone1}`;
    }
}

// Contact Form Management
function initializeMainContactForm() {
    const form = document.getElementById('mainContactForm');
    if (form) {
        form.addEventListener('submit', handleMainContactSubmit);
    }
}

function handleMainContactSubmit(e) {
    e.preventDefault();

    const formData = {
        name: document.getElementById('mainContactName').value,
        email: document.getElementById('mainContactEmail').value,
        phone: document.getElementById('mainContactPhone').value,
        subject: document.getElementById('mainContactSubject').value,
        message: document.getElementById('mainContactMessage').value
    };

    // Validate form
    if (!validateContactForm(formData)) {
        return;
    }

    // Show loading state
    const submitBtn = document.querySelector('.contact-submit-btn');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> <span>جاري الإرسال...</span>';
    submitBtn.disabled = true;

    // Simulate form submission
    setTimeout(() => {
        // Reset form
        document.getElementById('mainContactForm').reset();

        // Reset button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;

        // Show success message
        showContactSuccessMessage();

        // Send to WhatsApp
        sendContactToWhatsApp(formData);
    }, 2000);
}

function validateContactForm(data) {
    const errors = [];

    if (!data.name.trim()) {
        errors.push('الاسم مطلوب');
    }

    if (!data.email.trim()) {
        errors.push('البريد الإلكتروني مطلوب');
    } else if (!isValidContactEmail(data.email)) {
        errors.push('البريد الإلكتروني غير صحيح');
    }

    if (!data.phone.trim()) {
        errors.push('رقم الهاتف مطلوب');
    }

    if (!data.subject) {
        errors.push('الموضوع مطلوب');
    }

    if (!data.message.trim()) {
        errors.push('الرسالة مطلوبة');
    }

    if (errors.length > 0) {
        showContactErrorMessage(errors.join('\n'));
        return false;
    }

    return true;
}

function isValidContactEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function showContactSuccessMessage() {
    const message = currentLanguage === 'ar'
        ? 'تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.'
        : 'Your message has been sent successfully! We will contact you soon.';

    showContactNotification(message, 'success');
}

function showContactErrorMessage(message) {
    showContactNotification(message, 'error');
}

function showContactNotification(message, type) {
    const notification = document.createElement('div');
    const isRTL = currentLanguage === 'ar';
    notification.className = `contact-notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'}"></i>
            <span>${message}</span>
        </div>
    `;

    // Add styles
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        ${isRTL ? 'right: 20px' : 'left: 20px'};
        background: ${type === 'success' ? '#27ae60' : '#e74c3c'};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        z-index: 10000;
        transform: translateX(${isRTL ? '100%' : '-100%'});
        transition: transform 0.3s ease;
        max-width: 400px;
        font-family: 'Cairo', sans-serif;
        direction: ${isRTL ? 'rtl' : 'ltr'};
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);

    // Remove after 5 seconds
    setTimeout(() => {
        notification.style.transform = `translateX(${isRTL ? '100%' : '-100%'})`;
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 5000);
}

function sendContactToWhatsApp(data) {
    const contactSettings = JSON.parse(localStorage.getItem('mainSiteContact')) || getDefaultContactInfo();

    const message = `
*رسالة جديدة من موقع برومت هايبر ماركت*

*الاسم:* ${data.name}
*البريد الإلكتروني:* ${data.email}
*الهاتف:* ${data.phone}
*الموضوع:* ${getContactSubjectText(data.subject)}

*الرسالة:*
${data.message}

---
تم الإرسال من قسم اتصل بنا
    `.trim();

    const whatsappUrl = `https://wa.me/${contactSettings.whatsappNumber}?text=${encodeURIComponent(message)}`;

    // Optional: Open WhatsApp automatically
    // window.open(whatsappUrl, '_blank');
}

function getContactSubjectText(value) {
    const subjects = {
        'general': currentLanguage === 'ar' ? 'استفسار عام' : 'General Inquiry',
        'order': currentLanguage === 'ar' ? 'استفسار عن طلب' : 'Order Inquiry',
        'complaint': currentLanguage === 'ar' ? 'شكوى' : 'Complaint',
        'suggestion': currentLanguage === 'ar' ? 'اقتراح' : 'Suggestion'
    };
    return subjects[value] || value;
}

// Logo Management
function updateLogo(logoUrl, storeName) {
    const logoElements = document.querySelectorAll('.logo');

    logoElements.forEach((logoElement) => {
        let iconElement = logoElement.querySelector('i.fas.fa-store');
        let imageElement = logoElement.querySelector('img.logo-image');
        const textElement = logoElement.querySelector('h2');

        if (logoUrl && logoUrl.trim() && logoUrl !== 'assets/placeholder.jpg') {
            // Hide icon
            if (iconElement) {
                iconElement.style.display = 'none';
            }

            // Create or update image
            if (!imageElement) {
                imageElement = document.createElement('img');
                imageElement.className = 'logo-image';
                logoElement.insertBefore(imageElement, textElement);
            }

            imageElement.src = logoUrl;
            imageElement.alt = storeName || 'شعار المتجر';
            imageElement.style.display = 'block';

            // Handle image load error
            imageElement.onerror = function() {
                this.style.display = 'none';
                if (iconElement) {
                    iconElement.style.display = 'block';
                }
            };

        } else {
            // Show default icon
            if (iconElement) {
                iconElement.style.display = 'block';
            }
            if (imageElement) {
                imageElement.style.display = 'none';
            }
        }

        // Update store name with animation
        if (textElement && storeName) {
            if (textElement.textContent !== storeName) {
                textElement.style.opacity = '0';
                setTimeout(() => {
                    textElement.textContent = storeName;
                    textElement.style.opacity = '1';
                }, 150);
            }
        }
    });
}

function updateLogoLanguage() {
    const contactSettings = JSON.parse(localStorage.getItem('mainSiteContact')) || getDefaultContactInfo();
    const isArabic = currentLanguage === 'ar';
    const storeName = isArabic ? contactSettings.storeNameAr : contactSettings.storeNameEn;

    console.log('Updating logo language:', {
        logoUrl: contactSettings.logoUrl,
        storeName: storeName,
        language: currentLanguage
    });

    updateLogo(contactSettings.logoUrl, storeName);
}

// Dynamic Categories Loading
function loadDynamicCategories() {
    const categories = JSON.parse(localStorage.getItem('mainSiteCategories')) || getDefaultCategories();
    const categoriesGrid = document.getElementById('mainCategoriesGrid');

    if (!categoriesGrid) return;

    const isArabic = currentLanguage === 'ar';

    categoriesGrid.innerHTML = categories.map(category => {
        const categoryName = isArabic ? category.nameAr : category.nameEn;
        const categoryDesc = isArabic ? category.descriptionAr : category.descriptionEn;

        // Count products in this category
        const products = JSON.parse(localStorage.getItem('products')) || [];
        const categoryProducts = products.filter(product =>
            product.category === category.nameAr && product.stock > 0
        );
        const productCount = categoryProducts.length;

        return `
            <div class="category-card" onclick="showCategoryProducts('${category.id}', '${category.nameAr}')">
                <div class="category-image-container">
                    ${category.image ?
                        `<img src="${category.image}" alt="${categoryName}">` :
                        ''
                    }
                    <div class="category-icon">
                        ${category.image ?
                            `<i class="fas fa-eye" style="font-size: 2rem;"></i>` :
                            `<i class="fas fa-th-large"></i>`
                        }
                    </div>
                    ${productCount > 0 ?
                        `<div class="category-badge">${productCount} ${isArabic ? 'منتج' : 'Products'}</div>` :
                        ''
                    }
                </div>
                <div class="category-content">
                    <h3 class="category-name">${categoryName}</h3>
                    <p class="category-desc">${categoryDesc}</p>
                </div>
                <div class="category-overlay"></div>
            </div>
        `;
    }).join('');
}

function getDefaultCategories() {
    return [
        {
            id: 'cat1',
            nameAr: 'منتجات غذائية',
            nameEn: 'Food Products',
            descriptionAr: 'جميع أنواع المواد الغذائية والمشروبات',
            descriptionEn: 'All types of food products and beverages',
            image: ''
        },
        {
            id: 'cat2',
            nameAr: 'منتجات تنظيف',
            nameEn: 'Cleaning Products',
            descriptionAr: 'مواد التنظيف ومستلزمات المنزل',
            descriptionEn: 'Cleaning supplies and household items',
            image: ''
        },
        {
            id: 'cat3',
            nameAr: 'العناية الشخصية',
            nameEn: 'Personal Care',
            descriptionAr: 'منتجات العناية الشخصية والصحة',
            descriptionEn: 'Personal care and health products',
            image: ''
        }
    ];
}

function showCategoryProducts(categoryId, categoryNameAr) {
    console.log('Showing products for category:', categoryNameAr); // Debug log

    // Get products for this category from admin products
    const adminProducts = JSON.parse(localStorage.getItem('adminProducts')) || [];
    console.log('All admin products:', adminProducts); // Debug log

    // Also try to get from main site products as backup
    const mainSiteProducts = JSON.parse(localStorage.getItem('products')) || [];
    console.log('Main site products:', mainSiteProducts); // Debug log

    // Combine both sources and filter
    const allProducts = [...adminProducts, ...mainSiteProducts];

    const categoryProducts = allProducts.filter(product => {
        console.log('Checking product:', product.name, 'Category:', product.category, 'Stock:', product.stock); // Debug log
        const matchesCategory = product.category === categoryNameAr ||
                               product.category === categoryId ||
                               (product.category && product.category.trim() === categoryNameAr.trim());
        const hasStock = (product.stock || 0) > 0;
        return matchesCategory && hasStock;
    });

    // Remove duplicates based on product ID
    const uniqueProducts = categoryProducts.filter((product, index, self) =>
        index === self.findIndex(p => p.id === product.id)
    );

    console.log('Filtered category products:', uniqueProducts); // Debug log

    if (uniqueProducts.length === 0) {
        // Try to show all products in category regardless of stock
        const allCategoryProducts = allProducts.filter(product => {
            const matchesCategory = product.category === categoryNameAr ||
                                   product.category === categoryId ||
                                   (product.category && product.category.trim() === categoryNameAr.trim());
            return matchesCategory;
        });

        if (allCategoryProducts.length === 0) {
            alert('لا توجد منتجات في هذه الفئة حالياً');
            return;
        } else {
            alert('جميع منتجات هذه الفئة نفذت حالياً');
            return;
        }
    }

    // Create category products modal
    const categories = JSON.parse(localStorage.getItem('mainSiteCategories')) || [];
    const category = categories.find(c => c.id === categoryId);
    const isArabic = currentLanguage === 'ar';
    const categoryName = category ? (isArabic ? category.nameAr : category.nameEn) : categoryNameAr;

    const modal = document.createElement('div');
    modal.className = 'category-products-modal';
    modal.innerHTML = `
        <div class="modal-overlay" onclick="closeCategoryModal()"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h2>${categoryName}</h2>
                <button class="close-btn" onclick="closeCategoryModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="category-products-grid">
                    ${uniqueProducts.map(product => {
                        // Check if product has active offer
                        const offers = JSON.parse(localStorage.getItem('mainSiteOffers')) || [];
                        const activeOffer = offers.find(offer =>
                            offer.productId === product.id &&
                            new Date(offer.endDate) >= new Date() &&
                            new Date(offer.startDate) <= new Date()
                        );

                        const finalPrice = activeOffer ? activeOffer.salePrice : product.price;
                        const hasDiscount = activeOffer ? true : false;
                        const discountPercentage = activeOffer ? activeOffer.discountPercentage : 0;

                        return `
                            <div class="product-card">
                                <div class="product-image">
                                    <img src="${product.image}" alt="${product.name}" onerror="this.src='assets/placeholder.jpg'">
                                    ${hasDiscount ? `<div class="product-badge">خصم ${discountPercentage}%</div>` : ''}
                                    ${(product.stock || 0) === 0 ? '<div class="out-of-stock-badge">نفذت الكمية</div>' : ''}
                                </div>
                                <div class="product-info">
                                    <h3 class="product-name">${product.name}</h3>
                                    <p class="product-description">${product.description || ''}</p>
                                    <div class="product-price">
                                        ${hasDiscount ?
                                            `<span class="current-price">${finalPrice} دينار</span>
                                             <span class="old-price">${product.price} دينار</span>` :
                                            `<span class="current-price">${product.price} دينار</span>`
                                        }
                                    </div>
                                    <div class="product-stock-info">
                                        <small style="color: #7f8c8d;">متوفر: ${product.stock || 0} قطعة</small>
                                    </div>
                                    ${(product.stock || 0) > 0 ?
                                        `<button class="add-to-cart" onclick="addToCart('${product.id}', '${product.name}', ${finalPrice}, '${product.image}')">
                                            أضف للسلة
                                        </button>` :
                                        `<button class="out-of-stock-btn" disabled>
                                            نفذت الكمية
                                        </button>`
                                    }
                                </div>
                            </div>
                        `;
                    }).join('')}
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    modal.style.display = 'flex';
}

function closeCategoryModal() {
    const modal = document.querySelector('.category-products-modal');
    if (modal) {
        modal.remove();
    }
}

// Update main site products from admin
function updateMainSiteProducts() {
    const adminProducts = JSON.parse(localStorage.getItem('adminProducts')) || [];

    // Update both main site products and the legacy products key
    localStorage.setItem('mainSiteProducts', JSON.stringify(adminProducts));
    localStorage.setItem('products', JSON.stringify(adminProducts));

    console.log('Updated main site products:', adminProducts.length, 'products'); // Debug log

    // Update categories with product counts
    updateCategoriesWithProductCounts();
}

function updateCategoriesWithProductCounts() {
    const categories = JSON.parse(localStorage.getItem('mainSiteCategories')) || [];
    const products = JSON.parse(localStorage.getItem('adminProducts')) || [];

    const updatedCategories = categories.map(category => {
        const categoryProducts = products.filter(p => p.category === category.nameAr);
        const availableProducts = categoryProducts.filter(p => (p.stock || 0) > 0);

        return {
            ...category,
            productCount: categoryProducts.length,
            availableCount: availableProducts.length
        };
    });

    localStorage.setItem('mainSiteCategories', JSON.stringify(updatedCategories));
}

// Listen for updates from admin panel
window.addEventListener('message', function(event) {
    if (event.data && event.data.type === 'ADMIN_UPDATE') {
        console.log('Received admin update:', event.data.action); // Debug log

        switch(event.data.action) {
            case 'PRODUCTS_UPDATED':
                updateMainSiteProducts();
                loadProducts();
                break;
            case 'CATEGORIES_UPDATED':
                loadCategories();
                break;
            case 'OFFERS_UPDATED':
                loadOffers();
                break;
        }
    }
});

// Load Categories Function
function loadCategories() {
    const categoriesGrid = document.getElementById('mainCategoriesGrid');
    if (!categoriesGrid) return;

    const adminCategories = JSON.parse(localStorage.getItem('adminCategories')) || [];

    if (adminCategories.length === 0) {
        // Default categories if none exist
        categoriesGrid.innerHTML = `
            <div class="category-card">
                <div class="category-image">
                    <i class="fas fa-apple-alt"></i>
                </div>
                <h3>فواكه وخضروات</h3>
                <p>طازجة ومختارة بعناية</p>
            </div>
            <div class="category-card">
                <div class="category-image">
                    <i class="fas fa-drumstick-bite"></i>
                </div>
                <h3>لحوم ودواجن</h3>
                <p>طازجة وعالية الجودة</p>
            </div>
            <div class="category-card">
                <div class="category-image">
                    <i class="fas fa-bread-slice"></i>
                </div>
                <h3>مخبوزات</h3>
                <p>طازجة يومياً</p>
            </div>
            <div class="category-card">
                <div class="category-image">
                    <i class="fas fa-bottle-water"></i>
                </div>
                <h3>مشروبات</h3>
                <p>متنوعة ومنعشة</p>
            </div>
        `;
        return;
    }

    // Display admin categories
    categoriesGrid.innerHTML = adminCategories.map(category => `
        <div class="category-card" onclick="filterByCategory('${category.id}')">
            <div class="category-image">
                ${category.image ?
                    `<img src="${category.image}" alt="${category.nameAr}" onerror="this.innerHTML='<i class=\\"fas fa-th-large\\"></i>'">` :
                    `<i class="fas fa-th-large"></i>`
                }
            </div>
            <h3>${currentLanguage === 'ar' ? category.nameAr : category.nameEn}</h3>
            <p>${currentLanguage === 'ar' ? category.descriptionAr : category.descriptionEn}</p>
        </div>
    `).join('');
}

// Load Products Function
function loadProducts() {
    console.log('Loading products...');

    // Get products from localStorage
    const adminProducts = JSON.parse(localStorage.getItem('adminProducts')) || [];
    const productsGrid = document.querySelector('.products-grid');

    if (!productsGrid) {
        console.log('Products grid not found');
        return;
    }

    if (adminProducts.length === 0) {
        console.log('No admin products found, keeping default products');
        return;
    }

    // Display admin products
    productsGrid.innerHTML = adminProducts.slice(0, 8).map(product => {
        const price = Math.round(product.price * 1000); // Convert to Iraqi Dinar
        const oldPrice = product.oldPrice ? Math.round(product.oldPrice * 1000) : null;

        return `
            <div class="product-card">
                <div class="product-image">
                    ${product.image ?
                        `<img src="${product.image}" alt="${product.nameAr}" onerror="this.src='assets/placeholder.jpg'">` :
                        `<img src="assets/placeholder.jpg" alt="${product.nameAr}">`
                    }
                    ${product.isNew ? '<div class="product-badge">جديد</div>' : ''}
                    ${product.isOffer ? '<div class="product-badge">عرض خاص</div>' : ''}
                </div>
                <div class="product-info">
                    <h3 class="product-name">${currentLanguage === 'ar' ? product.nameAr : product.nameEn}</h3>
                    <p class="product-price">
                        <span class="current-price">${price.toLocaleString()} دينار</span>
                        ${oldPrice ? `<span class="old-price">${oldPrice.toLocaleString()} دينار</span>` : ''}
                    </p>
                    <button class="add-to-cart"
                            data-id="${product.id}"
                            data-name="${product.nameAr}"
                            data-price="${price}"
                            data-image="${product.image || 'assets/placeholder.jpg'}">
                        أضف للسلة
                    </button>
                </div>
            </div>
        `;
    }).join('');

    console.log(`✅ Loaded ${adminProducts.length} products`);
}

// Load Offers Function
function loadOffers() {
    console.log('Loading offers...');

    // Get offers from localStorage
    const adminOffers = JSON.parse(localStorage.getItem('adminOffers')) || [];
    const offersGrid = document.querySelector('.offers-grid');

    if (!offersGrid) {
        console.log('Offers grid not found');
        return;
    }

    if (adminOffers.length === 0) {
        console.log('No admin offers found, keeping default offers');
        return;
    }

    // Display admin offers
    offersGrid.innerHTML = adminOffers.slice(0, 4).map(offer => {
        const currentPrice = Math.round(offer.currentPrice * 1000); // Convert to Iraqi Dinar
        const oldPrice = Math.round(offer.oldPrice * 1000);
        const discountPercent = Math.round(((oldPrice - currentPrice) / oldPrice) * 100);

        return `
            <div class="offer-card">
                <div class="offer-badge">خصم ${discountPercent}%</div>
                <div class="product-image">
                    ${offer.image ?
                        `<img src="${offer.image}" alt="${offer.nameAr}" onerror="this.src='assets/placeholder.jpg'">` :
                        `<img src="assets/placeholder.jpg" alt="${offer.nameAr}">`
                    }
                </div>
                <div class="product-info">
                    <h3 class="product-name">${currentLanguage === 'ar' ? offer.nameAr : offer.nameEn}</h3>
                    <p class="product-price">
                        <span class="current-price">${currentPrice.toLocaleString()} دينار</span>
                        <span class="old-price">${oldPrice.toLocaleString()} دينار</span>
                    </p>
                    <button class="add-to-cart"
                            data-id="${offer.id}"
                            data-name="${offer.nameAr}"
                            data-price="${currentPrice}"
                            data-image="${offer.image || 'assets/placeholder.jpg'}">
                        أضف للسلة
                    </button>
                </div>
            </div>
        `;
    }).join('');

    console.log(`✅ Loaded ${adminOffers.length} offers`);
}

// Filter by category function
function filterByCategory(categoryId) {
    // Scroll to products section
    const productsSection = document.getElementById('products');
    if (productsSection) {
        productsSection.scrollIntoView({ behavior: 'smooth' });
    }

    // Here you can add filtering logic if needed
    console.log('Filtering by category:', categoryId);
}

// Load Settings Function
function loadSettings() {
    console.log('Loading website settings...');

    // Load contact settings from admin panel
    const contactSettings = JSON.parse(localStorage.getItem('contactSettings')) || {};

    // Update contact information if available
    if (contactSettings.addressAr) {
        const addressElement = document.getElementById('contactAddress');
        if (addressElement) {
            addressElement.textContent = currentLanguage === 'ar' ? contactSettings.addressAr : contactSettings.addressEn;
        }
    }

    if (contactSettings.phone1) {
        const phone1Element = document.getElementById('contactPhone1');
        if (phone1Element) {
            phone1Element.textContent = contactSettings.phone1;
            phone1Element.href = `tel:${contactSettings.phone1}`;
        }
    }

    if (contactSettings.phone2) {
        const phone2Element = document.getElementById('contactPhone2');
        if (phone2Element) {
            phone2Element.textContent = contactSettings.phone2;
            phone2Element.href = `tel:${contactSettings.phone2}`;
        }
    }

    if (contactSettings.email1) {
        const email1Element = document.getElementById('contactEmail1');
        if (email1Element) {
            email1Element.textContent = contactSettings.email1;
            email1Element.href = `mailto:${contactSettings.email1}`;
        }
    }

    if (contactSettings.email2) {
        const email2Element = document.getElementById('contactEmail2');
        if (email2Element) {
            email2Element.textContent = contactSettings.email2;
            email2Element.href = `mailto:${contactSettings.email2}`;
        }
    }

    if (contactSettings.workingHoursAr) {
        const hoursElement = document.getElementById('contactHours');
        if (hoursElement) {
            hoursElement.innerHTML = currentLanguage === 'ar' ? contactSettings.workingHoursAr : contactSettings.workingHoursEn;
        }
    }

    // Update WhatsApp links
    if (contactSettings.whatsappNumber) {
        const whatsappLinks = document.querySelectorAll('#whatsappLink, .whatsapp-btn[href*="wa.me"]');
        whatsappLinks.forEach(link => {
            link.href = `https://wa.me/${contactSettings.whatsappNumber}`;
        });
    }

    // Update call links
    if (contactSettings.phone1) {
        const callLinks = document.querySelectorAll('#callLink, .call-btn[href*="tel:"]');
        callLinks.forEach(link => {
            link.href = `tel:${contactSettings.phone1}`;
        });
    }

    console.log('✅ Settings loaded successfully');
}

// Initialize the page when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('🌐 Initializing main website...');

    // Update products from admin first
    updateMainSiteProducts();

    // Load all sections
    loadCategories();
    loadProducts();
    loadOffers();
    loadDeliveryZones();
    loadSliders();
    loadCustomTheme();
    updateCartDisplay();

    // Load settings
    loadSettings();

    // Set default language
    setLanguage('ar');

    console.log('✅ Main website initialized successfully');
});

// مستمع للأحداث المخصصة لتحديث المنتجات
window.addEventListener('productsUpdated', function(event) {
    console.log('🔄 تم استلام إشعار تحديث المنتجات:', event.detail);

    // إعادة تحميل المنتجات
    loadProducts();

    // إظهار إشعار للمستخدم
    if (event.detail && event.detail.count) {
        console.log(`✅ تم تحديث ${event.detail.count} منتج في الموقع الرئيسي`);
    }
});

// مراقبة تغييرات localStorage للمنتجات
window.addEventListener('storage', function(event) {
    if (event.key === 'adminProducts' || event.key === 'products') {
        console.log('🔄 تم اكتشاف تغيير في المنتجات، إعادة تحميل...');
        loadProducts();
    }
});

// ===== نظام إدارة التوصيل =====

/**
 * تحميل مناطق التوصيل من لوحة التحكم
 */
function loadDeliveryZones() {
    try {
        const storedZones = localStorage.getItem('deliveryZones');
        if (storedZones) {
            deliveryZones = JSON.parse(storedZones);
            console.log(`📦 تم تحميل ${deliveryZones.length} منطقة توصيل`);
        } else {
            // مناطق افتراضية إذا لم توجد
            deliveryZones = [
                {
                    id: 'zone_1',
                    name: 'وسط المدينة',
                    deliveryFee: 2000,
                    deliveryTime: '30-45',
                    status: 'active'
                },
                {
                    id: 'zone_2',
                    name: 'الأحياء الشمالية',
                    deliveryFee: 3000,
                    deliveryTime: '45-60',
                    status: 'active'
                },
                {
                    id: 'zone_3',
                    name: 'الأحياء الجنوبية',
                    deliveryFee: 3500,
                    deliveryTime: '60-75',
                    status: 'active'
                }
            ];
            console.log('📦 تم تحميل مناطق التوصيل الافتراضية');
        }

        // تحديث قائمة المناطق في الواجهة
        updateZonesList();

    } catch (error) {
        console.error('❌ خطأ في تحميل مناطق التوصيل:', error);
        deliveryZones = [];
    }
}

/**
 * تحديث قائمة المناطق في select
 */
function updateZonesList() {
    const zoneSelect = document.getElementById('deliveryZone');
    if (!zoneSelect) return;

    // مسح الخيارات الحالية (عدا الخيار الأول)
    while (zoneSelect.children.length > 1) {
        zoneSelect.removeChild(zoneSelect.lastChild);
    }

    // إضافة المناطق النشطة
    const activeZones = deliveryZones.filter(zone => zone.status === 'active');
    activeZones.forEach(zone => {
        const option = document.createElement('option');
        option.value = zone.id;
        option.textContent = `${zone.name} - ${zone.deliveryFee.toLocaleString()} دينار (${zone.deliveryTime} دقيقة)`;
        zoneSelect.appendChild(option);
    });

    console.log(`✅ تم تحديث قائمة المناطق: ${activeZones.length} منطقة نشطة`);
}

/**
 * تحديث خيار التوصيل (استلام من المحل أو توصيل للمنزل)
 */
function updateDeliveryOption() {
    const pickupRadio = document.getElementById('pickup');
    const deliveryRadio = document.getElementById('delivery');
    const zoneSelection = document.getElementById('zoneSelection');
    const deliveryFeeRow = document.getElementById('deliveryFeeRow');

    if (pickupRadio && pickupRadio.checked) {
        // استلام من المحل
        deliveryType = 'pickup';
        deliveryFee = 0;
        selectedDeliveryZone = null;

        // إخفاء اختيار المنطقة وعمولة التوصيل
        if (zoneSelection) zoneSelection.style.display = 'none';
        if (deliveryFeeRow) deliveryFeeRow.style.display = 'none';

    } else if (deliveryRadio && deliveryRadio.checked) {
        // توصيل للمنزل
        deliveryType = 'delivery';

        // إظهار اختيار المنطقة
        if (zoneSelection) zoneSelection.style.display = 'block';

        // إعادة تعيين المنطقة المختارة
        const zoneSelect = document.getElementById('deliveryZone');
        if (zoneSelect) {
            zoneSelect.value = '';
            updateDeliveryFee();
        }
    }

    // تحديث المجموع
    updateCartTotal();
    console.log(`🚚 تم تغيير نوع التوصيل إلى: ${deliveryType}`);
}

/**
 * تحديث عمولة التوصيل حسب المنطقة المختارة
 */
function updateDeliveryFee() {
    const zoneSelect = document.getElementById('deliveryZone');
    const deliveryFeeRow = document.getElementById('deliveryFeeRow');
    const deliveryFeeAmount = document.getElementById('deliveryFeeAmount');

    if (!zoneSelect || !zoneSelect.value) {
        // لم يتم اختيار منطقة
        deliveryFee = 0;
        selectedDeliveryZone = null;
        if (deliveryFeeRow) deliveryFeeRow.style.display = 'none';
        updateCartTotal();
        return;
    }

    // البحث عن المنطقة المختارة
    const zone = deliveryZones.find(z => z.id === zoneSelect.value);
    if (zone) {
        deliveryFee = zone.deliveryFee;
        selectedDeliveryZone = zone;

        // إظهار عمولة التوصيل
        if (deliveryFeeRow) deliveryFeeRow.style.display = 'flex';
        if (deliveryFeeAmount) {
            deliveryFeeAmount.textContent = `${deliveryFee.toLocaleString()} دينار`;
        }

        console.log(`💰 تم تحديد عمولة التوصيل: ${deliveryFee.toLocaleString()} دينار للمنطقة: ${zone.name}`);
    }

    // تحديث المجموع
    updateCartTotal();
}

// ===== نظام السلايدرات الاحترافية =====

let sliders = [];
let currentSliderIndex = 0;
let sliderInterval = null;
let isSliderPlaying = true;

/**
 * تحميل السلايدرات من لوحة التحكم
 */
function loadSliders() {
    try {
        const storedSliders = localStorage.getItem('sliders');
        if (storedSliders) {
            sliders = JSON.parse(storedSliders);
            // تصفية السلايدرات النشطة فقط
            sliders = sliders.filter(slider => slider.status === 'active');
            // ترتيب السلايدرات حسب الترتيب
            sliders.sort((a, b) => (a.order || 0) - (b.order || 0));
            console.log(`📦 تم تحميل ${sliders.length} سلايدر نشط`);
        } else {
            sliders = [];
            console.log('📦 لا توجد سلايدرات محفوظة');
        }

        // إنشاء السلايدرات في الواجهة
        createSlidersHTML();

    } catch (error) {
        console.error('❌ خطأ في تحميل السلايدرات:', error);
        sliders = [];
    }
}

/**
 * إنشاء HTML للسلايدرات
 */
function createSlidersHTML() {
    const slidersContainer = document.getElementById('slidersContainer');
    const indicatorsContainer = document.getElementById('sliderIndicators');

    if (!slidersContainer || !indicatorsContainer) {
        console.error('❌ لم يتم العثور على حاويات السلايدرات');
        return;
    }

    // مسح المحتوى السابق
    slidersContainer.innerHTML = '';
    indicatorsContainer.innerHTML = '';

    if (sliders.length === 0) {
        // إنشاء سلايدر افتراضي إذا لم توجد سلايدرات
        createDefaultSlider(slidersContainer);
        return;
    }

    // إنشاء السلايدرات
    sliders.forEach((slider, index) => {
        const sliderElement = createSliderElement(slider, index);
        slidersContainer.appendChild(sliderElement);

        // إنشاء مؤشر السلايدر
        const indicator = createSliderIndicator(index);
        indicatorsContainer.appendChild(indicator);
    });

    // تفعيل أول سلايدر
    if (sliders.length > 0) {
        showSlider(0);
        startSliderAutoplay();
    }

    console.log(`✅ تم إنشاء ${sliders.length} سلايدر في الواجهة`);
}

/**
 * إنشاء عنصر سلايدر واحد
 */
function createSliderElement(slider, index) {
    const sliderDiv = document.createElement('div');
    sliderDiv.className = `slider-item ${index === 0 ? 'active' : ''}`;
    sliderDiv.style.backgroundImage = `url('${slider.image}')`;

    // تحديد اللغة الحالية
    const isArabic = currentLanguage === 'ar';
    const title = isArabic ? slider.title : (slider.titleEn || slider.title);
    const subtitle = isArabic ? slider.subtitle : (slider.subtitleEn || slider.subtitle);
    const description = isArabic ? slider.description : (slider.descriptionEn || slider.description);
    const buttonText = isArabic ? slider.buttonText : (slider.buttonTextEn || slider.buttonText);

    sliderDiv.innerHTML = `
        <div class="slider-overlay" style="background: linear-gradient(135deg, ${slider.backgroundColor}88, ${slider.backgroundColor}44)"></div>
        <div class="slider-content">
            <div class="slider-text" style="color: ${slider.textColor}">
                <h1 class="slider-title animate-${slider.animation}" style="color: ${slider.textColor}">
                    ${title}
                </h1>
                <h2 class="slider-subtitle animate-${slider.animation}" style="color: ${slider.textColor}88">
                    ${subtitle}
                </h2>
                <p class="slider-description animate-${slider.animation}" style="color: ${slider.textColor}77">
                    ${description}
                </p>
                <a href="${slider.buttonLink}" class="slider-button animate-${slider.animation}"
                   style="background: ${slider.buttonColor}; color: white;">
                    ${buttonText}
                    <i class="fas fa-arrow-left"></i>
                </a>
            </div>
        </div>
        <div class="slider-decorations">
            <div class="decoration-shape shape-1"></div>
            <div class="decoration-shape shape-2"></div>
            <div class="decoration-shape shape-3"></div>
        </div>
    `;

    return sliderDiv;
}

/**
 * إنشاء مؤشر سلايدر
 */
function createSliderIndicator(index) {
    const indicator = document.createElement('button');
    indicator.className = `slider-indicator ${index === 0 ? 'active' : ''}`;
    indicator.onclick = () => showSlider(index);

    // إضافة معاينة صغيرة
    const slider = sliders[index];
    indicator.innerHTML = `
        <div class="indicator-preview" style="background-image: url('${slider.image}')"></div>
        <div class="indicator-info">
            <span class="indicator-title">${slider.title}</span>
            <div class="indicator-progress"></div>
        </div>
    `;

    return indicator;
}

/**
 * إنشاء سلايدر افتراضي
 */
function createDefaultSlider(container) {
    const defaultSlider = document.createElement('div');
    defaultSlider.className = 'slider-item active default-slider';
    defaultSlider.innerHTML = `
        <div class="slider-overlay"></div>
        <div class="slider-content">
            <div class="slider-text">
                <h1 class="slider-title animate-fadeInUp">مرحباً بكم في متجرنا</h1>
                <h2 class="slider-subtitle animate-fadeInUp">أفضل المنتجات بأفضل الأسعار</h2>
                <p class="slider-description animate-fadeInUp">اكتشف مجموعتنا الواسعة من المنتجات عالية الجودة</p>
                <a href="#products" class="slider-button animate-fadeInUp">
                    تسوق الآن
                    <i class="fas fa-arrow-left"></i>
                </a>
            </div>
        </div>
    `;
    container.appendChild(defaultSlider);
}

/**
 * عرض سلايدر محدد
 */
function showSlider(index) {
    if (sliders.length === 0) return;

    // إزالة الفئة النشطة من جميع السلايدرات
    const allSliders = document.querySelectorAll('.slider-item');
    const allIndicators = document.querySelectorAll('.slider-indicator');

    allSliders.forEach(slider => slider.classList.remove('active'));
    allIndicators.forEach(indicator => indicator.classList.remove('active'));

    // تفعيل السلايدر والمؤشر الحالي
    if (allSliders[index]) {
        allSliders[index].classList.add('active');
    }
    if (allIndicators[index]) {
        allIndicators[index].classList.add('active');
    }

    currentSliderIndex = index;

    // إعادة تشغيل الحركات
    setTimeout(() => {
        const activeSlider = allSliders[index];
        if (activeSlider) {
            const animatedElements = activeSlider.querySelectorAll('[class*="animate-"]');
            animatedElements.forEach(element => {
                element.style.animation = 'none';
                element.offsetHeight; // إجبار إعادة الرسم
                element.style.animation = null;
            });
        }
    }, 100);
}

/**
 * الانتقال للسلايدر التالي
 */
function nextSlider() {
    if (sliders.length === 0) return;
    const nextIndex = (currentSliderIndex + 1) % sliders.length;
    showSlider(nextIndex);
}

/**
 * الانتقال للسلايدر السابق
 */
function previousSlider() {
    if (sliders.length === 0) return;
    const prevIndex = (currentSliderIndex - 1 + sliders.length) % sliders.length;
    showSlider(prevIndex);
}

/**
 * بدء التشغيل التلقائي للسلايدرات
 */
function startSliderAutoplay() {
    if (sliders.length <= 1) return;

    stopSliderAutoplay(); // إيقاف أي تشغيل سابق

    sliderInterval = setInterval(() => {
        nextSlider();
    }, sliders[currentSliderIndex]?.duration || 5000);
}

/**
 * إيقاف التشغيل التلقائي
 */
function stopSliderAutoplay() {
    if (sliderInterval) {
        clearInterval(sliderInterval);
        sliderInterval = null;
    }
}

/**
 * تبديل التشغيل التلقائي
 */
function toggleSliderAutoplay() {
    const playPauseBtn = document.getElementById('playPauseBtn');

    if (isSliderPlaying) {
        stopSliderAutoplay();
        isSliderPlaying = false;
        if (playPauseBtn) {
            playPauseBtn.innerHTML = '<i class="fas fa-play"></i>';
        }
    } else {
        startSliderAutoplay();
        isSliderPlaying = true;
        if (playPauseBtn) {
            playPauseBtn.innerHTML = '<i class="fas fa-pause"></i>';
        }
    }
}

// ===== نظام تطبيق الثيم المخصص =====

/**
 * تحميل وتطبيق الثيم المخصص من لوحة التحكم
 */
function loadCustomTheme() {
    try {
        const customCSS = localStorage.getItem('customSiteCSS');
        if (customCSS) {
            // إزالة أي ثيم مخصص سابق
            const existingStyle = document.getElementById('customThemeStyle');
            if (existingStyle) {
                existingStyle.remove();
            }

            // إنشاء عنصر style جديد
            const styleElement = document.createElement('style');
            styleElement.id = 'customThemeStyle';
            styleElement.textContent = customCSS;

            // إضافة الثيم للصفحة
            document.head.appendChild(styleElement);

            console.log('🎨 تم تطبيق الثيم المخصص على الموقع');
        } else {
            console.log('🎨 لا يوجد ثيم مخصص محفوظ');
        }
    } catch (error) {
        console.error('❌ خطأ في تحميل الثيم المخصص:', error);
    }
}

/**
 * تحديث الثيم عند تغيير اللغة
 */
function updateThemeForLanguage() {
    // إعادة تطبيق الثيم عند تغيير اللغة
    loadCustomTheme();
}

/**
 * تطبيق ثيم مؤقت للمعاينة
 */
function applyPreviewTheme(themeData) {
    try {
        const previewCSS = generatePreviewCSS(themeData);

        // إزالة أي معاينة سابقة
        const existingPreview = document.getElementById('previewThemeStyle');
        if (existingPreview) {
            existingPreview.remove();
        }

        // إنشاء عنصر style للمعاينة
        const styleElement = document.createElement('style');
        styleElement.id = 'previewThemeStyle';
        styleElement.textContent = previewCSS;

        // إضافة المعاينة للصفحة
        document.head.appendChild(styleElement);

        console.log('👁️ تم تطبيق معاينة الثيم');
    } catch (error) {
        console.error('❌ خطأ في معاينة الثيم:', error);
    }
}

/**
 * إنشاء CSS للمعاينة
 */
function generatePreviewCSS(theme) {
    return `
        /* معاينة الثيم المخصص */
        .header, .navbar {
            background-color: ${theme.headerBg} !important;
            transition: all 0.3s ease !important;
        }

        .nav-link {
            color: ${theme.navText} !important;
        }

        .nav-link.active, .nav-link:hover {
            color: ${theme.navActive} !important;
        }

        body {
            background-color: ${theme.bodyBg} !important;
            color: ${theme.text} !important;
            transition: all 0.3s ease !important;
        }

        .section-title, h1, h2, h3, h4, h5, h6 {
            color: ${theme.text} !important;
        }

        p, .secondary-text, .hero-subtitle {
            color: ${theme.secondaryText} !important;
        }

        .btn-primary, .cta-button, .add-to-cart {
            background-color: ${theme.primaryBtn} !important;
            border-color: ${theme.primaryBtn} !important;
        }

        .btn-secondary {
            background-color: ${theme.secondaryBtn} !important;
            border-color: ${theme.secondaryBtn} !important;
        }

        .btn-success, .contact-submit-btn {
            background-color: ${theme.successBtn} !important;
            border-color: ${theme.successBtn} !important;
        }

        .product-card, .offer-card, .contact-card, .category-card {
            background-color: ${theme.cardBg} !important;
            border-color: ${theme.cardBorder} !important;
            box-shadow: 0 4px 15px ${theme.cardShadow}20 !important;
        }

        .footer {
            background-color: ${theme.footerBg} !important;
            color: ${theme.footerText} !important;
        }

        .footer p, .footer a {
            color: ${theme.footerText} !important;
        }

        /* تأثيرات إضافية للسلايدرات */
        .slider-button {
            background: ${theme.primaryBtn} !important;
        }

        .slider-nav-btn:hover {
            background: ${theme.primaryBtn}40 !important;
        }

        .slider-indicator.active {
            background: ${theme.primaryBtn}90 !important;
        }
    `;
}

/**
 * إزالة معاينة الثيم
 */
function removePreviewTheme() {
    const previewStyle = document.getElementById('previewThemeStyle');
    if (previewStyle) {
        previewStyle.remove();
        console.log('🗑️ تم إزالة معاينة الثيم');
    }
}

/**
 * تصدير الثيم الحالي
 */
function exportCurrentTheme() {
    try {
        const customCSS = localStorage.getItem('customSiteCSS');
        const siteTheme = localStorage.getItem('siteTheme');

        if (customCSS && siteTheme) {
            const exportData = {
                theme: JSON.parse(siteTheme),
                css: customCSS,
                exportDate: new Date().toISOString(),
                siteName: 'هايبر ماركت',
                version: '1.0'
            };

            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `site-theme-${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            console.log('📤 تم تصدير الثيم الحالي');
        } else {
            console.log('⚠️ لا يوجد ثيم مخصص للتصدير');
        }
    } catch (error) {
        console.error('❌ خطأ في تصدير الثيم:', error);
    }
}

/**
 * تحديث المجموع الكلي مع عمولة التوصيل
 */
function updateCartTotal() {
    const cartTotal = document.getElementById('cartTotal');
    const subtotalEl = document.getElementById('subtotal');
    const deliveryFeeAmount = document.getElementById('deliveryFeeAmount');

    if (!cartTotal) return;

    // حساب المجموع الفرعي
    const subtotal = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);

    // حساب الخصم
    let discount = 0;
    if (appliedCoupon) {
        const coupons = loadCoupons();
        const coupon = coupons[appliedCoupon.code];
        if (coupon && subtotal >= coupon.minAmount) {
            if (coupon.type === 'percentage') {
                discount = (subtotal * coupon.value) / 100;
            } else if (coupon.type === 'fixed') {
                discount = Math.min(coupon.value, subtotal);
            }
        }
    }

    // المجموع بعد الخصم
    const totalAfterDiscount = subtotal - discount;

    // المجموع النهائي مع عمولة التوصيل
    const finalTotal = totalAfterDiscount + deliveryFee;

    // تحديث العرض
    if (subtotalEl) subtotalEl.textContent = `${subtotal.toLocaleString()} دينار`;
    if (deliveryFeeAmount) deliveryFeeAmount.textContent = `${deliveryFee.toLocaleString()} دينار`;
    cartTotal.textContent = `${finalTotal.toLocaleString()} دينار`;

    console.log(`💰 تم تحديث المجموع: ${subtotal.toLocaleString()} - ${discount.toLocaleString()} + ${deliveryFee.toLocaleString()} = ${finalTotal.toLocaleString()} دينار`);
}

/*
 * ===============================================
 * وظائف تحميل المنتجات الديناميكية
 * ===============================================
 */

// متغيرات المنتجات
let allProducts = []; // جميع المنتجات
let filteredProducts = []; // المنتجات المفلترة
let currentFilter = 'all'; // الفلتر الحالي

/**
 * تحميل المنتجات من localStorage أو استخدام منتجات افتراضية
 */
function loadProducts() {
    console.log('🔄 بدء تحميل المنتجات...');

    // محاولة تحميل المنتجات من لوحة الإدارة
    const adminProducts = JSON.parse(localStorage.getItem('adminProducts')) || [];
    const products = JSON.parse(localStorage.getItem('products')) || [];

    // استخدام المنتجات من لوحة الإدارة إذا كانت متوفرة
    if (adminProducts.length > 0) {
        allProducts = adminProducts;
        console.log(`📦 تم تحميل ${adminProducts.length} منتج من لوحة الإدارة`);
    } else if (products.length > 0) {
        allProducts = products;
        console.log(`📦 تم تحميل ${products.length} منتج من المخزن العام`);
    } else {
        // استخدام منتجات افتراضية إذا لم توجد منتجات
        allProducts = getDefaultProducts();
        console.log(`📦 تم تحميل ${allProducts.length} منتج افتراضي`);
    }

    // تطبيق الفلتر الحالي
    applyCurrentFilter();
}

/**
 * المنتجات الافتراضية في حالة عدم وجود منتجات في localStorage
 */
function getDefaultProducts() {
    return [
        {
            id: 'default_1',
            name: 'تفاح أحمر طازج',
            nameAr: 'تفاح أحمر طازج',
            nameEn: 'Fresh Red Apples',
            category: 'فواكه',
            price: 15000,
            oldPrice: 20000,
            image: 'https://images.unsplash.com/photo-1560806887-1e4cd0b6cbd6?w=400',
            quantity: 50,
            stock: 50,
            status: 'available',
            featured: true,
            description: 'تفاح أحمر طازج وعالي الجودة',
            descriptionAr: 'تفاح أحمر طازج وعالي الجودة',
            descriptionEn: 'Fresh and high quality red apples'
        },
        {
            id: 'default_2',
            name: 'دجاج طازج',
            nameAr: 'دجاج طازج',
            nameEn: 'Fresh Chicken',
            category: 'لحوم',
            price: 25000,
            image: 'https://images.unsplash.com/photo-1604503468506-a8da13d82791?w=400',
            quantity: 30,
            stock: 30,
            status: 'available',
            featured: false,
            description: 'دجاج طازج عالي الجودة',
            descriptionAr: 'دجاج طازج عالي الجودة',
            descriptionEn: 'Fresh high quality chicken'
        },
        {
            id: 'default_3',
            name: 'أرز بسمتي فاخر',
            nameAr: 'أرز بسمتي فاخر',
            nameEn: 'Premium Basmati Rice',
            category: 'حبوب',
            price: 35000,
            image: 'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=400',
            quantity: 100,
            stock: 100,
            status: 'available',
            featured: true,
            description: 'أرز بسمتي فاخر عالي الجودة',
            descriptionAr: 'أرز بسمتي فاخر عالي الجودة',
            descriptionEn: 'Premium high quality basmati rice'
        },
        {
            id: 'default_4',
            name: 'منظف غسيل فعال',
            nameAr: 'منظف غسيل فعال',
            nameEn: 'Effective Detergent',
            category: 'منظفات',
            price: 18000,
            image: 'https://images.unsplash.com/photo-1563453392212-326f5e854473?w=400',
            quantity: 75,
            stock: 75,
            status: 'available',
            featured: false,
            description: 'منظف غسيل فعال للملابس',
            descriptionAr: 'منظف غسيل فعال للملابس',
            descriptionEn: 'Effective detergent for clothes'
        }
    ];
}

/**
 * عرض المنتجات في الشبكة
 */
function displayProducts(products) {
    const productsGrid = document.getElementById('productsGrid');
    const noProducts = document.getElementById('noProducts');

    if (!productsGrid) return;

    // إخفاء رسالة عدم وجود منتجات
    if (noProducts) {
        noProducts.style.display = 'none';
    }

    if (products.length === 0) {
        // عرض رسالة عدم وجود منتجات
        if (noProducts) {
            noProducts.style.display = 'block';
        } else {
            productsGrid.innerHTML = `
                <div class="no-products">
                    <div class="no-products-icon">
                        <i class="fas fa-box-open"></i>
                    </div>
                    <h3>لا توجد منتجات</h3>
                    <p>لم يتم العثور على منتجات تطابق البحث</p>
                    <button class="btn-primary" onclick="loadProducts()">إعادة تحميل</button>
                </div>
            `;
        }
        return;
    }

    // عرض المنتجات
    productsGrid.innerHTML = products.map(product => createProductCard(product)).join('');

    console.log(`✅ تم عرض ${products.length} منتج`);
}

/**
 * إنشاء بطاقة منتج HTML
 */
function createProductCard(product) {
    const isOutOfStock = product.stock <= 0 || product.status === 'unavailable';
    const hasOldPrice = product.oldPrice && product.oldPrice > product.price;
    const isFeatured = product.featured;

    // تحديد شارة المنتج
    let badge = '';
    if (isOutOfStock) {
        badge = '<div class="product-badge out-of-stock">نفذت الكمية</div>';
    } else if (hasOldPrice) {
        badge = '<div class="product-badge sale">عرض خاص</div>';
    } else if (isFeatured) {
        badge = '<div class="product-badge featured">مميز</div>';
    }

    // تحديد السعر القديم
    const oldPriceHtml = hasOldPrice ?
        `<span class="old-price">${product.oldPrice.toLocaleString()} دينار</span>` : '';

    // تحديد حالة الزر
    const buttonClass = isOutOfStock ? 'add-to-cart disabled' : 'add-to-cart';
    const buttonText = isOutOfStock ? 'غير متوفر' : 'أضف للسلة';
    const buttonAction = isOutOfStock ? '' :
        `onclick="addToCart('${product.id}', '${product.name}', ${product.price}, '${product.image}')"`;

    return `
        <div class="product-card ${isOutOfStock ? 'out-of-stock' : ''}" data-category="${product.category}">
            <div class="product-image">
                <img src="${product.image}" alt="${product.name}"
                     onerror="this.src='https://via.placeholder.com/300x300?text=صورة+غير+متوفرة'">
                ${badge}
            </div>
            <div class="product-info">
                <h3 class="product-name">${product.name}</h3>
                <p class="product-description">${product.description || ''}</p>
                <div class="product-meta">
                    <span class="product-category">${product.category}</span>
                    <span class="product-stock">متوفر: ${product.stock}</span>
                </div>
                <p class="product-price">
                    <span class="current-price">${product.price.toLocaleString()} دينار</span>
                    ${oldPriceHtml}
                </p>
                <button class="${buttonClass}" ${buttonAction}>
                    ${buttonText}
                </button>
            </div>
        </div>
    `;
}

/**
 * فلترة المنتجات حسب النوع
 */
function filterProducts(filter) {
    currentFilter = filter;

    // تحديث أزرار الفلتر
    document.querySelectorAll('.filter-tab').forEach(tab => {
        tab.classList.remove('active');
    });
    event.target.classList.add('active');

    applyCurrentFilter();
}

/**
 * تطبيق الفلتر الحالي
 */
function applyCurrentFilter() {
    let filtered = [...allProducts];

    // تطبيق فلتر النوع
    if (currentFilter === 'featured') {
        filtered = filtered.filter(product => product.featured);
    } else if (currentFilter === 'offers') {
        filtered = filtered.filter(product => product.oldPrice && product.oldPrice > product.price);
    }

    // تطبيق فلتر البحث إذا كان موجوداً
    const searchInput = document.getElementById('productSearch');
    if (searchInput && searchInput.value.trim()) {
        const searchTerm = searchInput.value.trim().toLowerCase();
        filtered = filtered.filter(product =>
            product.name.toLowerCase().includes(searchTerm) ||
            product.nameEn?.toLowerCase().includes(searchTerm) ||
            product.category.toLowerCase().includes(searchTerm) ||
            product.description?.toLowerCase().includes(searchTerm)
        );
    }

    filteredProducts = filtered;
    displayProducts(filteredProducts);

    console.log(`🔍 تم تطبيق الفلتر "${currentFilter}" - عرض ${filtered.length} من ${allProducts.length} منتج`);
}

/**
 * البحث في المنتجات
 */
function searchProducts() {
    applyCurrentFilter();
}

/**
 * تحديث مخزون المنتج
 */
function updateProductStock(productId, quantityUsed) {
    const adminProducts = JSON.parse(localStorage.getItem('adminProducts')) || [];
    const productIndex = adminProducts.findIndex(p => p.id === productId);

    if (productIndex !== -1) {
        adminProducts[productIndex].stock = Math.max(0, adminProducts[productIndex].stock - quantityUsed);
        localStorage.setItem('adminProducts', JSON.stringify(adminProducts));

        // تحديث المنتجات المحلية
        const localProductIndex = allProducts.findIndex(p => p.id === productId);
        if (localProductIndex !== -1) {
            allProducts[localProductIndex].stock = adminProducts[productIndex].stock;
        }

        // إعادة عرض المنتجات لإظهار المخزون المحدث
        applyCurrentFilter();

        console.log(`📦 تم تحديث مخزون المنتج ${productId}: ${adminProducts[productIndex].stock}`);
    }
}

/**
 * تحميل المنتجات الديناميكية (للاستخدام من الخارج)
 */
function loadDynamicProducts() {
    loadProducts();
}

/**
 * إعادة تحميل المنتجات من localStorage
 */
function reloadProducts() {
    console.log('🔄 إعادة تحميل المنتجات...');
    loadProducts();
}

// تحميل المنتجات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // تحميل المنتجات إذا كان هناك عنصر productsGrid
    if (document.getElementById('productsGrid')) {
        loadProducts();
    }

    // الاستماع لتحديثات المنتجات من لوحة الإدارة
    window.addEventListener('productsUpdated', function(event) {
        console.log('📢 تم استلام إشعار تحديث المنتجات:', event.detail);
        setTimeout(() => {
            reloadProducts();
        }, 500);
    });

    // الاستماع للرسائل من النوافذ الأخرى (لوحة التحكم، الكاشير)
    window.addEventListener('message', function(event) {
        if (event.data && event.data.type === 'productsUpdated') {
            console.log('📨 تم استلام رسالة تحديث المنتجات من نافذة أخرى:', event.data.data);
            setTimeout(() => {
                reloadProducts();
                showUpdateNotification(event.data.data);
            }, 500);
        }
    });

    console.log('✅ تم تهيئة نظام المنتجات الديناميكية');
});

/**
 * عرض إشعار تحديث المنتجات
 */
function showUpdateNotification(updateData) {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, #27ae60, #2ecc71);
        color: white;
        padding: 15px 20px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
        z-index: 10000;
        font-weight: 600;
        max-width: 350px;
        animation: slideInRight 0.3s ease;
    `;

    let message = '';
    switch (updateData.source) {
        case 'admin_excel':
            message = `تم إضافة ${updateData.count} منتج جديد من لوحة التحكم`;
            break;
        case 'cashier':
            message = `تم تحديث المخزون من الكاشير`;
            break;
        case 'manual_sync':
            message = `تم مزامنة ${updateData.count} منتج`;
            break;
        default:
            message = `تم تحديث المنتجات (${updateData.count})`;
    }

    notification.innerHTML = `
        <div style="display: flex; align-items: center; gap: 10px;">
            <i class="fas fa-sync-alt" style="animation: spin 1s linear infinite;"></i>
            <span>${message}</span>
        </div>
    `;

    document.body.appendChild(notification);

    // إزالة الإشعار بعد 4 ثوان
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (document.body.contains(notification)) {
                document.body.removeChild(notification);
            }
        }, 300);
    }, 4000);
}
