/*
 * ===============================================
 * ملف التنسيقات الرئيسي - Main Stylesheet
 * ===============================================
 * هذا الملف يحتوي على جميع تنسيقات الموقع
 * بما في ذلك الألوان والخطوط والتخطيط
 */

/* =============================================== */
/* إعادة تعيين التنسيقات الأساسية - Reset and Base Styles */
/* =============================================== */
* {
    margin: 0;                         /* إزالة الهوامش الافتراضية */
    padding: 0;                        /* إزالة الحشو الافتراضي */
    box-sizing: border-box;            /* حساب العرض والارتفاع مع الحدود */
}

body {
    font-family: 'Cairo', sans-serif;  /* خط القاهرة للنصوص العربية */
    line-height: 1.6;                  /* ارتفاع السطر */
    color: #333;                       /* لون النص الأساسي */
    direction: rtl;                    /* اتجاه النص من اليمين لليسار */
    text-align: right;                 /* محاذاة النص لليمين */
}

/* =============================================== */
/* الحاوي الرئيسي - Main Container */
/* =============================================== */
.container {
    max-width: 1200px;                 /* العرض الأقصى للحاوي */
    margin: 0 auto;                    /* توسيط الحاوي */
    padding: 0 20px;                   /* حشو جانبي */
}

/* =============================================== */
/* تنسيقات رأس الصفحة - Header Styles */
/* =============================================== */
.header {
    background: #fff;                  /* خلفية بيضاء */
    box-shadow: 0 2px 10px rgba(0,0,0,0.1); /* ظل خفيف */
    position: fixed;                   /* موضع ثابت */
    top: 0;                           /* في أعلى الصفحة */
    width: 100%;                      /* عرض كامل */
    z-index: 1000;                    /* طبقة عالية */
}

.navbar {
    padding: 1rem 0;
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 0;
}

.logo {
    display: flex;
    align-items: center;
    gap: 1rem;
    text-decoration: none;
    color: #2c5530;
    transition: all 0.3s ease;
    padding: 0.5rem;
    border-radius: 12px;
}

.logo:hover {
    transform: translateY(-2px);
    background: rgba(44, 85, 48, 0.05);
    box-shadow: 0 4px 15px rgba(44, 85, 48, 0.1);
}

.logo i {
    font-size: 2.8rem;
    color: #2c5530;
    text-shadow: 0 2px 4px rgba(44, 85, 48, 0.2);
    transition: all 0.3s ease;
}

.logo:hover i {
    color: #1e3a23;
    transform: scale(1.05);
}

.logo img.logo-image {
    height: 50px;
    width: auto;
    max-width: 140px;
    object-fit: contain;
    border-radius: 10px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.logo:hover img.logo-image {
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    transform: scale(1.02);
}

.logo h2 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 800;
    background: linear-gradient(135deg, #2c5530, #1e3a23, #4CAF50);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: 0.5px;
    text-shadow: none;
    position: relative;
}

.logo h2::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #2c5530, #4CAF50);
    transition: width 0.3s ease;
}

.logo:hover h2::after {
    width: 100%;
}

.logo h2 {
    transition: opacity 0.3s ease;
}

.logo i,
.logo img.logo-image {
    transition: all 0.3s ease;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
    padding: 0.5rem 1rem;
    border-radius: 5px;
}

.nav-link:hover,
.nav-link.active {
    color: #2c5530;
    background: #f0f8f0;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Header Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Language Switcher */
.language-switcher {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 20px;
    background: #f0f8f0;
    transition: all 0.3s ease;
    font-weight: 600;
    font-size: 0.9rem;
}

.language-switcher:hover {
    background: #2c5530;
    color: white;
}

.language-switcher i {
    font-size: 1rem;
    color: #2c5530;
    transition: color 0.3s ease;
}

.language-switcher:hover i {
    color: white;
}

/* Cart Icon */
.cart-icon {
    position: relative;
    cursor: pointer;
    padding: 10px;
    border-radius: 50%;
    background: #f0f8f0;
    transition: all 0.3s ease;
}

.cart-icon:hover {
    background: #2c5530;
    color: white;
}

.cart-icon i {
    font-size: 1.5rem;
    color: #2c5530;
    transition: color 0.3s ease;
}

.cart-icon:hover i {
    color: white;
}

.cart-count {
    position: absolute;
    top: -5px;
    left: -5px;
    background: #e74c3c;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 600;
    min-width: 20px;
}

/* Hero Section */
.hero {
    height: 100vh;
    background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)), 
                url('https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?ixlib=rb-4.0.3') center/cover;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
    position: relative;
}

.hero-content {
    z-index: 2;
    max-width: 600px;
    padding: 0 20px;
}

.hero-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.cta-button {
    display: inline-block;
    background: #2c5530;
    color: white;
    padding: 15px 30px;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(44, 85, 48, 0.3);
}

.cta-button:hover {
    background: #1e3a21;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(44, 85, 48, 0.4);
}

/* Section Titles */
.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c5530;
    margin-bottom: 3rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    right: 50%;
    transform: translateX(50%);
    width: 80px;
    height: 4px;
    background: #2c5530;
    border-radius: 2px;
}

/* Special Offers Section */
.offers {
    padding: 80px 0;
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    color: white;
}

.offers .section-title {
    color: white;
}

.offers .section-title::after {
    background: white;
}

.offer-timer {
    text-align: center;
    margin-bottom: 3rem;
}

.offer-timer h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.countdown {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.time-unit {
    background: rgba(255,255,255,0.1);
    padding: 1rem;
    border-radius: 10px;
    min-width: 80px;
    backdrop-filter: blur(10px);
}

.time-unit span {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.time-unit label {
    font-size: 0.9rem;
    opacity: 0.8;
}

.offers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.offer-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
    position: relative;
}

.offer-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
}

.offer-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: #e74c3c;
    color: white;
    padding: 8px 15px;
    border-radius: 25px;
    font-weight: 700;
    font-size: 0.9rem;
    z-index: 2;
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.4);
}

.offer-card .product-info {
    color: #333;
}

/* Categories Section */
.categories {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2.5rem;
    margin-top: 3rem;
}

.category-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: all 0.4s ease;
    cursor: pointer;
    position: relative;
}

.category-card:hover {
    transform: translateY(-15px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(44, 85, 48, 0.1), rgba(76, 175, 80, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
}

.category-card:hover::before {
    opacity: 1;
}

.category-image-container {
    position: relative;
    height: 200px;
    overflow: hidden;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.category-image-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(44, 85, 48, 0.3), rgba(76, 175, 80, 0.2));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.category-card:hover .category-image-container::after {
    opacity: 1;
}

.category-image-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.4s ease;
    filter: brightness(1) contrast(1.1);
}

.category-card:hover .category-image-container img {
    transform: scale(1.1);
    filter: brightness(1.1) contrast(1.2);
}

.category-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, #2c5530, #4CAF50);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2.5rem;
    box-shadow: 0 8px 20px rgba(44, 85, 48, 0.3);
    transition: all 0.4s ease;
    z-index: 3;
    border: 3px solid rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
}

.category-card:hover .category-icon {
    transform: translate(-50%, -50%) scale(1.15) rotate(5deg);
    box-shadow: 0 15px 35px rgba(44, 85, 48, 0.5);
    background: linear-gradient(135deg, #1e3a23, #2c5530);
}

.category-icon i {
    transition: all 0.3s ease;
}

.category-card:hover .category-icon i {
    transform: scale(1.1);
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.category-content {
    padding: 2rem;
    position: relative;
    z-index: 2;
}

.category-name {
    font-size: 1.4rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.8rem;
    transition: color 0.3s ease;
}

.category-card:hover .category-name {
    color: #2c5530;
}

.category-desc {
    color: #7f8c8d;
    line-height: 1.6;
    font-size: 0.95rem;
    transition: color 0.3s ease;
}

.category-card:hover .category-desc {
    color: #5a6c7d;
}

.category-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #2c5530, #4CAF50);
    transform: scaleX(0);
    transition: transform 0.4s ease;
}

.category-card:hover .category-overlay {
    transform: scaleX(1);
}

/* تأثير النبضة للفئات */
@keyframes categoryPulse {
    0% {
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }
    50% {
        box-shadow: 0 12px 35px rgba(44, 85, 48, 0.2);
    }
    100% {
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }
}

.category-card {
    animation: categoryPulse 3s ease-in-out infinite;
}

.category-card:hover {
    animation: none;
}

/* تأثير تموج للصورة */
@keyframes imageRipple {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    100% {
        transform: scale(1.4);
        opacity: 0;
    }
}

.category-image-container::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    transform: translate(-50%, -50%) scale(0);
    transition: all 0.3s ease;
    z-index: 2;
}

.category-card:hover .category-image-container::before {
    animation: imageRipple 0.6s ease-out;
}

/* شارة عدد المنتجات */
.category-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 700;
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.4);
    z-index: 4;
    transition: all 0.3s ease;
    border: 2px solid rgba(255, 255, 255, 0.9);
}

.category-card:hover .category-badge {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.6);
}

/* Category Products Modal */
.category-products-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 3000;
    display: none;
    align-items: center;
    justify-content: center;
}

.category-products-modal .modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
}

.category-products-modal .modal-content {
    position: relative;
    background: white;
    border-radius: 15px;
    width: 90%;
    max-width: 1200px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.category-products-modal .modal-header {
    background: linear-gradient(135deg, #2c5530, #1e3a23);
    color: white;
    padding: 1.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.category-products-modal .modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
}

.category-products-modal .close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 2rem;
    cursor: pointer;
    padding: 0;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.category-products-modal .close-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

.category-products-modal .modal-body {
    padding: 2rem;
    max-height: calc(90vh - 100px);
    overflow-y: auto;
}

.category-products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
}

.category-products-modal .product-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.category-products-modal .product-card:hover {
    transform: translateY(-3px);
}

.category-products-modal .product-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.category-products-modal .product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.category-products-modal .product-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #e74c3c;
    color: white;
    padding: 0.3rem 0.6rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.category-products-modal .out-of-stock-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background: #95a5a6;
    color: white;
    padding: 0.3rem 0.6rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.category-products-modal .product-info {
    padding: 1.2rem;
}

.category-products-modal .product-name {
    font-size: 1.1rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.category-products-modal .product-description {
    color: #7f8c8d;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    line-height: 1.4;
}

.category-products-modal .product-price {
    margin-bottom: 1rem;
}

.category-products-modal .current-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: #2c5530;
}

.category-products-modal .old-price {
    font-size: 1rem;
    color: #95a5a6;
    text-decoration: line-through;
    margin-right: 0.5rem;
}

.category-products-modal .add-to-cart {
    width: 100%;
    background: #2c5530;
    color: white;
    border: none;
    padding: 0.8rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s ease;
}

.category-products-modal .add-to-cart:hover {
    background: #1e3a23;
}

.category-products-modal .out-of-stock-btn {
    width: 100%;
    background: #95a5a6;
    color: white;
    border: none;
    padding: 0.8rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: not-allowed;
}

.category-icon {
    font-size: 3rem;
    color: #2c5530;
    margin-bottom: 1rem;
}

.category-name {
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.category-desc {
    color: #666;
    font-size: 0.9rem;
}

/* Products Section */
.products {
    padding: 80px 0;
}

/* Products Filters */
.products-filters {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3rem;
    gap: 2rem;
    flex-wrap: wrap;
}

.filter-tabs {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.filter-tab {
    padding: 12px 24px;
    border: 2px solid #e0e0e0;
    background: white;
    color: #666;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    font-size: 0.9rem;
}

.filter-tab:hover {
    border-color: #2c5530;
    color: #2c5530;
    transform: translateY(-2px);
}

.filter-tab.active {
    background: #2c5530;
    color: white;
    border-color: #2c5530;
    box-shadow: 0 4px 15px rgba(44, 85, 48, 0.3);
}

.search-filter {
    position: relative;
    min-width: 300px;
}

.search-filter input {
    width: 100%;
    padding: 12px 45px 12px 20px;
    border: 2px solid #e0e0e0;
    border-radius: 25px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.search-filter input:focus {
    outline: none;
    border-color: #2c5530;
    box-shadow: 0 0 0 3px rgba(44, 85, 48, 0.1);
}

.search-filter i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    font-size: 1.1rem;
}

/* Products Grid */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    min-height: 400px;
}

/* Loading State */
.loading-products {
    grid-column: 1 / -1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    color: #666;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #2c5530;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* No Products State */
.no-products {
    grid-column: 1 / -1;
    text-align: center;
    padding: 4rem 2rem;
    color: #666;
}

.no-products-icon {
    font-size: 4rem;
    color: #ddd;
    margin-bottom: 1rem;
}

.no-products h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: #333;
}

.no-products p {
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

.btn-primary {
    background: #2c5530;
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-primary:hover {
    background: #1e3a21;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(44, 85, 48, 0.3);
}

.product-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.product-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.1);
}

.product-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #e74c3c;
    color: white;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.product-badge.new {
    background: #27ae60;
}

.product-badge.featured-badge {
    background: linear-gradient(45deg, #f39c12, #e67e22);
    box-shadow: 0 2px 8px rgba(243, 156, 18, 0.3);
}

.product-info {
    padding: 1.5rem;
}

.product-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.product-price {
    margin-bottom: 1rem;
}

.current-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: #2c5530;
}

.old-price {
    font-size: 0.9rem;
    color: #999;
    text-decoration: line-through;
    margin-right: 10px;
}

.add-to-cart {
    width: 100%;
    background: #2c5530;
    color: white;
    border: none;
    padding: 12px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s ease;
}

.add-to-cart:hover {
    background: #1e3a21;
}

/* Stock status styles */
.product-card.out-of-stock {
    opacity: 0.7;
}

.product-card.out-of-stock .product-image {
    position: relative;
}

.product-card.out-of-stock .product-image::after {
    content: 'نفذت الكمية';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.1rem;
}

/* Product Meta Information */
.product-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0.5rem 0;
    font-size: 0.85rem;
    color: #666;
}

.product-category {
    background: #f0f8f0;
    color: #2c5530;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-weight: 600;
}

.product-stock {
    color: #27ae60;
    font-weight: 600;
}

.product-card.out-of-stock .product-stock {
    color: #e74c3c;
}

/* Product Description */
.product-description {
    font-size: 0.9rem;
    color: #666;
    margin: 0.5rem 0;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Enhanced Product Badges */
.product-badge.featured {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
}

.product-badge.sale {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.product-badge.out-of-stock {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
    color: white;
}

/* Disabled Button State */
.add-to-cart.disabled {
    background: #bdc3c7;
    color: #7f8c8d;
    cursor: not-allowed;
    transform: none;
}

.add-to-cart.disabled:hover {
    background: #bdc3c7;
    transform: none;
}

/* Responsive Filters */
@media (max-width: 768px) {
    .products-filters {
        flex-direction: column;
        gap: 1rem;
    }

    .search-filter {
        min-width: 100%;
    }

    .filter-tabs {
        justify-content: center;
    }

    .filter-tab {
        font-size: 0.8rem;
        padding: 10px 16px;
    }
}

/* Notification Animations */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Product Sync Status */
.sync-status {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: rgba(44, 85, 48, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 1000;
    display: none;
}

.sync-status.active {
    display: flex;
    align-items: center;
    gap: 8px;
    animation: fadeIn 0.3s ease;
}

.sync-status i {
    animation: spin 1s linear infinite;
}
    bottom: 0;
    background: rgba(0,0,0,0.3);
    border-radius: 15px 15px 0 0;
}

.stock-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    padding: 0.3rem 0.6rem;
    border-radius: 15px;
    font-size: 0.7rem;
    font-weight: 700;
    color: white;
    z-index: 2;
}

.stock-badge.low-stock {
    background: #f39c12;
}

.stock-badge.out-of-stock {
    background: #e74c3c;
}

.out-of-stock-message {
    background: #f8d7da;
    color: #721c24;
    padding: 12px;
    border-radius: 8px;
    text-align: center;
    font-weight: 600;
    border: 1px solid #f5c6cb;
}

.out-of-stock-message i {
    margin-left: 0.5rem;
    font-size: 1.1rem;
}

.out-of-stock-message small {
    font-weight: 400;
    opacity: 0.8;
}

/* Contact Section */
.contact-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
}

.contact-content {
    margin-top: 3rem;
}

.contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: start;
}

.contact-form-section {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.form-header {
    margin-bottom: 1.5rem;
    text-align: center;
}

.form-header h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c5530;
    margin-bottom: 0.5rem;
}

.form-header p {
    color: #666;
    font-size: 0.9rem;
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 0.8rem;
    border: 2px solid #e1e1e1;
    border-radius: 8px;
    font-family: 'Cairo', sans-serif;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #2c5530;
}

.contact-submit-btn {
    background: linear-gradient(135deg, #2c5530, #1e3a23);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.contact-submit-btn:hover {
    transform: translateY(-2px);
}

.contact-info-section {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.contact-cards {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.contact-card {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    transition: transform 0.3s ease;
}

.contact-card:hover {
    transform: translateY(-3px);
}

.card-icon {
    background: linear-gradient(135deg, #2c5530, #1e3a23);
    color: white;
    width: 45px;
    height: 45px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    flex-shrink: 0;
}

.card-content h4 {
    font-size: 1rem;
    font-weight: 700;
    color: #2c5530;
    margin-bottom: 0.5rem;
}

.card-content p {
    color: #666;
    line-height: 1.6;
    font-size: 0.9rem;
}

.card-content a {
    color: #2c5530;
    text-decoration: none;
    font-weight: 600;
}

.card-content a:hover {
    text-decoration: underline;
}

.quick-contact-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.whatsapp-btn,
.call-btn {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.whatsapp-btn {
    background: #25d366;
    color: white;
}

.call-btn {
    background: #2c5530;
    color: white;
}

.whatsapp-btn:hover,
.call-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* Footer */
.footer {
    background: linear-gradient(135deg, #2c5530, #1e3a23);
    color: white;
    padding: 30px 0;
}






.footer-bottom {
    text-align: center;
    opacity: 0.9;
}

/* Shopping Cart Modal */
.cart-modal {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    background: white;
    z-index: 2000;
    transition: right 0.3s ease, transform 0.3s ease;
    box-shadow: -5px 0 20px rgba(0,0,0,0.1);
    border-radius: 0 0 0 10px;
}

.cart-modal.active {
    right: 0;
    transform: translateX(0);
}

.cart-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: rgba(0,0,0,0.5);
    z-index: 1999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    backdrop-filter: blur(2px);
}

.cart-overlay.active {
    opacity: 1;
    visibility: visible;
}

.cart-content {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.cart-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #2c5530;
    color: white;
    position: relative;
}

.cart-header:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(to right, transparent, rgba(255,255,255,0.2), transparent);
}

.cart-header h2 {
    margin: 0;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.cart-header h2:before {
    content: '\f07a';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
}

.close-cart {
    font-size: 1.8rem;
    cursor: pointer;
    color: white;
    transition: all 0.3s ease;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.close-cart:hover {
    background: rgba(255,255,255,0.2);
    transform: rotate(90deg);
}

.cart-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 0;
    overflow-y: auto;
    max-height: calc(100vh - 80px);
    background: #f9f9f9;
}

.cart-items {
    padding: 1rem;
    margin-bottom: 0;
    background: white;
}

/* Custom scrollbar for cart */
.cart-body::-webkit-scrollbar {
    width: 6px;
}

.cart-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.cart-body::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.cart-body::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

.cart-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #eee;
    gap: 1rem;
}

.cart-item img {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 8px;
}

.cart-item-info {
    flex: 1;
}

.cart-item-name {
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.cart-item-price {
    color: #2c5530;
    font-weight: 700;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0.5rem 0;
}

.quantity-btn {
    background: #2c5530;
    color: white;
    border: none;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
}

.quantity-btn:hover {
    background: #1e3a21;
}

.quantity {
    font-weight: 600;
    min-width: 30px;
    text-align: center;
}

.remove-item {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 0.3rem 0.6rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
}

.remove-item:hover {
    background: #c0392b;
}

.empty-cart {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 8px;
    margin: 1rem;
}

.empty-cart:before {
    content: '\f07a';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    font-size: 3rem;
    color: #ddd;
    display: block;
    margin-bottom: 1rem;
}

/* Enhanced Coupon Section */
.coupon-section {
    background: white;
    margin: 1rem;
    padding: 1.5rem;
    border-radius: 12px;
    border: 2px solid #e8f5e8;
    box-shadow: 0 2px 8px rgba(44, 85, 48, 0.1);
}

.coupon-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    color: #2c5530;
}

.coupon-header i {
    font-size: 1.2rem;
    color: #2c5530;
}

.coupon-header h4 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.coupon-input-group {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.coupon-input-group input {
    flex: 1;
    padding: 12px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.coupon-input-group input:focus {
    outline: none;
    border-color: #2c5530;
    box-shadow: 0 0 0 3px rgba(44, 85, 48, 0.1);
}

.apply-coupon-btn {
    padding: 12px 16px;
    background: #2c5530;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    min-width: 80px;
    justify-content: center;
}

.apply-coupon-btn:hover {
    background: #1e3a23;
    transform: translateY(-1px);
}

.coupon-message {
    padding: 0.8rem;
    border-radius: 6px;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    display: none;
}

.coupon-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
    display: block;
}

.coupon-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    display: block;
}

.applied-coupon-info {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 8px;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.coupon-success {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #155724;
    font-weight: 600;
}

.coupon-success i {
    color: #28a745;
}

.remove-coupon-btn {
    background: #dc3545;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

.remove-coupon-btn:hover {
    background: #c82333;
}

/* Coupon Section */
.coupon-section {
    margin: 0 1rem 1rem 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.coupon-section h4 {
    margin: 0 0 1rem 0;
    color: #2c5530;
    font-size: 1rem;
}

.coupon-input-group {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.coupon-input-group input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 0.9rem;
    direction: ltr;
    text-align: left;
}

.coupon-input-group input:focus {
    outline: none;
    border-color: #2c5530;
}

.apply-coupon-btn {
    background: #2c5530;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    white-space: nowrap;
}

.apply-coupon-btn:hover {
    background: #1e3a21;
}

.coupon-message {
    font-size: 0.85rem;
    margin-bottom: 1rem;
    padding: 0.5rem;
    border-radius: 4px;
    text-align: center;
}

.coupon-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.coupon-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.available-coupons p {
    margin: 0 0 0.5rem 0;
    font-size: 0.85rem;
    color: #666;
}

.coupon-list {
    display: flex;
    flex-direction: column;
    gap: 0.3rem;
}

.coupon-code {
    background: #e9ecef;
    padding: 0.4rem 0.6rem;
    border-radius: 4px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.coupon-code:hover {
    background: #2c5530;
    color: white;
    border-color: #2c5530;
}

/* Order Summary */
.order-summary {
    background: white;
    padding: 1.5rem;
    margin: 1rem;
    border-radius: 12px;
    border: 2px solid #e8f5e8;
    box-shadow: 0 2px 8px rgba(44, 85, 48, 0.1);
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    font-size: 0.95rem;
    padding: 0.5rem 0;
}

.summary-row:not(:last-child) {
    border-bottom: 1px solid #f0f0f0;
}

.summary-row.total-row {
    font-weight: 700;
    font-size: 1.2rem;
    color: #2c5530;
    border-top: 2px solid #2c5530;
    border-bottom: none;
    padding-top: 1rem;
    margin-top: 1rem;
    background: #f8fdf8;
    margin: 1rem -1.5rem -1.5rem -1.5rem;
    padding: 1rem 1.5rem;
    border-radius: 0 0 12px 12px;
}

.discount-row {
    color: #e74c3c;
    font-weight: 600;
    background: #fff5f5;
    margin: 0 -1.5rem;
    padding: 0.8rem 1.5rem;
    border-radius: 6px;
}

.discount-value {
    color: #e74c3c;
    font-weight: 700;
}

.savings-info {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    padding: 1rem;
    margin: 1rem;
    border-radius: 8px;
    text-align: center;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.savings-amount {
    font-size: 1.1rem;
    font-weight: 700;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.discount-row {
    color: #e74c3c;
    font-weight: 600;
}

.total-row {
    border-top: 1px solid #ddd;
    padding-top: 0.5rem;
    margin-top: 0.5rem;
    font-weight: 700;
    font-size: 1rem;
    color: #2c5530;
}

/* Customer Info */
.customer-info {
    margin: 0 1rem 1rem 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.customer-info h4 {
    margin: 0 0 1rem 0;
    color: #2c5530;
    font-size: 1rem;
}

.customer-info input,
.customer-info textarea {
    width: 100%;
    padding: 10px;
    margin-bottom: 0.8rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 0.9rem;
    font-family: 'Cairo', sans-serif;
    box-sizing: border-box;
}

.customer-info input:focus,
.customer-info textarea:focus {
    outline: none;
    border-color: #2c5530;
}

.customer-info textarea {
    resize: vertical;
    min-height: 60px;
}

.cart-actions {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    background: white;
    border-top: 1px solid #eee;
    margin-top: auto;
}

.clear-cart,
.whatsapp-btn {
    flex: 1;
    padding: 12px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.clear-cart {
    background: #e74c3c;
    color: white;
}

.clear-cart:hover {
    background: #c0392b;
}

.whatsapp-btn {
    background: #25d366;
    color: white;
    font-size: 0.9rem;
}

.whatsapp-btn:hover {
    background: #128c7e;
    transform: translateY(-1px);
}

.whatsapp-btn i {
    font-size: 1.1rem;
}

.cart-footer-info {
    background: #f8f9fa;
    padding: 1rem;
    margin: 0 1rem 1rem 1rem;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.delivery-info {
    margin: 0;
    font-size: 0.85rem;
    color: #666;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-align: center;
    justify-content: center;
}

.delivery-info i {
    color: #2c5530;
}

/* Language Support */
.lang-en {
    font-family: 'Arial', sans-serif;
}

.lang-en .hero-title,
.lang-en .section-title {
    font-family: 'Arial', sans-serif;
    font-weight: 700;
}

.lang-en .nav-menu {
    font-family: 'Arial', sans-serif;
}

.lang-en .product-name,
.lang-en .category-name {
    font-family: 'Arial', sans-serif;
    font-weight: 600;
}

/* RTL/LTR specific adjustments */
[dir="ltr"] .header-actions {
    flex-direction: row-reverse;
}

[dir="ltr"] .cart-count {
    right: -5px;
    left: auto;
}

[dir="ltr"] .language-switcher {
    margin-left: 1rem;
    margin-right: 0;
}



[dir="ltr"] .footer-bottom {
    text-align: center;
}

[dir="ltr"] .social-links {
    justify-content: flex-start;
}



/* Responsive Design */
@media (max-width: 768px) {
    .categories-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 2rem;
    }

    .category-image-container {
        height: 160px;
    }

    .category-icon {
        width: 80px;
        height: 80px;
        font-size: 2rem;
    }

    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        right: -100%;
        top: 70px;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0,0,0,0.05);
        padding: 2rem 0;
    }

    .nav-menu.active {
        right: 0;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .categories-grid,
    .products-grid,
    .offers-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .category-card {
        margin: 0 auto;
        max-width: 350px;
    }

    .category-image-container {
        height: 180px;
    }

    .category-content {
        padding: 1.5rem;
    }

    .category-name {
        font-size: 1.2rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .contact-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .quick-contact-actions {
        flex-direction: column;
    }

    .contact-form-section {
        padding: 1.5rem;
    }

    .cart-modal {
        width: 100%;
        right: -100%;
    }

    .cart-body {
        max-height: calc(100vh - 60px);
    }

    .coupon-section,
    .order-summary,
    .customer-info {
        margin: 0 0.5rem 1rem 0.5rem;
        padding: 0.8rem;
    }

    .cart-actions {
        padding: 0.8rem;
    }

    .cart-items {
        padding: 0.5rem;
    }

    .countdown {
        gap: 1rem;
    }

    .time-unit {
        min-width: 60px;
        padding: 0.8rem;
    }

    .time-unit span {
        font-size: 1.5rem;
    }
}

/* ===== أنماط خيارات التوصيل ===== */
.delivery-options {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
    margin: 1rem 0;
    border: 1px solid #e9ecef;
}

.delivery-options h4 {
    margin: 0 0 1rem 0;
    color: #2c3e50;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.delivery-options h4::before {
    content: "🚚";
    font-size: 1.2rem;
}

.delivery-option {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    cursor: pointer;
}

.delivery-option:hover {
    border-color: #667eea;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
}

.delivery-option input[type="radio"] {
    margin-left: 1rem;
    transform: scale(1.2);
    accent-color: #667eea;
}

.delivery-option label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    flex: 1;
    margin: 0;
}

.delivery-option label i {
    font-size: 1.2rem;
    color: #667eea;
    width: 20px;
    text-align: center;
}

.delivery-option label span {
    font-weight: 600;
    color: #2c3e50;
}

.delivery-option label small {
    color: #6c757d;
    font-size: 0.85rem;
    margin-right: auto;
}

.delivery-option input[type="radio"]:checked + label {
    color: #667eea;
}

.delivery-option input[type="radio"]:checked + label i {
    color: #56ab2f;
}

/* اختيار المنطقة */
.zone-selection {
    margin-top: 1rem;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.zone-selection label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #495057;
}

.zone-selection select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 0.9rem;
    background: white;
    color: #495057;
    transition: border-color 0.3s ease;
}

.zone-selection select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* صف عمولة التوصيل */
.delivery-fee-row {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-radius: 8px;
    padding: 0.75rem 1rem;
    margin: 0.5rem 0;
}

.delivery-fee-row span:first-child {
    font-weight: 600;
}

.delivery-fee-row span:last-child {
    font-weight: 700;
    font-size: 1.1rem;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .delivery-options {
        padding: 1rem;
        margin: 0.5rem 0;
    }

    .delivery-option {
        padding: 0.75rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .delivery-option input[type="radio"] {
        margin-left: 0;
        margin-bottom: 0.5rem;
    }

    .delivery-option label {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }

    .zone-selection {
        padding: 0.75rem;
    }

    .zone-selection select {
        padding: 0.6rem;
        font-size: 0.85rem;
    }
}

/* ===== أنماط السلايدرات الاحترافية ===== */

.sliders-section {
    position: relative;
    height: 100vh;
    min-height: 600px;
    overflow: hidden;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.sliders-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.slider-item {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0;
    transform: scale(1.1);
    transition: all 1s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    z-index: 1;
}

.slider-item.active {
    opacity: 1;
    transform: scale(1);
    z-index: 2;
}

.slider-item.default-slider {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #56ab2f 100%);
}

.slider-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.2));
    z-index: 3;
}

.slider-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: white;
    z-index: 4;
    width: 90%;
    max-width: 800px;
}

.slider-text {
    position: relative;
}

.slider-title {
    font-size: 4rem;
    font-weight: 800;
    margin-bottom: 1rem;
    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    line-height: 1.2;
    letter-spacing: -2px;
}

.slider-subtitle {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    opacity: 0.9;
}

.slider-description {
    font-size: 1.2rem;
    font-weight: 400;
    margin-bottom: 2rem;
    text-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
    opacity: 0.8;
    line-height: 1.6;
}

.slider-button {
    display: inline-flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 2.5rem;
    background: linear-gradient(135deg, #56ab2f, #a8e6cf);
    color: white;
    text-decoration: none;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 10px 30px rgba(86, 171, 47, 0.3);
    position: relative;
    overflow: hidden;
}

.slider-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.slider-button:hover::before {
    left: 100%;
}

.slider-button:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 15px 40px rgba(86, 171, 47, 0.5);
}

.slider-button i {
    transition: transform 0.3s ease;
}

.slider-button:hover i {
    transform: translateX(-5px);
}

/* العناصر الزخرفية */
.slider-decorations {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 2;
}

.decoration-shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.decoration-shape.shape-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    right: 10%;
    animation-delay: 0s;
}

.decoration-shape.shape-2 {
    width: 150px;
    height: 150px;
    bottom: 20%;
    left: 15%;
    animation-delay: 2s;
}

.decoration-shape.shape-3 {
    width: 100px;
    height: 100px;
    top: 60%;
    right: 20%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(10deg);
    }
}

/* مؤشرات السلايدرات */
.slider-indicators {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 1rem;
    z-index: 5;
}

.slider-indicator {
    background: rgba(255, 255, 255, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.5);
    border-radius: 15px;
    padding: 0.75rem 1rem;
    cursor: pointer;
    transition: all 0.4s ease;
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    min-width: 120px;
}

.slider-indicator:hover {
    background: rgba(255, 255, 255, 0.4);
    transform: translateY(-3px);
}

.slider-indicator.active {
    background: rgba(255, 255, 255, 0.9);
    color: #2c3e50;
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.indicator-preview {
    width: 40px;
    height: 25px;
    border-radius: 5px;
    background-size: cover;
    background-position: center;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.indicator-info {
    flex: 1;
}

.indicator-title {
    font-size: 0.8rem;
    font-weight: 600;
    display: block;
    margin-bottom: 0.25rem;
}

.indicator-progress {
    height: 2px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 1px;
    overflow: hidden;
    position: relative;
}

.slider-indicator.active .indicator-progress::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: #56ab2f;
    animation: progress-fill 5s linear infinite;
}

@keyframes progress-fill {
    0% { width: 0%; }
    100% { width: 100%; }
}

/* أزرار التنقل */
.slider-navigation {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 2rem;
    z-index: 5;
    pointer-events: none;
}

.slider-nav-btn {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.4s ease;
    backdrop-filter: blur(10px);
    pointer-events: all;
    display: flex;
    align-items: center;
    justify-content: center;
}

.slider-nav-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
}

.slider-nav-btn:active {
    transform: scale(0.95);
}

/* أزرار التحكم */
.slider-controls {
    position: absolute;
    top: 30px;
    right: 30px;
    z-index: 5;
}

.slider-control-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.4s ease;
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
}

.slider-control-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* حركات السلايدرات */
@keyframes slideInRight {
    0% {
        opacity: 0;
        transform: translateX(100px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInLeft {
    0% {
        opacity: 0;
        transform: translateX(-100px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInUp {
    0% {
        opacity: 0;
        transform: translateY(100px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    0% {
        opacity: 0;
        transform: translateY(-100px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}

@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(50px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    0% {
        opacity: 0;
        transform: translateY(-50px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes zoomIn {
    0% {
        opacity: 0;
        transform: scale(0.5);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes zoomOut {
    0% {
        opacity: 0;
        transform: scale(1.5);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes rotateIn {
    0% {
        opacity: 0;
        transform: rotate(-180deg);
    }
    100% {
        opacity: 1;
        transform: rotate(0deg);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes flipInX {
    0% {
        opacity: 0;
        transform: perspective(400px) rotateX(90deg);
    }
    40% {
        transform: perspective(400px) rotateX(-20deg);
    }
    60% {
        transform: perspective(400px) rotateX(10deg);
    }
    80% {
        transform: perspective(400px) rotateX(-5deg);
    }
    100% {
        opacity: 1;
        transform: perspective(400px) rotateX(0deg);
    }
}

@keyframes flipInY {
    0% {
        opacity: 0;
        transform: perspective(400px) rotateY(90deg);
    }
    40% {
        transform: perspective(400px) rotateY(-20deg);
    }
    60% {
        transform: perspective(400px) rotateY(10deg);
    }
    80% {
        transform: perspective(400px) rotateY(-5deg);
    }
    100% {
        opacity: 1;
        transform: perspective(400px) rotateY(0deg);
    }
}

/* تطبيق الحركات */
.animate-slideInRight {
    animation: slideInRight 1s ease-out forwards;
}

.animate-slideInLeft {
    animation: slideInLeft 1s ease-out forwards;
}

.animate-slideInUp {
    animation: slideInUp 1s ease-out forwards;
}

.animate-slideInDown {
    animation: slideInDown 1s ease-out forwards;
}

.animate-fadeIn {
    animation: fadeIn 1.5s ease-out forwards;
}

.animate-fadeInUp {
    animation: fadeInUp 1s ease-out forwards;
}

.animate-fadeInDown {
    animation: fadeInDown 1s ease-out forwards;
}

.animate-zoomIn {
    animation: zoomIn 1s ease-out forwards;
}

.animate-zoomOut {
    animation: zoomOut 1s ease-out forwards;
}

.animate-rotateIn {
    animation: rotateIn 1s ease-out forwards;
}

.animate-bounceIn {
    animation: bounceIn 1s ease-out forwards;
}

.animate-flipInX {
    animation: flipInX 1s ease-out forwards;
}

.animate-flipInY {
    animation: flipInY 1s ease-out forwards;
}

/* تأخير الحركات للعناصر المختلفة */
.slider-title.animate-slideInRight,
.slider-title.animate-slideInLeft,
.slider-title.animate-slideInUp,
.slider-title.animate-slideInDown,
.slider-title.animate-fadeIn,
.slider-title.animate-fadeInUp,
.slider-title.animate-fadeInDown,
.slider-title.animate-zoomIn,
.slider-title.animate-zoomOut,
.slider-title.animate-rotateIn,
.slider-title.animate-bounceIn,
.slider-title.animate-flipInX,
.slider-title.animate-flipInY {
    animation-delay: 0.2s;
}

.slider-subtitle.animate-slideInRight,
.slider-subtitle.animate-slideInLeft,
.slider-subtitle.animate-slideInUp,
.slider-subtitle.animate-slideInDown,
.slider-subtitle.animate-fadeIn,
.slider-subtitle.animate-fadeInUp,
.slider-subtitle.animate-fadeInDown,
.slider-subtitle.animate-zoomIn,
.slider-subtitle.animate-zoomOut,
.slider-subtitle.animate-rotateIn,
.slider-subtitle.animate-bounceIn,
.slider-subtitle.animate-flipInX,
.slider-subtitle.animate-flipInY {
    animation-delay: 0.4s;
}

.slider-description.animate-slideInRight,
.slider-description.animate-slideInLeft,
.slider-description.animate-slideInUp,
.slider-description.animate-slideInDown,
.slider-description.animate-fadeIn,
.slider-description.animate-fadeInUp,
.slider-description.animate-fadeInDown,
.slider-description.animate-zoomIn,
.slider-description.animate-zoomOut,
.slider-description.animate-rotateIn,
.slider-description.animate-bounceIn,
.slider-description.animate-flipInX,
.slider-description.animate-flipInY {
    animation-delay: 0.6s;
}

.slider-button.animate-slideInRight,
.slider-button.animate-slideInLeft,
.slider-button.animate-slideInUp,
.slider-button.animate-slideInDown,
.slider-button.animate-fadeIn,
.slider-button.animate-fadeInUp,
.slider-button.animate-fadeInDown,
.slider-button.animate-zoomIn,
.slider-button.animate-zoomOut,
.slider-button.animate-rotateIn,
.slider-button.animate-bounceIn,
.slider-button.animate-flipInX,
.slider-button.animate-flipInY {
    animation-delay: 0.8s;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .sliders-section {
        height: 70vh;
        min-height: 500px;
    }

    .slider-title {
        font-size: 2.5rem;
        margin-bottom: 0.75rem;
    }

    .slider-subtitle {
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }

    .slider-description {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }

    .slider-button {
        padding: 0.75rem 2rem;
        font-size: 1rem;
    }

    .slider-indicators {
        bottom: 20px;
        gap: 0.5rem;
    }

    .slider-indicator {
        padding: 0.5rem 0.75rem;
        min-width: 100px;
    }

    .indicator-title {
        font-size: 0.7rem;
    }

    .slider-navigation {
        padding: 0 1rem;
    }

    .slider-nav-btn {
        width: 50px;
        height: 50px;
        font-size: 1rem;
    }

    .slider-controls {
        top: 20px;
        right: 20px;
    }

    .slider-control-btn {
        width: 40px;
        height: 40px;
        font-size: 0.9rem;
    }

    .decoration-shape {
        display: none;
    }
}
