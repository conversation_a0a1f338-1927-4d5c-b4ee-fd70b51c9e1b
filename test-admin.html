<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام الإداري</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
        }

        .header h1 {
            font-size: 2.5rem;
            color: #333;
            margin-bottom: 1rem;
        }

        .header p {
            color: #666;
            font-size: 1.2rem;
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-bottom: 3rem;
        }

        .test-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .test-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border-color: #ff6b6b;
        }

        .test-card .icon {
            font-size: 3rem;
            color: #ff6b6b;
            margin-bottom: 1rem;
        }

        .test-card h3 {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 1rem;
        }

        .test-card p {
            color: #666;
            margin-bottom: 1.5rem;
            line-height: 1.6;
        }

        .test-btn {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
        }

        .results-section {
            background: #e3f2fd;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .results-section h2 {
            color: #1976d2;
            margin-bottom: 1rem;
            text-align: center;
        }

        .result-item {
            background: white;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            border-right: 4px solid #4caf50;
        }

        .result-item.error {
            border-right-color: #f44336;
        }

        .result-item.warning {
            border-right-color: #ff9800;
        }

        .back-section {
            text-align: center;
            margin-top: 3rem;
        }

        .back-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .back-btn:hover {
            background: #5a6268;
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .test-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-vial"></i> اختبار النظام الإداري</h1>
            <p>أدوات اختبار وتشخيص النظام الإداري للمتجر</p>
        </div>

        <div class="test-grid">
            <div class="test-card">
                <div class="icon">
                    <i class="fas fa-database"></i>
                </div>
                <h3>اختبار قاعدة البيانات</h3>
                <p>فحص حالة قاعدة البيانات المحلية والتأكد من سلامة البيانات المحفوظة</p>
                <button class="test-btn" onclick="testDatabase()">بدء الاختبار</button>
            </div>

            <div class="test-card">
                <div class="icon">
                    <i class="fas fa-users"></i>
                </div>
                <h3>اختبار نظام الموظفين</h3>
                <p>التحقق من صحة بيانات الموظفين وصلاحياتهم في النظام</p>
                <button class="test-btn" onclick="testEmployees()">بدء الاختبار</button>
            </div>

            <div class="test-card">
                <div class="icon">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <h3>اختبار نظام المنتجات</h3>
                <p>فحص المنتجات والفئات والتأكد من صحة البيانات المدخلة</p>
                <button class="test-btn" onclick="testProducts()">بدء الاختبار</button>
            </div>

            <div class="test-card">
                <div class="icon">
                    <i class="fas fa-key"></i>
                </div>
                <h3>اختبار نظام الصلاحيات</h3>
                <p>التحقق من صحة عمل نظام الصلاحيات والتحكم في الوصول</p>
                <button class="test-btn" onclick="testPermissions()">بدء الاختبار</button>
            </div>

            <div class="test-card">
                <div class="icon">
                    <i class="fas fa-cog"></i>
                </div>
                <h3>اختبار الإعدادات</h3>
                <p>فحص إعدادات النظام والتأكد من صحة التكوين العام</p>
                <button class="test-btn" onclick="testSettings()">بدء الاختبار</button>
            </div>

            <div class="test-card">
                <div class="icon">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <h3>اختبار التقارير</h3>
                <p>التحقق من صحة عمل نظام التقارير والإحصائيات</p>
                <button class="test-btn" onclick="testReports()">بدء الاختبار</button>
            </div>
        </div>

        <div class="results-section" id="resultsSection" style="display: none;">
            <h2><i class="fas fa-clipboard-list"></i> نتائج الاختبارات</h2>
            <div id="testResults"></div>
        </div>

        <div class="back-section">
            <a href="admin.html" class="back-btn">
                <i class="fas fa-arrow-right"></i> العودة للوحة التحكم
            </a>
        </div>
    </div>

    <script>
        let testResults = [];

        function addResult(test, status, message) {
            testResults.push({ test, status, message, time: new Date().toLocaleString('ar') });
            displayResults();
        }

        function displayResults() {
            const resultsSection = document.getElementById('resultsSection');
            const testResultsDiv = document.getElementById('testResults');
            
            resultsSection.style.display = 'block';
            testResultsDiv.innerHTML = '';
            
            testResults.forEach(result => {
                const resultDiv = document.createElement('div');
                resultDiv.className = `result-item ${result.status}`;
                resultDiv.innerHTML = `
                    <strong>${result.test}</strong> - ${result.time}<br>
                    <span style="color: ${result.status === 'success' ? '#4caf50' : result.status === 'error' ? '#f44336' : '#ff9800'};">
                        ${result.message}
                    </span>
                `;
                testResultsDiv.appendChild(resultDiv);
            });
        }

        function testDatabase() {
            try {
                // اختبار localStorage
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                
                // فحص البيانات الموجودة
                const employees = JSON.parse(localStorage.getItem('employees')) || [];
                const products = JSON.parse(localStorage.getItem('products')) || [];
                const categories = JSON.parse(localStorage.getItem('categories')) || [];
                
                addResult('اختبار قاعدة البيانات', 'success', 
                    `قاعدة البيانات تعمل بشكل صحيح. الموظفين: ${employees.length}, المنتجات: ${products.length}, الفئات: ${categories.length}`);
            } catch (error) {
                addResult('اختبار قاعدة البيانات', 'error', `خطأ في قاعدة البيانات: ${error.message}`);
            }
        }

        function testEmployees() {
            try {
                const employees = JSON.parse(localStorage.getItem('employees')) || [];
                
                if (employees.length === 0) {
                    addResult('اختبار نظام الموظفين', 'warning', 'لا يوجد موظفين في النظام');
                    return;
                }
                
                const activeEmployees = employees.filter(emp => emp.status === 'active');
                const roles = [...new Set(employees.map(emp => emp.role))];
                
                addResult('اختبار نظام الموظفين', 'success', 
                    `النظام يحتوي على ${employees.length} موظف، ${activeEmployees.length} نشط. الأدوار: ${roles.join(', ')}`);
            } catch (error) {
                addResult('اختبار نظام الموظفين', 'error', `خطأ في نظام الموظفين: ${error.message}`);
            }
        }

        function testProducts() {
            try {
                const products = JSON.parse(localStorage.getItem('products')) || [];
                const categories = JSON.parse(localStorage.getItem('categories')) || [];
                
                if (products.length === 0) {
                    addResult('اختبار نظام المنتجات', 'warning', 'لا توجد منتجات في النظام');
                } else {
                    addResult('اختبار نظام المنتجات', 'success', 
                        `النظام يحتوي على ${products.length} منتج و ${categories.length} فئة`);
                }
            } catch (error) {
                addResult('اختبار نظام المنتجات', 'error', `خطأ في نظام المنتجات: ${error.message}`);
            }
        }

        function testPermissions() {
            try {
                const userSession = sessionStorage.getItem('userSession');
                
                if (!userSession) {
                    addResult('اختبار نظام الصلاحيات', 'warning', 'لا توجد جلسة مستخدم نشطة');
                    return;
                }
                
                const user = JSON.parse(userSession);
                addResult('اختبار نظام الصلاحيات', 'success', 
                    `المستخدم ${user.name} مسجل دخول بصفة ${user.role}`);
            } catch (error) {
                addResult('اختبار نظام الصلاحيات', 'error', `خطأ في نظام الصلاحيات: ${error.message}`);
            }
        }

        function testSettings() {
            try {
                const settings = JSON.parse(localStorage.getItem('siteSettings')) || {};
                const contactSettings = JSON.parse(localStorage.getItem('contactSettings')) || {};
                
                addResult('اختبار الإعدادات', 'success', 
                    `إعدادات الموقع: ${Object.keys(settings).length} عنصر، إعدادات التواصل: ${Object.keys(contactSettings).length} عنصر`);
            } catch (error) {
                addResult('اختبار الإعدادات', 'error', `خطأ في الإعدادات: ${error.message}`);
            }
        }

        function testReports() {
            try {
                // محاكاة اختبار التقارير
                const products = JSON.parse(localStorage.getItem('products')) || [];
                const employees = JSON.parse(localStorage.getItem('employees')) || [];
                
                addResult('اختبار التقارير', 'success', 
                    `نظام التقارير يعمل بشكل صحيح. يمكن إنشاء تقارير للمنتجات والموظفين`);
            } catch (error) {
                addResult('اختبار التقارير', 'error', `خطأ في نظام التقارير: ${error.message}`);
            }
        }

        // تشغيل اختبار سريع عند تحميل الصفحة
        window.addEventListener('load', function() {
            console.log('🧪 صفحة اختبار النظام الإداري جاهزة');
        });
    </script>
</body>
</html>
