<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار رفع ملف الإكسل</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #333;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #666;
            font-size: 1.1rem;
        }
        
        .upload-area {
            border: 3px dashed #ddd;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            margin-bottom: 30px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .upload-area:hover {
            border-color: #667eea;
            background: #f8f9ff;
        }
        
        .upload-area.dragover {
            border-color: #667eea;
            background: #f0f4ff;
        }
        
        .upload-icon {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 20px;
        }
        
        .upload-text h3 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.5rem;
        }
        
        .upload-text p {
            color: #666;
            margin-bottom: 20px;
        }
        
        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 5px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }
        
        .btn-info {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }
        
        .results {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
            display: none;
        }
        
        .results h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .log {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-success { color: #2ecc71; }
        .log-error { color: #e74c3c; }
        .log-warning { color: #f39c12; }
        .log-info { color: #3498db; }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        
        .hidden {
            display: none;
        }
        
        .file-info {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #3498db;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 اختبار رفع ملف الإكسل</h1>
            <p>اختبار شامل لوظيفة رفع وتحليل ملفات Excel و CSV</p>
        </div>
        
        <div class="upload-area" id="uploadArea" onclick="document.getElementById('fileInput').click()">
            <div class="upload-icon">📁</div>
            <div class="upload-text">
                <h3>اسحب ملف Excel أو CSV هنا أو انقر للاختيار</h3>
                <p>يدعم ملفات .xlsx, .xls, .csv</p>
                <button class="btn btn-primary">اختيار ملف</button>
            </div>
        </div>
        
        <input type="file" id="fileInput" accept=".xlsx,.xls,.csv" class="hidden">
        
        <div class="file-info hidden" id="fileInfo">
            <h4>معلومات الملف:</h4>
            <div id="fileDetails"></div>
        </div>
        
        <div class="text-center">
            <button class="btn btn-success" onclick="processFile()" id="processBtn" disabled>
                📊 تحليل الملف
            </button>
            <button class="btn btn-info" onclick="saveToLocalStorage()" id="saveBtn" disabled>
                💾 حفظ في النظام
            </button>
            <button class="btn btn-info" onclick="checkStoredData()">
                🔍 فحص البيانات المحفوظة
            </button>
            <button class="btn btn-info" onclick="openCashier()">
                🛒 فتح الكاشير
            </button>
            <button class="btn btn-danger" onclick="clearLog()">
                🗑️ مسح السجل
            </button>
        </div>
        
        <div class="results" id="results">
            <h3>نتائج التحليل</h3>
            <div class="stats" id="stats"></div>
        </div>
        
        <div class="log" id="log"></div>
    </div>

    <script>
        let selectedFile = null;
        let parsedData = [];
        let validProducts = [];
        
        // إعداد الأحداث
        document.addEventListener('DOMContentLoaded', function() {
            const fileInput = document.getElementById('fileInput');
            const uploadArea = document.getElementById('uploadArea');
            
            fileInput.addEventListener('change', handleFileSelection);
            
            // السحب والإفلات
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('dragleave', handleDragLeave);
            uploadArea.addEventListener('drop', handleDrop);
            
            log('✅ تم تحميل النظام بنجاح', 'success');
            log('📋 جاهز لرفع الملفات', 'info');
        });
        
        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${timestamp}] ${message}`;
            logElement.appendChild(entry);
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
            log('🗑️ تم مسح السجل', 'info');
        }
        
        function handleFileSelection(event) {
            const file = event.target.files[0];
            if (file) {
                selectFile(file);
            }
        }
        
        function handleDragOver(event) {
            event.preventDefault();
            event.currentTarget.classList.add('dragover');
        }
        
        function handleDragLeave(event) {
            event.currentTarget.classList.remove('dragover');
        }
        
        function handleDrop(event) {
            event.preventDefault();
            event.currentTarget.classList.remove('dragover');
            
            const files = event.dataTransfer.files;
            if (files.length > 0) {
                selectFile(files[0]);
            }
        }
        
        function selectFile(file) {
            selectedFile = file;
            
            log(`📁 تم اختيار الملف: ${file.name}`, 'success');
            log(`📊 نوع الملف: ${file.type}`, 'info');
            log(`📏 حجم الملف: ${formatFileSize(file.size)}`, 'info');
            
            // عرض معلومات الملف
            const fileInfo = document.getElementById('fileInfo');
            const fileDetails = document.getElementById('fileDetails');
            
            fileDetails.innerHTML = `
                <p><strong>الاسم:</strong> ${file.name}</p>
                <p><strong>النوع:</strong> ${file.type}</p>
                <p><strong>الحجم:</strong> ${formatFileSize(file.size)}</p>
                <p><strong>آخر تعديل:</strong> ${new Date(file.lastModified).toLocaleString('ar-SA')}</p>
            `;
            
            fileInfo.classList.remove('hidden');
            document.getElementById('processBtn').disabled = false;
        }
        
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        function processFile() {
            if (!selectedFile) {
                log('❌ لم يتم اختيار ملف', 'error');
                return;
            }
            
            log('🔄 بدء تحليل الملف...', 'info');
            
            const reader = new FileReader();
            
            reader.onload = function(e) {
                try {
                    const fileName = selectedFile.name.toLowerCase();
                    
                    if (fileName.endsWith('.csv')) {
                        log('📊 تحليل ملف CSV...', 'info');
                        parseCSV(e.target.result);
                    } else {
                        log('📊 تحليل ملف Excel...', 'info');
                        parseExcel(e.target.result);
                    }
                } catch (error) {
                    log(`❌ خطأ في تحليل الملف: ${error.message}`, 'error');
                }
            };
            
            reader.onerror = function() {
                log('❌ فشل في قراءة الملف', 'error');
            };
            
            if (selectedFile.name.toLowerCase().endsWith('.csv')) {
                reader.readAsText(selectedFile, 'UTF-8');
            } else {
                reader.readAsArrayBuffer(selectedFile);
            }
        }
        
        function parseCSV(csvText) {
            const lines = csvText.split('\n').filter(line => line.trim());
            log(`📋 تم العثور على ${lines.length} صف`, 'info');
            
            if (lines.length < 2) {
                log('❌ الملف يجب أن يحتوي على صف العناوين وبيانات', 'error');
                return;
            }
            
            const headers = lines[0].split(',').map(h => h.trim());
            log(`📋 أعمدة الملف: ${headers.join(', ')}`, 'info');
            
            parsedData = [];
            validProducts = [];
            
            for (let i = 1; i < lines.length; i++) {
                const values = lines[i].split(',').map(v => v.trim());
                
                if (values.length >= headers.length && values[0]) {
                    const product = {
                        rowNumber: i + 1,
                        nameAr: values[0] || '',
                        nameEn: values[1] || '',
                        category: values[2] || '',
                        price: values[3] || '',
                        wholesalePrice: values[4] || '',
                        oldPrice: values[5] || '',
                        descriptionAr: values[6] || '',
                        descriptionEn: values[7] || '',
                        image: values[8] || '',
                        quantity: values[9] || '',
                        status: values[10] || ''
                    };
                    
                    parsedData.push(product);
                    
                    if (validateProduct(product)) {
                        validProducts.push({
                            ...product,
                            id: `product_${Date.now()}_${i}`,
                            createdAt: new Date().toISOString()
                        });
                    }
                }
            }
            
            showResults();
        }
        
        function parseExcel(arrayBuffer) {
            try {
                const workbook = XLSX.read(arrayBuffer, { type: 'array' });
                const firstSheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[firstSheetName];
                
                log(`📊 تحليل ورقة العمل: ${firstSheetName}`, 'info');
                
                const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
                
                if (jsonData.length < 2) {
                    log('❌ الملف فارغ أو لا يحتوي على بيانات كافية', 'error');
                    return;
                }
                
                log(`📋 تم العثور على ${jsonData.length} صف`, 'info');
                
                const headers = jsonData[0];
                log(`📋 أعمدة الملف: ${headers.join(', ')}`, 'info');
                
                parsedData = [];
                validProducts = [];
                
                for (let i = 1; i < jsonData.length; i++) {
                    const row = jsonData[i];
                    
                    if (row && row.length > 0 && row[0]) {
                        const product = {
                            rowNumber: i + 1,
                            nameAr: row[0] || '',
                            nameEn: row[1] || '',
                            category: row[2] || '',
                            price: row[3] || '',
                            wholesalePrice: row[4] || '',
                            oldPrice: row[5] || '',
                            descriptionAr: row[6] || '',
                            descriptionEn: row[7] || '',
                            image: row[8] || '',
                            quantity: row[9] || '',
                            status: row[10] || ''
                        };
                        
                        parsedData.push(product);
                        
                        if (validateProduct(product)) {
                            validProducts.push({
                                ...product,
                                id: `product_${Date.now()}_${i}`,
                                createdAt: new Date().toISOString()
                            });
                        }
                    }
                }
                
                showResults();
                
            } catch (error) {
                log(`❌ خطأ في تحليل Excel: ${error.message}`, 'error');
            }
        }
        
        function validateProduct(product) {
            if (!product.nameAr || !product.category || !product.price) {
                return false;
            }
            
            if (isNaN(parseFloat(product.price)) || parseFloat(product.price) <= 0) {
                return false;
            }
            
            return true;
        }
        
        function showResults() {
            const resultsDiv = document.getElementById('results');
            const statsDiv = document.getElementById('stats');
            
            const totalRows = parsedData.length;
            const validRows = validProducts.length;
            const invalidRows = totalRows - validRows;
            
            log(`✅ تم تحليل ${totalRows} صف`, 'success');
            log(`✅ منتجات صحيحة: ${validRows}`, 'success');
            log(`⚠️ منتجات خاطئة: ${invalidRows}`, invalidRows > 0 ? 'warning' : 'info');
            
            statsDiv.innerHTML = `
                <div class="stat-card">
                    <div class="stat-number">${totalRows}</div>
                    <div class="stat-label">إجمالي الصفوف</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${validRows}</div>
                    <div class="stat-label">منتجات صحيحة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${invalidRows}</div>
                    <div class="stat-label">منتجات خاطئة</div>
                </div>
            `;
            
            resultsDiv.style.display = 'block';
            document.getElementById('saveBtn').disabled = validRows === 0;
            
            // عرض عينة من المنتجات
            if (validProducts.length > 0) {
                log('📄 عينة من المنتجات الصحيحة:', 'info');
                validProducts.slice(0, 3).forEach((product, index) => {
                    log(`${index + 1}. ${product.nameAr} - ${product.price} دينار`, 'info');
                });
            }
        }
        
        function saveToLocalStorage() {
            if (validProducts.length === 0) {
                log('❌ لا توجد منتجات صحيحة للحفظ', 'error');
                return;
            }
            
            try {
                // تحميل المنتجات الحالية
                const existingProducts = JSON.parse(localStorage.getItem('adminProducts')) || [];
                log(`📦 تم العثور على ${existingProducts.length} منتج موجود`, 'info');
                
                // تحويل المنتجات للتنسيق المطلوب
                const formattedProducts = validProducts.map(product => ({
                    id: product.id,
                    name: product.nameAr,
                    nameAr: product.nameAr,
                    nameEn: product.nameEn || product.nameAr,
                    category: product.category,
                    price: parseFloat(product.price),
                    wholesalePrice: parseFloat(product.wholesalePrice) || parseFloat(product.price),
                    oldPrice: product.oldPrice ? parseFloat(product.oldPrice) : null,
                    description: product.descriptionAr || '',
                    descriptionAr: product.descriptionAr || '',
                    descriptionEn: product.descriptionEn || '',
                    image: product.image || 'https://via.placeholder.com/300x300?text=No+Image',
                    quantity: parseInt(product.quantity) || 0,
                    stock: parseInt(product.quantity) || 0,
                    status: product.status === 'متوفر' ? 'available' : 'unavailable',
                    featured: false,
                    createdAt: product.createdAt,
                    updatedAt: new Date().toISOString(),
                    barcode: `BC${Date.now()}${Math.floor(Math.random() * 1000)}`,
                    available: parseInt(product.quantity) > 0,
                    inStock: parseInt(product.quantity) > 0
                }));
                
                // دمج مع المنتجات الموجودة
                const allProducts = [...existingProducts, ...formattedProducts];
                
                // حفظ في localStorage
                localStorage.setItem('adminProducts', JSON.stringify(allProducts));
                localStorage.setItem('products', JSON.stringify(allProducts));
                
                log(`✅ تم حفظ ${formattedProducts.length} منتج جديد`, 'success');
                log(`📦 إجمالي المنتجات: ${allProducts.length}`, 'success');
                
                // عرض عينة من البيانات المحفوظة
                log('📄 عينة من البيانات المحفوظة:', 'info');
                console.log(formattedProducts[0]);
                
            } catch (error) {
                log(`❌ خطأ في حفظ البيانات: ${error.message}`, 'error');
            }
        }
        
        function checkStoredData() {
            try {
                const adminProducts = JSON.parse(localStorage.getItem('adminProducts')) || [];
                const products = JSON.parse(localStorage.getItem('products')) || [];
                
                log('🔍 فحص البيانات المحفوظة:', 'info');
                log(`📦 adminProducts: ${adminProducts.length} منتج`, 'info');
                log(`📦 products: ${products.length} منتج`, 'info');
                
                if (adminProducts.length > 0) {
                    log('📄 عينة من البيانات:', 'info');
                    const sample = adminProducts[0];
                    log(`- الاسم: ${sample.name || sample.nameAr}`, 'info');
                    log(`- السعر: ${sample.price}`, 'info');
                    log(`- الفئة: ${sample.category}`, 'info');
                    log(`- المخزون: ${sample.quantity || sample.stock}`, 'info');
                    
                    console.log('عينة كاملة من البيانات:', sample);
                } else {
                    log('⚠️ لا توجد منتجات محفوظة', 'warning');
                }
                
            } catch (error) {
                log(`❌ خطأ في فحص البيانات: ${error.message}`, 'error');
            }
        }
        
        function openCashier() {
            const adminProducts = JSON.parse(localStorage.getItem('adminProducts')) || [];
            
            if (adminProducts.length === 0) {
                log('⚠️ لا توجد منتجات لعرضها في الكاشير', 'warning');
                alert('لا توجد منتجات محفوظة. قم برفع ملف Excel أولاً.');
                return;
            }
            
            log(`🛒 فتح الكاشير مع ${adminProducts.length} منتج`, 'success');
            
            const cashierWindow = window.open('cashier.html', '_blank');
            
            if (cashierWindow) {
                log('✅ تم فتح واجهة الكاشير', 'success');
            } else {
                log('❌ فشل في فتح واجهة الكاشير', 'error');
                alert('فشل في فتح واجهة الكاشير. تحقق من إعدادات المتصفح.');
            }
        }
    </script>
</body>
</html>
