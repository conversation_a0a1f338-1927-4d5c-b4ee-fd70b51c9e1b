<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التكامل بين الواجهات</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #333;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #666;
            font-size: 1.1rem;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .test-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .test-card:hover {
            border-color: #667eea;
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.2);
        }
        
        .test-card h3 {
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .test-card p {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.6;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
        }
        
        .btn-info {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .status-panel {
            background: #2c3e50;
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .status-panel h3 {
            margin-bottom: 15px;
            color: #ecf0f1;
        }
        
        .log {
            background: #34495e;
            border-radius: 10px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-success { color: #2ecc71; }
        .log-error { color: #e74c3c; }
        .log-warning { color: #f39c12; }
        .log-info { color: #3498db; }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .stat-card {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 1.8rem;
            font-weight: bold;
            color: #3498db;
        }
        
        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 اختبار التكامل بين الواجهات</h1>
            <p>اختبار شامل للتكامل بين الموقع الإلكتروني وواجهة الكاشير ولوحة التحكم</p>
        </div>
        
        <div class="test-grid">
            <!-- اختبار الموقع الإلكتروني -->
            <div class="test-card">
                <h3>
                    <i class="fas fa-globe" style="color: #27ae60;"></i>
                    الموقع الإلكتروني
                </h3>
                <p>اختبار عرض المنتجات والتفاعل مع السلة في الموقع الرئيسي</p>
                <a href="index.html" target="_blank" class="btn btn-success">
                    <i class="fas fa-external-link-alt"></i>
                    فتح الموقع
                </a>
                <button class="btn btn-info" onclick="checkWebsiteProducts()">
                    <i class="fas fa-search"></i>
                    فحص المنتجات
                </button>
            </div>
            
            <!-- اختبار واجهة الكاشير -->
            <div class="test-card">
                <h3>
                    <i class="fas fa-cash-register" style="color: #3498db;"></i>
                    واجهة الكاشير
                </h3>
                <p>اختبار نظام البيع والمخزون والآلة الحاسبة المتقدمة</p>
                <a href="cashier.html" target="_blank" class="btn btn-primary">
                    <i class="fas fa-external-link-alt"></i>
                    فتح الكاشير
                </a>
                <button class="btn btn-info" onclick="checkCashierProducts()">
                    <i class="fas fa-search"></i>
                    فحص المنتجات
                </button>
            </div>
            
            <!-- اختبار لوحة التحكم -->
            <div class="test-card">
                <h3>
                    <i class="fas fa-cogs" style="color: #f39c12;"></i>
                    لوحة التحكم
                </h3>
                <p>اختبار إضافة المنتجات ورفع ملفات Excel والإدارة</p>
                <a href="admin.html" target="_blank" class="btn btn-warning">
                    <i class="fas fa-external-link-alt"></i>
                    فتح لوحة التحكم
                </a>
                <button class="btn btn-info" onclick="checkAdminProducts()">
                    <i class="fas fa-search"></i>
                    فحص المنتجات
                </button>
            </div>
            
            <!-- اختبار رفع Excel -->
            <div class="test-card">
                <h3>
                    <i class="fas fa-file-excel" style="color: #27ae60;"></i>
                    رفع ملف Excel
                </h3>
                <p>اختبار رفع وتحليل ملفات Excel مع المعاينة والتحقق</p>
                <a href="test_excel_upload.html" target="_blank" class="btn btn-success">
                    <i class="fas fa-external-link-alt"></i>
                    صفحة الاختبار
                </a>
                <button class="btn btn-info" onclick="downloadSampleFile()">
                    <i class="fas fa-download"></i>
                    تحميل ملف تجريبي
                </button>
            </div>
        </div>
        
        <!-- اختبارات التكامل -->
        <div class="test-grid">
            <div class="test-card">
                <h3>
                    <i class="fas fa-sync-alt" style="color: #9b59b6;"></i>
                    اختبار المزامنة
                </h3>
                <p>اختبار مزامنة المنتجات بين جميع الواجهات</p>
                <button class="btn btn-primary" onclick="testProductSync()">
                    <i class="fas fa-sync"></i>
                    اختبار المزامنة
                </button>
                <button class="btn btn-info" onclick="simulateProductUpdate()">
                    <i class="fas fa-plus"></i>
                    محاكاة إضافة منتج
                </button>
            </div>
            
            <div class="test-card">
                <h3>
                    <i class="fas fa-database" style="color: #e74c3c;"></i>
                    إدارة البيانات
                </h3>
                <p>فحص وإدارة بيانات المنتجات في localStorage</p>
                <button class="btn btn-info" onclick="checkAllData()">
                    <i class="fas fa-search"></i>
                    فحص جميع البيانات
                </button>
                <button class="btn btn-warning" onclick="clearAllData()">
                    <i class="fas fa-trash"></i>
                    مسح البيانات
                </button>
            </div>
        </div>
        
        <!-- لوحة الحالة -->
        <div class="status-panel">
            <h3>📊 حالة النظام</h3>
            <div class="stats" id="systemStats">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
            <div class="log" id="testLog">
                <div class="log-entry log-info">[بدء] مرحباً بك في نظام اختبار التكامل</div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <script>
        // متغيرات النظام
        let testResults = {
            website: 0,
            cashier: 0,
            admin: 0,
            sync: 0
        };

        // تحميل الإحصائيات عند بدء الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateSystemStats();
            log('✅ تم تحميل نظام اختبار التكامل', 'success');
        });

        // وظيفة تسجيل الأحداث
        function log(message, type = 'info') {
            const logElement = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${timestamp}] ${message}`;
            logElement.appendChild(entry);
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        // تحديث إحصائيات النظام
        function updateSystemStats() {
            const adminProducts = JSON.parse(localStorage.getItem('adminProducts')) || [];
            const products = JSON.parse(localStorage.getItem('products')) || [];
            const cart = JSON.parse(localStorage.getItem('cart')) || [];
            const coupons = JSON.parse(localStorage.getItem('mainSiteCoupons')) || {};

            const statsHTML = `
                <div class="stat-card">
                    <div class="stat-number">${adminProducts.length}</div>
                    <div class="stat-label">منتجات الإدارة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${products.length}</div>
                    <div class="stat-label">منتجات عامة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${cart.length}</div>
                    <div class="stat-label">منتجات في السلة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${Object.keys(coupons).length}</div>
                    <div class="stat-label">كوبونات متاحة</div>
                </div>
            `;

            document.getElementById('systemStats').innerHTML = statsHTML;
        }

        // فحص منتجات الموقع الإلكتروني
        function checkWebsiteProducts() {
            log('🔍 فحص منتجات الموقع الإلكتروني...', 'info');

            const adminProducts = JSON.parse(localStorage.getItem('adminProducts')) || [];
            const products = JSON.parse(localStorage.getItem('products')) || [];

            if (adminProducts.length > 0) {
                log(`✅ تم العثور على ${adminProducts.length} منتج في adminProducts`, 'success');
                testResults.website++;
            } else {
                log('⚠️ لا توجد منتجات في adminProducts', 'warning');
            }

            if (products.length > 0) {
                log(`✅ تم العثور على ${products.length} منتج في products`, 'success');
            } else {
                log('⚠️ لا توجد منتجات في products', 'warning');
            }

            updateSystemStats();
        }

        // فحص منتجات الكاشير
        function checkCashierProducts() {
            log('🔍 فحص منتجات الكاشير...', 'info');

            const adminProducts = JSON.parse(localStorage.getItem('adminProducts')) || [];

            if (adminProducts.length > 0) {
                log(`✅ الكاشير يمكنه الوصول لـ ${adminProducts.length} منتج`, 'success');

                // فحص تفاصيل المنتجات
                const sampleProduct = adminProducts[0];
                const requiredFields = ['id', 'name', 'price', 'stock', 'category'];
                const missingFields = requiredFields.filter(field => !sampleProduct[field]);

                if (missingFields.length === 0) {
                    log('✅ جميع الحقول المطلوبة للكاشير موجودة', 'success');
                    testResults.cashier++;
                } else {
                    log(`❌ حقول مفقودة للكاشير: ${missingFields.join(', ')}`, 'error');
                }
            } else {
                log('❌ لا توجد منتجات متاحة للكاشير', 'error');
            }

            updateSystemStats();
        }

        // فحص منتجات لوحة التحكم
        function checkAdminProducts() {
            log('🔍 فحص منتجات لوحة التحكم...', 'info');

            const adminProducts = JSON.parse(localStorage.getItem('adminProducts')) || [];

            if (adminProducts.length > 0) {
                log(`✅ لوحة التحكم تحتوي على ${adminProducts.length} منتج`, 'success');

                // فحص تنوع الفئات
                const categories = [...new Set(adminProducts.map(p => p.category))];
                log(`📂 عدد الفئات: ${categories.length} (${categories.join(', ')})`, 'info');

                // فحص المنتجات المميزة
                const featuredCount = adminProducts.filter(p => p.featured).length;
                log(`⭐ منتجات مميزة: ${featuredCount}`, 'info');

                testResults.admin++;
            } else {
                log('❌ لوحة التحكم فارغة من المنتجات', 'error');
            }

            updateSystemStats();
        }

        // اختبار مزامنة المنتجات
        function testProductSync() {
            log('🔄 اختبار مزامنة المنتجات...', 'info');

            const adminProducts = JSON.parse(localStorage.getItem('adminProducts')) || [];
            const products = JSON.parse(localStorage.getItem('products')) || [];

            if (adminProducts.length === products.length) {
                log('✅ المنتجات متزامنة بين adminProducts و products', 'success');

                // فحص تطابق البيانات
                let syncErrors = 0;
                adminProducts.forEach((adminProduct, index) => {
                    const product = products.find(p => p.id === adminProduct.id);
                    if (!product) {
                        syncErrors++;
                    } else if (product.stock !== adminProduct.stock) {
                        syncErrors++;
                    }
                });

                if (syncErrors === 0) {
                    log('✅ جميع البيانات متطابقة بين المصادر', 'success');
                    testResults.sync++;
                } else {
                    log(`⚠️ تم العثور على ${syncErrors} خطأ في المزامنة`, 'warning');
                }
            } else {
                log(`❌ عدم تطابق في عدد المنتجات: adminProducts(${adminProducts.length}) vs products(${products.length})`, 'error');
            }

            updateSystemStats();
        }

        // محاكاة إضافة منتج
        function simulateProductUpdate() {
            log('🧪 محاكاة إضافة منتج جديد...', 'info');

            const newProduct = {
                id: `test_${Date.now()}`,
                name: `منتج تجريبي ${new Date().toLocaleTimeString('ar-SA')}`,
                nameAr: `منتج تجريبي ${new Date().toLocaleTimeString('ar-SA')}`,
                nameEn: `Test Product ${new Date().toLocaleTimeString()}`,
                category: 'اختبار',
                price: Math.floor(Math.random() * 50000) + 10000,
                stock: Math.floor(Math.random() * 100) + 1,
                quantity: Math.floor(Math.random() * 100) + 1,
                status: 'available',
                featured: Math.random() > 0.5,
                image: 'https://via.placeholder.com/300x300?text=منتج+تجريبي',
                description: 'منتج تجريبي لاختبار النظام',
                createdAt: new Date().toISOString()
            };

            // إضافة للمنتجات
            const adminProducts = JSON.parse(localStorage.getItem('adminProducts')) || [];
            adminProducts.push(newProduct);
            localStorage.setItem('adminProducts', JSON.stringify(adminProducts));
            localStorage.setItem('products', JSON.stringify(adminProducts));

            log(`✅ تم إضافة منتج تجريبي: ${newProduct.name}`, 'success');
            log(`💰 السعر: ${newProduct.price.toLocaleString()} دينار`, 'info');
            log(`📦 المخزون: ${newProduct.stock} قطعة`, 'info');

            // إرسال إشعار التحديث
            try {
                window.dispatchEvent(new CustomEvent('productsUpdated', {
                    detail: {
                        source: 'test_simulation',
                        count: 1,
                        action: 'add_product',
                        productName: newProduct.name
                    }
                }));
                log('📢 تم إرسال إشعار التحديث', 'success');
            } catch (error) {
                log('⚠️ تعذر إرسال إشعار التحديث', 'warning');
            }

            updateSystemStats();
        }

        // فحص جميع البيانات
        function checkAllData() {
            log('🔍 فحص جميع بيانات النظام...', 'info');

            const keys = [
                'adminProducts',
                'products',
                'cart',
                'mainSiteCoupons',
                'calculatorHistory',
                'lastSales',
                'heldSales'
            ];

            keys.forEach(key => {
                const data = localStorage.getItem(key);
                if (data) {
                    try {
                        const parsed = JSON.parse(data);
                        const count = Array.isArray(parsed) ? parsed.length : Object.keys(parsed).length;
                        log(`📊 ${key}: ${count} عنصر`, 'info');
                    } catch (error) {
                        log(`❌ خطأ في تحليل ${key}`, 'error');
                    }
                } else {
                    log(`⚪ ${key}: غير موجود`, 'warning');
                }
            });

            updateSystemStats();
        }

        // مسح جميع البيانات
        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
                log('🗑️ بدء مسح جميع البيانات...', 'warning');

                const keys = Object.keys(localStorage);
                let clearedCount = 0;

                keys.forEach(key => {
                    localStorage.removeItem(key);
                    clearedCount++;
                });

                log(`✅ تم مسح ${clearedCount} عنصر من localStorage`, 'success');
                log('🔄 يرجى إعادة تحميل الصفحات الأخرى', 'info');

                // إعادة تعيين النتائج
                testResults = { website: 0, cashier: 0, admin: 0, sync: 0 };
                updateSystemStats();
            }
        }

        // تحميل ملف تجريبي
        function downloadSampleFile() {
            log('📥 تحميل ملف Excel التجريبي...', 'info');

            // إنشاء رابط تحميل للملف التجريبي
            const link = document.createElement('a');
            link.href = 'test_products_excel.csv';
            link.download = 'test_products_excel.csv';
            link.click();

            log('✅ تم بدء تحميل الملف التجريبي', 'success');
        }

        // الاستماع لتحديثات المنتجات
        window.addEventListener('productsUpdated', function(event) {
            log(`📢 تم استلام إشعار تحديث: ${event.detail.source}`, 'success');
            if (event.detail.count) {
                log(`📦 عدد المنتجات المحدثة: ${event.detail.count}`, 'info');
            }
            updateSystemStats();
        });
    </script>
</body>
</html>
